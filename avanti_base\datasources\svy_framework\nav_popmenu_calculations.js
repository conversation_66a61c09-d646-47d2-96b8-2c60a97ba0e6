/**
 * @properties={type:12,typeid:36,uuid:"65EE50F0-8292-43F1-9C03-CF45D2400356"}
 */
function rpt_menu_selected()
{
	var bFull = true;
	if (utils.hasRecords(nav_popmenu_to_nav_popmenu$children_inner_join))
	{
		var rSubMenu;
		for (var i = 1; i <= nav_popmenu_to_nav_popmenu$children.getSize(); i++)
		{
			rSubMenu = nav_popmenu_to_nav_popmenu$children.getRecord(i);
			
			if (!rSubMenu.rpt_id) 
			{
				bFull = false;
				break;
			}
		}
	}
	else if (globals.avRpt_selectedReport == rpt_id && globals.avRpt_selectedProgram == program_name)
	{
		bFull = true
	}
	else
	{
		bFull = false;
	}
	
	return bFull;
	
//	if (bFull)
//	{
//		return globals.icon_checkbox_checked;
//	}
//	else
//	{
//		return globals.icon_checkbox_unchecked;
//	}
}

/**
 * @properties={type:12,typeid:36,uuid:"C331A156-2A1F-43B8-9B37-9504E9F4FEE1"}
 */
function rpt_submenu_selected()
{
	if(globals.avRpt_selectedReport == rpt_id && globals.avRpt_selectedProgram == program_name)
	{
		return true;
//		return globals.icon_checkbox_checked;
	}
	else if (rpt_id)
	{
		return false;
	}
	else
	{
		return false;
//		return globals.icon_checkbox_unchecked;
	}
}

/**
 * @return {MEDIA}
 *
 *
 * @properties={typeid:36,uuid:"DF3C226C-C58E-410D-AC3F-F953B1168CA4"}
 */
function rpt_submenu_enabled()
{
	if(globals.avRpt_selectedReport == rpt_id && globals.avRpt_selectedProgram == program_name)
	{
		return true;
	}
	else if (rpt_id)
	{
		return false;
	}
	else
	{
		return true;
	}
}
