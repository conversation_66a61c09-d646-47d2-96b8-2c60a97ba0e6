/**
 * @public 
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"48C1E1A8-64C4-4CA5-8B09-A151434929E8",variableType:-4}
 */
var bGetTestPackage = false;

/**
 * @properties={typeid:35,uuid:"7C323099-4EEB-4E41-809D-FA3B79A820DD",variableType:-4}
 */
var bDebug = false;

//abbreviation 'WWMS' stands for 'WIRELESS_WAREHOUSE_MANAGEMENT_SERVICE'
/**
 * @type {JSON}
 *
 * @properties={typeid:35,uuid:"*************-4DD7-B890-C50168D1284B",variableType:-4}
 */
var WWMS_security_program_name = {
    "Inventory_Transaction_Entry": {
        "nameForResponse": "DirectInventoryReceipt",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Item_Inquiry": {
        "nameForResponse": "InventoryInquiry",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Receipts_From_Production": {
        "nameForResponse": "ReceiptsFromProduction",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Purchase_Order_Receipts": {
        "nameForResponse": "PurchaseOrderReceipt",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Inventory_Replenishment_Transfer": {
        "nameForResponse": "Replenishment",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Item_Count": {
        "nameForResponse": "InventoryCount",
        "isAllowed": false,
        "isAlreadyValidated": false
    },
    "Picking_Slip_View": {
        "nameForResponse": "Fulfillment",
        "isAllowed": false,
        "isAlreadyValidated": false
    }
};

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"CA77DB05-CF3A-4F7D-AFD2-5021EE183C12",variableType:8}
 */
var WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX = -1; // -1 = (all)

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2FD91389-6BB1-4227-BBF6-0C78C5F22271",variableType:8}
 */
var WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW = 10;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B6DD2809-A7A2-4373-9CA9-F14AB7A43895",variableType:4}
 */
var WWMS_populate_default_location = 0;

/**
 * @type {Object}
 *
 * @properties={typeid:35,uuid:"7A18B0B8-ABAF-40AB-8E63-A197E51DD861",variableType:-4}
 */
var methods = {

    Config_ControlFlags: function(data) {
        
        var Config_ControlFlagsResult = {
            InvCountShowOnHandQty: false
        };
        var Config_ControlFlagsResponse = {
            Config_ControlFlagsResult: Config_ControlFlagsResult
        };
        var response = {
            Config_ControlFlagsResponse: Config_ControlFlagsResponse
        };
        return response;
    },
    
    Config_SystemPreferences: function(data){
        var oPref = {
        	Supervisor: isSupervisor(globals.avBase_employeeUUID),
            ShowDefaultLocation: (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.PopulateDefaultLocation] === 'Yes') ? true : false,
            ForcePickQtyEntry: (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.ForceEntryOfPickQty] === '1') ? true : false,
            UseProjectForLotControl: scopes.avInv.isProjectInventoryOn() ? true : false,
          	SelectLabelCheckBox: (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.SelectLabelCheckBoxInMobileApp] === '1') ? true : false,
            WayBillNumberRequired: (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.WaybillNumberRequired] === '1') ? true : false
        };
        
    	/** @type{JSFoundSet<db:/avanti/in_warehouse>} */
    	var fsInWarehouse = scopes.avDB.getFS("in_warehouse", ["whse_id"], [globals.avBase_employeeDefaultWarehouse]);
    	var recInWarehouse = fsInWarehouse.getRecord(1);
        
        var Config_SystemPreferencesResult = {
            SystemPreferences: oPref,
			EmployeeDefaultWarehouse: {
				EmployeeDefaultWarehouseCode: recInWarehouse.whse_code,
				EmployeeDefaultWarehouseDesc: recInWarehouse.whse_desc,
				EmployeeDefaultWarehouseID: globals.avBase_employeeDefaultWarehouse
			}
        };
        var Config_SystemPreferencesResponse = {
            Config_SystemPreferencesResult: Config_SystemPreferencesResult
        };
        var response = {
            Config_SystemPreferencesResponse: Config_SystemPreferencesResponse
        };

        return response;
    },
	
    auth_AvantiMobileAppType: function(data) {
    	
        var result = setMobileApplicationType(data.serviceCode);
        
        var auth_AvantiMobileEndpointResponse = {
        	AvantiMobileAppType: result
        };
        var response = {
        	auth_AvantiMobileAppType: auth_AvantiMobileEndpointResponse
        };
        return response;
    },
	
    auth_IsAvantiMobileDeviceAuthorized: function(data) {
    	
    	var bResult = false;
    	var sSQL = "SELECT mdevice_id FROM sys_mobile_device WHERE mdevice_id = ? ";
    	var uDeviceId = application.getUUID(data.device_id);
    	
		if (uDeviceId) {
			try {
				var aArgs = [data.device_id.toString()];
				
				/** @type{JSRecord<db:/avanti/sys_mobile_device>} */
				var rMobileDevice = scopes.avDB.getRecFromSQL(sSQL, "sys_mobile_device", aArgs);

				if (rMobileDevice && rMobileDevice.mdevice_passcode_auth_date) {
					bResult = true;
				} else {
					bResult = false;
				}
			} catch (e) {
				bResult = false;
			}
		}

        var auth_AvantiMobileEndpointResponse = {
        	IsAvantiMobileDeviceAuthorized: bResult
        };
        var response = {
        	auth_IsAvantiMobileDeviceAuthorized: auth_AvantiMobileEndpointResponse
        };
        return response;
    },
	
    auth_RemoveMobileDeviceAuthorization: function(data) {
    	
    	var bResult = false;
    	var sSQL = "SELECT mdevice_id FROM sys_mobile_device WHERE mdevice_id = ? ";
    	var uDeviceId = application.getUUID(data.device_id);
    	
		if (uDeviceId) {
			try {
				var aArgs = [data.device_id.toString()];
				
				/** @type{JSFoundset<db:/avanti/sys_mobile_device>} */
				var fsMobileDevice = scopes.avDB.getFSFromSQL(sSQL, "sys_mobile_device", aArgs);

				if (fsMobileDevice && fsMobileDevice.getSize() == 1) {
					fsMobileDevice.deleteRecord(1);
					bResult = true;
				} else {
					bResult = false;
				}
			} catch (e) {
				bResult = false;
			}
		}

        var auth_AvantiMobileEndpointResponse = {
        	IsAvantiMobileDeviceAuthoriztionDeleted: bResult
        };
        var response = {
        	auth_RemoveMobileDeviceAuthorization: auth_AvantiMobileEndpointResponse
        };
        return response;
    },
	
    auth_UpdateAvantiMobileDeviceLastLogin: function(data) {
        
        var uDeviceId = application.getUUID(data.device_id);
    	var result = updateMobileDeviceLastLogin(uDeviceId);
        
        var auth_UpdateAvantiMobileDeviceLastLoginResponse = {
        	auth_UpdateAvantiMobileDeviceLastLoginResult: result
        };
        
        var response = {
        	auth_UpdateAvantiMobileDeviceLastLoginResponse: auth_UpdateAvantiMobileDeviceLastLoginResponse
        };
        
        return response;
    },

    //NB IK, 2017-04-19, finally decided to use "-" as separator. So this is not really used by Wireless application
    Config_InventoryLocation: function(data) {
        var Config_InventoryLocationResult = {
            UseLevel1: true,
            NameLevel1: "WAREH",
            LengthLevel1: 6,

            UseLevel2: true,
            NameLevel2: "ZONE",
            LengthLevel2: 6,

            UseLevel3: true,
            NameLevel3: "AISLE",
            LengthLevel3: 6,

            UseLevel4: true,
            NameLevel4: "SLOT",
            LengthLevel4: 6,

            UseLevel5: true,
            NameLevel5: "LEVEL",
            LengthLevel5: 6,

            UseLevel6: true,
            NameLevel6: "BOX",
            LengthLevel6: 6
        };

        var Config_InventoryLocationResponse = {
            Config_InventoryLocationResult: Config_InventoryLocationResult
        };
        var response = {
            Config_InventoryLocationResponse: Config_InventoryLocationResponse
        };
        return response;
    },

    SysLicenseCtrl: function(data) {
        var sec_id_map = { };
        var sqlInClauseStr = null;

        for (var key in WWMS_security_program_name) {
            sqlInClauseStr += ( sqlInClauseStr == null ) ? "'" + key + "'" : ( ", " + "'" + key + "'" );
        }
        
        var license = plugins.MFCrypt.getBase64Cryptor().decode(_to_sys_organization.sys_organization_to_sec_owner.license_key);
        var SysctrlResult = { };

        if (license) {
            /** @type{String[]}*/
            var permission_array = JSON.parse(license);
            var sqlQueryStr = null;
            var args = null;
            var maxRowIndex = 0;
            var ds = null;
            var i = 0;
            //Setting programs to allowed in WWMS_security_program_name, if they are enabled in license
            for(var idx = 0; idx < permission_array.length; idx++) {
                if (permission_array[idx]) {
                    sqlQueryStr = "SELECT distinct program_name FROM sys_organization_licensing_prg\
                                WHERE program_name IN (" + sqlInClauseStr + ") AND  org_licensing_grp_id='"+permission_array[idx]+"' AND is_enabled=1"
                    ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sqlQueryStr, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
                    
                    if (ds.getMaxRowIndex() > 0) {
                        maxRowIndex = ds.getMaxRowIndex();
                        for (i = 1; i <= maxRowIndex; i++) {
                            ds.rowIndex = i;
                            WWMS_security_program_name[ds['program_name']].isAllowed = true;
                        }
                    }
                }
            }

            //Building security id map for the programs
            sqlQueryStr = "SELECT security_key_id, program_name FROM sec_security_key \
                                WHERE name='program' AND program_name IN (" + sqlInClauseStr + ")";
            ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_framework, sqlQueryStr, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            if (ds.getMaxRowIndex() > 0) {
                maxRowIndex = ds.getMaxRowIndex();
                for (var index = 1; index <= maxRowIndex; index++) {
                    ds.rowIndex = index;
                    sec_id_map[ds['security_key_id']] = WWMS_security_program_name[ds['program_name']];
                }
            }

            sqlInClauseStr = null;
            for (key in sec_id_map) {
                sqlInClauseStr += ( sqlInClauseStr == null ) ? key : ( ", " + key );
            }

            //first, search for user-specific permission which takes precedence over role-specific one
            args = [scopes.globals.svy_sec_lgn_user_id];
            sqlQueryStr = "SELECT security_key_id, is_denied FROM sec_user_right \
                            WHERE user_id=? AND security_key_id IN (" + sqlInClauseStr + ")";
            ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_framework, sqlQueryStr, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            if (ds.getMaxRowIndex() > 0) {
                maxRowIndex = ds.getMaxRowIndex();
                for (i = 1; i <= maxRowIndex; i++) {
                    ds.rowIndex = i;
                    //just in case
                    if (sec_id_map[ds['security_key_id']] != null) {
                        if (ds['is_denied'] == 1 && sec_id_map[ds['security_key_id']].isAllowed == true) {
                            sec_id_map[ds['security_key_id']].isAllowed = false;
                        }
                        sec_id_map[ds['security_key_id']].isAlreadyValidated = true;
                    }
                }
            }

            //perform search for role-specific permission
            args = [scopes.globals.svy_sec_lgn_user_id, scopes.globals.org_id];
            sqlQueryStr = "SELECT  sec_user_right.security_key_id, sec_user_right.is_denied \
                           FROM sec_user_org \
                           INNER JOIN sec_user_in_group ON (sec_user_in_group.user_org_id = sec_user_org.user_org_id) \
                           INNER JOIN sec_user_right ON (sec_user_right.group_id = sec_user_in_group.group_id) \
                           WHERE sec_user_org.user_id=? \
                           AND sec_user_org.organization_id = ? AND sec_user_right.security_key_id IN (";
            
            sqlQueryStr += sqlInClauseStr + ") GROUP BY sec_user_right.security_key_id, sec_user_right.is_denied";
            ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_framework, sqlQueryStr, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            if (ds.getMaxRowIndex() > 0) {
                maxRowIndex = ds.getMaxRowIndex();
                for (i = 1; i <= maxRowIndex; i++) {
                    ds.rowIndex = i;
                    //NB There is a chance that different Roles have a conflict with the same privileges. It's up to Administrator to avoid conflicts in Roles
                    if (sec_id_map[ds['security_key_id']] != null && !sec_id_map[ds['security_key_id']].isAlreadyValidated) {
                        if (ds['is_denied'] == 1 && sec_id_map[ds['security_key_id']].isAllowed == true) {
                            sec_id_map[ds['security_key_id']].isAllowed = false;
                        }
                        sec_id_map[ds['security_key_id']].isAlreadyValidated = true;
                    }
                }
            }

            for (key in sec_id_map) {
                SysctrlResult[sec_id_map[key].nameForResponse] = sec_id_map[key].isAllowed && sec_id_map[key].isAlreadyValidated;
                if (sec_id_map[key].nameForResponse == "DirectInventoryReceipt") {
                    SysctrlResult["InventoryMove"] = sec_id_map[key].isAllowed;
                    SysctrlResult["InventoryAdjustment"] = sec_id_map[key].isAllowed;
                    SysctrlResult["PendingReceipt"] = sec_id_map[key].isAllowed;
                }
            }
        }
        else{//return not allowed for all, if license is not found
            for (key in WWMS_security_program_name) {
                SysctrlResult[WWMS_security_program_name[key].nameForResponse] = WWMS_security_program_name[key].isAllowed;
            }
        }

        var SysctrlResponse = {
            SysctrlResult: SysctrlResult
        };
        var response = {
            SysctrlResponse: SysctrlResponse
        };

        return response;
    },
	
    Employee_Check: function(data) {
        var result = false;
        //2017-03-22 IK: data.EmployeeCode received actually contains username in Slingshot's terminology
        if (data.EmployeeCode != null && data.EmployeeCode.length > 0) {
        	
        	
            
            var sSql = "SELECT user_id\
                        FROM sec_user AS su\
                        WHERE su.user_name = ?";
            var args = [data.EmployeeCode];
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_framework, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
            
            /** @type{JSFoundSet<db:/avanti/sys_employee>} */
        	var fsSysEmployee = scopes.avDB.getFS("sys_employee", ["user_id"], ds.getColumnAsArray(0));
            
        	
        	if (utils.hasRecords(fsSysEmployee)) {
                var eachRec = null;
                for (var i = 1; i <= fsSysEmployee.getSize(); i++) {
                    eachRec = fsSysEmployee.getRecord(i);
                    if (eachRec.org_id == scopes.globals.org_id) {
                        result = true;
                        break;
                    }
                }
            }
        }

        var Employee_CheckResponse = {
            Employee_CheckResult: result
        };
        var response = {
            Employee_CheckResponse: Employee_CheckResponse
        };
        return response;
    },
	
    PrinterList: function(data) {
        var printer1 = "Printer 1";
        var printer2 = "Pronter 2";

        /*
         * implementation here
         */

        //check it up with DOC later
        var PrinterListResult = [printer1, printer2];

        var PrinterListResponse = {
            PrinterListResult: PrinterListResult
        };
        var response = {
            PrinterListResponse: PrinterListResponse
        };
        return response;
    },
	
    InventoryInfo_FG_IsValid: function(data) {
        var result = false;
        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
            var args = [data.ItemNumber, scopes.globals.org_id];
            var sSql = "SELECT item_id FROM in_item WHERE item_code = ? AND item_status='A' AND org_id = ? AND itemtype_code='F'";
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sSql = "SELECT item_id FROM in_item \
            		INNER JOIN in_item_warehouse ON in_item.item_id = in_item_warehouse.item_id \
            		WHERE in_item.item_code = ? AND in_item.item_status='A' AND in_item.org_id = ?  AND itemtype_code='F' AND \
            		in_item_warehouse.whse_id IN " + getWarehousesByEmployee();
            }
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }

        var InventoryInfo_FG_IsValidResponse = {
            InventoryInfo_FG_IsValidResult: result
        };
        var response = {
            InventoryInfo_FG_IsValidResponse: InventoryInfo_FG_IsValidResponse
        };
        return response;
    },
    
    //IK: based on InventoryInfo_List
    InventoryInfo_FG_List: function(data) {       
        var InventoryInfo = new Array();
        var sSql = "SELECT distinct item.item_id, item.item_code, item.item_desc1, item.item_onhand_qty, item.item_onpo_qty, item.item_committed_qty, aip.itemtype_i18n, suom.uom_desc, \
			item.item_color, item.item_dimension_length, item.item_dimension_width, item.item_dimension_heigth, item_class.itemclass_type, item_paper.paper_first_dim, item_paper.paper_second_dim \
			FROM in_item AS item \
			INNER JOIN in_item_warehouse AS item_warehouse ON item_warehouse.item_id = item.item_id \
			LEFT JOIN in_warehouse AS warehouse ON warehouse.whse_id = item_warehouse.whse_id AND warehouse.whse_active=1 \
			LEFT JOIN app_item_type AS aip ON item.itemtype_code = aip.itemtype_code \
			LEFT JOIN sys_unit_of_measure AS suom ON item.item_standard_uom_id = suom.uom_id \
			LEFT JOIN in_item_class AS item_class ON item_class.itemclass_id = item.itemclass_id \
			LEFT JOIN in_item_paper AS item_paper ON item_paper.item_id = item.item_id \
            WHERE item.item_status='A' AND item.itemtype_code='F' AND item.org_id = ? ";
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND warehouse.whse_id IN " + getWarehousesByEmployee();
        }
        
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;
            var args = [scopes.globals.org_id, "%" + data.Keyword + "%", start, end];
            sSql =  "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.item_code) as RownNum, * FROM (" + sSql +			
            		" AND item.item_code LIKE ? ) AS RowResult) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ?  ORDER By RownNum";
        }
        else {
            args = [scopes.globals.org_id]; 
        }
        
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ItemNumber: ds['item_code'],
                Description: ds['item_desc1'],
                InvType: i18n.getI18NMessage(ds['itemtype_i18n']),
                QuantityOnHand: ds['item_onhand_qty'],
                QuantityOnOrder: ds['item_onpo_qty'],
                QuantityCommitted: ds['item_committed_qty'],
                UnitOfMeasure: ds['uom_desc'] != null ? ds['uom_desc'] : "",
                Size: "",
                Color: ds['item_color'] != null ? ds['item_color'] : "",
                LastCost: 0,
                DefaultLocation: ""
            };

            //determine size
            if ('P' == ds['itemclass_type'] || 'R' == ds['itemclass_type']) {
                if (ds['paper_first_dim'] != null || ds['paper_second_dim'] != null) {
                    eachElement.Size = "" + ds['paper_first_dim'] + " x " + ds['paper_second_dim'];
                }
            }
            else {
                //this will cover this case as well: in_item_to_in_item_class.itemclass_type == 'RC'
                if (ds['item_dimension_length'] != null || ds['item_dimension_width'] != null || ds['item_dimension_height'] != null) {
                    //not all 3 measures are always used
                    eachElement.Size = "" + ds['item_dimension_length'] + " x " + ds['item_dimension_width'];
                    if ('CA'==ds['itemclass_type'] 
                        || 'PA'==ds['itemclass_type'] 
                        || 'SK'==ds['itemclass_type'] 
                        || 'O'==ds['itemclass_type'])
                    {
                        eachElement.Size += " x " + ds['item_dimension_heigth'];
                    }
                }
            }

            // Get Last Cost and Default Location
            if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PopulateDefaultLocation) == 'Yes'){
            	WWMS_populate_default_location = 1;
            }
            
            //determine default location
            if (WWMS_populate_default_location == scopes.globals.$1) {
                var itemWarehouseDefaultBinLocation = globals.getItemDefaultReceiptBinLocation(ds['item_id']);
                if (itemWarehouseDefaultBinLocation != null) {
                    eachElement.DefaultLocation = itemWarehouseDefaultBinLocation.whseloc_bin_location;
                    eachElement.LastCost = itemWarehouseDefaultBinLocation.itemwhse_last_cost;
                }
            }

            InventoryInfo.push(eachElement);
        }

        var InventoryInfo_FG_ListResult = {
            InventoryInfo: InventoryInfo
        };
        var InventoryInfo_FG_ListResponse = {
            InventoryInfo_FG_ListResult: InventoryInfo_FG_ListResult
        };
        var response = {
            InventoryInfo_FG_ListResponse: InventoryInfo_FG_ListResponse
        };
        return response;
    },

    InventoryInfo_IsValid: function(data) {
        var result = false;
        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
            var args = [data.ItemNumber, scopes.globals.org_id];
            var sSql =  "SELECT in_item.item_id FROM in_item WHERE item_code = ? AND item_status='A' AND org_id = ?";
            
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sSql = "SELECT in_item.item_id FROM in_item \
            		INNER JOIN in_item_warehouse ON in_item.item_id = in_item_warehouse.item_id \
            		WHERE in_item.item_code = ? AND in_item.item_status='A' AND in_item.org_id = ? AND \
            		in_item_warehouse.whse_id IN " + getWarehousesByEmployee();
            }
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }

        var InventoryInfo_IsValidResponse = {
            InventoryInfo_IsValidResult: result
        };
        var response = {
            InventoryInfo_IsValidResponse: InventoryInfo_IsValidResponse
        };
        return response;
    },

    InventoryInfo_IsItemProjectValid: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryInfo_IsItemProjectValid');
        }
      
        var result = scopes.avInv.doesItemUseThisProject(data.ItemNumber, data.Project);
        
        var InventoryInfo_IsItemProjectValidResponse = {
        	InventoryInfo_IsItemProjectValidResult: result
        };
        
        var response = {
        	InventoryInfo_IsItemProjectValidResponse: InventoryInfo_IsItemProjectValidResponse
        };
        
        return response;
    },

    InventoryInfo_IsProjectValid: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryInfo_IsProjectValid');
        }
      
        var result = scopes.avInv.isProjectValid(data.Project);
        
        var InventoryInfo_IsProjectValidResponse = {
        	InventoryInfo_IsProjectValidResult: result
        };
        
        var response = {
        	InventoryInfo_IsProjectValidResponse: InventoryInfo_IsProjectValidResponse
        };
        
        return response;
    },

    //2017-03-29, IK: current implementation does not fetch Items which have no record in in_item_warehouse
    //to show all, remove 'distinct' keyword and INNER JOIN in_item_warehouse AS...
    //warehouse.whse_active=1 IS considered
    InventoryInfo_ItemNumber: function(data) {
    	var itemNumberArray = [];
        var eachElement = new Object();

        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
        	
        	/** @type {Array} */
        	var commaSeparatedItems = data.ItemNumber.split(',')
			var args = commaSeparatedItems.concat([scopes.globals.org_id])

            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT distinct item.item_id, item.item_code, item.item_desc1, item.item_onhand_qty \
                ,item.item_onpo_qty, item.item_committed_qty, aip.itemtype_i18n, suom.uom_desc, item.item_color \
                ,item.item_dimension_length, item.item_dimension_width, item.item_dimension_heigth, item_class.itemclass_type, item_paper.paper_first_dim, item_paper.paper_second_dim, \
                item.item_track_rolls, item.item_generate_roll_number, item.item_status, sa.cust_code, item.item_cust_part_number, \
                FORMAT(item.item_last_modified_date, 'yyyy-MM-ddTHH:mm:ss') AS item_last_modified_date, item.item_avg_lead_time, \
                item.item_max_weight, \
                (SELECT STRING_AGG(doc_path_storage, '|') FROM sys_document WHERE sys_document.item_id = item.item_id) as documents \
                FROM in_item AS item \
                INNER JOIN in_item_warehouse AS item_warehouse ON (item_warehouse.item_id = item.item_id) \
                LEFT JOIN in_warehouse AS warehouse ON (warehouse.whse_id = item_warehouse.whse_id AND warehouse.whse_active=1) \
                LEFT JOIN app_item_type AS aip ON (item.itemtype_code = aip.itemtype_code) \
                LEFT JOIN sys_unit_of_measure AS suom ON (item.item_standard_uom_id = suom.uom_id) \
                LEFT JOIN in_item_class AS item_class ON (item_class.itemclass_id = item.itemclass_id) \
                LEFT JOIN in_item_paper AS item_paper ON (item_paper.item_id = item.item_id) \
                LEFT JOIN sa_customer AS sa ON (sa.cust_id = item.cust_id) \
                WHERE item.item_code IN (" + commaSeparatedItems.map(function(){return "?"}).join(',') + ") AND item.item_status='A' AND item.org_id = ? \
                ORDER BY item.item_code", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
        	
        	var maxRowIndex = ds.getMaxRowIndex();
        	for (var i = 1; i <= maxRowIndex; i++) {
        		ds.rowIndex = i;

        		/** @type{JSFoundSet<db:/avanti/in_item_selling_uom>} */
            	var fsInItemSellingUOM = scopes.avDB.getFS("in_item_selling_uom", ["item_id"], [ds['item_id']]);
            	var sellingUOMList = [];
            	for(var z = 1; z <= fsInItemSellingUOM.getSize(); z++) {
            		var recInItemSellingUOM = fsInItemSellingUOM.getRecord(z);
            		if(utils.hasRecords(recInItemSellingUOM.in_item_selling_uom_to_sys_unit_of_measure$pricing_uom)) {
						sellingUOMList.push({
	            			ListPrice: globals.avUtilities_roundNumber(recInItemSellingUOM.itemselluom_list_price, recInItemSellingUOM.in_item_selling_uom_to_sys_unit_of_measure$pricing_uom.uom_decimals_price),
							PricingUnits: recInItemSellingUOM.in_item_selling_uom_to_sys_unit_of_measure$pricing_uom.uom_desc,
	            		});
            		}
            	}
            	
            	function determineItemStatus(status) {
            		var item_status = null;
            		var keys = Object.keys(scopes.avUtils.ITEM_STATUS);
            		for(var x = 0; x < keys.length; x++) {
            			var keyName = keys[x];
            			var value = scopes.avUtils.ITEM_STATUS[keyName];
            			if(status === value) {
            				item_status = keyName;
            				break;
            			}            			
            		}
            		return item_status;
            	}
            		
                eachElement = {
                	ItemNumber: ds['item_code'],
                    Description: ds['item_desc1'],
					ItemStatus: determineItemStatus(ds['item_status']),
					CustomerCode: ds['cust_code'],
					CustomerPartNumber: ds['item_cust_part_number'],
					LastModifiedDate: ds['item_last_modified_date'],
					SellingUnits: sellingUOMList,
                    InvType: i18n.getI18NMessage(ds['itemtype_i18n']),
                    QuantityOnHand: ds['item_onhand_qty'],
                    QuantityOnOrder: ds['item_onpo_qty'],
                    QuantityCommitted: ds['item_committed_qty'],
                    UnitOfMeasure: ds['uom_desc'] != null ? ds['uom_desc'] : "",
                    Size: "",
                    Color: ds['item_color'] != null ? ds['item_color'] : "",
                    LastCost: 0,
                    DefaultLocation: "",
					TrackRolls: Boolean(ds['item_track_rolls']),
					GenerateRollNumber: Boolean(ds['item_generate_roll_number']),
					ItemAvgLeadTime: ds['item_avg_lead_time'] !== null ? ds['item_avg_lead_time'] : 0,
	                MaxWeight: ds['item_max_weight'] !== null ? ds['item_max_weight'] : 0,
                    DocumentFilePaths: ds['documents'] ? ds['documents'].split('|') : []
                };

                //determine size
                if ('P' == ds['itemclass_type'] || 'R' == ds['itemclass_type']) {
                    if (ds['paper_first_dim'] != null || ds['paper_second_dim'] != null) {

                        //TODO to confirm with Tim: do I need perform such validation as in in_item_details_dtl.js?
                        eachElement.Size = "" + ds['paper_first_dim'] + " x " + ds['paper_second_dim'];
                    }
                }
                else {
                    //this will cover this case as well: in_item_to_in_item_class.itemclass_type == 'RC'
                    if (ds['item_dimension_length'] != null || ds['item_dimension_width'] != null || ds['item_dimension_height'] != null) {
                        //not all 3 measures are always used
                        eachElement.Size = "" + ds['item_dimension_length'] + " x " + ds['item_dimension_width'];
                        if ('CA'==ds['itemclass_type'] 
                            || 'PA'==ds['itemclass_type'] 
                            || 'SK'==ds['itemclass_type'] 
                            || 'O'==ds['itemclass_type'])
                        {
                            eachElement.Size += " x " + ds['item_dimension_heigth'];
                        }
                    }
                }
            	
                // Get Last Cost and Default Location
                if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PopulateDefaultLocation) == 'Yes'){
                	WWMS_populate_default_location = 1;
                }
                
                //determine default location
                if (WWMS_populate_default_location == scopes.globals.$1) {
                    var itemWarehouseDefaultBinLocation = globals.getItemDefaultReceiptBinLocation(ds['item_id']);
                    if (itemWarehouseDefaultBinLocation != null) {
                        eachElement.DefaultLocation = itemWarehouseDefaultBinLocation.whseloc_bin_location;//TODO: plant filter
                        eachElement.LastCost = itemWarehouseDefaultBinLocation.itemwhse_last_cost;
                    }
                }
                itemNumberArray.push(eachElement);
            }
        }
        
        if(itemNumberArray.length > 1) {
        	eachElement = itemNumberArray;
        }

        var InventoryInfo_ItemNumberResponse = {
            InventoryInfo_ItemNumberResult: eachElement
        };
        var response = {
            InventoryInfo_ItemNumberResponse: InventoryInfo_ItemNumberResponse
        };
        
        var sLogUUID = globals.dbLog("Inventory Info API Post Request Results", "Import", "processing", "Method", "InventoryInfo_ItemNumber", globals.org_id, 'logging', 'Summary', null, 'Success');
        globals.dbLogUpdate(globals.UUIDtoString(sLogUUID), "Inventory Info API Post Request Results", 'Success', null, 'wtp_queue', 'processing', 'Inventory', 'http', 'Item Availability');
        globals.dbLogWriteDetails(sLogUUID, 'wtp_queue', plugins.serialize.toJSON(response), getRequestFullURL());
        return response;
    },

    //2017-03-29, IK: based on Inventoryinfo_ItemNumber - see more notices regarding SQL statement over there
    InventoryInfo_List: function(data) {
        var InventoryInfo = new Array();
        var sSql = "SELECT distinct item.item_id, item.item_code, item.item_desc1, item.item_onhand_qty, item.item_onpo_qty, item.item_committed_qty, aip.itemtype_i18n, suom.uom_desc, item.item_color, \
			item.item_dimension_length, item.item_dimension_width, item.item_dimension_heigth, item_class.itemclass_type, item_paper.paper_first_dim, item_paper.paper_second_dim, \
			sa.cust_code, item.item_cust_part_number, \
        	FORMAT( \
				CASE \
					WHEN item.item_last_modified_date IS NOT NULL THEN item.item_last_modified_date \
					ELSE item.item_creation_date \
				END, \
			'yyyy-MM-ddTHH:mm:ss' \
			 ) AS item_last_modified_date, \
			item.item_avg_lead_time, \
            item.item_max_weight, \
            (SELECT STRING_AGG(doc_path_storage, '|') FROM sys_document WHERE sys_document.item_id = item.item_id) as documents \
			FROM in_item AS item  \
			INNER JOIN in_item_warehouse AS item_warehouse ON item_warehouse.item_id = item.item_id \
			LEFT JOIN  in_warehouse AS warehouse ON warehouse.whse_id = item_warehouse.whse_id  AND warehouse.whse_active=1 \
			LEFT JOIN app_item_type AS aip ON item.itemtype_code = aip.itemtype_code \
			LEFT JOIN sys_unit_of_measure AS suom ON item.item_standard_uom_id = suom.uom_id \
			LEFT JOIN in_item_class AS item_class ON item_class.itemclass_id = item.itemclass_id \
			LEFT JOIN in_item_paper AS item_paper ON item_paper.item_id = item.item_id \
			LEFT JOIN sa_customer AS sa ON sa.cust_id = item.cust_id \
            WHERE item.item_status='A' AND item.itemtype_code='S' AND item.org_id = ?";
        
        var args = [scopes.globals.org_id]
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND warehouse.whse_id IN " + getWarehousesByEmployee();
        }
        
        // Apply customer exact match filtering
        if(data.CustomerCode) {
        	sSql += " AND sa.cust_code = ?";
        	args.push(data.CustomerCode);
        }
        
        if(data.LastModifiedDate) {
        	sSql += " AND ((item.item_last_modified_date >= ?) OR \
        		( item.item_last_modified_date IS NULL AND item.item_creation_date >= ?))";    	
        	// "2024-03-01T23:59:59"
        	args.push(data.LastModifiedDate);
        	args.push(data.LastModifiedDate);
        }
        
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;
            args.push("%" + data.Keyword + "%", start, end)
            sSql = "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.item_code) as RownNum, * FROM (" + sSql +			
        		" AND item.item_code LIKE ? ) AS RowResult) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ? ORDER By RownNum";
        }
        
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ItemNumber: ds['item_code'],
                Description: ds['item_desc1'],
                InvType: i18n.getI18NMessage(ds['itemtype_i18n']),
                QuantityOnHand: ds['item_onhand_qty'],
                QuantityOnOrder: ds['item_onpo_qty'],
                QuantityCommitted: ds['item_committed_qty'],
                UnitOfMeasure: ds['uom_desc'] != null ? ds['uom_desc'] : "",
                Size: "",
                Color: ds['item_color'] != null ? ds['item_color'] : "",
                LastCost: 0,
                DefaultLocation: "",
				CustomerCode: ds['cust_code'],
				CustomerPartNumber: ds['item_cust_part_number'],
				LastModifiedDate: ds['item_last_modified_date'],
				ItemAvgLeadTime: ds['item_avg_lead_time'] !== null ? ds['item_avg_lead_time'] : 0,
                MaxWeight: ds['item_max_weight'] !== null ? ds['item_max_weight'] : 0,
                DocumentFilePaths: ds['documents'] ? ds['documents'].split('|') : []
            };

            //determine size
            if ('P' == ds['itemclass_type'] || 'R' == ds['itemclass_type']) {
                if (ds['paper_first_dim'] != null || ds['paper_second_dim'] != null) {
                    eachElement.Size = "" + ds['paper_first_dim'] + " x " + ds['paper_second_dim'];
                }
            }
            else {
                //this will cover this case as well: in_item_to_in_item_class.itemclass_type == 'RC'
                if (ds['item_dimension_length'] != null || ds['item_dimension_width'] != null || ds['item_dimension_height'] != null) {
                    //not all 3 measures are always used
                    eachElement.Size = "" + ds['item_dimension_length'] + " x " + ds['item_dimension_width'];
                    if ('CA'==ds['itemclass_type'] 
                        || 'PA'==ds['itemclass_type'] 
                        || 'SK'==ds['itemclass_type'] 
                        || 'O'==ds['itemclass_type'])
                    {
                        eachElement.Size += " x " + ds['item_dimension_heigth'];
                    }
                }
            }

            // Get Last Cost and Default Location
            if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PopulateDefaultLocation) == 'Yes'){
            	WWMS_populate_default_location = 1;
            }
            
            //determine default location
            if (WWMS_populate_default_location == scopes.globals.$1) {
                var itemWarehouseDefaultBinLocation = globals.getItemDefaultReceiptBinLocation(ds['item_id']);
                if (itemWarehouseDefaultBinLocation != null) {
                    eachElement.DefaultLocation = itemWarehouseDefaultBinLocation.whseloc_bin_location;
                    eachElement.LastCost = itemWarehouseDefaultBinLocation.itemwhse_last_cost;
                }
            }

            InventoryInfo.push(eachElement);
        }

        var InventoryInfo_ListResult = {
            InventoryInfo: InventoryInfo
        };
        var InventoryInfo_ListResponse = {
            InventoryInfo_ListResult: InventoryInfo_ListResult
        };
        var response = {
            InventoryInfo_ListResponse: InventoryInfo_ListResponse
        };
        return response;
    },

    //2017-06-12, IK: based on InventoryInfo_List. Added per Amin's request
    InventoryInfo_ALL_List: function(data) {
        var InventoryInfo = new Array();
        var sSql = "SELECT distinct item.item_id, item.item_code, item.item_desc1, item.item_onhand_qty , item.item_onpo_qty, item.item_committed_qty, aip.itemtype_i18n , suom.uom_desc, item.item_color, \
			item.item_dimension_length, item.item_dimension_width, item.item_dimension_heigth, item_class.itemclass_type, item_paper.paper_first_dim, item_paper.paper_second_dim, \
        	sa.cust_code, item.item_cust_part_number, FORMAT( \
				CASE \
					WHEN item.item_last_modified_date IS NOT NULL THEN item.item_last_modified_date \
					ELSE item.item_creation_date \
				END, \
			'yyyy-MM-ddTHH:mm:ss' \
			 ) AS item_last_modified_date, \
			item.item_avg_lead_time, \
			item.item_max_weight, \
			(SELECT STRING_AGG(doc_path_storage, '|') FROM sys_document WHERE sys_document.item_id = item.item_id) as documents \
			FROM in_item AS item \
			INNER JOIN in_item_warehouse AS item_warehouse ON item_warehouse.item_id = item.item_id  \
			LEFT JOIN in_warehouse AS warehouse ON warehouse.whse_id = item_warehouse.whse_id AND warehouse.whse_active=1 \
			LEFT JOIN app_item_type AS aip ON item.itemtype_code = aip.itemtype_code \
			LEFT JOIN sys_unit_of_measure AS suom ON item.item_standard_uom_id = suom.uom_id \
			LEFT JOIN in_item_class AS item_class ON item_class.itemclass_id = item.itemclass_id \
			LEFT JOIN in_item_paper AS item_paper ON item_paper.item_id = item.item_id \
			LEFT JOIN sa_customer AS sa ON sa.cust_id = item.cust_id \
        	WHERE item.item_status='A'";
        	
        
        var item_types_array = [
        	"item.itemtype_code ='S'", "item.itemtype_code = 'F'", "item.itemtype_code ='A'"
        ]
        if(data.IncludeProducts) {
        	item_types_array.push("item.itemtype_code ='P'")
        }
        if(data.IncludeServiceItems) {
        	item_types_array.push("item.itemtype_code ='SE'")
        }
        if(data.IncludeBuildToOrderKits) {
        	item_types_array.push("item.itemtype_code ='B'")
        }
        
        var sqlWhereitemtype_code = 'AND (' + item_types_array.join(' OR ') + ")"
        var sqlWhereOrg = " AND item.org_id = ? ";
        
        sSql += sqlWhereitemtype_code + sqlWhereOrg;
        
    	var args = [];
        
        if(data.org_id) {
        	args.push(data.org_id);
        } else {
        	args.push(scopes.globals.org_id);
        }
        
        // Apply customer exact match filtering
        if(data.CustomerCode) {
        	sSql += " AND sa.cust_code = ?";
        	args.push(data.CustomerCode);
    	}
        
    	if(data.LastModifiedDate) {    
        	sSql += " AND ((item.item_last_modified_date >= ?) OR \
        		( item.item_last_modified_date IS NULL AND item.item_creation_date >= ?))";
        	
        	// "2024-03-01T23:59:59"
        	args.push(data.LastModifiedDate);
        	args.push(data.LastModifiedDate);
        }
    	
        if (data.PageNumber) {
        	var start = (data.PageNumber - 1) * 500;
            var end = start + 500;
            args.push("%" + data.Keyword + "%", start, end);
            sSql = "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.item_code) as RownNum , * FROM (" + sSql +
                " AND item.item_code LIKE ? ) AS RowResult) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ? ORDER BY RownNum";
        }
        
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ItemNumber: ds['item_code'],
                Description: ds['item_desc1'],
                InvType: i18n.getI18NMessage(ds['itemtype_i18n']),
                QuantityOnHand: ds['item_onhand_qty'],
                QuantityOnOrder: ds['item_onpo_qty'],
                QuantityCommitted: ds['item_committed_qty'],
                UnitOfMeasure: ds['uom_desc'] != null ? ds['uom_desc'] : "",
                Size: "",
                Color: ds['item_color'] != null ? ds['item_color'] : "",
                LastCost: 0,
                DefaultLocation: "",
				CustomerCode: ds['cust_code'],
				CustomerPartNumber: ds['item_cust_part_number'],
				LastModifiedDate: ds['item_last_modified_date'],
				ItemAvgLeadTime: ds['item_avg_lead_time'] !== null ? ds['item_avg_lead_time'] : 0,
				MaxWeight: ds['item_max_weight'] !== null ? ds['item_max_weight'] : 0,
				DocumentFilePaths: ds['documents'] ? ds['documents'].split('|') : []
            };

            //determine size
            if ('P' == ds['itemclass_type'] || 'R' == ds['itemclass_type']) {
                if (ds['paper_first_dim'] != null || ds['paper_second_dim'] != null) {
                    eachElement.Size = "" + ds['paper_first_dim'] + " x " + ds['paper_second_dim'];
                }
            }
            else {
                //this will cover this case as well: in_item_to_in_item_class.itemclass_type == 'RC'
                if (ds['item_dimension_length'] != null || ds['item_dimension_width'] != null || ds['item_dimension_height'] != null) {
                    //not all 3 measures are always used
                    eachElement.Size = "" + ds['item_dimension_length'] + " x " + ds['item_dimension_width'];
                    if ('CA'==ds['itemclass_type'] 
                        || 'PA'==ds['itemclass_type'] 
                        || 'SK'==ds['itemclass_type'] 
                        || 'O'==ds['itemclass_type'])
                    {
                        eachElement.Size += " x " + ds['item_dimension_heigth'];
                    }
                }
            }

            // Get Last Cost and Default Location
            if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PopulateDefaultLocation) == 'Yes'){
            	WWMS_populate_default_location = 1;
            }
            
            //determine default location
            if (WWMS_populate_default_location == scopes.globals.$1) {
                var itemWarehouseDefaultBinLocation = globals.getItemDefaultReceiptBinLocation(ds['item_id']);
                if (itemWarehouseDefaultBinLocation != null) {
                    eachElement.DefaultLocation = itemWarehouseDefaultBinLocation.whseloc_bin_location;
                    eachElement.LastCost = itemWarehouseDefaultBinLocation.itemwhse_last_cost;
                }
            }

            InventoryInfo.push(eachElement);
        }

        var InventoryInfo_ListResult = {
            InventoryInfo: InventoryInfo
        };
        var InventoryInfo_ListResponse = {
            InventoryInfo_ALL_ListResult: InventoryInfo_ListResult
        };
        var response = {
            InventoryInfo_ALL_ListResponse: InventoryInfo_ListResponse
        };
        return response;
    },

    InventoryInfo_ItemProjects: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryInfo_ItemProjects');
        }
      
        var aoItemProjects = [];

        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
        	var asItemProjects = scopes.avInv.getItemProjects(data.ItemNumber);
            
			if (asItemProjects) {
	            for (var i = 0; i < asItemProjects.length; i++) {
	            	var sProject = asItemProjects[i];
	                var eachElement = {Project: sProject};
	                
	                aoItemProjects.push(eachElement);
	            }
			}
        }

        var InventoryInfo_ItemProjectsResult = {
        	ItemProjects: aoItemProjects
        };
        var InventoryInfo_ItemProjectsResponse = {
        	InventoryInfo_ItemProjectsResult: InventoryInfo_ItemProjectsResult
        };
        var response = {
        	InventoryInfo_ItemProjectsResponse: InventoryInfo_ItemProjectsResponse
        };
        
        return response;
    },
	
    InventoryInfo_ItemDefaultProject: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryInfo_ItemDefaultProject');
        }
        
        var sDefaultProject = "";
      
        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
        	var uItemID = getItemID(data.ItemNumber);
        	var asItemProjects = scopes.avInv.getItemProjects(data.ItemNumber);
            
			if (asItemProjects && asItemProjects.length == 1 && uItemID && !scopes.avInv.doesItemHaveNonProjectItemProjectRecs(uItemID)) {
				sDefaultProject = asItemProjects[0];
			}
        }

        var InventoryInfo_ItemDefaultProjectResult = {
        	ItemDefaultProject: sDefaultProject
        };
        var InventoryInfo_ItemDefaultProjectResponse = {
        	InventoryInfo_ItemDefaultProjectResult: InventoryInfo_ItemDefaultProjectResult
        };
        var response = {
        	InventoryInfo_ItemDefaultProjectResponse: InventoryInfo_ItemDefaultProjectResponse
        };
        
        return response;
    },
	
    //2017-03-29, IK: current implementation does not fetch Items which have no record in in_item_warehouse
    //the Default location is added to list if its not returned as a place where item has ever been before (there is a record at in_item_warehouse_location)
    InventoryLocations: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryLocations');
        }
      
        var InventoryLocation = new Array();

        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
        	var uItemID = getItemID(data.ItemNumber);
            var bProjectInventory = scopes.avInv.doesItemUseProjectInventory(uItemID);
            
            //IK: this fetch existing records from 
            //1) in_item_warehouse_location and 
            //2) records from in_warehouse_location based on item_warehouse.itemwhse_default_receipt_bin
            //records might be duplicated and we need check up such cases
            var args = [data.ItemNumber, scopes.globals.org_id, data.ItemNumber, scopes.globals.org_id];
            var sWith = "WITH t1 AS ( ";            	
            var sSelect = "SELECT item_warehouse.itemwhse_onpo_qty, item_warehouse.itemwhse_committed_qty, warehouse_location.whseloc_bin_location, 'false' AS 'default_bin_record_directly' ";
            var sTables = "FROM in_item AS item \
	                       INNER JOIN in_item_warehouse AS item_warehouse ON (item.item_id = item_warehouse.item_id) \
	                       INNER JOIN in_item_warehouse_location AS item_warehouse_location ON (item_warehouse_location.itemwhse_id = item_warehouse.itemwhse_id) \
	                       INNER JOIN in_warehouse_location AS warehouse_location ON (warehouse_location.whseloc_id = item_warehouse_location.whseloc_id) ";
            var sWhere =  "WHERE item.item_code=? AND item.org_id = ? ";
            var sUnion =  "UNION ALL \
                		   SELECT item_warehouse.itemwhse_onpo_qty, item_warehouse.itemwhse_committed_qty, warehouse_location.whseloc_bin_location, 'true' AS 'default_bin_record_directly', 0 \
            			   FROM in_item AS item \
		                  INNER JOIN in_item_warehouse AS item_warehouse ON (item.item_id = item_warehouse.item_id) \
		                  INNER JOIN in_warehouse_location AS warehouse_location ON (warehouse_location.whseloc_id = item_warehouse.itemwhse_default_receipt_bin) \
		                  WHERE item.item_code=? AND item.org_id = ? \
		                  ) ";
            var sSelectT1 = "SELECT * from t1 ORDER BY default_bin_record_directly DESC";
            
			if (bProjectInventory) {
				sSelect += ", ip.ip_id, ip.custproj_desc ";
				sTables += "LEFT JOIN in_item_project AS ip ON ip.item_id = item.item_id AND ip.itemwhseloc_id = item_warehouse_location.itemwhseloc_id ";
				sUnion = "UNION ALL \
						   SELECT item_warehouse.itemwhse_onpo_qty, item_warehouse.itemwhse_committed_qty, warehouse_location.whseloc_bin_location, 'true' AS 'default_bin_record_directly', ip.ip_id, ip.custproj_desc  \
						   FROM in_item AS item \
						   INNER JOIN in_item_warehouse AS item_warehouse ON (item.item_id = item_warehouse.item_id) \
						   INNER JOIN in_item_warehouse_location AS item_warehouse_location ON (item_warehouse_location.itemwhse_id = item_warehouse.itemwhse_id)  \
						   INNER JOIN in_warehouse_location AS warehouse_location ON (warehouse_location.whseloc_id = item_warehouse.itemwhse_default_receipt_bin) \
						   LEFT JOIN in_item_project AS ip ON ip.item_id = item.item_id AND ip.itemwhseloc_id = item_warehouse_location.itemwhseloc_id \
						   WHERE item.item_code=? AND item.org_id = ? \
						   ) ";
				
				// this project inventory
				if (data.Project) {
					sWhere += "AND ip.custproj_desc = ? ";
		            args = [data.ItemNumber, scopes.globals.org_id, data.Project, data.ItemNumber, scopes.globals.org_id];
				}
				// non project inventory
				else {
					sWhere += "AND ip.custproj_desc IS NULL ";		            
				}
			}
			else {
				sSelect += ", item_warehouse_location.itemwhseloc_onhand_qty ";
			}
            
			// Apply plant filtering
			if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {			
				sWhere += "AND warehouse_location.whse_id IN " + getWarehousesByEmployee();
			}
			
            var sSQL = sWith + sSelect + sTables + sWhere + sUnion + sSelectT1;
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            var eachElement = null;
            var locationExists = false;
            var maxRowIndex = ds.getMaxRowIndex();
            var nOHQ = 0;
            var project_desc = "";
            
            for (var i = 1; i <= maxRowIndex; i++) {
                ds.rowIndex = i;
                locationExists = false;
                
				if (bProjectInventory) {
					// sl-25422 - changed code to get project OHQ from clc_whseloc_ohq (which uses transactions), rather than in_item_project table. we had some problems with data in 
					// in_item_project, which i believe have been fixed, but i changed it to use trans in case there are still problems. This is in keeping with how item inquiry in
					// slingshot works. 
					if (ds['ip_id']) {
						/** @type {JSRecord<db:/avanti/in_item_project>} */
						var rItemProject = scopes.avDB.getRecFromSQL("SELECT ip_id FROM in_item_project p WHERE ip_id = ?", "in_item_project", [ds['ip_id']]);
						
						nOHQ = rItemProject.clc_whseloc_ohq;
					}
					
					project_desc = ds['custproj_desc']
					
					if (project_desc == null) {
						project_desc = "";
					}
				}
				else {
					nOHQ = ds['itemwhseloc_onhand_qty'];
				}
				
				if (!nOHQ) {
					nOHQ = 0;
				}
                
                var inventoryLocationLength = InventoryLocation.length;
                for (var j = 0; j < inventoryLocationLength; j++) {
                    if (InventoryLocation[j].Location == ds['whseloc_bin_location']) {
                        InventoryLocation[j].QuantityOnHand = nOHQ;
                        InventoryLocation[j].QuantityOnOrder = ds['itemwhse_onpo_qty'] ? ds['itemwhse_onpo_qty'] : 0;
                        InventoryLocation[j].QuantityCommitted = ds['itemwhse_committed_qty'] ? ds['itemwhse_committed_qty'] : 0;
                        locationExists = true;
                        break;
                    }
                }
                if (!locationExists) {
                    eachElement = {
                        ItemNumber: data.ItemNumber,
                        QuantityOnHand: nOHQ,
                        QuantityOnOrder: ds['itemwhse_onpo_qty'] ? ds['itemwhse_onpo_qty'] : 0,
                        QuantityCommitted: ds['itemwhse_committed_qty'] ? ds['itemwhse_committed_qty'] : 0,
                        Location: ds['whseloc_bin_location'],
						Project: project_desc
                    }
                    InventoryLocation.push(eachElement);
                }
            }

        }

        var InventoryLocationsResult = {
            InventoryLocation: InventoryLocation
        };
        var InventoryLocationsResponse = {
            InventoryLocationsResult: InventoryLocationsResult
        };
        var response = {
            InventoryLocationsResponse: InventoryLocationsResponse
        };
        return response;
    },
	
    InventoryLocationsWarehouses: function(data) {
        var Warehouses = null;

        if (data.ItemNumber != null && data.ItemNumber.length > 0) {
           Warehouses = getWarehousesByItemCode(data.ItemNumber);
        }

        var InventoryLocationsWarehousesResult = {
            InventoryLocationsWarehouses: Warehouses
        };
        var InventoryLocationsWarehousesResponse = {
            InventoryLocationsWarehousesResult: InventoryLocationsWarehousesResult
        };
        var response = {
            InventoryLocationsWarehousesResponse: InventoryLocationsWarehousesResponse
        };
        return response;
    },
    
    InventoryLocationsLevelCodes: function(data) {
        var LevelCodes = new Array();
        if (data.SelectedCodes.Warehouse_ID != null && data.SelectedCodes.Warehouse_ID.length > 0) {
            var args = [data.SelectedCodes.Warehouse_ID, scopes.globals.org_id];
            //Warehouse_ID is considered Level 1 in the mobile UI
            var sLevel = "1";
            var sAndLevels = "";
            
            //In Slingshot whselevelcode1 may represent the user_defined_level_1 which is shown as Level2 in the mobile UI.
            if (data.SelectedCodes.Level2Code_ID && data.SelectedCodes.Level2Code_ID.length > 0) {
                sLevel = "2";
                sAndLevels = "AND in_warehouse_location.whselevelcode1_id=? ";
                args.push(data.SelectedCodes.Level2Code_ID);
            }
            
            //In slingshot whselevelcode2 may represent the user_defined_level_2 which is shown as Level3 in the mobile UI.
            if (data.SelectedCodes.Level3Code_ID && data.SelectedCodes.Level3Code_ID.length > 0 && sLevel == "2") {
                sLevel = "3";
                sAndLevels += "AND in_warehouse_location.whselevelcode2_id=? ";
                args.push(data.SelectedCodes.Level3Code_ID);
            }
            
            //In slingshot whselevelcode3 may represent the user_defined_level_3 which is shown as Level4 in the mobile UI.
            if (data.SelectedCodes.Level4Code_ID && data.SelectedCodes.Level4Code_ID.length > 0 && sLevel == "3") {
                sLevel = "4";
                sAndLevels += "AND in_warehouse_location.whselevelcode3_id=? ";
                args.push(data.SelectedCodes.Level4Code_ID);
            }
            
            //In slingshot whselevelcode4 may represent the user_defined_level_4 which is shown as Level5 in the mobile UI.
            if (data.SelectedCodes.Level5Code_ID && data.SelectedCodes.Level5Code_ID.length > 0 && sLevel == "4") {
                sLevel = "5";
                sAndLevels += "AND in_warehouse_location.whselevelcode4_id=? ";
                args.push(data.SelectedCodes.Level5Code_ID);
            }

            var sSql = "SELECT distinct level_code.whselevelcode_code, level_code.whselevelcode_id \
                    FROM in_warehouse_level_code AS level_code \
                    INNER JOIN in_warehouse_level ON in_warehouse_level.whselevel_id = level_code.whselevel_id \
                    INNER JOIN in_warehouse_location ON in_warehouse_location.whselevelcode" + sLevel + "_id = level_code.whselevelcode_id \
                    WHERE in_warehouse_level.whse_id = ? AND level_code.org_id = ? " + sAndLevels + "ORDER BY level_code.whselevelcode_code";

            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
            var maxRowIndex = ds.getMaxRowIndex();
            if (maxRowIndex > 0) {
                var eachElement = null;
                for (var i = 1; i <= maxRowIndex; i++) {
                    ds.rowIndex = i;
                    eachElement = {
                        LevelCode: ds['whselevelcode_code'],
                        LevelCode_ID: ds['whselevelcode_id']
                    };
                    LevelCodes.push(eachElement);
                }
            }
        }

        var InventoryLocationsLevelCodesResult = {
            InventoryLocationsLevelCodes: LevelCodes
        };
        var InventoryLocationsLevelCodesResponse = {
            InventoryLocationsLevelCodesResult: InventoryLocationsLevelCodesResult
        };
        var response = {
            InventoryLocationsLevelCodesResponse: InventoryLocationsLevelCodesResponse
        };
        return response;
    },
	
    InventoryLocationsAllLevelCodes: function(data) {
        var LevelCodes = new Array();
        if (data.SelectedCodes.Warehouse_ID != null && data.SelectedCodes.Warehouse_ID.length > 0) {
            var args = [data.SelectedCodes.Warehouse_ID, scopes.globals.org_id];
            
            var sSql = "SELECT distinct level_code.whselevelcode_code, level_code.whselevelcode_id, in_warehouse_level.whselevel_level \
					FROM in_warehouse_level_code AS level_code \
					INNER JOIN in_warehouse_level ON in_warehouse_level.whselevel_id = level_code.whselevel_id \
					WHERE in_warehouse_level.whse_id = ? AND level_code.org_id = ? \
					ORDER BY whselevel_level, whselevelcode_code";
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
            var maxRowIndex = ds.getMaxRowIndex();
            if (maxRowIndex > 0) {
                var eachElement = null;
                for (var i = 1; i <= maxRowIndex; i++) {
                    ds.rowIndex = i;
                    eachElement = {
                        LevelCode: ds['whselevelcode_code'],
                        LevelCode_ID: ds['whselevelcode_id'],
						Level: ds['whselevel_level']
                    };
                    LevelCodes.push(eachElement);
                }
            }
        }

        var InventoryLocationsAllLevelCodesResult = {
            InventoryLocationsAllLevelCodes: LevelCodes
        };
        var InventoryLocationsAllLevelCodesResponse = {
            InventoryLocationsAllLevelCodesResult: InventoryLocationsAllLevelCodesResult
        };
        var response = {
            InventoryLocationsAllLevelCodesResponse: InventoryLocationsAllLevelCodesResponse
        };
        return response;
    },

    Replenishment_OpenMoves: function(data){
        var aOpenMoves = new Array();
        var args = [scopes.globals.org_id, getEmplCode(globals.svy_sec_lgn_user_id), scopes.avInv.TRANSACTION_TYPE.BinTransfer];
        var sSql = "SELECT iteh.intraneh_transaction_no, iteh.intraneh_reference, iteh.intraneh_id \
                    FROM in_trans_entry_header AS iteh \
                    INNER JOIN in_transaction_type AS itt ON iteh.intranstype_id = itt.intranstype_id \
                    INNER JOIN sys_employee AS empl ON (iteh.intraneh_assignee_id = empl.empl_id OR iteh.intraneh_assignee_id is NULL) \
                    WHERE iteh.org_id = ? AND iteh.intraneh_status = 0 AND iteh.intraneh_reference = 'Replenishment' AND (empl.empl_code = ?)  \
                    AND (itt.intranstype_trans_code = ?) AND (itt.intranstype_system_record = 1)";
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND iteh.intraneh_whse_id IN " + getWarehousesByEmployee();
        }
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                TransactionNumber: ds['intraneh_transaction_no'],
                ReferenceNumber: ds['intraneh_reference'],
                Transaction_ID: ds['intraneh_id']
            };
            aOpenMoves.push(eachElement);
        }
        var Replenishment_OpenMovesResult = {
            OpenMoves: aOpenMoves
        };
        var Replenishment_OpenMovesResponse = {
            Replenishment_OpenMovesResult: Replenishment_OpenMovesResult
        };
        var response = {
            Replenishment_OpenMovesResponse: Replenishment_OpenMovesResponse
        };
        
        return response;
    },
    
    Replenishment_MoveItemList: function(data){
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Replenishment_MoveItemList');
        }
      
        var aItemList = new Array();

        var sSql = "SELECT item.item_code, floc.whseloc_bin_location AS bin_location_from, \
                    tloc.whseloc_bin_location AS bin_location_to, ited.intraned_trans_qty, item.item_desc1, \
                    sys_unit_of_measure.uom_code, ited.intraned_id, ited.ited_picked_qty, ited.custproj_desc \
                    FROM in_trans_entry_detail AS ited \
                    INNER JOIN in_item AS item ON ited.item_id = item.item_id \
                    LEFT JOIN in_item_warehouse_location AS floc \
                        ON ited.intraned_whseloc_id_from = floc.whseloc_id \
                        AND ited.item_id = floc.item_id \
                    INNER JOIN in_item_warehouse_location AS tloc \
                        ON ited.intraned_whseloc_id = tloc.whseloc_id \
                        AND ited.item_id = tloc.item_id \
                    INNER JOIN in_trans_entry_header AS iteh \
                        ON ited.intraneh_id = iteh.intraneh_id \
                    INNER JOIN sys_unit_of_measure ON ited.uom_id = sys_unit_of_measure.uom_id \
                    WHERE (iteh.org_id =?) AND (iteh.intraneh_transaction_no = ?)";
            
        var args = [scopes.globals.org_id, data.TransactionNumber];

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti,
            sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ItemNumber: ds['item_code'],
                ItemDescription: ds['item_desc1'],
                FromLocation: ds['bin_location_from'],
                ToLocation: ds['bin_location_to'],
                Quantity: ds['intraned_trans_qty'] ? ds['intraned_trans_qty'] : 0,
                AlreadyPickedQty: ds['ited_picked_qty'] ? ds['ited_picked_qty'] : 0,
                UnitOfMeasure: ds['uom_code'] ? ds['uom_code'] : "",
                TransEntryDetail_ID: ds['intraned_id'],
                Project: ds['custproj_desc'],
                PickSkipID: 0
            };
            aItemList.push(eachElement);
        }
        
        var Replenishment_MoveItemListResult = {
            MoveItemList: aItemList
        };
        var Replenishment_MoveItemListResponse = {
            Replenishment_MoveItemListResult: Replenishment_MoveItemListResult
        };
        var response = {
            Replenishment_MoveItemListResponse: Replenishment_MoveItemListResponse
        };
        
        return response;
    },
    
    Replenishment_InventoryMove: function(data){
                 
        if (application.isInDeveloper() && bGetTestPackage) {
             data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Replenishment_InventoryMove');
        }
       
       /** @type{JSFoundSet<db:/avanti/in_trans_entry_detail>} */
       var fsInTransDetail = null;
       
       //prepare response with Error in advance
       var InventoryMoveResult = {
           MoveResult: "Error" //"Item moved" - success
       };

       var InventoryMoveResponse = {
           InventoryMoveResult: InventoryMoveResult
       };
       var response = {
           InventoryMoveResponse: InventoryMoveResponse
       };
       
       if(data.ReplenishmentItems.Items.length < 1){
           return response;
       }
       
       var bHasSkippedItems = false;
       
       for (var i = 0; i < data.ReplenishmentItems.Items.length; i++) {
            /** @type {{ItemNumber:String, ItemDescription:String,FromLocation:String, ToLocation:String, Quantity:Number,
             * AlreadyPickedQty:String, UnitOfMeasure:String, TransEntryDetail_ID:String, PickSkipID:string }} */
            var itemToMove = data.ReplenishmentItems.Items[i];
            var args = [itemToMove.ItemNumber, scopes.globals.org_id];
            var sSql = "SELECT distinct item.item_id, item.item_standard_uom_id, item.item_track_rolls, \
                  item.item_gl_inventory_adj, item_class.itemclass_gl_inventory_adj \
                  FROM in_item AS item \
                  LEFT JOIN in_item_class AS item_class ON(item_class.itemclass_id=item.itemclass_id) \
                  WHERE item.item_code = ? AND item.org_id = ?";
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

            if (ds.getMaxRowIndex() == 0) {
                InventoryMoveResult.MoveResult = data.method + ", Error: Incorrect ItemNumber";
                return response;
            }

            ds.rowIndex = 1;
            var item_item_id = ds['item_id'];
            var item_uom_id = ds['item_standard_uom_id'];
            var glacct_id = ds['item_gl_inventory_adj'] != null ? ds['item_gl_inventory_adj'] : ds['itemclass_gl_inventory_adj'] != null ? ds['itemclass_gl_inventory_adj'] : null;

            var oLocation = buildLocationObject(item_item_id, itemToMove.FromLocation, itemToMove.ToLocation);
            if (oLocation.error) {
                InventoryMoveResult.MoveResult = data.method + oLocation.error;
                return response;
            }

            sSql = "SELECT intraned_id FROM in_trans_entry_detail WHERE (org_id = ?) AND (intraned_id  = ?)";
            args = [scopes.globals.org_id, itemToMove.TransEntryDetail_ID];
            fsInTransDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_trans_entry_detail');
            fsInTransDetail.loadRecords(sSql, args);

            if (fsInTransDetail.getSize() < 1) {
                InventoryMoveResult.MoveResult = data.method + "No Transaction Entry Detail record found!";
                return response;
            }

            if (itemToMove.PickSkipID && itemToMove.PickSkipID != "0") {
                if (!isPickSkipReasonValid(itemToMove.PickSkipID)) {
                    InventoryMoveResult.MoveResult = data.method + "Invalid Pick Skip Reason provided";
                    return response;
                }
                bHasSkippedItems = true;
                fsInTransDetail.pickskip_id = itemToMove.PickSkipID;
                fsInTransDetail.ited_pick_status = 'Skipped';
            }
            else {
                fsInTransDetail.ited_pick_status = 'Picked';
            }

            fsInTransDetail.org_id = scopes.globals.org_id;
            fsInTransDetail.item_id = item_item_id;
            fsInTransDetail.intraned_trans_qty = itemToMove.Quantity;
            fsInTransDetail.ited_picked_qty += itemToMove.Quantity;
            fsInTransDetail.uom_id = item_uom_id;
            fsInTransDetail.intraned_cost_amt = oLocation.item_itemwhse_avg_cost_TO;
            fsInTransDetail.intraned_cost_uom_id = item_uom_id;
            fsInTransDetail.intraned_cost_uom_cost_factor = 1;
            fsInTransDetail.intraned_extended_cost = itemToMove.Quantity * oLocation.item_itemwhse_avg_cost_TO;

            fsInTransDetail.sequence_nr = 1;
            fsInTransDetail.intraned_whse_id_transfer_from = null;
            fsInTransDetail.intraned_whse_id = oLocation.intraneh_whse_id_TO;
            fsInTransDetail.intraned_cost_entered = oLocation.item_itemwhse_avg_cost_TO;
            fsInTransDetail.intraned_itemwhseloc_id_from = oLocation.intraned_itemwhseloc_id_FROM;
            fsInTransDetail.intraned_itemwhseloc_id = oLocation.intraned_itemwhseloc_id_TO;
            fsInTransDetail.intraned_ui_bin_id = fsInTransDetail.intraned_itemwhseloc_id;
            fsInTransDetail.intraned_whseloc_id_from = oLocation.intraned_whseloc_id_FROM;
            fsInTransDetail.intraned_ui_bin_id_from = fsInTransDetail.intraned_whseloc_id_from;
            fsInTransDetail.intraned_whseloc_id = oLocation.intraned_whseloc_id_TO;
            fsInTransDetail.itemwhse_id = oLocation.item_itemwhse_id_TO;

            fsInTransDetail.glacct_id = glacct_id;
            fsInTransDetail.rollconv_id = null;
            fsInTransDetail.intraned_base_cost_adj = null;

            fsInTransDetail.intraned_balance = null;
            fsInTransDetail.intraned_received_to_date = null;
        }

        try {
            var result = databaseManager.saveData();
            
            if(!result){
                InventoryMoveResult.MoveResult = data.method + "Failed to save all the transaction details!";
                return response;
            }
            /** @type{JSFoundSet<db:/avanti/in_trans_entry_header>} */
            var fsInTransEntryHeader = fsInTransDetail.in_trans_entry_detail_to_in_trans_entry_header;
            
            if(bHasSkippedItems){
                result = spawnSkippedItemTransfer(fsInTransDetail.intraneh_id.toString());
                if(!result){
                    application.output("Failed to spawn new transaction for skipped items in transaction :"+fsInTransDetail.intraneh_id, LOGGINGLEVEL.ERROR);
                }
            }

            result = scopes.avInv.buildTempTransactionTables(fsInTransEntryHeader.getSelectedRecord());
            
            if (result) {
                InventoryMoveResult.MoveResult = "Item moved";
            }
        }
        catch (ex) {
            InventoryMoveResult.MoveResult = data.method + ", Error: Code 1: " + ( ex.message != null ? ex.message : ex );
            application.output("api_wireless_inventory, method: " + data.method + " , Exception: " + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack, LOGGINGLEVEL.ERROR);
        }

        return response;
    },

    InventoryMove_MoveReasons: function(data){
        var sSql = "SELECT transfer_reason_id, reason_description \
                    FROM sys_transfer_reason WHERE (org_id = ?)\
                    ORDER BY reason_description";
        var args = [scopes.globals.org_id];

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args,
                 WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        var aTransferReasons = new Array();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                MoveReasonId: ds['transfer_reason_id'],
                MoveReasonDescription: ds['reason_description'] ? ds['reason_description'] : ""
            };
            aTransferReasons.push(eachElement);
        }

        var InventoryMove_MoveReasonsResult = {
            MoveReasons: aTransferReasons
        };
        var InventoryMove_MoveReasonsResponse = {
            InventoryMove_MoveReasonsResult: InventoryMove_MoveReasonsResult
        };
        var response = {
            InventoryMove_MoveReasonsResponse: InventoryMove_MoveReasonsResponse
        };
        return response;
    },
	
    GetActiveWarehouses: function(data) {
    	/** @type{JSFoundSet<db:/avanti/in_warehouse>} */
    	var fsActiveWarehouses = scopes.avDB.getFS("in_warehouse", ["whse_active"], [1]);
    	
    	var activeWarehouseArray = [];
    	for(var i = 1; i <= fsActiveWarehouses.getSize(); i++) {
    		var recWarehouse = fsActiveWarehouses.getRecord(i);
    		activeWarehouseArray.push({
    			WarehouseCode: recWarehouse.whse_code,
    			WarehouseDesc: recWarehouse.whse_desc,
				WarehouseID: recWarehouse.whse_id.toString()
    		});
    	}
		
				
    	var GetEmployeeWarehouseInformationResult = {
			ActiveWarehouses: activeWarehouseArray
    	};
    	

        var GetEmployeeWarehouseInformationResponse = {
        	GetEmployeeWarehouseInformationResult: GetEmployeeWarehouseInformationResult
        };
        	
        var response = {
        	GetEmployeeWarehouseInformationResponse: GetEmployeeWarehouseInformationResponse
        };
		
        return response;
	},
	
    InventoryTransactionRollList: function(data) {
		var itemCode = data.ItemNumber;
    	/** @type{JSFoundSet<db:/avanti/in_item>} */
    	var fsInItem = scopes.avDB.getFS("in_item", ["item_code"], [itemCode]);
    	var recInItem = fsInItem.getRecord(1);
    	
    	var warehouseCode = data.WhseCode;
    	/** @type{JSFoundSet<db:/avanti/in_warehouse>} */
    	var fsInWarehouse = scopes.avDB.getFS("in_warehouse", ["whse_code"], [warehouseCode]);
    	var recInWarehouse = fsInWarehouse.getRecord(1);
    	
    	var itemID = recInItem.item_id;
    	var wareID = recInWarehouse.whse_id;
    	var vbShowOHQ = true;
    	
		
		var vlName = 'vl_rollByItem';
		
		if (recInItem) {
			if (recInItem.in_item_to_in_item_class.itemclass_show_roll_unique_id == 1) {
				vlName = 'vl_rollByItem';
			}
			if (recInItem.in_item_to_in_item_class.itemclass_show_mill_roll_num == 1) {
				vlName = 'vl_rollByItem_millnum';
			}

			var eachRec = {};
			var rollInformationArray = [];
			/***@type {JSFoundset<db:/avanti/in_item_roll>} */
			var fsInItemRoll = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll');
			if (fsInItemRoll.find()) {
				fsInItemRoll.item_id = itemID;
				fsInItemRoll.whse_id = wareID;
				
				if (fsInItemRoll.search()) {
					for (var i = 1; i <= fsInItemRoll.getSize(); i++) {
		
						var record = fsInItemRoll.getRecord(i);
						if (record.initemroll_qty_on_hand <= 0) {
							continue;
						}
						
						eachRec = {
							initemroll_id: record.initemroll_id.toString(),
							initemroll_weight: record.initemroll_weight,
							initemroll_qty_on_hand: record.initemroll_qty_on_hand,
							sequence_nr: record.sequence_nr
						};
						
						if(recInItem && utils.hasRecords(recInItem.in_item_to_in_item_paper)) {
							eachRec['width'] = recInItem.in_item_to_in_item_paper.paper_first_dim;
						}
						
						if (vlName == 'vl_rollByItem') {
							var initemroll_roll_number = '';
							if (vbShowOHQ) {
								initemroll_roll_number = record.initemroll_roll_number + ' (OHQ:' + record.initemroll_qty_on_hand + ')';
							} else {
								initemroll_roll_number = record.initemroll_roll_number;
							}
						} else if (vlName == 'vl_rollByItem_millnum') {
							initemroll_roll_number = record.initemroll_roll_number;
						}
						eachRec['initemroll_roll_number'] = initemroll_roll_number;
						rollInformationArray.push(eachRec);
					}
				}
			}
		}
		

    	var InventoryTransactionRollListResult = rollInformationArray;

        var InventoryTransactionRollListResponse = {
        	InventoryTransactionRollListResult: InventoryTransactionRollListResult
        };
        	
        var response = {
        	InventoryTransactionRollListResponse: InventoryTransactionRollListResponse
        };
		
        return response;
	},
	
    InventoryMove: function(data) {
        try {
	    	var bTension = utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_name.indexOf("Tension") > -1; 
	    	
	        if (application.isInDeveloper() && bGetTestPackage) {
	             data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryMove');
	        }
	
			logDataPackage("SL-24467", data, bTension);
	        
	    	/** @type {{ItemNumber:String, Project:String, FromLocation:String, ToLocation:String, Quantity:Number,
	         ReferenceNumber:String, MoveReasonId:String, JobNumber:String, EmployeeCode:string, MoveResult:String, RollNumber:String }} */
	        var itemToMove = data.InvMove;
	        var item_item_id = null;
	        var item_uom_id = null;
	        var glacct_id = null;
	        var itemroll_id = null;
	        var item_track_rolls = null;
	        
	        //prepare response with Error in advance
	        var InventoryMoveResult = {
	            ItemNumber: itemToMove.ItemNumber,
	            Project: itemToMove.Project,
	            FromLocation: itemToMove.FromLocation,
	            ToLocation: itemToMove.ToLocation,
	            Quantity: itemToMove.Quantity,
	            ReferenceNumber: itemToMove.ReferenceNumber,
	            MoveReasonId: itemToMove.MoveReasonId,
	            JobNumber: itemToMove.JobNumber,
	            EmployeeCode: itemToMove.EmployeeCode,
	            RollNumber: itemToMove.RollNumber != null ? itemToMove.RollNumber : "",
	            MoveResult: "Error" 
	        };
	
	        var InventoryMoveResponse = {
	            InventoryMoveResult: InventoryMoveResult
	        };
	        var response = {
	            InventoryMoveResponse: InventoryMoveResponse
	        };
	
	        //get additional needed information for item
	        var args = [itemToMove.ItemNumber, scopes.globals.org_id];
	        var sSql = "SELECT distinct item.item_id, item.item_standard_uom_id, item.item_track_rolls, item.item_gl_inventory_adj, item_class.itemclass_gl_inventory_adj \
	            FROM in_item AS item \
	            LEFT JOIN in_item_class AS item_class ON item_class.itemclass_id = item.itemclass_id \
	            WHERE item.item_code = ? AND item.org_id = ?";
	        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	
	        if (ds.getMaxRowIndex() == 0) {
	            InventoryMoveResult.MoveResult = data.method + ", Error: Incorrect ItemNumber";
	            return response;
	        }
	
	        ds.rowIndex = 1;
	        item_item_id = ds['item_id'];
	        item_uom_id = ds['item_standard_uom_id'];
	        glacct_id = ds['item_gl_inventory_adj'] != null ? ds['item_gl_inventory_adj'] : ds['itemclass_gl_inventory_adj'] != null ? ds['itemclass_gl_inventory_adj'] : null;
	        item_track_rolls = ds['item_track_rolls'];
	
	        var locationBin_code_FROM = itemToMove.FromLocation;
	        var locationBin_code_TO = itemToMove.ToLocation;
	        
	        var oLocation = buildLocationObject(item_item_id,locationBin_code_FROM,locationBin_code_TO);
	
	        if(oLocation.error){
	        	// Creating new binlocation
	        	var binArray = locationBin_code_TO.split('-');
	        	
	        	if(binArray.length > 0) {
	        		var whse_code = binArray[0];
		        	
		            /** @type{JSFoundSet<db:/avanti/in_warehouse>} */
		            var fsInWarehouse = scopes.avDB.getFS("in_warehouse", ["whse_code"], [whse_code]);
		            
		            if(fsInWarehouse.getSize() === 0) {
			            InventoryMoveResult.MoveResult = data.method + oLocation.error;
			            return response;
		            }
	
	            	// Validating that the in_warehouse_level_codes exist in database and with correct sequence
		            var argsX = [fsInWarehouse.whse_id.toString(), scopes.globals.org_id];
		            var sSqlX = "SELECT distinct level_code.whselevelcode_code, level_code.whselevelcode_id, in_warehouse_level.whselevel_level \
						FROM in_warehouse_level_code AS level_code \
						INNER JOIN in_warehouse_level ON in_warehouse_level.whselevel_id = level_code.whselevel_id \
						WHERE in_warehouse_level.whse_id = ? AND level_code.org_id = ? ";
		            
		            var sqlArray = [];
					for(var i = 1; i < binArray.length; i++) {
						sqlArray.push("whselevel_level = ? AND whselevelcode_code = ?");
						argsX.push(i);
						argsX.push(binArray[i]);
					}
					
					if(sqlArray.length > 0) {
						sSqlX += "AND ("  + sqlArray.join(' OR ') + ") "
					}
					sSqlX += " ORDER BY whselevel_level, whselevelcode_code";
		            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSqlX, argsX, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
		            var maxRowIndex = ds.getMaxRowIndex();
		            if(sqlArray.length !== maxRowIndex) {
			            InventoryMoveResult.MoveResult = data.method + oLocation.error;
			            return response;
		            }
		            
		            /** @type{JSFoundSet<db:/avanti/in_warehouse_location>} */
		            var fsInWarehouseLocation = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_warehouse_location')
		            
		            var recInWarehouseLocation = fsInWarehouseLocation.getRecord(fsInWarehouseLocation.newRecord());
		            recInWarehouseLocation.whse_id = fsInWarehouse.whse_id;
		            recInWarehouseLocation.whseloc_bin_location = locationBin_code_TO;
		            
	                for (var i = 1; i <= maxRowIndex; i++) {
	                    ds.rowIndex = i;
	                    switch (i) {
	                    case 1:
	                    	recInWarehouseLocation.whselevelcode1_id = ds['whselevelcode_id'];
	                    	break;
	                    case 2:
	                    	recInWarehouseLocation.whselevelcode2_id = ds['whselevelcode_id'];
	                    	break;
	                    case 3:
	                    	recInWarehouseLocation.whselevelcode3_id = ds['whselevelcode_id'];
	                    	break;
	                    case 4:
	                    	recInWarehouseLocation.whselevelcode4_id = ds['whselevelcode_id'];
	                    	break;
	                    case 5:
	                    	recInWarehouseLocation.whselevelcode5_id = ds['whselevelcode_id'];
	                    	break;
	                    default:
	                    	break;
	                    }
                	}
	                databaseManager.saveData(recInWarehouseLocation);
		                
		            oLocation = buildLocationObject(item_item_id,locationBin_code_FROM,locationBin_code_TO);
		            if(oLocation.error) {
			            InventoryMoveResult.MoveResult = data.method + oLocation.error;
			            return response;
		            }
		            
	        	}
	        }
   
			logDataPackage("SL-24467", oLocation, bTension);
	
	        //TODO 2017-05-17, TEMP until phase 2 which will provide RoolNumber form the list, not entered - no not needed to be verified
	        if (itemToMove.RollNumber != null && itemToMove.RollNumber.length > 0 && 1 == item_track_rolls) {
	            args = [item_item_id, itemToMove.RollNumber, scopes.globals.org_id];
	            ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT initemroll_id FROM in_item_roll \
	                    WHERE item_id = ? AND initemroll_roll_number=? AND org_id = ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	
	            if (ds.getMaxRowIndex() == 0) {
	                InventoryMoveResult.MoveResult = data.method + ", Error: Incorrect RollNumber";
	                return response;
	            }
	
	            ds.rowIndex = 1;
	            itemroll_id = ds['initemroll_id'];
	        }
	
	        //determine the transaction type ID: BTO, RBTO, WTO, IR, RR, QA, some others
	        var intranstype_trans_code = scopes.avInv.TRANSACTION_TYPE.BinTransfer;
	
	        if (oLocation.intraneh_whse_id_FROM != oLocation.intraneh_whse_id_TO) {
	            //original line
	            //intranstype_trans_code = 'WTO';
	
	            //TODO TEMP do not allow to such transactions in phase 1
	            InventoryMoveResult.MoveResult = data.method + ", Error: Transactions between Warehouses not supported at the moment.";
	            return response;
	
	        }
	        else if (1 == item_track_rolls && itemroll_id != null) {
	            intranstype_trans_code = scopes.avInv.TRANSACTION_TYPE.RollBinTransfer;
	        }
	        else if (1 == item_track_rolls && itemroll_id == null) {
	            InventoryMoveResult.MoveResult = data.method + ", Error: Incorrect parameters provided";
	            return response;
	        }
	
	        // FILL IN NEEDED INFORMATIN FOR SUBSEQUENT TRANSACTIONS
	
	        //********************************** HEADER TABLE
	        /** @type {JSFoundSet<db:/avanti/in_trans_entry_header>} */
	        var fsTransEntryHeader = forms['in_trans_entry_header'].foundset;
	        /** @type {JSRecord<db:/avanti/in_trans_entry_header>} */
	        var rTransEntryHeader = fsTransEntryHeader.getRecord(fsTransEntryHeader.newRecord());
	        rTransEntryHeader.org_id = scopes.globals.org_id;
	        rTransEntryHeader.intranstype_id = scopes.avInv.getTransactionTypeUUID("IT", intranstype_trans_code);
	        rTransEntryHeader.intraneh_reference = itemToMove.ReferenceNumber;
	        rTransEntryHeader.intraneh_whse_id = oLocation.intraneh_whse_id_FROM;
	        rTransEntryHeader.intraneh_createdby_empl_id = globals.avBase_employeeUUID;
	        rTransEntryHeader.intraneh_status = 0;
	        rTransEntryHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
	
	        //TODO Tim: New on 2017-05-17, In this release, not supporting WTO, should it be null (as in shared docs) or this is fine as well ???
	        //maybe in case when it =intraneh_whse_id_TO (not null), this is why Transaction exeption
	        rTransEntryHeader.intraneh_whse_id_transfer_to = null; //intraneh_whse_id_TO;
	
	        rTransEntryHeader.intraneh_status_field = 'O';
	        rTransEntryHeader.itreg_id = null;
	        rTransEntryHeader.intraneh_complete = null;
	        rTransEntryHeader.intraneh_desc = null
	        rTransEntryHeader.intraneh_expected_date = null;
	        rTransEntryHeader.intraneh_reference_pr = null;
	
	        //********************************** DETAIL TABLE
	        /** @type {JSRecord<db:/avanti/in_trans_entry_detail>} */
	        var rTransEntryDetail = rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.newRecord());
	        rTransEntryDetail.org_id = scopes.globals.org_id;
	        rTransEntryDetail.item_id = item_item_id;
	        rTransEntryDetail.intraned_trans_qty = itemToMove.Quantity;
	        rTransEntryDetail.uom_id = item_uom_id;
	        rTransEntryDetail.intraned_cost_amt = oLocation.item_itemwhse_avg_cost_TO;
	        rTransEntryDetail.intraned_cost_uom_id = item_uom_id;
	        rTransEntryDetail.intraned_cost_uom_cost_factor = 1;
	        rTransEntryDetail.intraned_extended_cost = itemToMove.Quantity * oLocation.item_itemwhse_avg_cost_TO;
	
	        rTransEntryDetail.sequence_nr = 1;
	        rTransEntryDetail.intraned_whse_id_transfer_from = null;
	        rTransEntryDetail.intraned_whse_id = oLocation.intraneh_whse_id_TO;
	        rTransEntryDetail.intraned_cost_entered = oLocation.item_itemwhse_avg_cost_TO;
	        rTransEntryDetail.intraned_itemwhseloc_id_from = oLocation.intraned_itemwhseloc_id_FROM;
	        rTransEntryDetail.intraned_itemwhseloc_id = oLocation.intraned_itemwhseloc_id_TO;
	        rTransEntryDetail.intraned_ui_bin_id = rTransEntryDetail.intraned_itemwhseloc_id;
	        rTransEntryDetail.intraned_whseloc_id_from = oLocation.intraned_whseloc_id_FROM;
	        rTransEntryDetail.intraned_ui_bin_id_from = rTransEntryDetail.intraned_whseloc_id_from;
	        rTransEntryDetail.intraned_whseloc_id = oLocation.intraned_whseloc_id_TO;
	        rTransEntryDetail.itemwhse_id = oLocation.item_itemwhse_id_TO;
	
	        rTransEntryDetail.glacct_id = glacct_id;
	        rTransEntryDetail.rollconv_id = null;
	        rTransEntryDetail.intraned_base_cost_adj = null;
	
	        rTransEntryDetail.intraned_balance = null;
	        rTransEntryDetail.intraned_received_to_date = null;
	        
	        // sl-20590 - in_item_project recs were being created with an empty string custproj_desc
	        var sProject = scopes.avText.trim(itemToMove.Project);
	        
			if (sProject) {
		        rTransEntryDetail.custproj_desc = sProject;
			}
			else {
		        rTransEntryDetail.custproj_desc = null;
			}
	        
	        if(itemToMove.PickSkipID && itemToMove.PickSkipID.length > 0){
	            rTransEntryDetail.pickskip_id = itemToMove.PickSkipID;
	        }
	        
	        if(itemToMove.MoveReasonId && itemToMove.MoveReasonId.length > 0){
	            rTransEntryDetail.transfer_reason_id = itemToMove.MoveReasonId;
	        }
        
            application.output("    ===== InventoryMove: EXECUTING DB SAVING...");
            databaseManager.saveData();
            
	        if (bTension) {
	    		scopes.avUtils.devLog("SL-24467", "rTransEntryHeader: " + rTransEntryHeader.intraneh_id + ", rTransEntryDetail: " + rTransEntryDetail.intraned_id);
	        }
            
            // sl-23545 - have to call adjustTransactionFIFO() to create in_item_fifo_reserved
            var sEffectOnQty = rTransEntryHeader.in_trans_entry_header_to_in_transaction_type.intranstype_effect_on_qty;
            scopes.avInv.adjustTransactionFIFO(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail, sEffectOnQty, true);
            
			if (scopes.avInv.buildTempTransactionTables(rTransEntryHeader)) {
	            InventoryMoveResult.MoveResult = "Item moved";
			}
			else {
	            InventoryMoveResult.MoveResult = "Error. buildTempTransactionTables returned false";
			}
        }
        catch (ex) {
            InventoryMoveResult.MoveResult = data.method + ", Error: Code 1: " + ( ex.message != null ? ex.message : ex );
       		scopes.avUtils.devLog("SL-24467", "api_wireless_inventory, method: " + data.method + " , Exception: " + ( ex.message != null ? ex.message : ex ), null, null, null, ex.stack);
            application.output("api_wireless_inventory, method: " + data.method + " , Exception: " + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack, LOGGINGLEVEL.ERROR);
        }

        application.output(">>> FINISHED");
        return response;
    },

    InventoryAdjustment: function(data) {
    	try {
	        if (application.isInDeveloper() && bGetTestPackage) {
	             data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InventoryAdjustment');
	        }
	       
	        /** @type {{ItemNumber:String, Location:String, Project:String, QuantityOnHand:Number, QuantityBy:Number,
	         ReferenceNumber:String, Type:String, RollNumber:String, Result:string }}*/
	        var itemAdj = data.InvAdj;
	        var item_item_id = null;
	        var item_uom_id = null;
	        var glacct_id = null;
	        var itemroll_id = null;
	        var item_track_rolls = null;
	        var item_generate_roll_number = null
	        var item_itemwhse_avg_cost_TO = 0;
	        var intranstype_id = null;
	
	        //prepare response with Error in advance
	        var InventoryAdjustmentResult = {
	            ItemNumber: itemAdj.ItemNumber,
	            Location: itemAdj.Location,
	            Project : itemAdj.Project,
	            QuantityOnHand: itemAdj.QuantityOnHand,
	            QuantityTo: itemAdj.QuantityBy,
	            ReferenceNumber: itemAdj.ReferenceNumber,
	            Type: itemAdj.Type,
	            RollNumber: itemAdj.RollNumber != null ? itemAdj.RollNumber : "",
	            Result: "Error" //"Item Adjusted" - success
	        };
	        var InventoryAdjustmentResponse = {
	            InventoryAdjustmentResult: InventoryAdjustmentResult
	        };
	        var response = {
	            InventoryAdjustmentResponse: InventoryAdjustmentResponse
	        };
	
	        //get additional needed information for item
	        var args = [itemAdj.ItemNumber, scopes.globals.org_id];
	        var sSql = "SELECT distinct item.item_id, item.item_standard_uom_id, item.item_track_rolls, item.item_generate_roll_number \
	            FROM in_item AS item  \
	            WHERE item.item_code = ? AND item.org_id = ?";
	        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	      
	        if (ds.getMaxRowIndex() == 0) {
	            InventoryAdjustmentResult.Result = "Error: Incorrect ItemNumber";
	            return response;
	        }
	
	        ds.rowIndex = 1;
	        item_item_id = ds['item_id'];
	        item_uom_id = ds['item_standard_uom_id'];
	        item_track_rolls = ds['item_track_rolls'];
	        item_generate_roll_number = ds['item_generate_roll_number'];
	
	        //get additional needed information for item_glacct_id
	        args = [itemAdj.Type, scopes.globals.org_id];
	        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT glacct_id \
	              FROM in_adjustment_types \
	              WHERE adjtype_code = ? AND org_id=?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	
	        if (ds.getMaxRowIndex() > 0) {
	            glacct_id = ds.getValue(1, 1);
	        }
	
	        if (1 == item_generate_roll_number) {
	
	        }
	
	        if (1 == item_track_rolls && itemAdj.RollNumber != null && itemAdj.RollNumber.length > 0) {
	            args = [item_item_id, itemAdj.RollNumber, scopes.globals.org_id];
	            ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT initemroll_id \
	                    FROM in_item_roll \
	                    WHERE item_id = ? AND initemroll_roll_number=? AND org_id = ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	
	            if (ds.getMaxRowIndex() == 0) {
	                InventoryAdjustmentResult.Result = data.method + ", Error: Incorrect RollNumber";
	                return response;
	            }
	
	            ds.rowIndex = 1;
	            itemroll_id = ds['initemroll_id'];
	        }
	
	        if (1 == item_track_rolls && itemroll_id == null) {
	            InventoryAdjustmentResult.Result = data.method + ", Error: Incorrect RollNumber";
	            return response;
	        }
	        //END temp for Phase 1
	
	        var intranstype_trans_code = 'QA';
	        intranstype_id = scopes.avInv.getTransactionTypeUUID("IT", intranstype_trans_code);
	
	        var locationBin_code_TO = itemAdj.Location;
	
	        var intraneh_whse_id_TO = null;
	        var intraned_whseloc_id_TO = null;
	        var intraned_itemwhseloc_id_TO = null;
	        var item_itemwhse_id_TO = null;
	
	        args = [item_item_id, item_item_id, locationBin_code_TO, scopes.globals.org_id];
	        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_warehouse_location.whse_id, in_warehouse_location.whseloc_id \
	            , in_item_warehouse_location.itemwhseloc_id, in_item_warehouse.itemwhse_avg_cost, in_item_warehouse.itemwhse_id \
	            FROM in_warehouse_location \
	            LEFT JOIN in_item_warehouse_location \
	            ON (in_item_warehouse_location.whseloc_id=in_warehouse_location.whseloc_id AND in_item_warehouse_location.item_id=?) \
	            INNER JOIN in_item_warehouse ON(in_item_warehouse.whse_id = in_warehouse_location.whse_id AND in_item_warehouse.item_id=?) \
	            WHERE in_warehouse_location.whseloc_bin_location= ? AND in_warehouse_location.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	
	        if (ds.getMaxRowIndex() == 0) {
	            InventoryAdjustmentResult.Result = "Error: Incorrect Location";
	            return response;
	        }
	
	        ds.rowIndex = 1;
	        intraneh_whse_id_TO = ds['whse_id'];
	        intraned_whseloc_id_TO = ds['whseloc_id'];
	        item_itemwhse_id_TO = ds['itemwhse_id'];
	        item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];
	
	        if (ds['itemwhseloc_id'] != null) {
	            intraned_itemwhseloc_id_TO = ds['itemwhseloc_id'];
	        }
	        // else - there is no record in in_item_warehouse_location for specified location.
	        // most likely Default location was specified which has never been used before => create a new record
	        else {
	            intraned_itemwhseloc_id_TO = createItemWarehouseLocation(item_item_id, item_itemwhse_id_TO, intraned_whseloc_id_TO);
	        }
	
	        //If not g/l account by adjustment type, then use default logic.
	        if (!glacct_id) {
	            glacct_id = scopes.avAccounting.getDefaultOffsetGLAccount(item_item_id, intraneh_whse_id_TO, intranstype_id);
	        }
	
	        // FILL IN NEEDED INFORMATIN FOR SUBSEQUENT TRANSACTIONS
	
	        //********************************** HEADER TABLE
	        /** @type {JSFoundSet<db:/avanti/in_trans_entry_header>} */
	        var fsTransEntryHeader = forms['in_trans_entry_header'].foundset;
	        /** @type {JSRecord<db:/avanti/in_trans_entry_header>} */
	        var rTransEntryHeader = fsTransEntryHeader.getRecord(fsTransEntryHeader.newRecord());
	        rTransEntryHeader.org_id = scopes.globals.org_id;
	        rTransEntryHeader.intranstype_id = intranstype_id;
	        rTransEntryHeader.intraneh_reference = itemAdj.ReferenceNumber;
	        rTransEntryHeader.intraneh_whse_id = intraneh_whse_id_TO;
	        rTransEntryHeader.intraneh_createdby_empl_id = globals.avBase_employeeUUID;
	        rTransEntryHeader.intraneh_status = 0;
	        rTransEntryHeader.intraneh_posted_date = null;
	        rTransEntryHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
	        rTransEntryHeader.intraneh_whse_id_transfer_to = null;
	        rTransEntryHeader.intraneh_status_field = 'O';
	        rTransEntryHeader.itreg_id = null;
	        rTransEntryHeader.intraneh_complete = null;
	        rTransEntryHeader.intraneh_desc = null
	        rTransEntryHeader.intraneh_expected_date = null;
	        rTransEntryHeader.intraneh_reference_pr = null;
	
	        //********************************** DETAIL TABLE
	        var rTransEntryDetail = rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.newRecord());
	        rTransEntryDetail.org_id = scopes.globals.org_id;
	        rTransEntryDetail.item_id = item_item_id;
	        rTransEntryDetail.intraned_trans_qty = itemAdj.QuantityBy;
	        rTransEntryDetail.uom_id = item_uom_id;
	        rTransEntryDetail.intraned_cost_amt = item_itemwhse_avg_cost_TO;
	        rTransEntryDetail.intraned_cost_uom_id = item_uom_id;
	        rTransEntryDetail.intraned_cost_uom_cost_factor = 1;
	        rTransEntryDetail.intraned_comment = null;
	
	        rTransEntryDetail.intraned_extended_cost = itemAdj.QuantityBy * item_itemwhse_avg_cost_TO;
	        rTransEntryDetail.sequence_nr = 1;
	        rTransEntryDetail.intraned_whse_id_transfer_from = null;
	        rTransEntryDetail.intraned_whse_id = intraneh_whse_id_TO;
	        rTransEntryDetail.intraned_cost_entere = item_itemwhse_avg_cost_TO;
	        rTransEntryDetail.intraned_itemwhseloc_id_from = null;
	        rTransEntryDetail.intraned_itemwhseloc_id = intraned_itemwhseloc_id_TO;
	        rTransEntryDetail.intraned_ui_bin_id = rTransEntryDetail.intraned_itemwhseloc_id;
	        rTransEntryDetail.intraned_whseloc_id_from = null;
	        rTransEntryDetail.intraned_whseloc_id = intraned_whseloc_id_TO;
	        rTransEntryDetail.itemwhse_id = item_itemwhse_id_TO;
	
	        rTransEntryDetail.glacct_id = glacct_id;
	        rTransEntryDetail.rollconv_id = null;
	        rTransEntryDetail.intraned_base_cost_adj = null;
	
	        rTransEntryDetail.initemroll_id = itemroll_id;
	
	        rTransEntryDetail.intraned_balance = null; // Tim's comment: This is only for Pending receipts
	        rTransEntryDetail.intraned_received_to_date = null;
	        
	        // sl-20590 - in_item_project recs were being created with an empty string custproj_desc
	        var sProject = scopes.avText.trim(itemAdj.Project);
	        
			if (sProject) {
		        rTransEntryDetail.custproj_desc = sProject;
			}
			else {
		        rTransEntryDetail.custproj_desc = null;
			}
	
	        try {
	            application.output("    ===== InventoryAdjustment: EXECUTING DB SAVING...");
	
	            databaseManager.saveData();
	            
	            // sl-23545 - have to call adjustTransactionFIFO() to create in_item_fifo_reserved
	            var sEffectOnQty = rTransEntryHeader.in_trans_entry_header_to_in_transaction_type.intranstype_effect_on_qty;
	            scopes.avInv.adjustTransactionFIFO(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail, sEffectOnQty, true);
	            scopes.avInv.buildTempTransactionTables(rTransEntryHeader);
	            
	            InventoryAdjustmentResult.Result = "Item Adjusted";
	        }
	        catch (ex) {
	            InventoryAdjustmentResult.Result = data.method + ", Error: Code 2: " + ( ex.message != null ? ex.message : ex );
	            application.output("api_wireless_inventory, method: " + data.method + " , Exception: " + + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack, LOGGINGLEVEL.ERROR);
	        }
	
	        application.output(">>> InventoryAdjustment FINISHED");
	        return response;
    	}
    	catch (ex) {
            InventoryAdjustmentResult.Result = "Error: An error occurred in InventoryAdjustment(): " + ex.message;
            
            scopes.avUtils.devLog("InventoryAdjustment.Error", InventoryAdjustmentResult.Result, null, null, null, ex.stack);
			logDataPackage("InventoryAdjustment", data, true);
            
	        return response;
    	}
    },

    InventoryAdjustment_AdjustmentTypes: function(data){
        var sSql = "SELECT at.adjtype_code,at.adjtype_desc \
                    FROM in_adjustment_types at \
                    INNER JOIN in_adjustment_types_item_class AS atic ON atic.adjtype_id = at.adjtype_id AND atic.is_selected = 1 \
                    INNER JOIN in_item as item ON item.itemclass_id = atic.itemclass_id \
                    WHERE at.org_id=? and item.item_code=?";
        var args = [scopes.globals.org_id, data.ItemNumber];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        var adjustmentTypes = new Array();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                AdjustmentTypeCode: ds['adjtype_code'],
                AdjustmentTypeDescription: ds['adjtype_desc'] ? ds['adjtype_desc'] : ""
            };
            adjustmentTypes.push(eachElement);
        }

        var InventoryAdjustment_AdjustmentTypesResult = {
            AdjustmentTypes: adjustmentTypes
        };
        var InventoryAdjustment_AdjustmentTypesResponse = {
            InventoryAdjustment_AdjustmentTypesResult: InventoryAdjustment_AdjustmentTypesResult
        };
        var response = {
            InventoryAdjustment_AdjustmentTypesResponse: InventoryAdjustment_AdjustmentTypesResponse
        };
        return response;
        
    },
	
    InvtCount_AddOnFlyCountItem: function(data) {
        //2017-04-26 IK: data.EmployeeCode received actually contains username in Slingshot's terminology
        //As a matter of the fact it's not used in this method.

        //NB, IK: if a record for specified location/item does not exist in in_item_warehous_location
        //item will be not added and false will be returned
        //2017-04-27 UPDATE: According to Amin, such case is supported in Classic and probably need to support this case => seems we need add record to in_item_warehous_location
        //However in Slingshot UI's Count item is not added to the list for Counting if specified bin/warehouse does not have items/records
        //even if all warehouses/locations checked.
        //We decided to keep code as it is and let testers determine the right behaviour

		if (application.isInDeveloper() && bGetTestPackage) {
		    data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InvtCount_AddOnFlyCountItem');
		}

        var InvtCount_AddOnFlyCountItemResponse = {
            InvtCount_AddOnFlyCountItemResult: false
        };
        var response = {
            InvtCount_AddOnFlyCountItemResponse: InvtCount_AddOnFlyCountItemResponse
        };

        if (data.CountCode==null || data.CountCode.length==0
                || data.ItemCode==null || data.ItemCode.length==0 || data.Location==null || data.Location.length==0) {

            return response;
        }

        //getting additional required information
        var args = [data.CountCode, scopes.globals.avBase_employeeUUID, scopes.globals.org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_item_warehouse_count.in_item_warehouse_count_id \
            , MAX(line_number) as max_line_number \
            FROM in_item_warehouse_count \
            LEFT JOIN  in_item_warehouse_count_items ON (in_item_warehouse_count_items.in_item_warehouse_count_id = in_item_warehouse_count.in_item_warehouse_count_id) \
            WHERE in_item_warehouse_count.in_itemwhscount_countcode=? AND in_item_warehouse_count.empl_id=? AND in_item_warehouse_count.org_id=? \
            GROUP BY in_item_warehouse_count.in_item_warehouse_count_id, in_item_warehouse_count.in_itemwhscount_countcode", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        var count_id = ds['in_item_warehouse_count_id'];
        var line_number = 1;
        //if there are already items for this CountCode, compute line_number
        if (ds['max_line_number'] != null) {
            line_number = ds['max_line_number'] + 1;
        }

        args = [data.ItemCode, scopes.globals.org_id, data.Location];
        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_item.item_id, in_item_warehouse.whse_id \
                , in_item_warehouse.itemwhse_avg_cost, in_item_warehouse_location.itemwhseloc_id \
                FROM in_item \
                INNER JOIN in_item_warehouse ON (in_item_warehouse.item_id=in_item.item_id) \
                INNER JOIN in_item_warehouse_location ON (in_item_warehouse_location.itemwhse_id=in_item_warehouse.itemwhse_id) \
                WHERE in_item.item_code=? AND in_item.org_id=? \
                AND in_item_warehouse_location.whseloc_id \
                IN (SELECT whseloc_id FROM in_warehouse_location WHERE in_item_warehouse_location.whseloc_bin_location=?)", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        var item_id = ds['item_id'];
        var whse_id = ds['whse_id'];
        var itemwhse_avg_cost = ds['itemwhse_avg_cost'];
        var itemwhseloc_id = ds['itemwhseloc_id'];

        /** @type{JSFoundSet<db:/avanti/in_item_warehouse_count_items>} */
        var fsInItemWarehouseCountItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count_items');
        /** @type {JSRecord<db:/avanti/in_item_warehouse_count_items>}  */
        var rInItemWarehouseCountItem = fsInItemWarehouseCountItem.getRecord(fsInItemWarehouseCountItem.newRecord());

        rInItemWarehouseCountItem.org_id = scopes.globals.org_id;
        rInItemWarehouseCountItem.in_item_warehouse_count_id = count_id;
        rInItemWarehouseCountItem.include_in_count = 1;
        rInItemWarehouseCountItem.is_newly_added = 1;
        rInItemWarehouseCountItem.item_id = item_id;
        rInItemWarehouseCountItem.whse_id = whse_id;
        rInItemWarehouseCountItem.itemwhseloc_id = itemwhseloc_id;
        rInItemWarehouseCountItem.itemwhse_avg_cost = itemwhse_avg_cost;
        rInItemWarehouseCountItem.line_number = line_number;
        
		if (scopes.avInv.doesItemUseProjectInventory(item_id)) {		
			rInItemWarehouseCountItem.ip_id = scopes.avInv.getItemProjectIDUsingItemBinProject(itemwhseloc_id, data.Project);
		}

        if (databaseManager.saveData(rInItemWarehouseCountItem)) {
            InvtCount_AddOnFlyCountItemResponse.InvtCount_AddOnFlyCountItemResult = true;
        }

        return response;
    },

    InvtCount_ConfirmVarianceReport: function(data) {
        var result = true;

        var InvtCount_ConfirmVarianceReportResponse = {
            InvtCount_ConfirmVarianceReportResult: result
        };
        var response = {
            InvtCount_ConfirmVarianceReportResponse: InvtCount_ConfirmVarianceReportResponse
        };
        return response;
    },

    InvtCount_GetCountCode: function(data) {
        //2017-04-26 IK: data.EmployeeCode received actually contains username in Slingshot's terminology
        //As a matter of the fact it's not used in this method. globals.svy_sec_lgn_user_id is used instead

        var InventoryCount = new Array();

        /* before 2017-05-11 - Zehava's testing. We need to get a records with no Employee assigned as well */

        var args = [globals.avBase_employeeUUID, scopes.globals.org_id, globals.avBase_employeeUUID];
        var sSql = "SELECT in_item_warehouse_count.in_itemwhscount_countcode, in_item_warehouse_count_items.batch_number \
            FROM in_item_warehouse_count \
            LEFT JOIN in_item_warehouse_count_items \
            ON (in_item_warehouse_count_items.in_item_warehouse_count_id = in_item_warehouse_count.in_item_warehouse_count_id) \
            WHERE (in_item_warehouse_count.empl_id=? OR in_item_warehouse_count.empl_id IS NULL) \
            AND in_item_warehouse_count.org_id = ? AND in_item_warehouse_count.in_itemwhsecount_countstatus='S' \
            AND (in_item_warehouse_count_items.batch_state is NULL OR in_item_warehouse_count_items.batch_state <> 'C')\
            AND (in_item_warehouse_count_items.empl_Id = ? OR in_item_warehouse_count_items.empl_Id IS NULL)";
            
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND in_item_warehouse_count_items.whse_id IN " + getWarehousesByEmployee() + 
	            " GROUP BY in_item_warehouse_count.in_itemwhscount_countcode, in_item_warehouse_count_items.batch_number \
	              ORDER BY in_item_warehouse_count.in_itemwhscount_countcode ASC, in_item_warehouse_count_items.batch_number ASC";
        }
        else {
        	sSql += " GROUP BY in_item_warehouse_count.in_itemwhscount_countcode, in_item_warehouse_count_items.batch_number \
              ORDER BY in_item_warehouse_count.in_itemwhscount_countcode ASC, in_item_warehouse_count_items.batch_number ASC";
        }
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                CountCode: ds['in_itemwhscount_countcode'],
                BatchCode: ds['batch_number'] != null ? ds['batch_number'] : ""
            };
            InventoryCount.push(eachElement);
        }

        var InvtCount_GetCountCodeResult = {
            InventoryCount: InventoryCount
        };
        var InvtCount_GetCountCodeResponse = {
            InvtCount_GetCountCodeResult: InvtCount_GetCountCodeResult
        };
        var response = {
            InvtCount_GetCountCodeResponse: InvtCount_GetCountCodeResponse
        };
        return response;
    },

	InvtCount_LockCountBatch: function(data) {
		var result = false;
		
		if (data == null || data.length == 0) {
			return response;
		}
		
		/** @type{JSFoundSet<db:/avanti/in_item_warehouse_count_items>} */
        var fs_in_item_warehouse_count_items = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count_items');
        if (fs_in_item_warehouse_count_items.find()) {
        	fs_in_item_warehouse_count_items.org_id = scopes.globals.org_id;
        	fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_warehouse_count.in_itemwhscount_countcode = data.InvtCountCode;
        	if (data.InvtBatchCode != null || data.length > 0){
        		fs_in_item_warehouse_count_items.batch_number = data.InvtBatchCode;
        	}       	
        }
        
        if (fs_in_item_warehouse_count_items.search() > 0) {
        	for (var index = 1; index <= fs_in_item_warehouse_count_items.getSize(); index++) {
        		var record = fs_in_item_warehouse_count_items.getRecord(index);
        		record.empl_id = scopes.globals.avBase_employeeUUID;
        	}
        	
        	if (databaseManager.saveData(fs_in_item_warehouse_count_items)) {
            	result = true;
            }        	
        }
        
        var InvtCount_LockCountBatchResponse = {
        	InvtCount_LockCountBatchResult: result
        };
        var response = {
        	InvtCount_LockCountBatchResponse: InvtCount_LockCountBatchResponse
        };
        return response;
	},
	
    InvtCount_GetCountItems: function(data) {
        //2017-04-26 IK: data.EmployeeCode received actually contains username in Slingshot's terminology
        //As a matter of the fact it's not used in this method. globals.svy_sec_lgn_user_id is used instead

        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InvtCount_GetCountItems');
        }
    	
        var InventoryCountDetails = new Array();

        if (data.InvtCountCode != null && data.InvtCountCode.length > 0) {

            var args = [globals.avBase_employeeUUID, scopes.globals.org_id, data.InvtCountCode, data.InvtBatchCode];
            var sqlQueryStr = "SELECT in_item_warehouse_count_items.book_quantity, in_item_warehouse_count_items.count_quantity, \
                in_item.item_code, in_item.item_desc1, in_item.item_id, in_item.item_color, \
                in_item.item_dimension_length, in_item.item_dimension_width, in_item.item_dimension_heigth, \
                item_class.itemclass_type, item_paper.paper_first_dim, item_paper.paper_second_dim,\
                in_warehouse_location.whseloc_bin_location, suom.uom_code, ip.custproj_desc, in_item_roll.initemroll_roll_number \
                FROM in_item_warehouse_count  \
                INNER JOIN in_item_warehouse_count_items ON in_item_warehouse_count_items.in_item_warehouse_count_id = in_item_warehouse_count.in_item_warehouse_count_id \
                LEFT JOIN in_item ON (in_item.item_id=in_item_warehouse_count_items.item_id) \
                LEFT JOIN in_item_class AS item_class ON (item_class.itemclass_id = in_item.itemclass_id) \
                LEFT JOIN in_item_paper AS item_paper ON (item_paper.item_id = in_item.item_id) \
                LEFT JOIN sys_unit_of_measure AS suom ON (in_item.item_standard_uom_id = suom.uom_id) \
                INNER JOIN in_item_warehouse_location ON (in_item_warehouse_location.itemwhseloc_id=in_item_warehouse_count_items.itemwhseloc_id) \
                INNER JOIN in_warehouse_location ON in_warehouse_location.whseloc_id=in_item_warehouse_location.whseloc_id \
                LEFT OUTER JOIN in_item_project ip ON ip.ip_id = in_item_warehouse_count_items.ip_id \
                LEFT JOIN in_item_roll ON in_item_roll.initemroll_id = in_item_warehouse_count_items.initemroll_id \
                WHERE (in_item_warehouse_count.empl_id=? OR in_item_warehouse_count.empl_id IS NULL) \
            	AND in_item_warehouse_count.org_id=? \
            	AND in_item_warehouse_count.in_itemwhscount_countcode=? \
            	AND in_item_warehouse_count.in_itemwhsecount_countstatus='S' \
            	AND (in_item_warehouse_count_items.batch_number =? OR in_item_warehouse_count_items.batch_number IS NULL)";

            if (data.InvtBatchCode != null && data.InvtBatchCode.length > 0) {
                sqlQueryStr += " AND in_item_warehouse_count_items.batch_number=" + data.InvtBatchCode;
            }
            
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sqlQueryStr += " AND in_item_warehouse_count_items.whse_id IN " + getWarehousesByEmployee() ;
            }

            sqlQueryStr += " ORDER BY in_warehouse_location.whseloc_bin_location";
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sqlQueryStr, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            var eachElement = null;
            var maxRowIndex = ds.getMaxRowIndex();
            for (var i = 1; i <= maxRowIndex; i++) {
                ds.rowIndex = i;
                eachElement = {
                    CountCode: data.InvtCountCode,
                    ItemCode: ds['item_code'],
                    ItemDesc: ds['item_desc1'],
                    Location: ds['whseloc_bin_location'],
                    Project: ds['custproj_desc'] != null ? ds['custproj_desc'] : "",
                    BookQty: ds['book_quantity'],
                    CountQty: ds['count_quantity'] != null ? ds['count_quantity'] : 0,
                    Size: "",
                    Color: ds['item_color'] != null ? ds['item_color'] : "",
                    StockingUnit: ds['uom_code'] != null ? ds['uom_code'] : "",
            		RollNumber: ds['initemroll_roll_number'] != null ? ds['initemroll_roll_number'] : ""
                };

                //determine size
                if ('P' == ds['itemclass_type'] || 'R' == ds['itemclass_type']) {
                    if (ds['paper_first_dim'] != null || ds['paper_second_dim'] != null) {
                        eachElement.Size = "" + ds['paper_first_dim'] + " x " + ds['paper_second_dim'];
                    }
                }
                else {
                    //this will cover this case as well: in_item_to_in_item_class.itemclass_type == 'RC'
                    if (ds['item_dimension_length'] != null || ds['item_dimension_width'] != null || ds['item_dimension_height'] != null) {
                        //not all 3 measures are always used
                        eachElement.Size = "" + ds['item_dimension_length'] + " x " + ds['item_dimension_width'];
                        //add third measure
                        if ('CA'==ds['itemclass_type'] 
                            || 'PA'==ds['itemclass_type'] 
                            || 'SK'==ds['itemclass_type'] 
                            || 'O'==ds['itemclass_type'])
                        {
                            eachElement.Size += " x " + ds['item_dimension_heigth'];
                        }
                    }
                }

                InventoryCountDetails.push(eachElement);
            }
        }

        var InvtCount_GetCountItemsResult = {
            InventoryCountDetails: InventoryCountDetails
        };
        var InvtCount_GetCountItemsResponse = {
            InvtCount_GetCountItemsResult: InvtCount_GetCountItemsResult
        };
        var response = {
            InvtCount_GetCountItemsResponse: InvtCount_GetCountItemsResponse
        };
        return response;
    },

    InvtCount_ItemAlreadyOnActiveCount: function(data) {
		if (application.isInDeveloper() && bGetTestPackage) {
		    data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InvtCount_ItemAlreadyOnActiveCount');
		}

        //made result's default true in this method. This could help to avoid incorrect evaluation in case of failure of DB (is down) :)
        var result = true;

    	var uItemID = getItemID(data.ItemCode);
        var args = [scopes.globals.org_id, data.ItemCode, data.Location];
        var sSQL = "SELECT in_item_warehouse_count_items.in_item_warehouse_cntitems_id \
			        FROM in_item_warehouse_count_items \
			        INNER JOIN in_item_warehouse_count ON (in_item_warehouse_count.in_item_warehouse_count_id=in_item_warehouse_count_items.in_item_warehouse_count_id \
			                        AND in_item_warehouse_count.in_itemwhsecount_countstatus='S') \
			        INNER JOIN in_item ON (in_item.item_id=in_item_warehouse_count_items.item_id) \
			        INNER JOIN in_item_warehouse_location ON (in_item_warehouse_location.itemwhseloc_id=in_item_warehouse_count_items.itemwhseloc_id) \
			        INNER JOIN in_warehouse_location ON (in_warehouse_location.whseloc_id=in_item_warehouse_location.whseloc_id) "

		if (scopes.avInv.doesItemUseProjectInventory(uItemID)) {
			sSQL += "INNER JOIN in_item_project ip ON ip.ip_id = in_item_warehouse_count_items.ip_id \
					 WHERE in_item_warehouse_count_items.org_id=? AND in_item.item_code=? AND in_warehouse_location.whseloc_bin_location=? ";
			
			// this project inventory
			if (data.Project) {
				sSQL += "AND ip.custproj_desc = ?";
	            args.push(data.Project);
			}
			// non project inventory
			else {
				sSQL += "AND ip.custproj_dec IS NULL";		            
			}
		}
		else {
			sSQL += "WHERE in_item_warehouse_count_items.org_id=? AND in_item.item_code=? AND in_warehouse_location.whseloc_bin_location=?";
		}

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
		
        if (ds.getMaxRowIndex() == 0) {
            result = false;
        }

        var InvtCount_ItemAlreadyOnActiveCountResponse = {
            InvtCount_ItemAlreadyOnActiveCountResult: result
        };
        var response = {
            InvtCount_ItemAlreadyOnActiveCountResponse: InvtCount_ItemAlreadyOnActiveCountResponse
        };
        return response;
    },

    InvtCount_PrintReport: function(data) {
        var result = false;

        /*
         * implementation here
         */

        var InvtCount_PrintReportResponse = {
            InvtCount_PrintReportResult: result
        };
        var response = {
            InvtCount_PrintReportResponse: InvtCount_PrintReportResponse
        };
        return response;
    },

    InvtCount_SaveCountedQty: function(data) {
		if (application.isInDeveloper() && bGetTestPackage) {
		    data = scopes.avApiTest.getTestWirelessInventoryDataPackage('InvtCount_SaveCountedQty');
		}

    	var result = false;
        
        /** @type {{CountQty:String, ItemCode:String, CountCode:String, Location:String, RollNumber:String }} */
        var element = data.CountedList;
        
        if (element.CountQty == null || element.CountQty.length == 0) {
            return response;
        }

        /** @type{JSFoundSet<db:/avanti/in_item_warehouse_count_items>} */
        var fs_in_item_warehouse_count_items = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count_items');
        if (fs_in_item_warehouse_count_items.find()) {
            fs_in_item_warehouse_count_items.org_id = scopes.globals.org_id;
            fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item.item_code = element.ItemCode;
            fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_warehouse_count.in_itemwhscount_countcode = element.CountCode;           
            fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_warehouse_location.in_item_warehouse_location_to_in_warehouse_location.whseloc_bin_location = element.Location;
            fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_roll.initemroll_roll_number = element.RollNumber;

    		if (scopes.avInv.doesItemUseProjectInventory(getItemID(element.ItemCode))) {
				if (element.Project) {
	    			fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_project.custproj_desc = element.Project;
				} else {
	    			fs_in_item_warehouse_count_items.in_item_warehouse_count_items_to_in_item_project.custproj_desc = "^";
				}
    		}
            
            if (fs_in_item_warehouse_count_items.search() > 0) {
            
                fs_in_item_warehouse_count_items.getRecord(1).count_quantity = element.CountQty;
                fs_in_item_warehouse_count_items.empl_id = scopes.globals.avBase_employeeUUID;
                if (databaseManager.saveData(fs_in_item_warehouse_count_items)) {
                	result = true;
                }
            }
        }

        var InvtCount_SaveCountedQtyResponse = {
        	InvtCount_SaveCountedQtyResult: result
        };
        var response = {
        	InvtCount_SaveCountedQtyResponse: InvtCount_SaveCountedQtyResponse
        };
        return response;
    },

    //TODO no data broadcasting when information is updated by this method
    InvtCount_StartOnFlyCount: function(data) {
        //2017-04-26 IK: data.EmployeeCode received actually contains username in Slingshot's terminology
        //As a matter of the fact it's not used in this method.
        //I'm getting empl_code from table based in globals.svy_sec_lgn_user_id is used instead

        var InvtCount_StartOnFlyCountResponse = {
            InvtCount_StartOnFlyCountResult: "" //new created CountCode will be returned
        };
        var response = {
            InvtCount_StartOnFlyCountResponse: InvtCount_StartOnFlyCountResponse
        };

        var empl_code = getEmplCode(globals.svy_sec_lgn_user_id);

        if (empl_code != null || empl_code.length > 0) {

            var currentDate = application.getServerTimeStamp();
            var currentDateFormatted = plugins.DateUtils.timeFormat(currentDate, "yyyyMMdd_HHmm_ss");
            var newCountCode = empl_code + "_" + currentDateFormatted;
            var newCountDesc = empl_code + "_" + currentDateFormatted;

            //TODO trying to solve: no data broadcasting when information is updated by this method
            //initial approach
            /** @type{JSFoundSet<db:/avanti/in_item_warehouse_count>} */
            var fsInItemWarehouseCount = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count');
            //using FS via form - same result
            //var fsInItemWarehouseCount = forms['in_item_warehouse_count_tbl'].foundset;

            /** @type {JSRecord<db:/avanti/in_item_warehouse_count>} */
            var rInItemWarehouseCount = fsInItemWarehouseCount.getRecord(fsInItemWarehouseCount.newRecord());
            rInItemWarehouseCount.org_id = scopes.globals.org_id;
            rInItemWarehouseCount.empl_id = scopes.globals.avBase_employeeUUID;
            rInItemWarehouseCount.in_itemwhscount_countcode = newCountCode;
            rInItemWarehouseCount.in_itemwhsecount_desc = newCountDesc;
            rInItemWarehouseCount.in_itemwhscount_counttype = 'P';
            rInItemWarehouseCount.in_itemwhsecount_countstatus = 'S';
            rInItemWarehouseCount.in_itemwhsecount_date = currentDate;
            rInItemWarehouseCount.in_itemwhsecount_lastused = currentDate;
            rInItemWarehouseCount.in_itemwhsecount_varianceseen = 0;
            rInItemWarehouseCount.in_itemwhscount_batchsize = null;
            rInItemWarehouseCount.in_itemwhscount_cycleoption = null;
            rInItemWarehouseCount.in_itemwhscount_numitems = null;

            if (databaseManager.saveData(rInItemWarehouseCount)) {
                InvtCount_StartOnFlyCountResponse.InvtCount_StartOnFlyCountResult = newCountCode;
            }
        }

        return response;
    },

    InvtCount_UpdateInventory: function(data) {
        var result = true;
    	
        if (data.InvtBatchCode == "") { 
    		data.InvtBatchCode = null;
		}
    	
        /** @type{JSFoundSet<db:/avanti/in_item_warehouse_count>} */
        var fs_in_item_warehouse_count = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count');
        if (fs_in_item_warehouse_count.find()) {
            fs_in_item_warehouse_count.org_id = scopes.globals.org_id;
            fs_in_item_warehouse_count.in_itemwhscount_countcode = data.InvtCountCode;
            if (fs_in_item_warehouse_count.search() > 0) {
                if (utils.hasRecords(fs_in_item_warehouse_count.in_item_warehouse_count_to_in_item_warehouse_count_items)) {
	            	/** @type{JSFoundSet<db:/avanti/in_item_warehouse_count_items>} */
	            	var fsCountItems = fs_in_item_warehouse_count.in_item_warehouse_count_to_in_item_warehouse_count_items;                      
                    var closedCountItems = 0;
                    for (var i = 1; i <= fsCountItems.getSize(); i++) {
                        var rRecord = fsCountItems.getRecord(i);
                        if ((rRecord.batch_number == data.InvtBatchCode) || (rRecord.batch_number)) {
                            if (rRecord.batch_state == 'C') {
                                result = false;
                                break;
                            }
                            rRecord.batch_state = 'C';
                            closedCountItems++;
                        }
                        else {
                            if (rRecord.batch_state == 'C') {
                            	closedCountItems++;
                            }
                        }
                    }
                    if (result) {
                        result = databaseManager.saveData(fsCountItems);
                        var nTotalItems = i-1;
                        if (closedCountItems == nTotalItems) {
                            fs_in_item_warehouse_count.getRecord(1).in_itemwhsecount_countstatus = 'E';

                            if (!databaseManager.saveData(fs_in_item_warehouse_count)) {
                                result = false;
                            }
                        }
                    }
                }
            }
        }

        var response = {
            InvtCount_UpdateInventoryResponse: result
        };
        return response;
    },

    //NB !!! data.JobNumber received in request is actually Sales Order Number
    Job_IsValid: function(data) {
        var result = false;
        if (data.JobNumber != null && data.JobNumber.length > 0) {
            var args = [data.JobNumber, scopes.globals.org_id];
            var sSql = "SELECT sa_order.ordh_document_num, sa_order_revision_header.ordrevh_order_status \
            	FROM sa_order \
				INNER JOIN sa_order_revision_header ON sa_order_revision_header.ordh_id = sa_order.ordh_id \
				WHERE sa_order.ordh_document_num = ? AND sa_order.org_id = ? AND \
				EXISTS(SELECT * FROM sa_order_revision_detail \
				INNER JOIN sa_order_revision_header ON sa_order_revision_detail.ordrevh_id = sa_order_revision_header.ordrevh_id \
				INNER JOIN prod_job ON sa_order_revision_detail.job_id = prod_job.job_id ) AND \
				sa_order.ordh_document_type = N'ORD' AND sa_order_revision_header.ordrevh_revision = 0 AND sa_order_revision_header.ordrevh_order_status IN \
				('Released', 'Staged', 'Partially Shipped', 'Open Change' , 'Pending Change', 'Approved Change', 'Released Change', 'Cancelled Change', 'Deleted Change', 'Completed' )";
			
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sSql += " AND sa_order.plant_id IN " + globals.getPlantsByEmployee();
            }
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW); 
            
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }
        var Job_IsValidResponse = {
            Job_IsValidResult: result
        };
        var response = {
            Job_IsValidResponse: Job_IsValidResponse
        };
        return response;
    },

    //NB !!! JobNumber returned in response is actually Sales Order Number
    Job_OpenList: function(data) {
        var Job = new Array();
        var sSql = "SELECT DISTINCT sa_order.ordh_document_num, sa_order_revision_header.ordrevh_order_status, sa_order.ordh_description, \
            sa_customer.cust_name, sa_customer.cust_code\
            FROM sa_order \
            LEFT JOIN sa_customer ON (sa_order.cust_id=sa_customer.cust_id) \
            INNER JOIN sa_order_revision_header ON (sa_order_revision_header.ordh_id = sa_order.ordh_id) \
            WHERE sa_order.org_id = ? AND \
            EXISTS( SELECT * FROM sa_order_revision_detail \
            INNER JOIN sa_order_revision_header ON (sa_order_revision_detail.ordrevh_id = sa_order_revision_header.ordrevh_id) \
            INNER JOIN prod_job ON (sa_order_revision_detail.job_id = prod_job.job_id) ) AND \
            sa_order.ordh_document_type = N'ORD' AND sa_order_revision_header.ordrevh_revision = 0 AND sa_order_revision_header.ordrevh_order_status IN \
            ('Released', 'Staged', 'Partially Shipped', 'Open Change' , 'Pending Change', 'Approved Change', 'Released Change', 'Cancelled Change', 'Deleted Change', 'Completed' )";
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND sa_order.plant_id IN " + globals.getPlantsByEmployee();
        }
        
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;

            var args = [scopes.globals.org_id, "%" + data.Keyword + "%", start, end];
            sSql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY RowResult.ordh_document_num) as RownNum, * FROM (" + sSql +			
    		" AND sa_order.ordh_document_num LIKE ?) AS RowResult) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ? ORDER By RownNum";
        }
        else {
            args = [scopes.globals.org_id];           
        }
        
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                JobNumber: ds['ordh_document_num'],
                JobDescription: ds['ordh_description'],
                JobStatus: ds['ordrevh_order_status'],
                CustomerCode: ds['cust_code'] != null ? ds['cust_code'] : "",
                CustomerName: ds['cust_name'] != null ? ds['cust_name'] : ""
            };
            Job.push(eachElement);
        }

        var Job_OpenListResult = {
            Job: Job
        };
        var Job_OpenListResponse = {
            Job_OpenListResult: Job_OpenListResult
        };
        var response = {
            Job_OpenListResponse: Job_OpenListResponse
        };
        return response;
    },
	  
    PickList_PickSkipReasons: function(data){
        var sSql = "SELECT pickskip_id, pickskip_desc FROM sys_pick_skip_reason \
                    WHERE (org_id = ?)";
        var args = [scopes.globals.org_id];

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        var pickSkipReasons = new Array();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                PickSkipID: ds['pickskip_id'],
                PickSkipDescription: ds['pickskip_desc'] ? ds['pickskip_desc']: ""
            };
            pickSkipReasons.push(eachElement);
        }

        var PickList_PickSkipReasonsResult = {
            PickSkipReasons: pickSkipReasons
        };
        var PickList_PickSkipReasonsResponse = {
            PickList_PickSkipReasonsResult: PickList_PickSkipReasonsResult
        };
        var response = {
            PickList_PickSkipReasonsResponse: PickList_PickSkipReasonsResponse
        };
        return response;
    },
    
    PickList_AvailablePicks: function(data) {
        var PickList_AvailablePicksResult = {
            Pick: getPickLists()
        };
        var PickList_AvailablePicksResponse = {
            PickList_AvailablePicksResult: PickList_AvailablePicksResult
        };
        var response = {
            PickList_AvailablePicksResponse: PickList_AvailablePicksResponse
        };

        return response;
    },
    
    PickList_IsPickValid: function(data){
    	var result = false;
    	
    	if (data.PickListNumber != null && data.PickListNumber.length > 0) {
    		var sSql = "SELECT pick.pick_doc_number FROM sa_pick AS pick \
    					LEFT OUTER JOIN sys_employee AS empl ON pick.assignee_id = empl.empl_id \
						WHERE pick.org_id = ? AND pick.pick_doc_number = ? AND \
						((pick.pick_status = N'O' AND (pick.assignee_id = ? OR pick.assignee_id IS NULL)) OR \
						((pick.pick_status = N'O' OR pick.pick_status = N'IP') AND pick.assignee_id = ?))";
    	
    		// Apply plant filtering
    		if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1'){
    			sSql += " AND pick.plant_id IN " + globals.getPlantsByEmployee();
    		}
    		
    		var args = [scopes.globals.org_id, data.PickListNumber, globals.avBase_employeeUUID, globals.avBase_employeeUUID];
    		var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
    		
    		if (ds.getMaxRowIndex() == 1) {
    			result = true;
    		}
    	}

    	var PickList_IsPickValidResponse = {
    		PickList_IsPickValidResult: result
    	};
    	var response = {
    		PickList_IsPickValidResponse: PickList_IsPickValidResponse
    	};
    	
    	return response;
    },
	
    PickList_IsPickValidForDevice: function(data){	
    	var result = scopes.avPicking.isPickValidForDevice(data.PickListNumber, data.mdevice_id);
    	
     	var PickList_IsPickValidForDeviceResponse = {
    		PickList_IsPickValidForDeviceResult: result
    	};
    	var response = {
    		PickList_IsPickValidForDeviceResponse: PickList_IsPickValidForDeviceResponse
    	};
    	
    	return response;
    },
	

	
    PickList_ReassignPick: function(data){    	
    	var result = false;
    	
    	var mdevice_id = data.mdevice_id;	
		if(mdevice_id) {		
	    	var sSQL = "SELECT pick_id FROM sa_pick WHERE pick_doc_number = ? AND org_id = ?";
	    	var aArgs = [data.PickListNumber, scopes.globals.org_id];
			/** @type{JSFoundset<db:/avanti/sa_pick>} */
			var fsMobileDevice = scopes.avDB.getFSFromSQL(sSQL, "sa_pick", aArgs);
			if(fsMobileDevice.getSize() == 1) {
				fsMobileDevice.mdevice_id = mdevice_id;
				databaseManager.saveData();
			}
		}

		var PickList_ReassignPickResponse = {
    		PickList_ReassignPickResult: result
    	};
    	var response = {
    		PickList_ReassignPickResponse: PickList_ReassignPickResponse
    	};
    	
    	return response;
    },
    
    PickList_AvailablePickItems: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('PickList_AvailablePickItems');
        }
        
        var pickListItems = getPickListItems(false, data.PickNumber);
        
        var PickList_AvailablePickItemsResult = {
            PickItems: pickListItems
        };
        var PickList_AvailablePickItemsResponse = {
            PickList_AvailablePickItemsResult: PickList_AvailablePickItemsResult
        };
        var response = {
            PickList_AvailablePickItemsResponse: PickList_AvailablePickItemsResponse
        };
        return response;
    },
    
    PickList_SkippedItems: function(data){
        var pickListItems = getPickListItems(true,data.PickNumber);
        
        var PickList_SkippedItemsResult = {
            SkippedItems: pickListItems
        };
        var PickList_SkippedItemsResponse = {
            PickList_SkippedItemsResult: PickList_SkippedItemsResult
        };
        var response = {
            PickList_SkippedItemsResponse: PickList_SkippedItemsResponse
        };
        return response;
    },
	
    PickList_CheckPickStatus: function(data) {
    	return 'debug';
    },
	
    PickList_CheckPickDeviceID: function(data) {
    	return 'debug';
    },

    PickList_UpdatePickListItem: function(data){
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('PickList_UpdatePickListItem');
        }

		logDataPackage("PickList_UpdatePickListItem", data);
        
        var PickList_UpdatePickListItemResult = {
            MovableUnitValid: false, 
            Result: false,
            ResultMessage: ""
        };

        var PickList_UpdatePickListItemResponse = {
            PickList_UpdatePickListItemResult: PickList_UpdatePickListItemResult
        };
        
        var response = {
            PickList_UpdatePickListItemResponse: PickList_UpdatePickListItemResponse
        };
        
        if(!scopes.avPicking.isPickValidForDevice(data.PickListItem.sPickDocNum, data.mdevice_id)) {
        	PickList_UpdatePickListItemResult.ResultMessage = "Invalid_Device_ID"
        	return response;
        }
        
        

        var sSql = "SELECT pickdb_id FROM sa_pick_detail_bin WHERE (org_id = ?) AND (pickdb_id = ?)";
        var args = [scopes.globals.org_id, data.PickListItem.PickDetailBin_ID];
        /** @type{JSFoundSet<db:/avanti/sa_pick_detail_bin>} */
        var fsPickDetailBin = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_pick_detail_bin');

        fsPickDetailBin.loadRecords(sSql, args);

        if (!utils.hasRecords(fsPickDetailBin)) {
        	PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "No Pick Detail bin record found!";
            application.output("No Pick Detail bin record found!", LOGGINGLEVEL.ERROR);
            return response;
        }
            
        var rPickDetailBin = fsPickDetailBin.getRecord(1);
        
        // Validate pick detail record
        if (!utils.hasRecords(rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail)) {
            PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "No Pick Detail record found!";
            application.output("No Pick Detail record found!", LOGGINGLEVEL.ERROR);
            return response;
        }
            
        // Check if the item is picked or skipped
        var skipped = false;
        if (data.PickListItem.PickSkipID && data.PickListItem.PickSkipID != "0") {
        	skipped = true;
        }
            
        if (skipped) {
        	// Validate Skip reason
            if (!isPickSkipReasonValid(data.PickListItem.PickSkipID)) {
            	PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "Invalid PickSkip Reason!";
                application.output("Invalid PickSkip Reason!", LOGGINGLEVEL.ERROR);
                return response;
            }  
            
            // Only update skip id for the record
        	rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickskip_id = data.PickListItem.PickSkipID;
        }
        // if we dont have a pick qty then there is nothing to do
        else if (data.PickListItem.PickQty > 0) {
        	// Validate location
        	var sItemId = rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.item_id.toString();
            var sUpdatedLocation = getNewItemLocationId(data.PickListItem.Location, sItemId);
            if (!sUpdatedLocation) {
                PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "Invalid Location!";
                application.output("Invalid Location!", LOGGINGLEVEL.ERROR);
                return response;
            }
            
            var rPickDetail = rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.getRecord(1);
            var uSellUOM = rPickDetail.sa_pick_detail_to_sa_order_revision_detail.uom_id;
            var uStockUOM = rPickDetail.sa_pick_detail_to_in_item.item_standard_uom_id; 
            var nPickQtySellUOM = data.PickListItem.PickQty;
            
            // data.PickListItem.PickQty comes from pick detail bin - its in stocking units, needs to be converted to selling units
			if (uSellUOM != uStockUOM) {
				// this is set when a pick is created, regardless of the stock uom pref 
				var nConvFactor = rPickDetail.pickd_stock_uom_conv_factor;
				
				// if this is an old pick that doesnt have this set then can use avCalcs_UnitOfMeasurementConversion to infer it. 
				if (!nConvFactor) {
					nConvFactor = globals.avCalcs_UnitOfMeasurementConversion(rPickDetail.item_id, uSellUOM, uStockUOM, 1, "S").nOutputValue;
				}
				
				if (nConvFactor) {
					nPickQtySellUOM = data.PickListItem.PickQty / nConvFactor;
				}
			}
            	
            // Validate Tote          
            if (data.PickListItem.Tote && data.PickListItem.Tote.length > 0) {
                
                if (!utils.hasRecords(rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.sa_pick_detail_to_sa_order_revision_detail) 
                    || !utils.hasRecords(rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.sa_pick_detail_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header)) {
                    PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "Sales Order Not Found!";
                    application.output("Sales Order Not Found!", LOGGINGLEVEL.ERROR);
                    return response;
                }
                
                var sOrdhId = rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.sa_pick_detail_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.ordh_id.toString();
                var oValidation = validateTote(data.PickListItem.sPickDocNum, data.PickListItem.Tote, sOrdhId);
                
                if (!oValidation.valid) {
                	PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = oValidation.message;
                    return response;
                }
                
                // Create movable unit
                if (!createMovableUnits(data.PickListItem.PickDetail_ID, nPickQtySellUOM, data.PickListItem.Tote)) {
                    PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.ResultMessage = "Failed to create movable unit record!";
                    application.output("Failed to create movale unit record!", LOGGINGLEVEL.ERROR);
                    return response;
                }
                else {
                	PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.MovableUnitValid = true;
                }
            }
            
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_picked += nPickQtySellUOM;
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_picked_stock_uom += data.PickListItem.PickQty;
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord = rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_ordered - rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_picked;
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord_stock_uom = rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_ordered_stock_uom - rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_picked_stock_uom;
            
            if (rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord < 0) {
            	rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord = 0;
            }
            
            if (rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord_stock_uom < 0) {
            	rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickd_qty_backord_stock_uom = 0;
            }
            
            rPickDetailBin.pickdb_qty_picked += data.PickListItem.PickQty;
            
            rPickDetailBin.itemwhseloc_id = sUpdatedLocation;
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pick_date_picked = application.getServerTimeStamp();
            rPickDetailBin.sa_pick_detail_bin_to_sa_pick_detail.pickskip_id = null;
        }
            
    	PickList_UpdatePickListItemResponse.PickList_UpdatePickListItemResult.Result = databaseManager.saveData();
    	
    	return response;
    },   
    
    PickList_UpdateStatus: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('PickList_UpdateStatus');
        }
        
        var sPickStatus = data.Status;
        var result = updatePickStatus(data.PickNumber,sPickStatus);
        
        var PickList_UpdateStatusResponse = {
            PickList_UpdateStatusResult: result
        };
        var response = {
            PickList_UpdateStatusResponse: PickList_UpdateStatusResponse
        };
        return response;
    },
    
    PickList_SpawnPickList: function(data){
        var result = false;
        var sSql = "SELECT pick_id FROM sa_pick WHERE (org_id = ?) AND (pick_doc_number = ?)";
        var args = [scopes.globals.org_id, data.PickNumber];
        /** @type{JSFoundSet<db:/avanti/sa_pick>} */
        var fsPick = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_pick');
        
		logDataPackage("PickList_SpawnPickList", data);
        
        fsPick.loadRecords(sSql,args);
        
        if (fsPick.getSize() > 0) {
            /** @type{JSFoundSet<db:/avanti/sa_pick_detail>} */
            var fsPickDetail = fsPick.sa_pick_to_sa_pick_detail;
            var nMaxSize = databaseManager.getFoundSetCount(fsPickDetail);
            
            fsPick.duplicateRecord(1,false, true);
            /** @type{JSRecord<db:/avanti/sa_pick>} */
            var rPick = fsPick.getSelectedRecord();
            
            rPick.pick_doc_number = globals.GetNextDocumentNumber("PIC", "0");
            rPick.pick_date = application.getTimeStamp();
            rPick.pick_status = "IP";
            rPick.assignee_id = getSupervisor(globals.avBase_employeeUUID);            

            databaseManager.saveData();
            
            //Duplicate pick details and update with new pick id
			for (var i = 1; i <= nMaxSize; i++) {
				/** @type{JSRecord<db:/avanti/sa_pick_detail>} */
				var rPickDetail = fsPickDetail.getRecord(i);

				fsPickDetail.duplicateRecord(i, false, true);
				/** @type{JSRecord<db:/avanti/sa_pick_detail>} */
				var rPickDetailDup = fsPickDetail.getSelectedRecord();

				rPickDetailDup.pick_id = rPick.pick_id;
				rPickDetailDup.pickskip_id = null;
				rPickDetailDup.pickd_qty_ordered = rPickDetailDup.pickd_qty_ordered - rPickDetailDup.pickd_qty_picked;
				rPickDetailDup.pickd_qty_ordered_stock_uom = rPickDetailDup.pickd_qty_ordered * rPickDetailDup.pickd_stock_uom_conv_factor;
				rPickDetailDup.pickd_qty_shipped = rPickDetailDup.pickd_qty_ordered;
				rPickDetailDup.pickd_qty_shipped_stock_uom = rPickDetailDup.pickd_qty_shipped * rPickDetailDup.pickd_stock_uom_conv_factor;
				rPickDetailDup.pickd_qty_picked = null;
				rPickDetailDup.pickd_qty_picked_stock_uom = null; 
				rPickDetailDup.pickd_qty_backord = 0;
				rPickDetailDup.pickd_qty_backord_stock_uom = 0;

				/** @type{JSFoundSet<db:/avanti/sa_pick_detail_bin>} */
				var fsPickDetailBin = rPickDetail.sa_pick_detail_to_sa_pick_detail_bin;
				var nFoundSetCount = databaseManager.getFoundSetCount(fsPickDetailBin);

				for (var j = 1; j <= nFoundSetCount; j++) {
					fsPickDetailBin.duplicateRecord(j, false, true);
					/** @type{JSRecord<db:/avanti/sa_pick_detail_bin>} */
					var rPickDetailBin = fsPickDetailBin.getSelectedRecord();

					rPickDetailBin.pickd_id = rPickDetailDup.pickd_id;
					rPickDetailBin.pickdb_qty_picked = 0;
					rPickDetailBin.pickdb_qty = rPickDetailDup.pickd_qty_shipped_stock_uom;
				}

				/** @type{JSFoundSet<db:/avanti/sa_pick_detail_fifo>} */
				var fsPickDetailFifo = rPickDetail.sa_pick_detail_to_sa_pick_detail_fifo;

				nFoundSetCount = databaseManager.getFoundSetCount(fsPickDetailFifo);
				for (j = 1; j <= nFoundSetCount; j++) {
					fsPickDetailFifo.duplicateRecord(j, false, true);
					/** @type{JSRecord<db:/avanti/sa_pick_detail_fifo>} */
					var rPickDetailFifo = fsPickDetailFifo.getSelectedRecord();
					rPickDetailFifo.pickd_id = rPickDetailDup.pickd_id;
				}

				databaseManager.saveData();
			}
            
            scopes.avPicking.additionalPickListFields(rPick);
            result = databaseManager.saveData();
        }
        
        
        var PickList_SpawnPickListResponse = {
            PickList_SpawnPickListResult: result
        };
        var response = {
            PickList_SpawnPickListResponse: PickList_SpawnPickListResponse
        };
        
        return response;
    },
    
    PurchaseOrder_OpenList: function(data) {
        var PurchaseOrder = new Array();
        var sSql = "SELECT distinct purchase.po_document_number, supplier.supplier_name \
            FROM po_purchase AS purchase \
            INNER JOIN po_purchase_detail AS purchase_detail ON purchase.po_id=purchase_detail.po_id \
            INNER JOIN in_item AS item ON purchase_detail.item_id=item.item_id \
            INNER JOIN ap_supplier AS supplier ON purchase.supplier_id=supplier.supplier_id \
            WHERE purchase.po_status in('O','P') AND purchase.org_id = ?";
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
         	sSql += " AND purchase.Plant_id IN " + globals.getPlantsByEmployee();
        }
        
        // Apply page number
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;
            var args = [scopes.globals.org_id, "%" + data.Keyword + "%", start, end];
            
            sSql = "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.po_document_number) as RownNum , * FROM ( " + sSql + 
        		" AND purchase.po_document_number LIKE ? ) AS RowResult ) AS RowConstrainedResult \
                WHERE RownNum > ? AND RownNum <= ? \
                ORDER BY RownNum";  
        }
        else {
            args = [scopes.globals.org_id];
        }
        
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                PONumber: ds['po_document_number'],
                POVendor: ds['supplier_name'],
                POStatus: "O",
                JobNumber: "" 
            };
            PurchaseOrder.push(eachElement);
        }

        var PurchaseOrder_OpenListResult = {
            PurchaseOrder: PurchaseOrder
        };
        var PurchaseOrder_OpenListResponse = {
            PurchaseOrder_OpenListResult: PurchaseOrder_OpenListResult
        };
        var response = {
            PurchaseOrder_OpenListResponse: PurchaseOrder_OpenListResponse
        };
        return response;
    },
    
    PendingReceipt_OpenList: function(data) {
        var PendingReceipt = new Array();
        var sSql = "SELECT transh.intraneh_desc, transh.intraneh_reference, transh.intraneh_expected_date \
            FROM in_trans_entry_header AS transh \
            INNER JOIN in_transaction_type AS transtype ON transh.intranstype_id = transtype.intranstype_id \
            INNER JOIN app_in_transaction_type AS apptranstype ON transtype.intranstype_appkey_uuid = apptranstype.intranstype_id \
            WHERE (apptranstype.intranstype_trans_code = 'PR') \
            	AND (transh.intraneh_status_field = 'U' OR transh.intraneh_status_field = 'P') \
            	AND (transh.intraneh_complete = 0) \
            	AND (transh.org_id = ?) ";
        
        // Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND transh.intraneh_whse_id IN " + getWarehousesByEmployee();
        }
        
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;

            var args = [scopes.globals.org_id, "%" + data.Keyword + "%", start, end];
            sSql = "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.intraneh_reference) as RownNum, * FROM (" + sSql +  
                 " AND transh.intraneh_reference LIKE ?) AS RowResult ) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ? ORDER By RownNum";
        }
        else {
            args = [scopes.globals.org_id];        
        }
        
        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ReferenceNumber: ds['intraneh_reference'],
                Description: ds['intraneh_desc'] != null ? ds['intraneh_desc'] : "",
                ExpectedDate: plugins.DateUtils.timeFormat(ds['intraneh_expected_date'], "MM/dd/yyyy")
            };
            PendingReceipt.push(eachElement);
        }

        var PendingReceipt_OpenListResult = {
            PendingReceipt: PendingReceipt
        };
        var PendingReceipt_OpenListResponse = {
            PendingReceipt_OpenListResult: PendingReceipt_OpenListResult
        };
        var response = {
            PendingReceipt_OpenListResponse: PendingReceipt_OpenListResponse
        };
        return response;

    },
	
    Receipt_WarehouseTransferReceiptList: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_WarehouseTransferReceiptList');
        }
    	
        var WarehouseTransferReceipt = new Array();
		var sSql = "SELECT transh.intraneh_desc, transh.intraneh_reference, transh.intraneh_expected_date \
                 FROM in_trans_entry_header AS transh \
                 INNER JOIN in_transaction_type AS transtype ON transh.intranstype_id = transtype.intranstype_id \
                 INNER JOIN app_in_transaction_type AS apptranstype ON transtype.intranstype_appkey_uuid = apptranstype.intranstype_id \
                 WHERE apptranstype.intranstype_trans_code = 'WTO' AND transh.intraneh_complete <> 1 AND transh.org_id = ? ";
        
		// Apply plant filtering
		if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
			sSql += " AND transh.intraneh_whse_id_transfer_to IN " + getWarehousesByEmployee();
		}
		
        if (data.PageNumber) {
            var start = ( data.PageNumber - 1 ) * 500;
            var end = start + 500;
            var args = [scopes.globals.org_id, "%" + data.Keyword + "%", start, end];           
            sSql = "SELECT * FROM ( SELECT ROW_NUMBER() OVER (ORDER BY RowResult.intraneh_reference) as RownNum, * FROM (" + sSql +
            	 " AND transh.intraneh_reference LIKE ? ) AS RowResult ) AS RowConstrainedResult WHERE RownNum > ? AND RownNum <= ? ORDER By RownNum";
        }
        else {
            args = [scopes.globals.org_id];
        }
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                ReferenceNumber: ds['intraneh_reference'],
                Description: ds['intraneh_desc'],
                ExpectedDate: plugins.DateUtils.timeFormat(ds['intraneh_expected_date'], "MM/dd/yyyy")
            };
            WarehouseTransferReceipt.push(eachElement);
        }

        var Receipt_WarehouseTransferReceiptListResult = {
        	WarehouseTransferReceipt: WarehouseTransferReceipt
        };
        var Receipt_WarehouseTransferReceiptListResponse = {
        	Receipt_WarehouseTransferReceiptListResult: Receipt_WarehouseTransferReceiptListResult
        };
        var response = {
        	Receipt_WarehouseTransferReceiptListResponse: Receipt_WarehouseTransferReceiptListResponse
        };
        
        return response;
    },
    
    //NB !!! data.JobNumber received in request is actually Sales Order Number
    //however, JobNumber(s) returned - are real JobNumbers which suppose to be used in ReceiptProd_JobProductionUpdate
    ReceiptProd_JobProductionItems: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('ReceiptProd_JobProductionItems');
        }
    	
    	var bProjectInventory = scopes.avInv.isProjectInventoryOn();
        var JobProductionItem = new Array();

        var ReceiptProd_JobProductionItemsResult = {
            JobProductionItem: JobProductionItem
        };
        var ReceiptProd_JobProductionItemsResponse = {
            ReceiptProd_JobProductionItemsResult: ReceiptProd_JobProductionItemsResult
        };
        var response = {
            ReceiptProd_JobProductionItemsResponse: ReceiptProd_JobProductionItemsResponse
        };

        if (data.JobNumber == null || data.JobNumber.length == 0) {
            return response;
        }

        var args = [data.JobNumber, scopes.globals.org_id];
        //TODO some of fields left so far testing. Has to be removed in Phase 2
        var sSQL = "SELECT prod_job.job_id, prod_job.job_number, sa_order_revision_detail.ordrevd_line_num \
	            , sa_order_revision_detail.ordrevd_id, sa_order_revision_detail.ordrevd_prod_desc \
	            , sa_order.ordh_document_num, ISNULL (sa_order_revision_detail.ordrevd_qty_ordered, 0) AS quantity_ordered \
	            , cp.custproj_desc AS project \
	            , ISNULL ( (SELECT SUM(prodrec_qty) AS Expr1 \
	                FROM prod_receipt_detail prd \
	                INNER JOIN prod_receipt pr ON pr.prodrec_id = prd.prodrec_id \
	                WHERE (ordrevd_id = sa_order_revision_detail.ordrevd_id) \
	                AND pr.prodrec_number IS NOT NULL), 0) AS total_received_from_production \
	            , in_item.item_code AS finished_good_item_code, in_item.item_id AS finished_good_item_id \
            FROM sa_order_revision_detail \
            INNER JOIN sa_order_revision_header ON (sa_order_revision_detail.ordrevh_id =sa_order_revision_header.ordrevh_id) \
            INNER JOIN sa_order ON (sa_order_revision_header.ordh_id = sa_order.ordh_id) \
            INNER JOIN prod_job ON (sa_order_revision_detail.job_id = prod_job.job_id) \
            LEFT JOIN in_item ON (sa_order_revision_detail.ordrevd_finished_good_item_id = in_item.item_id) \
            LEFT OUTER JOIN sa_customer_project AS cp ON cp.custproj_id = sa_order.custproj_id \
            WHERE \
            	sa_order.ordh_document_type = N'ORD' \
            	AND sa_order_revision_header.ordrevh_revision = 0 \
            	AND sa_order.ordh_document_num = ? \
            	AND sa_order_revision_detail.org_id = ?";

        if (data.JobLineNumber > 0) {
        	sSQL += " AND sa_order_revision_detail.sequence_nr = ?";
        	args.push(data.JobLineNumber);
        }
        else {
        	sSQL += " ORDER BY prod_job.job_number, sa_order_revision_detail.ordrevd_line_num";
        }

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;

            eachElement = {
                JobNumber: ds['job_number'],
                JobLine: ds['ordrevd_line_num'],
                ItemNumber: "", //seems it's not really used by Wireless application at this point
                InventoryFinishedGoods: ds['finished_good_item_code'] != null ? ds['finished_good_item_code'] : "",
                ItemDescription: ds['ordrevd_prod_desc'], //not really a description of item
                Quantity: ds['quantity_ordered'],
                QuantityReceived: 0, //seems it's not really used by Wireless application at this point
                QuantityAlreadyReceived: ds['total_received_from_production'],
                QuantityPerBox: 0, 
                BoxNumber: 0, 
                Location: "",
                Project: "",
                Reference: "" //seems it's not really used by Wireless application at this point
            };
            
			if (bProjectInventory) {
				eachElement.Project = ds['project'];
			}

            // Get Last Cost and Default Location
            if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PopulateDefaultLocation) == 'Yes'){
            	WWMS_populate_default_location = 1;
            }
            
            //TODO specify Default location - not sure that this is needed according to specs - ask Amin
            //determine default location
            if (WWMS_populate_default_location == scopes.globals.$1 && ds['finished_good_item_id'] != null) {
                var itemWarehouseDefaultBinLocation = globals.getItemDefaultReceiptBinLocation(ds['finished_good_item_id']);
                if (itemWarehouseDefaultBinLocation != null) {
                    eachElement.Location = itemWarehouseDefaultBinLocation.whseloc_bin_location;
                }
            }

            JobProductionItem.push(eachElement);
        }

        return response;
    },

    ReceiptProd_JobProductionUpdate: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('ReceiptProd_JobProductionUpdate');
        }

        //NB IK, Seems: ItemNumber, QuantityAlreadyReceived, Quantity, ItemDescription are not used

        /** @type {{JobNumber:String, JobLine:Number, ItemNumber:String, ItemDescription:String, InventoryFinishedGoods:String, Location:String,
         Quantity:Number, QuantityReceived:Number, QuantityAlreadyReceived:Number, QuantityPerBox:Number, BoxNumber:Number, Reference:String }} */
        var jobProdItem = data.List;

        //prepare response with Error in advance
        var ReceiptProd_JobProductionUpdateResponse = {
            ReceiptProd_JobProductionUpdateResult: false
        };
        
        var response = {
            ReceiptProd_JobProductionUpdateResponse: ReceiptProd_JobProductionUpdateResponse
        };

        if (jobProdItem.JobNumber==null || jobProdItem.JobNumber.length==0
            || jobProdItem.InventoryFinishedGoods==null || jobProdItem.InventoryFinishedGoods.length==0) {

            return response;
        }

        var item_item_code = jobProdItem.InventoryFinishedGoods;
        var item_item_id = null;
        var uom_id = null;

        var prodrech_whse_id = globals.avBase_employeeDefaultWarehouse;

        var prodrec_qty = jobProdItem.QuantityReceived;
        var prodrec_unit_cost = 0;
        var prodrec_unit_price = 0;
        var prodrec_note = jobProdItem.Reference;

        var ordh_id = null;
        var ordrevd_id = null;
        var ordrevd_line_number = jobProdItem.JobLine;

        var intranstype_id = null;

        var args = [item_item_code, scopes.globals.org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT distinct in_item.item_id, in_item.item_standard_uom_id FROM in_item AS in_item  \
                WHERE in_item.item_code = ? AND in_item.org_id = ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        item_item_id = ds['item_id'];
        uom_id = ds['item_standard_uom_id'];

        var locationBin_code_TO = jobProdItem.Location;
        var whse_id_TO = null;
        var whseloc_id_TO = null;
        var itemwhse_id_TO = null;

        args = [item_item_id, item_item_id, locationBin_code_TO, scopes.globals.org_id];
        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, 
        	"SELECT in_warehouse_location.whse_id, in_warehouse_location.whseloc_id \
        		, in_item_warehouse_location.itemwhseloc_id, in_item_warehouse.itemwhse_id \
            FROM in_warehouse_location \
            LEFT JOIN in_item_warehouse_location ON (in_item_warehouse_location.whseloc_id=in_warehouse_location.whseloc_id AND in_item_warehouse_location.item_id=?) \
            INNER JOIN in_item_warehouse ON(in_item_warehouse.whse_id = in_warehouse_location.whse_id AND in_item_warehouse.item_id=?) \
            WHERE in_warehouse_location.whseloc_bin_location= ? AND in_warehouse_location.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        whse_id_TO = ds['whse_id'];
        whseloc_id_TO = ds['whseloc_id'];
        itemwhse_id_TO = ds['itemwhse_id'];

        if (ds['itemwhseloc_id'] == null) {
            // there is no record in in_item_warehouse_location for specified location.
            // most likely Default location was specified which has never been used before => create a new record
            createItemWarehouseLocation(item_item_id, itemwhse_id_TO, whseloc_id_TO);
        }

        //this to determine some costs/prices
        //a multiple records can be here. We take just a first one which is buggy
        args = [jobProdItem.JobNumber, scopes.globals.org_id, ordrevd_line_number];
        ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT prod_job.ordh_id, revision_detail.ordrevd_id, jobstat_id, revision_detail.ordrevd_line_num \
                    , revision_detail.ordrevd_unit_cost, revision_detail.ordrevd_unit_price, job_actual_labour_cost, job_actual_material_cost, job_actual_purchase_cost, job_actual_other_cost \
                    , revision_detail.ordrevd_qty_ordered, revision_detail.ordrevd_prod_receipt_qty \
                FROM prod_job \
                    INNER JOIN sa_order ON (sa_order.ordh_id = prod_job.ordh_id) \
                    INNER JOIN sa_order_revision_header AS revision_header ON (revision_header.ordh_id=sa_order.ordh_id) \
                    INNER JOIN sa_order_revision_detail AS revision_detail ON (revision_detail.ordrevh_id=revision_header.ordrevh_id) \
                WHERE prod_job.job_number=? AND prod_job.org_id = ? AND sa_order.ordh_document_type = N'ORD' AND revision_header.ordrevh_revision = 0 \
                AND revision_detail.ordrevd_line_num=?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        ordh_id = ds['ordh_id'];
        ordrevd_id = ds['ordrevd_id'];
        prodrec_unit_price = ds['ordrevd_unit_price'];
        //Sort of default value. Might be changed later - see below
        prodrec_unit_cost = ds['ordrevd_unit_cost'];
        
        //just simplified if/else version of code from function onAction_btnAdd(event) from prod_receipt_dtl.js
        //TODO !!! There is a chance that if somebody works on the same job/item number via UI and updates quantity information
        //the update performed here could result in incorrect calculation here
        var sCostingType = globals.avBase_getSystemPreference_String(188);
        if (sCostingType == "Budget") {
            //prod_receipt_to_prod_receipt_detail.prodrec_unit_cost = _to_ap_prod$avsales_selectedordrevlineuuid.ordrevd_unit_cost;
        }
        else if (sCostingType == "Actual") {

            //TODO come to this later: will it ever happened if we consider only <>Completed Jobs
            if ('Completed' == ds['jobstat_id']) { //&& prodrec_qty!=0
                var temp_job_actual_total_cost = ds['job_actual_labour_cost'] + ds['job_actual_material_cost'] + ds['job_actual_purchase_cost'] + ds['job_actual_other_cost']
                var temp_prodrec_qty = ds['ordrevd_qty_ordered'] - ds['ordrevd_prod_receipt_qty'];
                prodrec_unit_cost = temp_job_actual_total_cost / temp_prodrec_qty;
            }
            //else - default will be used
        }
        else {
            //Default to budget
            //prod_receipt_to_prod_receipt_detail.prodrec_unit_cost = _to_ap_prod$avsales_selectedordrevlineuuid.ordrevd_unit_cost;
        }

        var intranstype_trans_code = 'RFP';
        intranstype_id = scopes.avInv.getTransactionTypeUUID("IT", intranstype_trans_code);

        // FILL IN NEEDED INFORMATIN FOR SUBSEQUENT TRANSACTIONS

        //********************************** HEADER TABLE
        /** @type {JSFoundSet<db:/avanti/prod_receipt>} */
        var fsTransEntryHeader = forms['prod_receipt'].foundset;
        /** @type {JSRecord<db:/avanti/prod_receipt>} */
        var rTransEntryHeader = fsTransEntryHeader.getRecord(fsTransEntryHeader.newRecord());
        rTransEntryHeader.org_id = scopes.globals.org_id;
        rTransEntryHeader.prodrec_date = application.getServerTimeStamp();
        rTransEntryHeader.ordh_id = ordh_id;
        rTransEntryHeader.created_date = application.getServerTimeStamp(); //set value instead of NULL from Tim's list
        rTransEntryHeader.created_by_id = globals.avBase_employeeUUID; //set value instead of NULL from Tim's list
        rTransEntryHeader.modified_by_id = globals.avBase_employeeUUID; //set value instead of NULL from Tim's list
        rTransEntryHeader.intranstype_id = intranstype_id;
        rTransEntryHeader.prodrec_status = 'O';
        rTransEntryHeader.prodrech_whse_id = prodrech_whse_id;
        rTransEntryHeader.prodrec_reg_id = null;
        rTransEntryHeader.prodrec_number = globals.getNextItemTransactionNumber('IT');

        //********************************** DETAIL TABLE
        var rTransEntryDetail = rTransEntryHeader.prod_receipt_to_prod_receipt_detail.getRecord(rTransEntryHeader.prod_receipt_to_prod_receipt_detail.newRecord());
        rTransEntryDetail.org_id = scopes.globals.org_id;
        rTransEntryDetail.ordrevd_id = ordrevd_id; //added to Tim's list - should not be NULL
        rTransEntryDetail.item_id = item_item_id;
        rTransEntryDetail.prodrec_qty = prodrec_qty;
        rTransEntryDetail.prodrec_note = prodrec_note; //added to Tim's list - should not be NULL
        rTransEntryDetail.prodrec_unit_cost = prodrec_unit_cost;
        rTransEntryDetail.prodrec_total_cost = prodrec_unit_cost * prodrec_qty;
        rTransEntryDetail.prodrec_unit_price = prodrec_unit_price;
        rTransEntryDetail.prodrec_total_price = prodrec_unit_price * prodrec_qty;
        rTransEntryDetail.prodrec_whse_id = whse_id_TO;
        rTransEntryDetail.prodrec_qty_per_box = jobProdItem.QuantityPerBox;
        rTransEntryDetail.prodrec_num_boxes = jobProdItem.BoxNumber;
        rTransEntryDetail.uom_id = uom_id;
        rTransEntryDetail.whseloc_id = whseloc_id_TO;
        rTransEntryDetail.itemwhse_id = itemwhse_id_TO;
        rTransEntryDetail.prodrec_bin_location = null;
        rTransEntryDetail.prodrec_cancel_bal = 0;

        try {
            application.output("    ===== ReceiptProd_JobProductionUpdate: EXECUTING DB SAVING...", LOGGINGLEVEL.INFO);
            
            addFGItem(ordrevd_id, item_item_id);
            databaseManager.saveData();
            
            forms['prod_receipt_dtl'].BuildTempTransactionTables(rTransEntryHeader);
            
            // Print box label report
            if (data.PrintLabel == true) {
                scopes.avInv.autoGenerateInventoryLabel("PROD", rTransEntryHeader.prodrec_id, globals.avBase_employeeUUID);
            }
            
            ReceiptProd_JobProductionUpdateResponse.ReceiptProd_JobProductionUpdateResult = true;
        }
        catch (ex) {
            application.output("api_wireless_inventory, method: " + data.method + " , Exception: " + + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack, LOGGINGLEVEL.ERROR);
        }

        application.output(">>> ReceiptProd_JobProductionUpdate FINISHED", LOGGINGLEVEL.INFO);

        return response;
    },
    
    Receipt_IsPONumberValid: function(data) {
        var result = false;
        if (data.PONumber != null && data.PONumber.length > 0) {
            var args = [data.PONumber, scopes.globals.org_id];
            var sSql = "SELECT po_id FROM po_purchase WHERE po_document_number = ? AND org_id = ?"
            
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sSql += " AND po_purchase.Plant_id IN " + globals.getPlantsByEmployee();
            }
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }

        var Receipt_IsPONumberValidResponse = {
            Receipt_IsPONumberValidResult: result
        };
        var response = {
            Receipt_IsPONumberValidResponse: Receipt_IsPONumberValidResponse
        };
        return response;
    },

    //2017-03-17 - NOT YET FINISHED - needs more information
    Receipt_PODetails: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_PODetails');
        }

        var Receipt = new Array();

        if (data.PONumber != null && data.PONumber.length > 0) {
            /** @type{JSRecord<db:/avanti/po_purchase>} */
            var rPO = scopes.avDB.getRec("po_purchase", ["po_document_number"], [data.PONumber]);
            
            if (rPO) {
                var rPODetail = null;
                var eachElement = null;

                globals.avInv_selectedWarehouseUUID = rPO.po_shipto_whse_id;
                rPO.po_purchase_to_po_purchase_detail.sort("sequence_nr asc");

                for (var i = 1; i <= rPO.po_purchase_to_po_purchase_detail.getSize(); i++) {
                    rPODetail = rPO.po_purchase_to_po_purchase_detail.getRecord(i);

                    if (utils.hasRecords(rPODetail.po_purchase_detail_to_in_item)) {
                    	var rItem = rPODetail.po_purchase_detail_to_in_item.getRecord(1)
						var bOutsourcedService = (rItem.itemtype_code == scopes.avUtils.ITEM_TYPE.OutsourcedService); 
                        var sBinLocation = "";
                        var sOrderNum = "";
                        var nLineItemNum = 0;
                        var sFinishedGoodItemNumber = "";
						var projectName = "";
						var sItemCode = ""
						var sItemDesc = ""
						
						if (bOutsourcedService) {
				            if (utils.hasRecords(rPODetail.po_purchase_detail_to_po_purchase_detail_qty) && rPODetail.po_purchase_detail_to_po_purchase_detail_qty.podetq_desc) {
								sItemDesc = rPODetail.po_purchase_detail_to_po_purchase_detail_qty.podetq_desc + " (" + rItem.item_desc1 + ")";
				            }
				            else {
								sItemDesc = rItem.item_desc1;
				            }
						}
						else {
							sItemCode = rItem.item_code;
							sItemDesc = rItem.item_desc1;
							
		                    if (rItem
		                    		&& utils.hasRecords(rItem.in_item_to_in_item_warehouse$avinv_selectedwarehouseuuid)
		                    		&& utils.hasRecords(rItem.in_item_to_in_item_warehouse$avinv_selectedwarehouseuuid.in_item_warehouse_to_in_item_warehouse_location$default_receipt_bin)) {
		                        sBinLocation = rPODetail.po_purchase_detail_to_in_item.in_item_to_in_item_warehouse$avinv_selectedwarehouseuuid.in_item_warehouse_to_in_item_warehouse_location$default_receipt_bin.whseloc_bin_location;
		                    }
						}
						
                        if (utils.hasRecords(rPODetail.po_purchase_detail_to_po_purchase_detail_qty)
								&& utils.hasRecords(rPODetail.po_purchase_detail_to_po_purchase_detail_qty.po_purchase_detail_qty_to_sa_order_revision_detail)) {
									
							var rLineItem = rPODetail.po_purchase_detail_to_po_purchase_detail_qty.po_purchase_detail_qty_to_sa_order_revision_detail.getRecord(1);
							var rOrder = rLineItem.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.getRecord(1);
							
							sOrderNum = rOrder.ordh_document_num;
							nLineItemNum = rLineItem.sequence_nr;

							if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_in_item$ordrevd_finished_good_item_id)) {
								sFinishedGoodItemNumber = rLineItem.sa_order_revision_detail_to_in_item$ordrevd_finished_good_item_id.item_code;
							}
							
							if (utils.hasRecords(rOrder.sa_order_to_sa_customer_project)) {
                                projectName = rOrder.sa_order_to_sa_customer_project.custproj_desc;
                            }
						}

                        eachElement = {
                            PONumber: rPO.po_document_number,
                            SupplierCode: rPO.po_purchase_to_ap_supplier.supplier_code,
                            PR_ReferenceNumber: "",
                            LineNumber: rPODetail.sequence_nr,
                            ItemNumber: sItemCode,
                            ItemDescription: sItemDesc,
							Project: projectName,
                            Status: rPO.po_status,
                            Cost: 0,
                            Condition: "",
							UnitOfMeasure: rPODetail.po_purchase_detail_to_sys_unit_of_measure.uom_code,
                            QuantityOrdered: rPODetail.podet_qty_ordered,
                            QuantityAlreadyReceived: rPODetail.podet_qty_received,
                            QuantityReceived: 0,
                            QuantityRejected: 0,
                            QuantityPerBox: 0,
                            BoxNumber: 0,                  
                            MillNumber: "", 
                            RollNumber: "",
                            Location: sBinLocation,
                            ReferenceNumber: "",
                            WayBillNumber: "",
                            Notes: "",
                            JobNumber: sOrderNum,
                            JobLineNumber: nLineItemNum,
                            FinishedGoodItemNumber: sFinishedGoodItemNumber,
							WarehouseCode: rPO.po_purchase_to_in_warehouse.whse_code,
							NumberOfRolls: 0,
							TrackRolls: Boolean(rItem.item_track_rolls),
							GenerateRollNumber: Boolean(rItem.item_generate_roll_number),
							podet_id: rPODetail.podet_id.toString()
                        };

                        Receipt.push(eachElement);
                    }
                }
            }
        }

        var Receipt_PODetailsResult = {
            Receipt: Receipt
        };
        var Receipt_PODetailsResponse = {
            Receipt_PODetailsResult: Receipt_PODetailsResult
        };
        var response = {
            Receipt_PODetailsResponse: Receipt_PODetailsResponse
        };
        return response;
    },

    Receipt_CreateNonPOReceipt: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
             data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_CreateNonPOReceipt');
        }
       
        /** @type {{
          PR_ReferenceNumber:String,
          ItemNumber:String, 
          QuantityReceived:Number, 
          QuantityPerBox:Number, 
          BoxNumber:Number,
          MillNumber:String, 
          RollNumber:String, 
          Warehouse:String,
          Location:String, 
          Project:String, 
          ReferenceNumber:String, 
          WarehouseTransferReference:String,
          Rolls: Array
          }}*/
        
        var receipt = data.List;

        //prepare response with Error in advance
        var Receipt_CreateNonPOReceiptResponse = {
            Receipt_CreateNonPOReceiptResult: false
        };
        var response = {
            Receipt_CreateNonPOReceiptResponse: Receipt_CreateNonPOReceiptResponse
        };
        
        /** @type {Array} */
        var duplicateRollNamesArray = this.Receipt_FindDuplicateRolls(data).Receipt_FindDuplicateRollsResponse.Receipt_FindDuplicateRollsResult;
        
        if(duplicateRollNamesArray.length > 0) {
            response.Receipt_CreateNonPOReceiptResponse.DuplicateRolls = duplicateRollNamesArray;
        	return response;    
        }
        
        var intraned_trans_qty = receipt.QuantityReceived;
        var item_item_id = null;
        var item_uom_id = null;
        var item_glacct_id = null;
        var item_itemwhse_avg_cost_TO = 0;

        var intranstype_trans_code = 'IR';

        //get additional needed information for item
        var args = [receipt.ItemNumber, scopes.globals.org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT distinct item.item_id, item.item_standard_uom_id, item_class.itemclass_type, item.item_track_rolls \
                , item.item_gl_inventory_adj, item_class.itemclass_gl_inventory_adj \
                FROM in_item AS item \
                LEFT JOIN in_item_class AS item_class ON(item_class.itemclass_id=item.itemclass_id) \
                WHERE item.item_code = ? AND item.org_id = ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

        if (ds.getMaxRowIndex() == 0) {
            return response;
        }

        ds.rowIndex = 1;
        item_item_id = ds['item_id'];
        item_uom_id = ds['item_standard_uom_id'];
        item_glacct_id = ds['item_gl_inventory_adj'] != null ? ds['item_gl_inventory_adj'] : ds['itemclass_gl_inventory_adj'] != null ? ds['itemclass_gl_inventory_adj'] : null;

        if (ds['item_track_rolls'] == 1 && 'R' == ds['itemclass_type']) {
            intranstype_trans_code = 'RR';
        }

        var intranstype_id = scopes.avInv.getTransactionTypeUUID("IT", intranstype_trans_code);

        var locationBin_code_TO = receipt.Location;

        var intraneh_whse_id_TO = null;
        var intraned_whseloc_id_TO = null;
        var intraned_itemwhseloc_id_TO = null;
        //used for DETAIL TABLE record
        var item_itemwhse_id_TO = null;

		if (receipt.Location != null && receipt.Location.length > 0) {
			args = [item_item_id, item_item_id, locationBin_code_TO, scopes.globals.org_id];
			ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT warehouse_location.whse_id, warehouse_location.whseloc_id \
              , item_warehouse_location.itemwhseloc_id, item_warehouse.itemwhse_avg_cost, item_warehouse.itemwhse_id \
              FROM in_warehouse_location AS warehouse_location \
              LEFT JOIN in_item_warehouse_location AS item_warehouse_location \
              ON (item_warehouse_location.whseloc_id=warehouse_location.whseloc_id AND item_warehouse_location.item_id=?) \
              INNER JOIN in_item_warehouse AS item_warehouse ON(item_warehouse.whse_id = warehouse_location.whse_id AND item_warehouse.item_id=?) \
              WHERE warehouse_location.whseloc_bin_location= ? AND warehouse_location.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

			if (ds.getMaxRowIndex() == 0) {
				return response;
			}

			ds.rowIndex = 1;
			intraneh_whse_id_TO = ds['whse_id'];

		} else if (receipt.Warehouse != null && receipt.Warehouse.length > 0) {
			args = [receipt.Warehouse, scopes.globals.org_id];
			//find warehouse info
			ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_warehouse.whse_id FROM in_warehouse  \
	            WHERE in_warehouse.whse_code= ? AND in_warehouse.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
			if (ds.getMaxRowIndex() == 0) {
				//warehouse not found
				return response;
			} else {
				ds.rowIndex = 1;
				intraneh_whse_id_TO = ds['whse_id'];
			}
		}

        // FILL IN NEEDED INFORMATIN FOR SUBSEQUENT TRANSACTIONS

        //********************************** HEADER TABLE
        /** @type {JSFoundSet<db:/avanti/in_trans_entry_header>} */
        var fsTransEntryHeader = forms['in_trans_entry_header'].foundset;
        /** @type {JSRecord<db:/avanti/in_trans_entry_header>} */
        var rTransEntryHeader = fsTransEntryHeader.getRecord(fsTransEntryHeader.newRecord());
        rTransEntryHeader.org_id = scopes.globals.org_id;
        rTransEntryHeader.intranstype_id = intranstype_id;
        rTransEntryHeader.intraneh_reference = receipt.ReferenceNumber;
        rTransEntryHeader.intraneh_whse_id = intraneh_whse_id_TO;
        rTransEntryHeader.intraneh_createdby_empl_id = globals.avBase_employeeUUID;
        rTransEntryHeader.intraneh_status = 0;
        rTransEntryHeader.intraneh_posted_date = null;
        rTransEntryHeader.intraneh_postedby_empl_id = null;
        rTransEntryHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
        rTransEntryHeader.intraneh_whse_id_transfer_to = null;
        rTransEntryHeader.intraneh_status_field = 'O';
        rTransEntryHeader.itreg_id = null;
        rTransEntryHeader.intraneh_complete = null;
        rTransEntryHeader.intraneh_desc = null
        rTransEntryHeader.intraneh_expected_date = null;
        
        if (receipt.PR_ReferenceNumber) {
            rTransEntryHeader.intraneh_reference_pr = getTransactionUUID(receipt.PR_ReferenceNumber);
        }
        else {
            rTransEntryHeader.intraneh_reference_pr = null;
        }

        if (receipt.WarehouseTransferReference) {
            rTransEntryHeader.intraneh_reference_wto = getTransactionUUID(receipt.WarehouseTransferReference);
        }
        else {
            rTransEntryHeader.intraneh_reference_wto = null;
        }
        
        var rollsArray = receipt.Rolls;
        var nRollsReceived = rollsArray != null ? rollsArray.length : 0;
        /** @type{JSFoundSet<db:/avanti/in_item>} */
        var fsInItem = scopes.avDB.getFS("in_item", ["item_id"], [item_item_id]);
        var recInItem = fsInItem.getRecord(1);
        
        var rTransEntryDetailRec;
        if (recInItem.item_track_rolls && nRollsReceived > 0) {
        	this.Receipt_DeleteRolls(data);
            /** @type{JSFoundSet<db:/avanti/in_item_roll>} */
            var fsInItemRoll = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll');
            scopes.avPurchasing.receiptRoll_itemRec = recInItem;
            scopes.avPurchasing.receiptRoll_lastRollNum = scopes.avPurchasing.receiptRoll_getLastRollNum();
            for (var j = 0; j < nRollsReceived; j++) {
            	
            	var rolldata = rollsArray[j];

				args = [item_item_id, item_item_id, rolldata.bin_location, scopes.globals.org_id];
				ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT warehouse_location.whse_id, warehouse_location.whseloc_id \
                  , item_warehouse_location.itemwhseloc_id, item_warehouse.itemwhse_avg_cost, item_warehouse.itemwhse_id \
                  FROM in_warehouse_location AS warehouse_location \
                  LEFT JOIN in_item_warehouse_location AS item_warehouse_location \
                  ON (item_warehouse_location.whseloc_id=warehouse_location.whseloc_id AND item_warehouse_location.item_id=?) \
                  INNER JOIN in_item_warehouse AS item_warehouse ON(item_warehouse.whse_id = warehouse_location.whse_id AND item_warehouse.item_id=?) \
                  WHERE warehouse_location.whseloc_bin_location= ? AND warehouse_location.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

				if (ds.getMaxRowIndex() == 0) {
					return response;
				}

				ds.rowIndex = 1;
				intraneh_whse_id_TO = ds['whse_id'];
				intraned_whseloc_id_TO = ds['whseloc_id'];
				item_itemwhse_id_TO = ds['itemwhse_id'];
				item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];

				if (ds['itemwhseloc_id'] != null) {
					intraned_itemwhseloc_id_TO = ds['itemwhseloc_id'];
				}
				// else - there is no record in in_item_warehouse_location for specified location.
				// most likely Default location was specified which has never been used before => create a new record
				else {
					intraned_itemwhseloc_id_TO = createItemWarehouseLocation(item_item_id, item_itemwhse_id_TO, intraned_whseloc_id_TO);
				}
            	
            	rTransEntryDetailRec = createRTransEntryDetail(rolldata.initemroll_qty_on_hand);
            	rTransEntryDetailRec.glacct_id = scopes.avInv.setGLAccID(null, "RR", recInItem.item_id, rTransEntryDetailRec.intraned_whse_id, rTransEntryDetailRec);
            	
                var rec = scopes.avPurchasing.createRollFromApi(rollsArray, fsInItemRoll, recInItem, intraned_trans_qty, nRollsReceived, j);
            	rec.intraned_id = rTransEntryDetailRec.intraned_id;
            	databaseManager.saveData(rec);
            }
        } else {
        	
        	args = [item_item_id, item_item_id, locationBin_code_TO, scopes.globals.org_id];
			ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT warehouse_location.whse_id, warehouse_location.whseloc_id \
              , item_warehouse_location.itemwhseloc_id, item_warehouse.itemwhse_avg_cost, item_warehouse.itemwhse_id \
              FROM in_warehouse_location AS warehouse_location \
              LEFT JOIN in_item_warehouse_location AS item_warehouse_location \
              ON (item_warehouse_location.whseloc_id=warehouse_location.whseloc_id AND item_warehouse_location.item_id=?) \
              INNER JOIN in_item_warehouse AS item_warehouse ON(item_warehouse.whse_id = warehouse_location.whse_id AND item_warehouse.item_id=?) \
              WHERE warehouse_location.whseloc_bin_location= ? AND warehouse_location.org_id= ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

			if (ds.getMaxRowIndex() == 0) {
				return response;
			}

			ds.rowIndex = 1;
			intraneh_whse_id_TO = ds['whse_id'];
			intraned_whseloc_id_TO = ds['whseloc_id'];
			item_itemwhse_id_TO = ds['itemwhse_id'];
			item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];

			if (ds['itemwhseloc_id'] != null) {
				intraned_itemwhseloc_id_TO = ds['itemwhseloc_id'];
			}
			// else - there is no record in in_item_warehouse_location for specified location.
			// most likely Default location was specified which has never been used before => create a new record
			else {
				intraned_itemwhseloc_id_TO = createItemWarehouseLocation(item_item_id, item_itemwhse_id_TO, intraned_whseloc_id_TO);
			}
        	
        	rTransEntryDetailRec = createRTransEntryDetail(intraned_trans_qty);
        	if (intranstype_trans_code == 'RR') {
            	//new on 2017-05-08 according Tim's input on Rolls. Taken from in_trans_entry_detail_tbl.fillRollRecord()
            	var rInItemRoll = null;
                rInItemRoll = rTransEntryDetailRec.in_trans_entry_detail_to_in_item_roll.getRecord(rTransEntryDetailRec.in_trans_entry_detail_to_in_item_roll.newRecord());

                if (receipt.RollNumber != null && receipt.RollNumber.length > 0) {
                    rInItemRoll.initemroll_roll_number = receipt.RollNumber;
                }
                if (receipt.MillNumber != null && receipt.MillNumber.length > 0) {
                    rInItemRoll.initemroll_mill_roll_number = receipt.MillNumber;
                }

                rInItemRoll.intraned_id = rTransEntryDetailRec.intraned_id;

                //TODO Tim - taken from fillRollRecord(), is it correct ?
                //rInItemRoll.initemroll_qty_on_hand = 0;
                rInItemRoll.initemroll_committed = 0; //TODO do I need to assign this
                rInItemRoll.initemroll_org_qty = 0; //TODO do I need to assign this

                //TODO Tim - added myself (IK) - is it correct?
                //rInItemRoll.sequence_nr = 1; // it's null via UI
                rInItemRoll.initemroll_weight = intraned_trans_qty;
                rInItemRoll.initemroll_qty_on_hand = intraned_trans_qty;
                rInItemRoll.initemroll_org_qty = intraned_trans_qty;

                //TODO Tim after updating such fileds of in_item_rolls have "unusual" values. is it Ok?
                //  porecd_id - is null, however others are not
                //  porecdb_id - is null, however others are not
                //  initemroll_org_qty_po_uom - is null, others - 0

                rInItemRoll.item_id = item_item_id;
                rInItemRoll.whse_id = intraneh_whse_id_TO;
                rInItemRoll.itemwhse_id = item_itemwhse_id_TO;
                rInItemRoll.whseloc_id = intraned_whseloc_id_TO;
                rInItemRoll.itemwhseloc_id = intraned_itemwhseloc_id_TO;

                //update Detail record
                rTransEntryDetailRec.initemroll_id = rInItemRoll.initemroll_id;

            }
        }
        
        try {
            application.output("    ===== Receipt_CreateNonPOReceipt EXECUTING SAVING...");
            databaseManager.saveData();
            
            scopes.avInv.buildTempTransactionTables(rTransEntryHeader);
            
            // Print box label report
            if (data.PrintLabel == true){
                scopes.avInv.autoGenerateInventoryLabel("INV", rTransEntryHeader.intraneh_id, globals.avBase_employeeUUID);
            }
            
            Receipt_CreateNonPOReceiptResponse.Receipt_CreateNonPOReceiptResult = true;
        }
        catch (ex) {
            Receipt_CreateNonPOReceiptResponse.Receipt_CreateNonPOReceiptResult = data.method + ", Error, Code 2: " + ( ex.message != null ? ex.message : ex );
            application.output("api_wireless_inventory, method: " + data.method + " , Exception: " + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack, LOGGINGLEVEL.ERROR);
        }
        application.output("    ===== Receipt_CreateNonPOReceipt - DONE");
        
        
        function createRTransEntryDetail(transactionQty) {
        	//********************************** DETAIL TABLE
            var rTransEntryDetail = rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.getRecord(rTransEntryHeader.in_trans_entry_header_to_in_trans_entry_detail.newRecord());
            rTransEntryDetail.org_id = scopes.globals.org_id;
            rTransEntryDetail.item_id = item_item_id;
            rTransEntryDetail.intraned_trans_qty = transactionQty;
            rTransEntryDetail.uom_id = item_uom_id;
            rTransEntryDetail.intraned_cost_amt = item_itemwhse_avg_cost_TO;
            rTransEntryDetail.intraned_cost_uom_id = item_uom_id;
            rTransEntryDetail.intraned_cost_uom_cost_factor = 1;
            rTransEntryDetail.comment = null;
            rTransEntryDetail.intraned_extended_cost = intraned_trans_qty * item_itemwhse_avg_cost_TO;
            rTransEntryDetail.sequence_nr = 1;
            rTransEntryDetail.intraned_whse_id_transfer_from = null;
            rTransEntryDetail.intraned_whse_id = intraneh_whse_id_TO;
            rTransEntryDetail.intraned_cost_entere = item_itemwhse_avg_cost_TO;
            rTransEntryDetail.intraned_itemwhseloc_id_from = null;
            rTransEntryDetail.intraned_itemwhseloc_id = intraned_itemwhseloc_id_TO;
            rTransEntryDetail.intraned_ui_bin_id = rTransEntryDetail.intraned_itemwhseloc_id;
            rTransEntryDetail.intraned_whseloc_id_from = null;
            rTransEntryDetail.intraned_whseloc_id = intraned_whseloc_id_TO;
            rTransEntryDetail.itemwhse_id = item_itemwhse_id_TO;
            rTransEntryDetail.glacct_id = item_glacct_id;
            rTransEntryDetail.rollconv_id = null;
            rTransEntryDetail.intraned_base_cost_adj = null;
            rTransEntryDetail.intraned_balance = null;
            rTransEntryDetail.intraned_received_to_date = null;
            rTransEntryDetail.itemroll_id = null;
            rTransEntryDetail.intraned_qty_per_box = receipt.QuantityPerBox;
            rTransEntryDetail.intraned_num_boxes = receipt.BoxNumber;
            
            // sl-20590 - in_item_project recs were being created with an empty string custproj_desc
            var sProject = scopes.avText.trim(receipt.Project);
            
    		if (sProject) {
    			rTransEntryDetail.custproj_desc = receipt.Project;
    		}
    		else {
    			rTransEntryDetail.custproj_desc = null;
    		}
    		
            return rTransEntryDetail;
        }
        return response;
    },

    Receipt_CreatePOReceipt: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
             data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_CreatePOReceipt');
        }
       
        //prepare response with Error in advance
        var Receipt_CreatePOReceiptResponse = {
            Receipt_CreatePOReceiptResult: false,
            Receipt_CreatePOReceiptErrorMessage: ""
        };
        
        var response = {
            Receipt_CreatePOReceiptResponse: Receipt_CreatePOReceiptResponse
        };
        
        /***@type {{bSuccess:Boolean,
         *          sErrorMessage:String,
         *          }}*/
        var oDetailObj = new Object();
        oDetailObj.bSuccess = false;
        oDetailObj.sErrorMessage = "";

        /**@type {JSRecord<db:/avanti/po_purchase>}*/
        var rPurchase;
		/**@type {{bSuccess:Boolean,
		 * 			  sErrorMessage:String,
		 * 			  rPurchaseReceipt:JSRecord<db:/avanti/po_receipt>
		 * 			  }}
		 * */
		var oPOReciept;
		
		for (var i = 0; i < data.List.Items.length; i++) {
            /** @type {{ Rolls: Array }} */
            var singleItem = data.List.Items[i];
            
            /** @type {Array} */
            var duplicateRollNamesArray = this.Receipt_FindDuplicateRolls({ mobileDeviceId: data.mobileDeviceId, List: { Rolls: singleItem.Rolls }}).Receipt_FindDuplicateRollsResponse.Receipt_FindDuplicateRollsResult;
            
            if(duplicateRollNamesArray.length > 0) {
                response.Receipt_CreatePOReceiptResponse.DuplicateRolls = duplicateRollNamesArray;
            	return response;    
            }
		}
		
        
        
        for (var i = 0; i < data.List.Items.length; i++) {
            /** @type { {
             PONumber: String,
             LineNumber: String,
             ItemNumber: String,
             QuantityReceived: Number,
             QuantityRejected: Number,
             QuantityPerBox: Number,
             BoxNumber: Number,
             Location: String,
             ReferenceNumber: String,
             WayBillNumber: String,
             Rolls: Array
             }} */

            var item = data.List.Items[i];

            if (i == 0 && item.PONumber != null && item.PONumber.length > 0) {

                rPurchase = scopes.avDB.getRec("po_purchase", ["po_document_number", "org_id"], [item.PONumber, globals.org_id]);

                if (rPurchase) {
                    oPOReciept = scopes.avReceipts.createPurchaseReceiptRecord(rPurchase, item.ReferenceNumber, item.WayBillNumber);
                }
            }

            if (oPOReciept && oPOReciept.bSuccess == true) {
                var nLineNumber = item.LineNumber;
                var sItemCode = item.ItemNumber;
                var nQuantityReceived = item.QuantityReceived;
                var nQuantityRejected = item.QuantityRejected;
                var nQtyPerBox = item.QuantityPerBox;
                var nNumBoxes = item.BoxNumber;
                var sBinLocation = item.Location;
                var rollsArray = item.Rolls;
            	var nRollsReceived = rollsArray != null ? rollsArray.length : 0;
                
                try {
                    oDetailObj = scopes.avReceipts.createPurchaseReceiptDetailRecord(oPOReciept.rPurchaseReceipt, nLineNumber, sItemCode, nQuantityReceived, sBinLocation, nQuantityRejected, nQtyPerBox, nNumBoxes, nRollsReceived);
                    
                	/** @type{JSFoundSet<db:/avanti/in_item>} */
                	var fsInItem = scopes.avDB.getFS("in_item", ["item_code"], [item.ItemNumber]);
                	var recInItem = fsInItem.getRecord(1);
                    
                   	if(recInItem.item_track_rolls) {
                   		this.Receipt_DeleteRolls(data)
                    	
                    	/** @type{JSFoundSet<db:/avanti/in_item_roll>} */
                    	var fsInItemRoll = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll');
                    	scopes.avPurchasing.receiptRoll_itemRec = recInItem;
                    	scopes.avPurchasing.receiptRoll_lastRollNum = scopes.avPurchasing.receiptRoll_getLastRollNum();
                    	
    			    	for (var j = 0; j < nRollsReceived; j++) {
    		                var rec = scopes.avPurchasing.createRollFromApi(rollsArray, fsInItemRoll, recInItem, nQuantityReceived, nRollsReceived, j);
    		            	rec.porecd_id = oDetailObj.porecd_id
    		            	databaseManager.saveData(rec); 
    			    	}	    		    	
                	}
                	
                }
                catch (ex) {
                    oDetailObj.sErrorMessage = ex.message;
                    break;
                }
            } else if(oPOReciept){
                oDetailObj.sErrorMessage = oPOReciept.sErrorMessage;
                break;
            }
        }
        
        if (oDetailObj.bSuccess) {
            var sSysPref = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.TransactionStatus);
            if (oPOReciept.bSuccess == true && sSysPref == "Updated") {
            	globals.updateReceipt(oPOReciept.rPurchaseReceipt, true)
            }

            // Print box label report
            if (data.PrintLabel == true) {
                scopes.avInv.autoGenerateInventoryLabel("PO", oPOReciept.rPurchaseReceipt.porec_id, globals.avBase_employeeUUID);
            }
        } else if(oPOReciept && oPOReciept.rPurchaseReceipt && oPOReciept.rPurchaseReceipt.foundset){
            oPOReciept.rPurchaseReceipt.foundset.deleteRecord();
        }

        Receipt_CreatePOReceiptResponse.Receipt_CreatePOReceiptResult = oDetailObj.bSuccess;
        Receipt_CreatePOReceiptResponse.Receipt_CreatePOReceiptErrorMessage = oDetailObj.sErrorMessage;

        return response;
    },
	
    Warehouse_ActiveLocationsAndQuantityOnHand: function(data) {
    	var Warehouse_ActiveLocationsAndQuantityOnHandResult = [];
    	var warehouseCode = data.whse_code;
    	var activeFilter = data.active_filter !== false;
    	
    	/** @type{JSFoundSet<db:/avanti/in_warehouse>} */
    	var fsInWarehouse = scopes.avDB.getFS("in_warehouse", ["whse_code"], [warehouseCode]);
    	var recInWarehouse = fsInWarehouse.getRecord(1);

    	/** @type{JSFoundSet<db:/avanti/in_warehouse_location>} */
    	var fsWarehouseLocation = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_warehouse_location');

    	scopes.avPurchasing.getWarehouse_ActiveLocationsAndQuantityOnHand(recInWarehouse.whse_id.toString(), "", activeFilter, fsWarehouseLocation);
    	
    	for(var i = 1; i <= fsWarehouseLocation.getSize(); i++) {
    		/** @type{JSRecord<db:/avanti/in_warehouse_location>} */
    		var recWarehouseLoc = fsWarehouseLocation.getRecord(i);
    		var qty_on_hand = recWarehouseLoc.in_warehouse_location_to_in_item_warehouse_location$leftouterjoin.itemwhseloc_total_onhand_bin;
    		Warehouse_ActiveLocationsAndQuantityOnHandResult.push({
    			qty_on_hand: qty_on_hand ? (+qty_on_hand.toFixed(2)).toString() : qty_on_hand,
				loc: recWarehouseLoc.whseloc_bin_location
    		})
    	}

        var Warehouse_ActiveLocationsAndQuantityOnHandResponse = {
        	Warehouse_ActiveLocationsAndQuantityOnHandResult: Warehouse_ActiveLocationsAndQuantityOnHandResult
        };
        var response = {
        	Warehouse_ActiveLocationsAndQuantityOnHandResponse: Warehouse_ActiveLocationsAndQuantityOnHandResponse
        };
        return response;
    },
	
    Receipt_DeleteRolls: function(data) {
		var mobile_device_id = data.mobileDeviceId;
		var Receipt_DeleteRollsResult = false;
		
		if(mobile_device_id) {
			/** @type{JSFoundSet<db:/avanti/in_item_roll>} */
			var fsInItemRoll = scopes.avDB.getFS("in_item_roll",["mobile_device_id"],[mobile_device_id]);
	    	if(fsInItemRoll.getSize() > 0) {
	    		Receipt_DeleteRollsResult = true;
	        	fsInItemRoll.deleteAllRecords();
	    	}
		}

		
        var Receipt_DeleteRollsResponse = {
        	Receipt_DeleteRollsResult: Receipt_DeleteRollsResult
        };
        var response = {
        	Receipt_DeleteRollsResponse: Receipt_DeleteRollsResponse
        };
        return response;
	},
	
	Receipt_FindDuplicateRolls: function(data) {
        var Receipt_FindDuplicateRollsResponse = {
        	Receipt_FindDuplicateRollsResult: []
        };
        var response = {
        	Receipt_FindDuplicateRollsResponse: Receipt_FindDuplicateRollsResponse
        };
      
		/** @type {Array} */
       var Rolls = data.List.Rolls;
       var mobileDeviceId = data.mobileDeviceId;
       
       if(!Rolls || !mobileDeviceId) {
    	   return response;
       }
       
       var duplicateRollsArray = [];
		
		if(Rolls && Rolls.length > 0) {
        	var args = [];
        	var questionArgs = [];
        	for(var i = 0; i < Rolls.length; i++) {
        		args.push(Rolls[i].initemroll_roll_number);
				questionArgs.push("?");
        	}
        	args.push(scopes.globals.org_id);
        	
        	
        	var sql = "SELECT DISTINCT in_item_roll.initemroll_roll_number, in_item_roll.mobile_device_id \
                FROM in_item_roll \
                WHERE in_item_roll.initemroll_roll_number IN (" + questionArgs.join(',') + ") \
                AND in_item_roll.org_id = ?";
   
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            
            if(ds.getMaxRowIndex() != 0) {
                for(var i = 0; i < ds.getMaxRowIndex(); i++) {
                	if(ds[i].mobile_device_id != mobileDeviceId) {
                		duplicateRollsArray.push(ds[i].initemroll_roll_number);           		
                	}
                }
            }
        }
		
        response.Receipt_FindDuplicateRollsResponse.Receipt_FindDuplicateRollsResult = duplicateRollsArray;

        return response;	
	},
	
	Receipt_GenerateRolls: function(data) {
    	var podet_id = data.Podet_id; // optional
    	var numberOfRolls = data.NumberOfRolls;
    	var itemCode = data.ItemCode;
    	var warehouseCode = data.WarehouseCode;
    	var quantityReceived = data.QuantityReceived;
    	var mobile_device_id = data.mobileDeviceId;
    	
    	/** @type{JSFoundSet<db:/avanti/in_item>} */
    	var fsInItem = scopes.avDB.getFS("in_item", ["item_code"], [itemCode]);
    	var recInItem = fsInItem.getRecord(1);

		var recInItemClass = recInItem.in_item_to_in_item_class.getRecord(1);
    	
    	/** @type{JSFoundSet<db:/avanti/in_warehouse>} */
    	var fsInWarehouse = scopes.avDB.getFS("in_warehouse", ["whse_code"], [warehouseCode]);
    	var recInWarehouse = fsInWarehouse.getRecord(1);
    	

    	this.Receipt_DeleteRolls(data);
    	/** @type{JSFoundSet<db:/avanti/in_item_roll>} */
    	var fsInItemRoll = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_roll');
    	
    	var podet_uom_conv_factor = 1;
    	if(podet_id) {
        	/** @type{JSFoundSet<db:/avanti/po_purchase_detail>} */
        	var fsPOPurchaseDetail = scopes.avDB.getFS("po_purchase_detail", ["podet_id"], [podet_id]);
        	var recPOPurchaseDetail = fsPOPurchaseDetail.getRecord(1);
        	if(recPOPurchaseDetail != null) {
        		podet_uom_conv_factor = recPOPurchaseDetail.podet_uom_conv_factor;
        	}
    	}
    	
    	scopes.avPurchasing.receiptRoll_nQtyReceivedInStockUom = 0;
    	scopes.avPurchasing.receiptRoll_itemRec = recInItem;
	
    	scopes.avPurchasing.receiptRoll_lastRollNum = scopes.avPurchasing.receiptRoll_getLastRollNum();
    	for (var i = 1; i <= numberOfRolls; i++) {
    		var rec = fsInItemRoll.getRecord(fsInItemRoll.newRecord());
			rec.sequence_nr = i;
			rec.item_id = recInItem.item_id;
			rec.whse_id = recInWarehouse.whse_id;
			rec.itemwhse_id = scopes.avPurchasing.receiptRoll_getItemWarehouseID(rec);
			if(recInItem.item_generate_roll_number) {
				rec.initemroll_roll_number = scopes.avPurchasing.receiptRoll_generateRollNum();				
			} else {
				rec.initemroll_roll_number = "";
			}
			rec.initemroll_org_qty_po_uom = quantityReceived / numberOfRolls;
			rec.initemroll_qty_on_hand = scopes.avUtils.roundNumber(rec.initemroll_org_qty_po_uom * podet_uom_conv_factor, recInItem.item_decimal_places);
			rec.initemroll_org_qty = rec.initemroll_qty_on_hand;
			rec.mobile_device_id = mobile_device_id;
			
			scopes.avPurchasing.receiptRoll_nQtyReceivedInStockUom += scopes.avUtils.roundNumber(rec.initemroll_qty_on_hand, recInItem.item_decimal_places);
			rec = scopes.avPurchasing.receiptRoll_inferUOMInputsFromQty(rec);
    		databaseManager.saveData(rec);
    	}

    	var rollsArray = new Array();
    	
    	for (var i = 1; i <= fsInItemRoll.getSize(); i++) {
    		var rollRec = fsInItemRoll.getRecord(i);
    		var baseItem = {
				bin_location: "",
				width: recInItem.in_item_to_in_item_paper.paper_first_dim,
				initemroll_qty_on_hand: rollRec.initemroll_weight,
				sequence_nr: rollRec.sequence_nr,
				whse_code: ""
    		};
   		
    		if(recInItemClass.itemclass_show_roll_area) {
    			baseItem.initemroll_roll_number = rollRec.initemroll_roll_number;
    		}
    		if(recInItemClass.itemclass_show_roll_caliper) {
    			baseItem.item_caliper = recInItem.in_item_to_in_item_paper.paper_caliper;
    		}
    		if(recInItemClass.itemclass_show_roll_core_diam) {
    			baseItem.initemroll_core_diameter = rollRec.initemroll_core_diameter;
    		}
    		if(recInItemClass.itemclass_show_roll_diameter) {
    			baseItem.initemroll_diameter = rollRec.initemroll_diameter;
    		}
    		if(recInItemClass.itemclass_show_roll_length) {
    			baseItem.initemroll_length = rollRec.initemroll_length;
    		}
    		if(recInItemClass.itemclass_show_roll_weight) {
    			baseItem.initemroll_weight = rollRec.initemroll_weight;
    		}
    		if(recInItemClass.itemclass_show_roll_unique_id) {
    			baseItem.initemroll_roll_number = rollRec.initemroll_roll_number;
    		}
    		if(recInItemClass.itemclass_show_mill_roll_num) {
    			baseItem.initemroll_mill_roll_number = rollRec.initemroll_mill_roll_number;
    		}    		

    		rollsArray.push(baseItem);
    	}
    	
        var Receipt_GenerateRollsResponse = {
        	Receipt_GenerateRollsResult: rollsArray
        };
        var response = {
        	Receipt_GenerateRollsResponse: Receipt_GenerateRollsResponse
        };
        return response;
        
    },
    
    Receipt_IsReferenceNumberValid: function(data) {
        var result = false;
        if (data.ReferenceNumber != null && data.ReferenceNumber.length > 0) {
            var args = [scopes.globals.org_id, data.ReferenceNumber];
            var sSql = "SELECT transh.intraneh_reference \
                FROM in_trans_entry_header AS transh \
                INNER JOIN in_transaction_type AS transtype ON transh.intranstype_id = transtype.intranstype_id \
                INNER JOIN app_in_transaction_type AS apptranstype ON transtype.intranstype_appkey_uuid = apptranstype.intranstype_id \
                WHERE (apptranstype.intranstype_trans_code = 'PR') AND (transh.intraneh_complete <> 1) \
                AND (transh.org_id = ?) AND (transh.intraneh_reference = ?)";
            
            // Apply plant filtering
            if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
            	sSql += " AND transh.intraneh_whse_id IN " + getWarehousesByEmployee();
            }
            
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }

        var Receipt_IsReferenceNumberValidResponse = {
            Receipt_IsReferenceNumberValidResult: result
        };
        var response = {
            Receipt_IsReferenceNumberValidResponse: Receipt_IsReferenceNumberValidResponse
        };
        return response;
    },

    Receipt_PendingReceiptDetails: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_PendingReceiptDetails');
        }
        
        var Receipt = new Array();

        if (data.ReferenceNumber != null && data.ReferenceNumber.length > 0) {
            var sSQL = "SELECT item.item_code,item.item_desc1, transd.intraned_trans_qty, transd.intraned_received_to_date,transd.intraned_balance,whsloc.whseloc_bin_location, \
            				transd.custproj_desc, wl.whseloc_bin_location [pr_bin] \
			            FROM in_trans_entry_detail AS transd \
			            INNER JOIN in_trans_entry_header AS transh ON transd.intraneh_id = transh.intraneh_id \
			            INNER JOIN in_item AS item ON transd.item_id = item.item_id \
			            INNER JOIN in_item_warehouse AS itemwhs ON item.item_id = itemwhs.item_id AND transd.intraned_whse_id=itemwhs.whse_id \
			            LEFT OUTER JOIN in_item_warehouse_location whsloc ON itemwhs.itemwhse_default_receipt_bin = whsloc.whseloc_id AND whsloc.item_id= item.item_id \
			            LEFT OUTER JOIN in_warehouse_location wl ON transd.intraned_whseloc_id = wl.whseloc_id \
			            INNER JOIN in_transaction_type AS transtype ON transh.intranstype_id = transtype.intranstype_id \
			            INNER JOIN app_in_transaction_type AS apptranstype ON transtype.intranstype_appkey_uuid = apptranstype.intranstype_id \
			            WHERE \
			            	(apptranstype.intranstype_trans_code = 'PR') \
			            	AND (transh.intraneh_complete <> 1) \
			            	AND (transh.org_id = ?) \
			            	AND (transh.intraneh_reference = ?)";

            var args = [scopes.globals.org_id, data.ReferenceNumber];

            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);

            var maxRowIndex = ds.getMaxRowIndex();
            for (var i = 1; i <= maxRowIndex; i++) {
                ds.rowIndex = i;
                var prDetails = {
                    ItemNumber: ds['item_code'],
                    ItemDescription: ds['item_desc1'],
                    QuantityOrdered: ds['intraned_trans_qty'],
                    QuantityAlreadyReceived: ds['intraned_received_to_date'],
                    Location: ds["pr_bin"] ? ds["pr_bin"] : ds['whseloc_bin_location'],
                    Project: ds['custproj_desc'],
                    ReferenceNumber: ""
                };
                Receipt.push(prDetails);
            }
        }

        var Receipt_PendingReceiptDetailsResult = {
            Receipt: Receipt
        };
        var Receipt_PendingReceiptDetailsResponse = {
            Receipt_PendingReceiptDetailsResult: Receipt_PendingReceiptDetailsResult
        };
        var response = {
            Receipt_PendingReceiptDetailsResponse: Receipt_PendingReceiptDetailsResponse
        };
        return response;

    },

    Receipt_IsWarehouseTransferReferenceNumberValid: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_IsWarehouseTransferReferenceNumberValid');
        }
      
        var sSql = "SELECT COUNT(*) \
					FROM in_trans_entry_header H \
					INNER JOIN in_transaction_type TT ON H.intranstype_id = TT.intranstype_id \
					INNER JOIN app_in_transaction_type ATT ON TT.intranstype_appkey_uuid = ATT.intranstype_id \
					WHERE \
						H.org_id = ? \
						AND ATT.intranstype_trans_code = 'WTO' \
						AND H.intraneh_complete <> 1 \
						AND H.intraneh_reference = ?";
        
		// Apply plant filtering
        if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
        	sSql += " AND H.intraneh_whse_id_transfer_to IN " + getWarehousesByEmployee();
        }
        
		var aArgs = [globals.org_id, data.WarehouseTransferReference];
		var result = scopes.avDB.SQLQuery(sSql, null, aArgs) > 0;
        
        var Receipt_IsWarehouseTransferReferenceNumberValidResponse = {
        	Receipt_IsWarehouseTransferReferenceNumberValidResult: result
        };
        
        var response = {
        	Receipt_IsWarehouseTransferReferenceNumberValidResponse: Receipt_IsWarehouseTransferReferenceNumberValidResponse
        };
        
        return response;
    },
	
    Receipt_WarehouseTransferDetails: function(data) {
        if (application.isInDeveloper() && bGetTestPackage) {
            data = scopes.avApiTest.getTestWirelessInventoryDataPackage('Receipt_WarehouseTransferDetails');
        }
        
        var Receipt = new Array();

        if (data.WarehouseTransferReference != null && data.WarehouseTransferReference.length > 0) {
            var sSQL = "SELECT item.item_code,item.item_desc1, transd.intraned_trans_qty, transd.intraned_received_to_date,transd.intraned_balance,whsloc.whseloc_bin_location, transd.custproj_desc \
		                FROM in_trans_entry_detail AS transd \
			            INNER JOIN in_trans_entry_header AS transh ON transd.intraneh_id = transh.intraneh_id \
			            INNER JOIN in_item AS item ON transd.item_id = item.item_id \
			            INNER JOIN in_item_warehouse AS itemwhs ON item.item_id = itemwhs.item_id AND transd.intraned_whse_id=itemwhs.whse_id \
			            LEFT OUTER JOIN in_item_warehouse_location whsloc ON itemwhs.itemwhse_default_receipt_bin = whsloc.whseloc_id AND whsloc.item_id= item.item_id \
			            INNER JOIN in_transaction_type AS transtype ON transh.intranstype_id = transtype.intranstype_id \
			            INNER JOIN app_in_transaction_type AS apptranstype ON transtype.intranstype_appkey_uuid = apptranstype.intranstype_id \
			            WHERE \
			            	(apptranstype.intranstype_trans_code = 'WTO') \
			            	AND (transh.intraneh_complete <> 1) \
			            	AND (transh.org_id = ?) \
			            	AND (transh.intraneh_reference = ?)";

            var aArgs = [globals.org_id, data.WarehouseTransferReference];
            var ds = scopes.avDB.getDataset(sSQL, aArgs);
            var maxRowIndex = ds.getMaxRowIndex();
            
            for (var i = 1; i <= maxRowIndex; i++) {
                ds.rowIndex = i;
                
                var prDetails = {
                    ItemNumber: ds['item_code'],
                    ItemDescription: ds['item_desc1'],
                    QuantityOrdered: ds['intraned_trans_qty'],
                    QuantityAlreadyReceived: ds['intraned_received_to_date'],
                    Location: ds['whseloc_bin_location'],
                    Project: ds['custproj_desc'],
                    ReferenceNumber: ""
                };
                
                Receipt.push(prDetails);
            }
        }

        var Receipt_WarehouseTransferDetailsResult = {
            Receipt: Receipt
        };
        var Receipt_WarehouseTransferDetailsResponse = {
        	Receipt_WarehouseTransferDetailsResult: Receipt_WarehouseTransferDetailsResult
        };
        var response = {
        	Receipt_WarehouseTransferDetailsResponse: Receipt_WarehouseTransferDetailsResponse
        };
        
        return response;
    },

    Vendor_IsValid: function(data) {
        var result = false;
        if (data.VendorCode != null && data.VendorCode.length > 0) {
            var args = [data.VendorCode, scopes.globals.org_id];
            var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT supplier_id FROM ap_supplier \
                    WHERE supplier_code = ? AND supplier_active=1 AND org_id = ?", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
            if (ds.getMaxRowIndex() > 0) {
                result = true;
            }
        }

        var Vendor_IsValidResponse = {
            Vendor_IsValidResult: result
        };
        var response = {
            Vendor_IsValidResponse: Vendor_IsValidResponse
        };
        return response;
    },
	
    auth_AuthorizeDevice: function(data) {
    	
    	var element = "";
       	var result = new Array();
       	var sDeviceId = "",
       	    sResponseCode = "";
       	var dCurrentTime = application.getServerTimeStamp();
        
        var sSQL = "SELECT mdevice_id FROM sys_mobile_device WHERE mdevice_passcode = ? ";
        var aArgs = [data.passcode];
        
        if (data.passcode == "") {
        	sResponseCode = "704"; //passcode not provided
        }
        else {
            /** @type{JSRecord<db:/avanti/sys_mobile_device>} */
            var rMobileDevice = scopes.avDB.getRecFromSQL(sSQL,"sys_mobile_device",aArgs);
            
            if (!rMobileDevice) {
            	sResponseCode = "701"; // Passcode not found
            }
            else if (rMobileDevice && !rMobileDevice.mdevice_passcode_auth_date && dCurrentTime > rMobileDevice.mdevice_passcode_expiry_date) {
            	sResponseCode = "702"; // Passcode has expired
            }
            else if (rMobileDevice && rMobileDevice.mdevice_passcode_auth_date) {
            	sResponseCode = "703"; //Passcode already authenticated.
            }
            else if (rMobileDevice && !rMobileDevice.mdevice_passcode_auth_date && dCurrentTime < rMobileDevice.mdevice_passcode_expiry_date) { // Can be authorized
    			sDeviceId = rMobileDevice.mdevice_id.toString();
    			sResponseCode = "700";

    			rMobileDevice.mdevice_passcode_auth_date = application.getServerTimeStamp();
    			rMobileDevice.mdevice_status = "2";
    			rMobileDevice.mdevice_type = data.device_type;
    			rMobileDevice.mdevice_name = data.device_name;
    			databaseManager.saveData(rMobileDevice);
    		} 
        }
        
        element = {ResponseCode: sResponseCode, DeviceId: sDeviceId};
        result.push(element);
        
        var auth_AuthorizeDeviceResponse = {
        	auth_AuthorizeDeviceResult: result
        };
        var response = {
        	auth_AuthorizeDeviceResultResponse: auth_AuthorizeDeviceResponse
        };
        return response;
    },

    Vendor_List: function(data) {
        var Vendor = new Array();

        var args = [scopes.globals.org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT distinct supplier_code, supplier_name FROM ap_supplier \
                        WHERE supplier_active=1 AND org_id = ? \
                        ORDER BY supplier_name ASC", args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        var eachElement = null;
        var maxRowIndex = ds.getMaxRowIndex();
        for (var i = 1; i <= maxRowIndex; i++) {
            ds.rowIndex = i;
            eachElement = {
                VendorCode: ds['supplier_code'],
                VendorName: ds['supplier_name']
            };
            Vendor.push(eachElement);
        }

        var Vendor_ListResult = {
            Vendor: Vendor
        };
        var Vendor_ListResponse = {
            Vendor_ListResult: Vendor_ListResult
        };
        var response = {
            Vendor_ListResponse: Vendor_ListResponse
        };
        return response;
    },

    ZExceptionHandlingTest: function(data) {
        var result = true;

        /*
         * implementation here
         */

        var ZExceptionHandlingTestResponse = {
            ZExceptionHandlingTestResult: result
        };
        var response = {
            ZExceptionHandlingTestResponse: ZExceptionHandlingTestResponse
        };
        return response;
    },

    ZdebugTest: function(data) {
        var result = true;

        /*
         * implementation here
         */

        var ZdebugTestResponse = {
            ZdebugTestResult: result
        };

        var response = {
            ZdebugTestResponse: ZdebugTestResponse
        };
        return response;
    }	

} //END var methods

/**
 * Checks whether the pickskip reason id is valid or not.
 * @param {String} sPickSkipID
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"********-AED3-4444-A456-1242297B4912"}
 */
function isPickSkipReasonValid(sPickSkipID){
    if(!sPickSkipID){
        return false;
    }
    var sSql = "SELECT pickskip_id FROM sys_pick_skip_reason \
        WHERE (org_id = ?) AND (pickskip_id = ?)";
    var args = [scopes.globals.org_id, sPickSkipID];
    /** @type{JSFoundSet<db:/avanti/sys_pick_skip_reason>} */
    var fsPickSkip = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_pick_skip_reason');
    fsPickSkip.loadRecords(sSql, args);
    
    return (fsPickSkip.getSize() > 0) ;
}

/**
 * Validates the From and To location and returns a location object with proper item warehouse locations
 * @param item_item_id
 * @param locationBin_code_FROM
 * @param locationBin_code_TO
 * @return {{intraneh_whse_id_FROM:String, 
 *          intraned_whseloc_id_FROM:String, 
 *          intraned_itemwhseloc_id_FROM:String, 
 *          intraneh_whse_id_TO:Number,
 *          intraned_whseloc_id_TO:String, 
 *          intraned_itemwhseloc_id_TO:String, 
 *          item_itemwhse_avg_cost_TO:String, 
 *          item_itemwhse_id_TO:String, 
 *          error:String }}
 *
 * @properties={typeid:24,uuid:"640111A1-25CB-431D-84EC-71985344A07B"}
 */
function buildLocationObject(item_item_id,locationBin_code_FROM,locationBin_code_TO) {
    /*** @type{{intraneh_whse_id_FROM:String, intraned_whseloc_id_FROM:String, intraned_itemwhseloc_id_FROM:String, intraneh_whse_id_TO:Number,
        intraned_whseloc_id_TO:String, intraned_itemwhseloc_id_TO:String, item_itemwhse_avg_cost_TO:String, item_itemwhse_id_TO:String,error:String }}*/
	var oLocation = new Object();
	
	var args = [item_item_id, item_item_id, locationBin_code_FROM, scopes.globals.org_id];
	var sSql="SELECT warehouse_location.whse_id, warehouse_location.whseloc_id, \
	    item_warehouse_location.itemwhseloc_id, item_warehouse.itemwhse_avg_cost, item_warehouse.itemwhse_id \
	    FROM in_warehouse_location AS warehouse_location \
	    LEFT JOIN in_item_warehouse_location AS item_warehouse_location \
	    ON (item_warehouse_location.whseloc_id=warehouse_location.whseloc_id AND item_warehouse_location.item_id=?) \
	    INNER JOIN in_item_warehouse AS item_warehouse ON(item_warehouse.whse_id = warehouse_location.whse_id AND item_warehouse.item_id=?) \
	    WHERE warehouse_location.whseloc_bin_location= ? AND warehouse_location.org_id= ?";
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql,args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	if (ds.getMaxRowIndex() == 0) {
	    oLocation.error = ", Error: Incorrect FromLocation";
	    return oLocation;
	}
	ds.rowIndex = 1;
	oLocation.intraneh_whse_id_FROM = ds['whse_id'].toString();
	oLocation.intraned_whseloc_id_FROM = ds['whseloc_id'].toString();
	if (ds['itemwhseloc_id'] != null) {
	    oLocation.intraned_itemwhseloc_id_FROM = ds['itemwhseloc_id'];
	}
	args = [item_item_id, item_item_id, locationBin_code_TO, scopes.globals.org_id];
	ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql,args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);
	if (ds.getMaxRowIndex() == 0) {
	    oLocation.error = ", Error: Incorrect ToLocation";
	    return oLocation;
	}
	ds.rowIndex = 1;
	oLocation.intraneh_whse_id_TO = ds['whse_id'];
	oLocation.intraned_whseloc_id_TO = ds['whseloc_id'];
	oLocation.item_itemwhse_id_TO = ds['itemwhse_id'];
	oLocation.item_itemwhse_avg_cost_TO = ds['itemwhse_avg_cost'];
	if (ds['itemwhseloc_id'] != null) {
	    oLocation.intraned_itemwhseloc_id_TO = ds['itemwhseloc_id'];
	}
	// else - there is no record in in_item_warehouse_location for specified location.
	// most likely Default location was specified which has never been used before => create a new record
	else {
	    oLocation.intraned_itemwhseloc_id_TO = createItemWarehouseLocation(item_item_id, oLocation.item_itemwhse_id_TO, oLocation.intraned_whseloc_id_TO);
	}
	oLocation.result = true;
	
	return oLocation;
}

/**
 * Returns Picklists based on the status
 *
 * @return
 * @properties={typeid:24,uuid:"5C4549BA-7DB3-4818-9A7E-C51C080D4B1F"}
 */
function getPickLists() {
    var pickLists = new Array();
	var sSql1 = "SELECT pick.pick_doc_number, pick.priority, pick.pick_id, pick.pick_description FROM sa_pick AS pick \
	            LEFT OUTER JOIN sys_employee AS empl ON pick.assignee_id = empl.empl_id \
	            WHERE pick.pick_status = N'O' AND (pick.org_id = ?) \
	            AND (empl.empl_id = ? OR pick.assignee_id IS NULL) ";
	            
	var sSql2 = " UNION \
	            SELECT pick.pick_doc_number, pick.priority, pick.pick_id, pick.pick_description FROM sa_pick AS pick \
                LEFT OUTER JOIN sys_employee AS empl ON pick.assignee_id = empl.empl_id \
                WHERE pick.pick_status = N'IP' AND (pick.org_id = ?) \
                AND (empl.empl_id = ?)";
	
	// Apply plant filtering
	if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1'){
		sSql1 += " AND pick.plant_id IN " + globals.getPlantsByEmployee();
		sSql2 += " AND pick.plant_id IN " + globals.getPlantsByEmployee() + " ORDER BY pick.priority ASC ";
	}
	else{
		sSql2 +=  " ORDER BY pick.priority ASC";
	}
	
	var sSql = sSql1 + sSql2;
	
	var args = [scopes.globals.org_id, globals.avBase_employeeUUID,scopes.globals.org_id, globals.avBase_employeeUUID];
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	var eachElement = null;
	var maxRowIndex = ds.getMaxRowIndex();
	for (var i = 1; i <= maxRowIndex; i++) {
	    ds.rowIndex = i;
	    eachElement = {
	        PickListNumber: ds['pick_doc_number'],
	        Pick_ID: ds['pick_id'],
	        Priority: ds['priority'] ? ds['priority'] : "",
	        Description: ds['pick_description'] ? ds['pick_description'] : ""
	    };

	    pickLists.push(eachElement);
	}
	
	return pickLists;
}

/**
 * Checks if the employee is a supervisor or not
 * @param {String} empolyeeId
 *
 * @return
 * @properties={typeid:24,uuid:"3EBD3D1A-1C22-435D-809B-A82E3936D0D8"}
 */
function isSupervisor(empolyeeId) {
	var sSql = "SELECT team.empl_id_leader from sys_employee_team AS team \
	            INNER JOIN sys_employee AS emp ON emp.empl_id = team.empl_id_leader \
	            WHERE (team.org_id = ?) AND (emp.empl_id = ?)";
	var args = [scopes.globals.org_id,empolyeeId];
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	if (ds.getMaxRowIndex() > 0) {
	    return true;
	}
	return false;
}

/**
 * Checks if the employee is a supervisor or not
 * @param {String} employeeId
 *
 * @return
 * @properties={typeid:24,uuid:"BD851FE0-13B2-462E-B7F6-A118F5DF4882"}
 */
function getSupervisor(employeeId) {
    if (employeeId) {
        var sSql = "SELECT team.empl_id_leader from sys_employee_team AS team \
                INNER JOIN sys_employee AS emp ON emp.empl_id = team.empl_id \
                WHERE (team.org_id = ?) AND (emp.empl_id = ?)";
        var args = [scopes.globals.org_id, employeeId];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
        if (ds.getMaxRowIndex() > 0) {
            ds.rowIndex = 1;
            return ds['empl_id_leader'];
        }
    }
    return null;
}

/**
 * Creates movable unit, the function calling it must call databaseManager.saveData()
 * @param {String} sPickDetailID
 * @param {Number} sPickedQty
 * @param {String} sTote
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"37DCCB47-AB73-4067-9307-AEBCF37A1DEE"}
 */
function createMovableUnits(sPickDetailID, sPickedQty, sTote) {
    
	var sSql = "SELECT pickd_id from sa_pick_detail WHERE pickd_id = ? AND org_id = ?";
	var args = [sPickDetailID, scopes.globals.org_id];
	
	/** @type{JSFoundSet<db:/avanti/sa_pick_detail>} */
	var fsPickDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_pick_detail');
	fsPickDetail.loadRecords(sSql,args);
	if(fsPickDetail.getSize() < 1){
	    return false;
	}
    if (utils.hasRecords(fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit)) {
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.pickdmu_quantity += sPickedQty;
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.pickdmu_reference = sTote;
    }
	else{
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.newRecord();
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.item_id = fsPickDetail.item_id;
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.ordrevd_id = fsPickDetail.ordrevd_id;
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.pickdmu_quantity = sPickedQty;
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.pickdmu_reference = sTote;
        fsPickDetail.sa_pick_detail_to_sa_pick_detail_movable_unit.uom_id = fsPickDetail.uom_id;
	}
	
	return true;
}


/**
 * @param {String} sPickDocNum
 * @param {String} sTote
 * @param {String} sOrdhId
 * @return {Object}
 *
 * @properties={typeid:24,uuid:"61093E8E-F8A8-4140-801D-0AFF70539D30"}
 */
function validateTote(sPickDocNum,sTote, sOrdhId) {
    
    var result = { valid: true, message: '' };
    var sSql = "SELECT sp.pick_id from sa_pick sp\
	            INNER JOIN sa_pick_detail spd ON spd.pick_id=sp.pick_id\
	            INNER JOIN sa_pick_detail_movable_unit mu ON spd.pickd_id = mu.pickd_id\
	            WHERE mu.pickdmu_reference = ? AND sp.pick_doc_number <> ?\
	            AND spd.org_id = ? AND sp.pick_status <> 'S'";
    var args = [sTote, sPickDocNum, scopes.globals.org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);
    if (ds.getMaxRowIndex() > 0) {

        result = { valid: false, message: 'The sales order pick associated with this Tote has not yet been shipped. Please use a different Tote to continue.' };
        return result;
    }

    sSql = "SELECT sp.pick_id FROM sa_pick sp\
            INNER JOIN sa_pick_detail spd ON spd.pick_id=sp.pick_id\
            INNER JOIN sa_order_revision_detail sord on sord.ordrevd_id = spd.ordrevd_id\
            INNER JOIN sa_order_revision_header sorh on sorh.ordrevh_id = sord.ordrevh_id\
            INNER JOIN sa_pick_detail_movable_unit mu ON spd.pickd_id = mu.pickd_id\
            WHERE mu.pickdmu_reference = ? AND \
            sp.pick_doc_number = ?  AND spd.org_id = ? AND sorh.ordh_id <> ?";
    
    args = [sTote, sPickDocNum, scopes.globals.org_id, sOrdhId];
    ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);
    if (ds.getMaxRowIndex() > 0) {

        result = { valid: false, message: 'The Tote has already been used in another sales order within the pick. Please use a different Tote to continue.' };
        return result;
    }
	
	return result;
}

/**
 * If the location is different than the one in Pick Detail, it will create
 * an Item Warehouse location, if missing and return it.
 * @param {String} sLocation
 * @param {String} sItemId
 * @return {String}
 *
 * @properties={typeid:24,uuid:"01165099-B0B2-44D9-86A8-FC93CFE58EBB"}
 */
function getNewItemLocationId(sLocation, sItemId) {
	var sSql = "SELECT itwl.itemwhseloc_id FROM in_item_warehouse_location AS itwl \
	    INNER JOIN in_warehouse_location AS iwl ON iwl.whseloc_id = itwl.whseloc_id \
	    WHERE itwl.org_id = ? AND iwl.whseloc_bin_location = ? AND itwl.item_id = ?";
	var args = [scopes.globals.org_id, sLocation, sItemId];
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	if(ds.getMaxRowIndex() < 1){
	    sSql = "SELECT itw.itemwhse_id, iwl.whseloc_id FROM in_item_warehouse AS itw \
	    INNER JOIN in_warehouse_location AS iwl ON iwl.whse_id = itw.whse_id \
	    WHERE itw.org_id = ? AND iwl.whseloc_bin_location = ? AND itw.item_id = ?";
	    
	    args = [scopes.globals.org_id, sLocation, sItemId];

	    ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	    
	    if(ds.getMaxRowIndex() > 0){//Should we only proceed further if a itemwhseid is found??
	        ds.rowIndex = 1;
	        return createItemWarehouseLocation(sItemId, ds['itemwhse_id'], ds['whseloc_id']);
	    }
	    return null;
	}
	else{
        ds.rowIndex = 1;
        return ds['itemwhseloc_id'];
	}
}

/**
 * Runs a query to get all the pick list items for the pick list
 * @param {Boolean} bSkipped
 * @param {String} sPickItem
 * @returns { Array <{sPickDocNum:String, SequenceNumber:String, Order:String, ItemNumber:String,
 *              ItemDescription:String, Location:String, PickQty:Number,
 *              AlreadyPickedQty:Number, RequiredQty:Number,
 *              UnitOfMeasure:String,Status:String,PickDetail_ID:String,
 *              PickDetailBin_ID:String,Tote:String} >}
 * @properties={typeid:24,uuid:"4839D87D-6929-45AE-A511-2F31805A910E"}
 */
function getPickListItems(bSkipped, sPickItem) {
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseStockingUOMForPick)) {
		/**@type {JSRecord<db:/avanti/sa_pick>} */
		var rPick = scopes.avDB.getRec('sa_pick', ['pick_doc_number'], [sPickItem]);
		scopes.avPicking.updateStockUOMCols(rPick);
	}

	var sSqlStatement = "SELECT so.ordh_document_num, in_warehouse.whse_code, inv.item_code, inv.item_desc1,\
								uoms_stock.uom_code AS uom_code, \
								pdb.pickdb_qty, pdb.pickdb_qty_picked, p.pick_status,\
								ord.ordrevd_line_num,so.ordh_id, p.pick_doc_number, pd.pickd_id, pdb.pickdb_id, \
								in_warehouse_location.whseloc_bin_location, mu.pickdmu_reference, cp.custproj_desc \
						FROM sa_pick_detail_bin AS pdb \
						INNER JOIN sa_pick_detail AS pd ON pd.pickd_id = pdb.pickd_id \
						INNER JOIN sys_unit_of_measure AS uoms ON uoms.uom_id = pd.uom_id AND uoms.org_id = pd.org_id \
						INNER JOIN sa_order_revision_detail AS ord ON pd.ordrevd_id = ord.ordrevd_id \
						INNER JOIN in_item AS inv ON pd.item_id = inv.item_id \
						LEFT OUTER JOIN sa_pick_detail_movable_unit AS mu ON mu.pickd_id = pd.pickd_id \
						INNER JOIN sa_pick AS p ON pd.pick_id = p.pick_id \
						INNER JOIN in_item_warehouse_location ON pdb.itemwhseloc_id = in_item_warehouse_location.itemwhseloc_id \
						INNER JOIN in_item_warehouse ON in_item_warehouse_location.itemwhse_id = in_item_warehouse.itemwhse_id \
						INNER JOIN in_warehouse ON in_item_warehouse.whse_id = in_warehouse.whse_id \
						LEFT OUTER JOIN in_warehouse_location ON in_item_warehouse_location.whseloc_id = in_warehouse_location.whseloc_id \
						LEFT OUTER JOIN sa_order AS so INNER JOIN sa_ship AS ship ON so.ordh_id = ship.ordh_id ON pd.ship_id = ship.ship_id \
						LEFT OUTER JOIN sa_customer_project AS cp ON so.custproj_id = cp.custproj_id \
						LEFT OUTER JOIN sys_unit_of_measure AS uoms_stock ON uoms_stock.uom_id = pd.uom_id_stock AND uoms_stock.org_id = pd.org_id \
						WHERE \
							(pd.org_id = ?) \
							AND (p.pick_doc_number = ?) \
							AND p.pick_date_picked IS NULL \
							AND pickdb_qty <> 0";

	if (bSkipped) {
		sSqlStatement = sSqlStatement + " AND pd.pickskip_id IS NOT NULL";
	}
	
	sSqlStatement = sSqlStatement + "ORDER BY in_warehouse.whse_code, in_warehouse_location.whseloc_bin_location";
	
	var sAsc = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.PickRouteOrder);
	
	if(sAsc != 'ASC'){
	    sSqlStatement = sSqlStatement +" DESC";
	}
	
	var args = [scopes.globals.org_id, sPickItem ];
	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSqlStatement, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
	
	var eachElement = null;
	var pickListItems = new Array();
	
	var maxRowIndex = ds.getMaxRowIndex();
	for (var i = 1; i <= maxRowIndex; i++) {
		ds.rowIndex = i;
	    
	    //If Last tote used is not null we check if the length is greater than the tote id length defined in sys pref
        if (ds['pickdmu_reference'] && ds['pickdmu_reference'].length > globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.ToteLength)) {
            sTote = ds['pickdmu_reference'];
        }
	    eachElement = {
	    	sPickDocNum: ds['pick_doc_number'],
	        SequenceNumber:i,
	        Order: ds['ordh_document_num'] ? ds['ordh_document_num'] : "",
	        ItemNumber: ds['item_code'] ? ds['item_code'] : "",
	        ItemDescription: ds['item_desc1'] ? ds['item_desc1'] : "",
	        Location: ds['whseloc_bin_location'] ? ds['whseloc_bin_location'] : "",
	        PickQty: ds['pickdb_qty'] ? ds['pickdb_qty'] : 0,
	        AlreadyPickedQty: ds['pickdb_qty_picked'] ? ds['pickdb_qty_picked'] : 0,
	        UnitOfMeasure: ds['uom_code'] ? ds['uom_code'] : "",
	        Status: ds['pick_status'] ? ds['pick_status'] : "",
	        Tote: (ds['pickdmu_reference'] && ds['pickdmu_reference'].length > globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.ToteLength)) ? ds['pickdmu_reference'] : "",
	        PickSkipID:0,    
	        PickDetail_ID: ds['pickd_id'],
	        PickDetailBin_ID: ds['pickdb_id'],     
	        Project: ds['custproj_desc']     
	    };

	    pickListItems.push(eachElement);
	}
	
	return pickListItems;
}

/**
 * Update Pick List status
 * @param {String} sPickDocNumber
 * @param {String} sPickStatus
 *
 * @return
 * @properties={typeid:24,uuid:"3DC0CC9F-399E-4F30-AB48-BD3B1E8B6507"}
 */
function updatePickStatus(sPickDocNumber,sPickStatus) {
    var sSql = "SELECT pick_id FROM sa_pick WHERE (org_id = ?) AND (pick_doc_number = ?)";
    var args = [scopes.globals.org_id, sPickDocNumber];
    /** @type{JSFoundSet<db:/avanti/sa_pick>} */
    var fsFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_pick');
    fsFoundset.loadRecords(sSql, args);
    var result = false;
    if (fsFoundset.getSize() > 0) {
        fsFoundset.pick_status = sPickStatus;
        fsFoundset.assignee_id = globals.avBase_employeeUUID;

        if (sPickStatus == 'P') {
            fsFoundset.pick_date_picked = application.getServerTimeStamp() ;
        }

        if (sPickStatus == 'PS') {
            fsFoundset.pick_date_staged = application.getServerTimeStamp() ;
        }

        fsFoundset.sa_pick_to_sa_pick_audit.newRecord();
        scopes.avPicking.additionalPickListFields(fsFoundset.getSelectedRecord(), 'PRINT');
        databaseManager.saveData();
        result = true;
    }
    return result;
}

/**
 * Creates a new record in the in_item_warehouse_location table
 * @param {String} item_id
 * @param {String} itemwhse_id
 * @param {String} whseloc_id
 * @return {String} itemwhseloc_id
 *
 * @properties={typeid:24,uuid:"364721F8-6486-4BF7-A3FC-5746626B4432"}
 */
function createItemWarehouseLocation(item_id, itemwhse_id, whseloc_id) {
    /** @type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
    var fsItemWhseLocation = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_location');
    fsItemWhseLocation.newRecord();
    fsItemWhseLocation.itemwhse_id = itemwhse_id;
    fsItemWhseLocation.whseloc_id = whseloc_id;
    fsItemWhseLocation.itemwhseloc_onhand_qty = 0;
    fsItemWhseLocation.itemwhseloc_unavailible_qty = 0;
    fsItemWhseLocation.itemwhseloc_unusable_qty = 0;
    fsItemWhseLocation.sequence_nr = scopes.avInv.getNextItemWarehouseLocationSequenceNr(fsItemWhseLocation.itemwhse_id);;
    fsItemWhseLocation.item_id = item_id;

    databaseManager.saveData(fsItemWhseLocation);
    return fsItemWhseLocation.itemwhseloc_id.toString();
}

/**
 * @param {String} whseloc_bin_location
 * @return {String}
 *
 * @properties={typeid:24,uuid:"FF8977A3-29F6-435B-AC83-1CAD4BB7E3A9"}
 */
function getWhseCodeFromBinLocation(whseloc_bin_location) {
    if (whseloc_bin_location == null || whseloc_bin_location.length == 0)
        return null;

    return ( whseloc_bin_location.indexOf("-") != -1 ) ? whseloc_bin_location.substring(0, whseloc_bin_location.indexOf("-")) : whseloc_bin_location;
}

/**
 * @param {Number} user_id
 * @return {String}
 *
 * @properties={typeid:24,uuid:"255310C7-E528-4A50-B572-99C468A7E739"}
 */
function getEmplCode(user_id) {
    var args = [user_id, scopes.globals.org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT empl_code FROM sys_employee \
        WHERE user_id=? AND org_id =?"
        , args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

    if (ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * Returns a transaction id from a reference number
 * @param intraneh_reference
 *
 * @return
 * @properties={typeid:24,uuid:"44761D19-7232-4841-8EA3-265B71B8C363"}
 */
function getTransactionUUID(intraneh_reference) {
    var args = [intraneh_reference, scopes.globals.org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT intraneh_id FROM in_trans_entry_header \
        WHERE intraneh_reference = ? AND org_id =?"
        , args, WWMS_SQL_ROW_NUMBER_TO_SELECT_FEW);

    if (ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * Creates a new Transaction if there are transaction items with skipped reasons in the detail
 * table. Duplicates those items and moves them under the new transaction by deleting the original
 * skipped detail records with skip reasons under the original transaction
 * 
 * @param {String} transaction_ID
 *
 * @return
 * @properties={typeid:24,uuid:"58C8512D-30E4-40A5-B995-1E099DCFE2A9"}
 */
function spawnSkippedItemTransfer(transaction_ID){
    
    var sSql = "SELECT intraned_id FROM in_trans_entry_detail \
        WHERE (org_id = ?) AND (pickskip_id IS NOT NULL) AND intraneh_id  = ?";
    var args = [scopes.globals.org_id, transaction_ID];
    /** @type{JSFoundSet<db:/avanti/in_trans_entry_detail>} */
    var fsInTransDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_trans_entry_detail');
    fsInTransDetail.loadRecords(sSql, args);
    
    
    if (fsInTransDetail.getSize() > 0) {
        fsInTransDetail.in_trans_entry_detail_to_in_trans_entry_header.duplicateRecord(1, false, true);
        /** @type{JSRecord<db:/avanti/in_trans_entry_header>} */
        var rTransEntryHeader = fsInTransDetail.in_trans_entry_detail_to_in_trans_entry_header.getSelectedRecord();

        rTransEntryHeader.intraneh_transaction_no = globals.getNextItemTransactionNumber('IT');
        rTransEntryHeader.intraneh_status = 0;
        rTransEntryHeader.intraneh_status_field = 'O';
        rTransEntryHeader.intraneh_assignee_id = getSupervisor(globals.avBase_employeeUUID);

        var maxSize = databaseManager.getFoundSetCount(fsInTransDetail);

        //Duplicate entry details and update with new transaction id
        for (var i = 1; i <= maxSize; i++) {
            fsInTransDetail.duplicateRecord(i, false, true);
            /** @type{JSRecord<db:/avanti/in_trans_entry_detail>} */
            var rInTransDetail = fsInTransDetail.getSelectedRecord();
            rInTransDetail.intraneh_id = rTransEntryHeader.intraneh_id;
            rInTransDetail.pickskip_id = null;
        }

        var result = databaseManager.saveData();
        if (result) {
            application.output("Duplication of skipped records with new transaction id is succesfull", LOGGINGLEVEL.INFO);
            fsInTransDetail.clear();
            fsInTransDetail.loadRecords(sSql, args);
            result = fsInTransDetail.deleteAllRecords();
            
            if (result) {
                result = databaseManager.saveData();
                application.output("Removal of duplicated skipped records from original transactions is succesfull", LOGGINGLEVEL.INFO);
            }
            else {
                return false;
            }
            return true;
        }
    }
    return false;
}

/**
 * Returns list of warehouses on the item
 * @param {String} itemCode
 * @return {Object}
 *
 * @properties={typeid:24,uuid:"FB8AA7DC-C9D9-4A82-A413-A989CCC6B33C"}
 */
function getWarehousesByItemCode(itemCode){
    var aWarehouseList = new Array();
    var args = [itemCode,scopes.globals.org_id];
    var sSql = "SELECT warehouse.whse_code,warehouse.whse_id  \
                    FROM in_item AS item \
                    INNER JOIN in_item_warehouse AS item_warehouse ON (item.item_id = item_warehouse.item_id) \
                    INNER JOIN in_warehouse AS warehouse ON (warehouse.whse_id = item_warehouse.whse_id) \
                    WHERE item.item_code=? AND item.org_id = ? AND warehouse.whse_enable_bin_locations = 1 ";
    
    // Apply plant filtering
    if (scopes.avUtils.Pref[scopes.avUtils.SYS_PREF.DivPlantFilter] === '1') {
    	sSql += " AND item_warehouse.whse_id  IN " + getWarehousesByEmployee();
    }
    
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
    
    var eachElement = null;
    var maxRowIndex = ds.getMaxRowIndex();
    for (var i = 1; i <= maxRowIndex; i++) {
        ds.rowIndex = i;
        eachElement = {
            WarehouseCodes: ds['whse_code'],
            Warehouse_ID:ds['whse_id']
        };
        aWarehouseList.push(eachElement);
    }
    
    return aWarehouseList;
}

/**
 * TODO Gets the list of warehouses for the provided employee id
 * @param sEmployeeCode
 *
 * @return
 * @properties={typeid:24,uuid:"FD252D4D-D49D-4D5C-A6D9-E20514445A9F"}
 */
function getWarehousesByEmployee()
{
    var aWarehouseList = new Array();  
	var sSql = "SELECT warehouse.whse_id  \
        FROM in_warehouse AS warehouse \
        INNER JOIN sys_employee_plant AS plant ON (warehouse.plant_id = plant.plant_id)\
        WHERE plant.empl_id=? AND warehouse.org_id = ? ";      	
    
    var args = [globals.avBase_employeeUUID,scopes.globals.org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
    
    var eachElement = null;
    var maxRowIndex = ds.getMaxRowIndex();
    for (var i = 1; i <= maxRowIndex; i++) {
        ds.rowIndex = i;
        aWarehouseList.push(ds['whse_id']);
    }
    return "(" + scopes.avText.arrayToString(aWarehouseList,",","'") + ")"
}

/**
 * Adds the finished good item to the sales order, you have to manually call saveData after.
 * @param ordrevd_id
 * @param item_item_id
 *
 * @properties={typeid:24,uuid:"521F6509-0A05-4B4C-9103-197FB29DFFC2"}
 */
function addFGItem(ordrevd_id,item_item_id){
    
    var sSql = "SELECT ordrevd_id FROM sa_order_revision_detail \
        WHERE (org_id = ?) AND  ordrevd_id  = ?";
    var args = [scopes.globals.org_id, ordrevd_id];
    /** @type{JSFoundSet<db:/avanti/sa_order_revision_detail>} */
    var fsOrdRevd = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revision_detail');
    fsOrdRevd.loadRecords(sSql, args);
    
    
    if (fsOrdRevd.getSize() > 0) {
        fsOrdRevd.ordrevd_finished_good_item_id = item_item_id;
    }
}

/**
 * @param {String} sItemCode
 * 
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"604EDF90-A998-4BB4-8805-FEFE1277187B"}
 */
function getItemID(sItemCode) {
	/**@type {UUID} */
	var uItemID = null;
	
	if (sItemCode) {
		uItemID = scopes.avDB.SQLQuery("SELECT item_id FROM in_item WHERE org_id = ? AND item_code = ?", null, [globals.org_id, sItemCode]);
	}
	
	return uItemID;
}

/**
 * Update Mobile Device Last Login Date
 * @param {UUID} uDeviceId
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B2BC5541-6F3F-495F-A395-E2285CD19F2F"}
 */
function updateMobileDeviceLastLogin(uDeviceId) {

	var result = false;
	
	if (uDeviceId) {
		/** @type{JSRecord<db:/avanti/sys_mobile_device>} */
		var rMobileDevice = scopes.avDB.getRec("sys_mobile_device", ["mdevice_id"], [uDeviceId])

		if (rMobileDevice) {
			rMobileDevice.mdevice_last_login_date = application.getServerTimeStamp();
			databaseManager.saveData(rMobileDevice);
			result = true;
		}
	}

	return result;
}

/**
 * Set the Mobile Application Type
 * 
 * @param {String} sServiceCode
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"064AFF02-A123-4A5C-9CA4-2F5C1CB298B5"}
 */
function setMobileApplicationType(sServiceCode) {

	var result = "SLINGSHOT";
	
	var sSql = "SELECT org_id \
				FROM sys_organization \
				WHERE org_web_service_code = ? ";      	   
    var args = [sServiceCode];
    var dsData = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, WWMS_SQL_ROW_NUMBER_TO_SELECT_MAX);
    
    if (dsData && dsData.getMaxRowIndex() <= 0) {
    	result = "705"; 
    }

    return result;
}

/**
 * @private 
 * 
 * @param {String} sProcedure
 * @param {Object} data
 * @param {Boolean} [bLogRegardlessOfDebugFlag]
 *
 * @properties={typeid:24,uuid:"EC3BB485-6C4D-4C35-9A63-E8DE6E885D23"}
 */
function logDataPackage(sProcedure, data, bLogRegardlessOfDebugFlag) {
	if (sProcedure && data && (bDebug || bLogRegardlessOfDebugFlag)) {
		scopes.avUtils.devLog(sProcedure, JSON.stringify(data));
	}
}


/**
 * @return {String} URL for API request
 * @properties={typeid:24,uuid:"1B94436F-633B-40A1-B906-FE8F4559327A"}
 */
function getRequestFullURL () {
	 var sUrl = '';
     var request = plugins.rest_ws.getRequest();
     if (request) {
     	sUrl += request.getRequestURL();
     	var aParamNames = request.getParameterNames();
     	if (aParamNames) {
     		if (aParamNames.length > 0) {
     			sUrl += '?';
     		}
     		for (var i = 0; i < aParamNames.length; i++) {
     			var sName = aParamNames[i];
     			var aValues = request.getParameterValues(sName);
     			sUrl += sName + '=';
     			for (var j = 0; j < aValues.length; j++) {
     				var sValue = aValues[j];
     				sUrl += sValue;
     				if (j < aValues.length - 1) {
     					sUrl += ',';
     				}
     			}
     			
     			if (i < aParamNames.length - 1) {
     				sUrl += '&';
     			}
     		}
     	}
     }
     
     return sUrl;
}
