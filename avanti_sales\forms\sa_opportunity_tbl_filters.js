/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"661C2A8E-4747-4668-82E0-5A872BC9B1D9"}
 */
var selectedRep = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E50C18EF-7F41-4426-904B-D1A3FAEE898F"}
 */
var statusFilter = 'Open';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F721C811-CAE5-40C3-83AE-5038E99A2310"}
 */
var stageFilter = i18n.getI18NMessage("avanti.lbl.all");

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F083E34C-AF9E-4D92-8FC5-82D44773107D"}
 */
var currentView = globals.avSales_CRM_MyOpportunities;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"07692CDD-891B-49B0-B989-DD0BF42C14A0",variableType:93}
 */
var baseDate = new Date();

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"822CA46B-E7D2-467D-A47D-88896C080589"}
 */
var expectedCloseDateBy = globals.avSales_CRM_SalesRep;

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"1D24FD60-785E-4CE9-8B2B-E064DDAE06BA"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	
	elements.base_date.format = globals.avBase_dateFormat;
	// On first show, reload the base date from employee settings.
	if(firstShow) {
		
		/** @type{JSFoundSet<db:/avanti/sys_employee_settings>} */
		var employee_setting_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_settings')
		if(employee_setting_fs.find() || employee_setting_fs.find()) {
			employee_setting_fs.empl_id = globals.avBase_employeeUUID
			employee_setting_fs.setting_key = 'avanti.opportunityView.baseDate'
			if(employee_setting_fs.search() > 0) {
				if(employee_setting_fs.setting_value != null) {
					baseDate = new Date(employee_setting_fs.setting_value)
				}
			}
		}
	}
	
	// GD - 2013-06-19: Getting an error if the employee has no base date; defaulting to now
	if (!baseDate)
	{
		baseDate = new Date();
	}
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard")
	{
		baseDate = new Date(); // Always set it to today
		expectedCloseDateBy = null;
		selectedRep = null;
		stageFilter = null;
		statusFilter = null;
		
		elements.managed_sales_reps.visible = false
		elements.managed_sales_reps_lbl.visible = false
	}
	else
	{
		loadStages()
	}
	
	// set to edit mode.
	var result =  _super.onShowForm(firstShow, event);
	applyFilters(firstShow);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * @param readonly
 *
 * @properties={typeid:24,uuid:"EFFBD3E6-A15E-4486-8C23-09FD6239E551"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"77889DA3-349D-482D-9D8F-DA3C37FC201B"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	// Set globals and default view to not show managed sales reps.
	globals.avSales_opportunityView_baseDate = baseDate
	globals.avSales_opportunityView_expectedClose = expectedCloseDateBy
	elements.managed_sales_reps.visible = false
	elements.managed_sales_reps_lbl.visible = false
	
	//var opportunity_fs = forms.sa_opportunity_tbl.foundset.duplicateFoundSet()
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard")
	{
		
	}
	else
	{
		applyFilters(true);		
		forms.sa_opportunity_tbl.refreshUI()
		forms.sa_opportunity_tbl.onActionInitialSort(event)
	}
}

/**
 * @AllowToRunInFind
 * 
 * Apply foundset filters
 * 
 * @param bReApplyFilters
 *
 * @properties={typeid:24,uuid:"C4ADE543-45C8-4D23-B98D-908494FA868B"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		// Set Expected Close Date by value list (always has Sales Rep)
		var vl_expectedCloseDate = new Array();
		
		// Remove any filters.
		forms.sa_opportunity_tbl.foundset.removeFoundSetFilterParam('statusFilter');
		forms.sa_opportunity_tbl.foundset.removeFoundSetFilterParam('stageFilter');
		forms.sa_opportunity_tbl.foundset.removeFoundSetFilterParam('employeeFilter');
		forms.sa_opportunity_tbl.foundset.removeFoundSetFilterParam('estimateFilter')
	
		// Add filter to status 
		if (statusFilter != null && statusFilter != i18n.getI18NMessage("avanti.lbl.all")) {
			forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_estimate_status','=',statusFilter,'statusFilter')
		}
		
		// Add filter to stage
		if (stageFilter != null && stageFilter != i18n.getI18NMessage("avanti.lbl.all")) {
			forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_current_orderstage_id','=',stageFilter,'stageFilter')
		}
			
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
						// If a sales rep, filter by salesper_id
						case globals.avSales_CRM_SalesRep:
							if(utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',employee_fs.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
							} else {
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						// If a sales manager, filter by salesper_id and add the 'Sales Manager' dropdown and make the managed sales rep drop down visible
						case globals.avSales_CRM_SalesManager:
							vl_expectedCloseDate.push(globals.avSales_CRM_SalesManager)
							if(currentView == globals.avSales_CRM_AllOpportunities){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if (currentView == globals.avSales_CRM_AllOpportunities && selectedRep != null && selectedRep != '') {
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',selectedRep,'employeeFilter')
							} else if (currentView == globals.avSales_CRM_AllOpportunities && selectedRep == null) {
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','sql:in',"select salesper_id from sys_employee_managed_reps where empl_id = '" + globals.avBase_employeeUUID + "'",'employeeFilter')
							} else if(currentView == globals.avSales_CRM_MyOpportunities && utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',employee_fs.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
							} else {
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						// If an estimator, filter by estimator_empl_id
						case globals.avSales_CRM_Estimator:
							forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_estimator_empl_id','=',globals.avBase_employeeUUID,'employeeFilter')
							break;
							// If an customer service rep, filter by csr_empl_id
						case globals.avSales_CRM_CustomerServiceRep:
							forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_csr_empl_id','=',globals.avBase_employeeUUID,'employeeFilter')
							break;
						// If an admin
						case globals.avSales_CRM_Administrator:
							vl_expectedCloseDate.push(globals.avSales_CRM_SalesManager)
							if(currentView == globals.avSales_CRM_AllOpportunities) {
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if(selectedRep != null && selectedRep != '') {
								forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',selectedRep,'employeeFilter')
							}
							break;
						default:
							// Filter by invalid uuid.
							forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							break;
				
					}
				} else {
					// Filter by invalid uuid.
					forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
				}
			}
		}
		
		// Set the value list items for the expected close date by option.
		// Add Sales Rep so it shows up second if there is a Sales Manager.
		vl_expectedCloseDate.push(globals.avSales_CRM_SalesRep)
		application.setValueListItems('vl_expectedCloseDateOptions',vl_expectedCloseDate)	
		
		// Filter by estimate
		forms.sa_opportunity_tbl.foundset.addFoundSetFilterParam('ordh_document_type','=','EST','estimateFilter')
		forms.sa_opportunity_tbl.foundset.loadAllRecords();
		forms.sa_opportunity_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.sa_opportunity_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		
		// Reload the table and run initial sort on it.
		//forms.sa_opportunity_tbl.foundset.loadRecords(opportunity_fs)
	}
}

/**
 * Set the base default date for an employee.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"8C808EF8-494F-479B-B279-F017C696FDE5"}
 * @AllowToRunInFind
 */
function onActionSetDefaultDate(event) {
	var today = new Date()
	var newBaseDate
	
	// Check if it's the same date and if so, then set new base date to null.
	if(baseDate.toDateString() == today.toDateString() ) {
		newBaseDate = null
	} else {
		newBaseDate = baseDate
	}
	
	// Get employee setting to overwrite the new value.
	/** @type{JSFoundSet<db:/avanti/sys_employee_settings>} */
	var employee_setting_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_settings')
	if(employee_setting_fs.find() || employee_setting_fs.find()) {
		employee_setting_fs.empl_id = globals.avBase_employeeUUID
		employee_setting_fs.setting_key = 'avanti.opportunityView.baseDate'
		if(employee_setting_fs.search() > 0) {
			employee_setting_fs.setting_value = newBaseDate 
		} else {
			var employee_setting_record = employee_setting_fs.getRecord(employee_setting_fs.newRecord())
			if (employee_setting_record.sys_employee_setting_id == null) employee_setting_record.sys_employee_setting_id = application.getUUID(); // prevent null error as PK is not always set correctly
			employee_setting_record.empl_id = globals.avBase_employeeUUID
			employee_setting_record.setting_key = 'avanti.opportunityView.baseDate'
			employee_setting_record.setting_value = newBaseDate
		}
	}
	databaseManager.saveData(employee_setting_fs)
	
}

/**
 * Perform the element default action to run initial sort
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"17DC98D0-ABE7-4726-8726-03C70E5934C8"}
 */
function onActionSort(event) {
	forms.sa_opportunity_tbl.onActionInitialSort(event)
}

/**
 * 
 * Get stages to fill in value list for Stage drop down.
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"EECB5680-FBC8-410D-BC44-98DF8439D972"}
 */
function loadStages() {
	var vlFilterRealValues = new Array();
	var vlFilterDisplayValues = new Array();
	
	/** @type{JSFoundSet<db:/avanti/sa_order_stage>} */
	var order_stage_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_stage')
	if(order_stage_fs.find() || order_stage_fs.find()) {
		order_stage_fs.org_id = globals.org_id 
		if(order_stage_fs.search() > 0) {
			order_stage_fs.sort('sequence_nr asc')
			for (var order_idx = 1; order_idx <= order_stage_fs.getSize(); order_idx++) {
				vlFilterDisplayValues.push(order_stage_fs.getRecord(order_idx).orderstage_desc)
				vlFilterRealValues.push(order_stage_fs.getRecord(order_idx).orderstage_id)
			}
		}
	}

	vlFilterDisplayValues.unshift(i18n.getI18NMessage("avanti.lbl.all")) 
	vlFilterRealValues.unshift(i18n.getI18NMessage("avanti.lbl.all"))
	
	application.setValueListItems('vl_EstimateStage', vlFilterDisplayValues, vlFilterRealValues);
	
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"3ADD6DBD-A923-4646-96E2-AD04432055C6"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView != "Dashboard")
	{
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					if(employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_SalesManager || employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_Administrator) {
						currentView = globals.avSales_CRM_AllOpportunities
						expectedCloseDateBy = globals.avSales_CRM_SalesManager
					}
				}
			}
		}
	}
	
	return _super.onLoad(event)
}
