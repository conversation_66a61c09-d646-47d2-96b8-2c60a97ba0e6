/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"115C832C-BD0E-4E52-97D9-4ADE6237B64D",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"69523FDF-F670-4693-B62A-BCA421C8B31D"}
 */
function onReady() {
    _gridReady = 1;
}



/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"3BA9B3C9-76AD-429B-97DE-7037A10A0990"}
 * @AllowToRunInFind
 */
function onShowForm(_firstShow, _event) {
	
	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}
	
	 //Set Default Warehouse for User
	 globals.avBase_selectedPPWarehouseUUID = globals.avBase_employeeDefaultWarehouse;
	 //Set the splitter
	 forms.in_item_bo_flush_dtl.elements.split_0.setDividerLocation(400);
	 
	 refreshUI();
	 
	 return _super.onShowForm(_firstShow, _event);
}

/**
 * @properties={typeid:24,uuid:"E8602792-C071-4B76-9B18-A8FABB8E732B"}
 */
function refreshUI() {
	forms.in_item_bo_flush_inventory_stats.controller.readOnly = false;
	
	if (globals.avBase_orgIsSingleWarehouse) {
		forms.in_item_bo_flush_inventory_stats.elements.avBase_selectedPPWarehouseUUID.visible = false;
	} 
	else {
		forms.in_item_bo_flush_inventory_stats.elements.avBase_selectedPPWarehouseUUID.visible = true;
		forms.in_item_bo_flush_inventory_stats.elements.avBase_selectedPPWarehouseUUID.enabled = true;
	}

	forms.svy_nav_fr_buttonbar_browser.elements.btn_cancel.enabled = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_save.enabled = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_new.enabled = false;
}


/**
 * @param [event]
 * @properties={typeid:24,uuid:"C7B8FE1F-307D-43B4-AB91-F9DA5D64670D"}
 */
function showBackorderSelectionsDialog(event) {
	// Check to ensure a valid user is logged in and not 'sysadmin'
	if (globals.avBase_employeeUUID == null) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.notification"),
			i18n.getI18NMessage("avanti.dialog.plannedpurchase_invalidemployee_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"));
	}
	// need differemt dlg dims for smart client so i dont have to resize 
	else if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
		globals.DIALOGS.showFormInModalDialog(forms.in_item_bo_flush_selection_dialog, -1, -1, 720, 665, i18n.getI18NMessage("avanti.dialog.backorderFlushSelections_title"), true, false, "dlgBOFlushSelection", true);
	}
	else {
		globals.DIALOGS.showFormInModalDialog(forms.in_item_bo_flush_selection_dialog, -1, -1, 700, 612, i18n.getI18NMessage("avanti.dialog.backorderFlushSelections_title"), true, false, "dlgBOFlushSelection", true);
	}
}
