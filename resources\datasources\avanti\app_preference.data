columns:[
{
name:"pref_id",
type:"[1,36,0]"
},
{
name:"deletion_date",
type:"[93,27,7]"
},
{
name:"modification_date",
type:"[93,27,7]"
},
{
name:"org_licensing_grp_id",
type:"[1,36,0]"
},
{
name:"pref_active_flag",
type:"[4,10,0]"
},
{
name:"pref_data_type",
type:"[-9,1,0]"
},
{
name:"pref_default_value",
type:"[-9,500,0]"
},
{
name:"pref_desc",
type:"[-9,128,0]"
},
{
name:"pref_detail_info",
type:"[-9,1073741823,0]"
},
{
name:"pref_display_type",
type:"[-9,16,0]"
},
{
name:"pref_format_string",
type:"[-9,50,0]"
},
{
name:"pref_number",
type:"[4,10,0]"
},
{
name:"pref_valuelist",
type:"[-9,256,0]"
},
{
name:"pref_valuelist_type",
type:"[-9,1,0]"
},
{
name:"prefnode_id",
type:"[1,36,0]"
},
{
name:"sequence_icon",
type:"[-9,128,0]"
},
{
name:"sequence_nr",
type:"[4,10,0]"
}
],
rows:[
[
"4FEDBD0C-0C0A-4B38-9C19-00D0CDA4987A",
null,
null,
null,
1,
"I",
"Sales Order",
"Invoice Report Grouping",
"Group the Standard Invoice form/report by either Sales Order or by unique Customer PO from the sales orders included on the invoice.",
"COMBO BOX",
null,
304,
"avReport_InvoiceOrder",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
122
],
[
"9CBB9421-2269-4154-A96D-016D79711AD2",
null,
"2022-08-25T17:21:39.942Z",
null,
1,
"T",
"WARN",
"Select Minimum order charge behavior",
"This system preference determines the behavior within Slingshot when a Sales Order with a subtotal less than the Minimum Order charge for that customer occurs. \
1. Use Minimum Order functionality, that only displays a warning message when the Sales Order is released (Default).\
2. Use Minimum Order functionality that displays a warning message and charges on the invoice a minimum order charge, when applicable, based on the Minimum order charge set on the customer record.",
"COMBO BOX",
null,
348,
"vl_minOrdChargeBehavior",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
155
],
[
"7EA16DE7-BC2D-41F3-8BF0-02C15887BEA5",
null,
null,
null,
1,
"I",
"0",
"Enable Billable Change Order breakout on Invoice",
"Option \"yes\" will turn on billable change orders. Only \"Authors Alterations\" will be billable \
and show up on an invoice as a separate line. All other types of change orders will be \
non-billable and will not affect the price of the sales order. \
The breakout Billable change Order option can be turned off by Customers at the Customers Record.",
"COMBO BOX",
null,
324,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
138
],
[
"E2D3887E-D3D5-4253-8CAF-0444B8AABC39",
null,
null,
null,
1,
"I",
"0",
"Invoice COGS based on shipment cost for non-production items",
"When 'No' is selected, the invoice cost is based on weighed average cost for non-production items.\
When 'Yes' is selected, the invoice cost is based on the shipment cost for non-production items.",
"COMBO BOX",
null,
410,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"C7F89602-9FF0-4E7B-9E92-04C2351DA237",
null,
"2017-09-28T19:51:48.871Z",
null,
1,
"I",
"1",
"Press Details - Setup Time",
"Displays the press setup time (Grand Format only)",
"COMBO BOX",
null,
178,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
26
],
[
"E8549EA5-EE91-4A2A-BF35-04F47F3D4A14",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
58,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"B3C1CD56-F5C9-455E-9B33-0500F85D1CB6",
null,
"2017-03-09T14:25:08.462Z",
null,
1,
"T",
null,
"Contract Order Charge Service Item",
"Link the inventory service item associated with order charges. This item will appear on sales order invoices when order charges are billed.",
"TYPE AHEAD",
null,
210,
"avItem_ServiceItems",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
63
],
[
"9F73E5D8-EBD0-4AB4-8103-05A048BA1F00",
null,
"2017-09-28T19:51:48.895Z",
null,
1,
"I",
"1",
"Paper Details: Show Press Sheet Size",
"Displays the \"Press Sheet Size\" field in the Paper details.",
"TEXT FIELD",
null,
89,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
38
],
[
"BF2D9C7B-9E5F-4095-941A-064048E2289F",
null,
null,
null,
1,
"I",
"Yes",
"Show Ship-To Customer Name",
null,
null,
null,
249,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
86
],
[
"CE3374AB-50C1-4C43-A278-06BDF6C8C1C9",
null,
null,
null,
null,
"T",
null,
"Quote Letter Opening Paragraph - Quote Letter 2",
"Information entered in this preference will print before the body of the quote letter.\
Any spaces left before or after the text will also reflect on the form.\
\
Note: you can use the following tokens in the text to print dynamic data.\
\
{est_contact_first_name}\
{est_contact_last_name}\
{est_cust_email_bus}\
{est_salesper_name}\
{est_salesper_email}\
{est_csr_first_name}\
{est_csr_last_name}\
\
Example: \
\
Thank you for allowing us to quote on your printing requirements.  Our prices are as follows:\
",
"TEXT AREA",
null,
123,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
21
],
[
"4C7D20CA-54B0-45D9-B30C-070E5086F1BB",
null,
"2017-11-06T17:13:13.953Z",
null,
1,
"I",
"1",
"Print Total Quantity Shipped To Date on the Packing Slip",
"When 'Yes' is selected, the total quantity shipped to date will print on the packing slip.",
"CHECK BOX",
null,
130,
"vl_YesNo",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"BF527F2D-D526-43F3-81C6-0718D3F8BAC0",
null,
null,
null,
1,
"T",
"YOUR ACCOUNT IS PAST DUE! WE WOULD APPRECIATE YOUR PROMPT PAYMENT.",
"Standard Past Due Notice Message",
"The standard text that prints on the Past Due Notice report 's footer.",
"TEXT AREA",
null,
141,
null,
null,
"8F6869F7-C964-4351-B638-96F29E44B656",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
37
],
[
"A83577EE-80B7-464A-B6AD-07ED06FC3636",
null,
null,
null,
1,
"I",
"0",
"Populated the Ship-To Contact Phone and Contact Email for Customer Pick-up Ship Method",
"When \u2018Yes' is selected, the Ship-To Contact Phone and Contact Email for are printed on the Standard Packing Slip for ship method \u2018Customer Pick-up.",
null,
null,
418,
"vl_YesNo",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
184
],
[
"********-D48A-4224-823F-098C1DA0BDA0",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
56,
null,
null,
"E7DC3CAF-2A90-42C4-8062-82AE6760682D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"5B4E95FE-3447-475C-8DB4-0A33F6E75624",
null,
"2018-03-28T11:15:17.026Z",
null,
1,
"N",
"4",
"Maximum Folds based on Minimum Paper Caliper",
"This is the maximum number of folds that can be done for minimum paper caliper. \r\
For example, if you have a minimum paper caliper of 0.1, then a sheet of paper that is greater than\r\
this caliper can only be folded the Maximum number specified here.",
"TEXT FIELD",
"#0",
4,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
46
],
[
"C45EE642-DC1C-4390-9B1D-0A776B1C4839",
null,
"2018-03-28T11:15:16.950Z",
null,
1,
null,
"0",
"Grand Format dimensions",
"The dimensions of a print section may be specified by either the width and length of the item (default) \
or the height and width of the item. \
This will apply only to sections where the work type specifies a Grand Format press.)",
"COMBO BOX",
null,
139,
"vl_grandFormatDimensions",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
13
],
[
"9311E78B-7F95-4770-AFE3-0A81194A04FB",
null,
"2017-02-15T15:44:03.371Z",
null,
1,
"I",
"0",
"Hide cost / price details in diff summary on Change Order Internal report",
"Controls whether or not the detailed diff cost /price informartion is displayed on the internal change order report.",
"COMBO BOX",
null,
145,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"4EBE1BC7-F184-4008-87EF-0A858C471E97",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Credit Index",
"Enter the default credit index to use for the \u201cAccount Distribution\u201d Chargeback File detail level when there is no Chargeback Credit Index defined at the Work Type or Estimating Standardlevels for production sales order lines and at the Item Class or Item levels for item sales order lines.",
null,
null,
284,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
104
],
[
"614EE247-70A0-43EF-8C3C-0A9A1C183B60",
null,
"2016-10-06T19:37:19.466Z",
null,
1,
"I",
"0",
"Display Service Rep Info - Quote Letter 2",
"Displays the Service Rep name, phone number, ext, and email address on Quote Letter 2",
"CHECK BOX",
null,
163,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
46
],
[
"54DB2D72-D641-47CE-ADA9-0ADAD0A241D3",
null,
"2019-06-06T11:20:16.861Z",
null,
1,
"I",
"0",
"Automatically create credit notes from cash receipt overpayments",
"Yes, gives a user the ability, with privilege\u2019s, to automatically create a unapplied credit note from cash receipt overpayments. \
You must define the additional charge service item for the refund credit note, preference 307.",
null,
null,
306,
"vl_YesNo",
"S",
"1E0879E6-E410-48AD-B8A5-5ABF3740B20B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
124
],
[
"AA07C24E-3E76-41EF-85FA-0B0EDD821552",
null,
null,
null,
1,
"I",
null,
"Include Comment on Picking Slip",
"Include/Exclude Comment on the Picking Slip",
"TEXT FIELD",
null,
33,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"E134086C-78AA-4F8D-8F60-0B1A7EF1C9D7",
null,
"2017-09-28T19:51:48.918Z",
null,
1,
"I",
"1",
"Show Shipping Info",
"Will show the multi-ship locations along with the shipping method for each location.",
"COMBO BOX",
null,
49,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
50
],
[
"5890C8CF-15D5-484C-A944-0C893E87D20F",
null,
"2018-08-22T22:52:45.239Z",
"899250C7-A418-4B87-BF10-3C536EEC04DA",
1,
"I",
"0",
"Lead time buffer, in days, to cover additional time for processing sales orders",
"The lead time buffer is the additional time required to perform company specific work flow activities, such as,\
quality checks. ",
"TEXT FIELD",
"##0",
274,
null,
null,
"29BE48E4-7A86-4347-8608-9C58871CC38C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
101
],
[
"FE29E9B2-FD2D-48F4-AD21-0CAFE75D8A8D",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Allow Box Weight to be defined in Box Count dialog?",
"Whether or not the box weight can be defined in the Box Count dialog.",
"COMBO BOX",
null,
327,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"DC4F044E-DAF9-4427-A7ED-0D08D0DF1039",
null,
"2018-03-28T11:15:16.979Z",
null,
1,
"I",
"1",
"Include Spoils in Click Price calculation",
"This allows you to include or exclude the spoils in the click price calculation. \r\
This does not affect click cost, which will always include spoils. \r\
Also does not affect runtime, or click price determined by grouped sections; all will include spoils.",
"TEXT FIELD",
null,
75,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
26
],
[
"C70FDBF1-4F6B-4AB5-AB0B-0E1E1F59C058",
null,
null,
null,
1,
"T",
"Printing Estimate",
"Standard Quote Title",
"This is a standard quote title that will be used on the new type 2 quote template.",
"TEXT FIELD",
null,
80,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"9807B866-0B67-47DC-BDFC-0F878526AFE5",
null,
"2017-09-28T19:51:48.861Z",
null,
1,
"I",
"1",
"Press Details - # Across",
"Display # across info (Grand Format only)",
"COMBO BOX",
null,
184,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
21
],
[
"2F7810BF-9FB4-4FFE-A56D-0FBF8C227C49",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Debit Index",
"Enter the default debit index to use for the \u201cAccount Distribution\u201d Chargeback File detail level when there is no Chargeback Debit Index defined at the Work Type or Estimating Standardlevels for production sales order lines and at the Item Class or Item levels for item sales order lines.",
null,
null,
286,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
106
],
[
"49B1F677-7778-4BF5-8C80-11129A0F96CA",
null,
"2024-09-19T18:16:20.133Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"0",
"Create Billing Code Summary On Invoices and Credit Notes",
"When 'Yes', a new tab called 'Billing Code Detail' will appear for Invoices and Credit Notes. Billing codes are assigned on Estimating Standards and\
are related to Inventory Codes in the organizations accounting system. ",
"COMBO BOX",
null,
400,
"vl_YesNo",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"6FFC107D-BB8B-41DA-AA46-1250D86960E1",
null,
"2016-11-04T15:34:34.821Z",
null,
1,
"T",
"no",
"Show Buyer email address and phone number",
"Show the email address and phone number from the employee record of the buyer",
"COMBO BOX",
null,
167,
"vl_YesNo",
"S",
"55F19C5D-5E7D-4DEA-A2F7-DC5F3F48A76B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
48
],
[
"A7B7C109-DE0A-417B-B423-15E308A59973",
null,
"2023-05-08T17:57:57.393Z",
null,
1,
"I",
null,
"Number of records in Chargeback Batch to trigger use of additional processor.",
null,
"TEXT FIELD",
null,
368,
null,
null,
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
162
],
[
"B2B7B069-7DAC-451B-93DE-173772190DEF",
null,
"2018-03-28T11:15:16.927Z",
null,
1,
"I",
"0",
"Enable mail details tab",
"Select if you want to enable the maildetails tab to capture project details related to mailing/postage for your sales orders.",
"CHECK BOX",
null,
221,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"DE19E61A-2446-4CE8-A7D4-176FE2DB9EE0",
null,
"2025-06-12T16:34:50.378Z",
null,
1,
"I",
"0",
"Use Enhanced Postage Tax Rules",
"The preference \u201cDetermines the tax status of additional charges and postage markup.\
\
-For example, the Standard tax setting is non-taxable.  The new field is marked \u201cyes.\u201d The result will be no tax on postage but a tax on markup and or additional charges.\
\
-For example, the Standard tax setting is taxable.  The new field is marked \u201cno.\u201d The result will be a tax on postage but no tax on markup and or additional charges.\
\
-For example, the Standard tax setting is non-taxable. The new field is marked \u201cno.\u201d There will be no tax on postage and no tax on markup and/or additional charges.\
\
-For example, the Standard tax setting is Customer/Ship to. The marking of the new field will determine the taxing of additional charges and markup. The standard taxing is determined by Customer or ship-to address.",
"COMBO BOX",
null,
415,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"F0F7064E-24C1-4515-AE74-1854853BD1C1",
null,
"2016-09-20T15:32:54.459Z",
null,
1,
"N",
"80",
"Number of hours in a regular pay period",
null,
"TEXT FIELD",
null,
151,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"794D051C-C3F0-4E4E-82FD-18B49AC99413",
null,
"2021-08-18T15:42:12.779Z",
null,
1,
"I",
"0",
"Show Customer info",
"Shows the Customer name on the Job Ticket.\
\
The system preference settings will be \u201cYes\u201d (show customer info), \u201cNo\u201d (show contact information) or \u201cNone\u201d (show no customer/contact information and delete the white bar from the reports).",
"COMBO BOX",
null,
39,
"vl_YesNoNone",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
60
],
[
"7EACBDE9-5776-4B94-A8FD-1A4962A8A127",
null,
null,
null,
1,
"I",
"0",
"Print Sales Order Number on 4x4 Shipping Labels ",
null,
"TEXT FIELD",
null,
373,
"vl_YesNo",
"S",
"685683E9-65D2-4137-9D85-6DF00BD5FED7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
166
],
[
"4C3BE80B-55EA-4147-8632-1A58EA4364DD",
null,
"2018-03-28T11:15:16.972Z",
null,
1,
"I",
null,
"Default number of Transit Days",
"Transit days are used to calculate the expected ship date based on the date due at customer.  The transit days \r\
can be set at the customer address level and the shipping method.  If nothing has been set the system will use the value\r\
entered in this system preference",
"TEXT FIELD",
null,
94,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
23
],
[
"C6411A33-8C36-45CE-9616-1AF09E8ABAF2",
null,
"2019-05-30T11:54:15.634Z",
null,
1,
"T",
null,
"Additional Charge Service Item for Auto Created credit notes.",
"If system preference #306 is activated, you must define the additional charge service item that will be used when\
automatically creating the refund credit note.",
null,
null,
307,
"avItem_ServiceItems",
"S",
"1E0879E6-E410-48AD-B8A5-5ABF3740B20B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
125
],
[
"9E8DD940-64E6-405C-A58A-1B2D7E9D11C9",
null,
"2024-07-18T19:47:33.779Z",
null,
1,
"I",
"0",
"Handling of \u201cImporting\u201d orders",
null,
"COMBO BOX",
null,
397,
"vl_handlingImportingOrders",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"FC385AA8-F3F4-42B0-B911-1B415AB91C77",
null,
"2018-03-28T11:15:16.977Z",
null,
1,
"I",
"0",
"Display Cost/Piece and Price/Piece",
"Shows cost/piece and price/piece pricing information on the section summary screen (below the tasks),\r\
and also shows cost/piece pricing information in the task dialogs. Turn this on if you use cost/piece pricing\r\
and have a need to see it at the estimate/order level.",
"TEXT FIELD",
null,
76,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
25
],
[
"9C036140-F74C-49B8-92DE-1B95691384DB",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
55,
null,
null,
"F50163B3-CE82-44E3-9225-870496144011",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"4CE70F7A-6487-4DAA-AD37-1C5F8924B609",
null,
null,
null,
1,
"I",
"2500",
"Max number of rows in the table",
"Max number of rows to be loaded in the Invoice Detail View table.",
"TEXT FIELD",
null,
341,
null,
null,
"2359C149-5B3A-4BAC-B1D0-E543547820A9",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
151
],
[
"DA0C8C8D-79EC-417B-B937-1CE925C2E014",
null,
null,
null,
1,
"T",
null,
"Contract Line Item Charge Service Item",
"Link the inventory service item associated with line item charges. This item will appear on sales order invoices when line item charges are billed.",
"TYPE AHEAD",
null,
211,
"avItem_ServiceItems",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
64
],
[
"E2B6B297-E348-4E56-BC49-1D518F3AC233",
null,
null,
null,
1,
"I",
"0",
"Track Staging Location",
null,
"COMBO BOX",
null,
398,
"vl_YesNo",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"9D2A091A-ADAC-41C0-90A6-1D6387564929",
null,
null,
null,
1,
"I",
"0",
"Allow \u201cCharge partial sheets as full sheets\u201d as a default setting in Estimates for substrates using partial sheet functionality",
"\u201cCharge partial sheets as full sheets\u201d unchecked (default) \u2013 When an estimate is created, the system will \
automatically calculate partial sheets. The user must manually enable the check box  \u201cCharge partial \
sheets as full sheets\u201d if that is the expected result.\
\
\u201cCharge partial sheets as full sheets\u201d checked - When an estimate is created, the system will automatically \
calculate full sheets . The user must manually disable the check box  \u201cCharge partial sheets as full sheets\u201d \
if charging for partial sheets is the expected result.\
",
"TEXT FIELD",
null,
339,
"vl_ChargePartialSheetsAsFullSheets",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
149
],
[
"BE1DAB77-2A33-4C4B-85E7-1D6B878A0DC0",
null,
"2017-09-28T19:51:48.899Z",
null,
1,
"I",
"1",
"Press Details - Show #Out",
"Display the \"# Out\" field in the Press results.",
"TEXT FIELD",
null,
87,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
40
],
[
"30C5EE56-4C21-4AF7-9EE2-1D9DE2AE3802",
null,
"2017-02-15T15:44:03.327Z",
null,
1,
"T",
"Open",
"New Sales Order default status",
"Provides the ability to set the default sales order status on New sales orders.",
"TEXT FIELD",
null,
73,
"vl_OrderStatusNew_Values",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"78E7F03A-DE49-4600-8797-1E4BB3273B8B",
null,
"2018-03-28T11:15:17.020Z",
null,
1,
"N",
".125",
"Minimum Side Gutter",
"The minimum size of the side gutters to use for the imposition.",
"TEXT FIELD",
".0000",
7,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
43
],
[
"B351B581-9E68-4A35-BE43-1E6E53A2F7E8",
null,
null,
null,
1,
"I",
"0",
"Suppress Printing Sales Tax and Shipping Cost",
"By Default, Total Sales Tax and Shipping Charges print on the standard quote form. If 'yes' is selected, these fields will\r\
be suppressed and the sub-total will become the total estimate price.",
"TEXT FIELD",
null,
78,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"674A3EFE-E416-4DD8-90A6-1E84DBF37855",
null,
null,
null,
1,
"I",
"0",
"Schedule employees",
null,
"COMBO BOX",
null,
251,
"vl_YesNo",
"S",
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
87
],
[
"CF2B2FF6-D455-4679-8BE8-1EDA8853D353",
null,
null,
null,
1,
"I",
"0",
"Allow default offset accounts to be used",
"When \"Yes\", under System Setup// Inventory//Warehouse, the G/L Accounts by Item Class tab allows you to create \
default GL accounts for Inventory transaction when a user has privileges. \"No\" this functionality is hidden from the \
G/L Accounts by item Class.",
null,
null,
295,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
111
],
[
"46518A46-B409-4E2A-BAF8-1F3BE567F4C8",
null,
null,
null,
1,
"I",
"0",
"Allow Refunding a Credit Note",
"Yes, gives a users the ability with privilege\u2019s to refund credit notes created without an invoice. \
This functionality is on the credit note screen and the table view for credit notes. \
Also the ability to add a note is available.",
null,
null,
305,
"vl_YesNo",
"S",
"2E12FFA9-D6E8-4F23-9EAA-02FEA6673F4D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
123
],
[
"14528A40-7DAE-4BAE-824C-210F20D6F72E",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Automatically validate the shipping address via USPS",
"Automatically validate the shipping address on a shipment, using the USPS Address Standardization Web Tool, when the shipment is confirmed or when the \u2018Get Packing Label\u2019 button is clicked and also allow the user to manually initiate the address validation.",
"COMBO BOX",
null,
201,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"9D02BA12-80CE-484E-A164-2145DA170321",
null,
"2024-07-12T17:49:30.505Z",
null,
1,
"I",
"0",
"Number of minutes to round time after Shift Starts",
"Number of minutes to round time after Shift Starts",
null,
null,
394,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
178
],
[
"8734AB4B-DF6E-43F2-9AC4-222B201827EB",
null,
"2017-09-28T19:51:48.926Z",
null,
1,
"I",
"0",
"Show Job x of y",
"Shows Job x of y on the Job Ticket.",
"COMBO BOX",
null,
45,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
54
],
[
"832CC99A-7F7A-422A-9B32-232D6ADEEA40",
null,
"2019-02-25T16:18:31.815Z",
null,
1,
"I",
"0",
"Distribute bottom Line Mark up to the tasks",
"\u2022 Yes - will enable new functionality allowing B/L mark ups to be distributed back to the tasks.\
This will also turn off current invoicing behavior which distributes B/L mark up for purposes of the G/L distribution \
and cost summary.\
The preference will add a flag that will identify existing orders and invoices created prior to the system preference \
being turned on, and change the behavior for future invoices, turning off this existing invoicing distribution behavior.\
\u2022 No (default) -  will maintain default behavior where the B/L mark up is NOT distributed to the tasks.\
",
"CHECK BOX",
null,
298,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
114
],
[
"39A8336A-D2E7-4809-B9B3-239D2A3E3B47",
null,
"2018-03-28T11:15:38.180Z",
null,
1,
"I",
"0",
"Envelope - Show envelope window die table in section details",
"Show envelope window die table in die/cylinder tab of section details",
null,
null,
259,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"8AE1039E-40F7-4739-9BD3-23B2882B4020",
null,
"2017-09-28T19:51:48.857Z",
null,
1,
"I",
"1",
"Substrate - # Sheets Ordered",
"Display # sheets ordered info (Grand Format only)",
"COMBO BOX",
null,
186,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
19
],
[
"5F6D5FD6-D017-40D8-8E71-23D87AA4775A",
null,
"2017-09-28T19:51:48.922Z",
null,
1,
"I",
"0",
"Show Sales Rep Name",
"Show the Sales Rep Name on the Job Ticket.",
"COMBO BOX",
null,
47,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
52
],
[
"2B7BE1AB-5A40-4DA0-8AD1-24CB2601E769",
null,
null,
null,
1,
"I",
"0",
"Use Operation Code as barcode on Operation Scan Sheet Report",
"Use the Operation Code (Department + Category + Operation) instead of the Description on the Operation Scan Sheet Report and include it in the \u201cAdd Other Operation\u201d field in Shop Floor. \
Setting this toggle to \u201cYes\u201d will resolve an issue with barcode scanning on some scanners caused by long concatenated descriptions. Default is \u201cNo\u201d.",
"COMBO BOX",
null,
381,
"vl_YesNo",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
172
],
[
"001C9310-7325-4F23-9DE0-24D16749DF9A",
null,
null,
null,
1,
"I",
"0",
"Use # Sheets instead of # Pages",
"If the section is two sided (where \"y\" in 'Colors: x/y' is greater than zero), \r\
show the number of pages divided by 2 and the word \"sheets\" instead of pages. \r\
For example, if the number of pages in the section is \"32\" and the value for 'Colors' is 4/1, \r\
show \"16 sheets\" on the quote letter instead of \"32 pages\". \r\
\r\
If the value for 'Pages' is an odd number, the number of \"sheets\" to show on the quote letter \r\
is rounded up to the nearest integer. For example, \"33\" pages is \"17 sheets\", \"9\" pages is \"5 sheets\", etc.",
"TEXT FIELD",
null,
99,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"745B3097-62C3-4450-AF0D-25262C8CEA0D",
null,
null,
null,
null,
"T",
null,
"Quote Letter Closing Paragraph - Quote Letter 2",
"Information in the preference will print after the body/details are printed.\
Any spaces left before or after the text will also reflect on the form.\
\
Note: you can use the following tokens in the text to print dynamic data.\
\
{SalesRep_Name}\
{CSR_Name}\
{EstimatorName}\
{CSR_Code}\
{est_good_until_date}\
{est_contact_first_name}\
{est_contact_last_name}\
{est_ship_method}\
{est_csr_first_name}\
{est_csr_last_name}\
{emp_csr_phone}\
{est_salesper_phone}\
\
\
Example:\
\
I hope this quotation meets with your satisfaction and approval. If you have any questions please do not hesitate to contact me. \
\
Yours very truly,\
Avanti Computer Systems Ltd.\
\
\
{SalesRep_Name}\
CSR: {CSR_Name}",
"TEXT AREA",
null,
124,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
22
],
[
"E993CAD6-74C7-4184-9EDB-260577F5141D",
null,
null,
null,
1,
"I",
"0",
"Select \"Label\" check box by default for all receipt transactions in the mobile application so that labels always print.",
"Yes - \u202fTurns on  \"Label\" check box by default for all receipt transactions in the mobile application so that labels always print. \
\
No (default setting) - Do not select \"Label\" check box for all receipt transactions in the mobile application. ",
"COMBO BOX",
null,
329,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
142
],
[
"F6596571-71C9-429C-9BA6-26698FD36E7E",
null,
"2017-09-28T19:51:48.897Z",
null,
1,
"I",
"1",
"Press Details - Show Layout",
"Display the \"Layout: field in the Press results.",
"TEXT FIELD",
null,
88,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
39
],
[
"A500252D-BCC3-4588-B0FF-273E4AFA5CC7",
null,
null,
null,
1,
"I",
"2",
"Date Format",
"The default date format that will be used. This can be changed at the Organization level, and also for the User account.",
"TEXT FIELD",
null,
21,
"avBase_dateFormat",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"078825B1-0323-44B9-AA96-27B9A3261D30",
null,
"2017-10-04T21:57:52.267Z",
null,
1,
"I",
"0",
"Style 1: Show Prepress details",
"Show Prepress details  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
232,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"C8698CC5-0B96-4F18-83E1-28AC07038225",
null,
null,
null,
1,
"I",
"0",
"Show Finish",
"Will display the Finish in the description on the quote letter, like:\r\
<Description 1>, <Substrate Type>, <Finish>. For example: \u201c80# Futura Laser Digital Cover \u2013 19 x 13, Cover, Gloss\u201d\r\
* See separate preference to display the Substrate Type.",
"TEXT FIELD",
null,
98,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"549461C2-2E2B-4FA7-A63C-29BBEFEC8841",
null,
"2018-03-28T11:15:16.997Z",
null,
1,
"N",
".125",
"Default Fold Gap",
"Default fold gap used for the imposition.",
"TEXT FIELD",
".0000",
18,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
33
],
[
"0D52141E-7606-4F18-AF20-2AD955720D92",
null,
"2024-08-09T15:51:03.776Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"1",
"Use Purchase Receipts Register to post purchase receipts/cancelled receipts to accounting system",
"Selecting 'Yes' will use the Purchase Receipts Register program to post purchase receipts and cancelled receipts transactions to the selected accounting system.\
Selecting 'No' will enable the feature to post purchase receipts and cancelled receipts to accounting system based on 'Ready To Post' status.",
null,
null,
389,
"vl_YesNo",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"0E72AAF5-56B8-4452-8B74-2C11F1ACA903",
null,
"2017-09-28T19:51:48.881Z",
null,
1,
"I",
"0",
"Style 2: Dept Info - Hide Dept info",
"Hide the Dept info on the Job Ticket.\r\
Job Ticket Style 2 only.",
"TEXT FIELD",
null,
101,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
31
],
[
"B3F2A0DE-2622-40F4-A87E-2D47A2BBB56D",
null,
"2022-03-31T15:07:19.816Z",
null,
1,
"I",
"30",
"Estimate is Good For [x] days",
"The default number of days to use to calculate the estimate \u2018Good Until' Date. \
The estimate status will automatically change to \u201cExpired\u201d when the estimate is viewed,\
in Detail View, if the 'Good Until\u2019 date has passed.",
"TEXT FIELD",
"#0",
17,
null,
null,
"07A7E161-29E7-4497-8771-D77FC37F2D8A",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
17
],
[
"4873C8AB-15DA-44C8-8CAC-2F4F482EBFEB",
null,
null,
null,
1,
"I",
"0",
"New Client Age in Days",
"Used in the commissions structure to determine if it should be applied to a estimate/order based on customer created date.",
"TEXT FIELD",
null,
70,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"A2CB14F1-C016-4240-A82C-2F90CE82C248",
null,
"2018-03-28T11:15:16.924Z",
null,
1,
"I",
"0",
"Order Type Required",
"If set to Yes, Order Type Required would require a selection be made in the Order Type field of a Sales Order \
before it can be Released. If a Selection is not made a warning would appear.",
null,
null,
243,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"807E799B-BB5A-4AD9-984F-2F9186A82B6B",
null,
"2017-11-28T17:12:33.186Z",
null,
1,
"I",
"1",
"Style 2: Show Sales Order Rush",
"Show the sales order rush info (style 2 only)",
"COMBO BOX",
null,
247,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
84
],
[
"771216DC-CAC9-47A6-977F-3068FD122E5D",
null,
"2018-03-29T15:10:48.654Z",
null,
1,
"T",
"I",
"Postage base units",
null,
"TEXT FIELD",
null,
260,
"avBaseUnits",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
91
],
[
"078533E5-4A34-4A03-9AE7-31F4A4A81082",
null,
"2017-09-08T20:27:01.965Z",
null,
1,
"T",
"P",
"Default Print Status When Printing An Invoice",
"The default setting for the Print Status in the report dialog when printing an invoice.",
"COMBO BOX",
null,
215,
"avInvoice_InvoiceStatusNoUpdate",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
74
],
[
"F19FB077-920F-4CB6-BAE9-3247D2BCD96A",
null,
"2024-08-09T15:51:03.776Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"1",
"Use Accounts Payable Register to post payable invoices to accounting system",
"Selecting 'Yes' will use the Acconts Payable Invoice Register program to post payable invoice transactions to the selected accounting system. \
Selecting 'No' will enable the feature to post payable invoices to accounting system based on 'Ready To Post' status.",
"COMBO BOX",
null,
388,
"vl_YesNo",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"8CA52B8A-D7A3-4E52-92CF-32577CC1F57E",
null,
"2018-05-10T15:09:27.376Z",
null,
1,
"T",
null,
"Default Operation to use for Job Cost Adjustments",
"Specify the default operation to use for job cost adjustment transactions. You will still be able to override the operation.",
null,
null,
265,
"vl_CostCenter",
"S",
"2C1E4D34-F258-42DE-85F3-50716F9C7FEB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
94
],
[
"F6593C50-7442-4EBD-AA08-3316FE0FD6EA",
null,
null,
null,
1,
"T",
null,
"Contract Receipt Charge Service Item",
"Link the inventory service item associated with receipt charges. This item will appear on contract invoices when receipt charges are billed.",
"TYPE AHEAD",
null,
213,
"avItem_ServiceItems",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
66
],
[
"F3FDB237-4E01-4916-B532-343D5BE21CCD",
null,
"2025-01-24T18:22:56.015Z",
null,
1,
"I",
null,
"Number of invoices in Batch Invoicing to trigger use of additional processor",
"The number of invoices that trigger the use of an additional processor for the Create Invoices function. \
Create Invoices starts a processor that runs in the background to create invoices, regardless of this preference. \
If there is a value for this preference it will be used to start additional processors if there are sufficient invoices. \
EG. If the preference value is 100 and there are 500 invoices that need to be created it will start 5 background processors, \
each to create 100 invoices.",
"TEXT FIELD",
null,
379,
null,
null,
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
171
],
[
"3A5B2FBC-916D-4C07-A8D2-34D628F450CE",
null,
"2017-11-06T17:13:13.950Z",
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
54,
null,
null,
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"D8660C3F-AB5F-4436-9DE4-34ED7E2E3A50",
null,
"2020-01-26T16:27:10.176Z",
null,
1,
"I",
"0",
"Send Invoice Costs to GP as a General Journal Entry",
"Creates 2 separate GP transactions for Slingshot Sales invoices. One Receivables Sales Invoice transaction and one if there are Costs/Finished Goods distributions (General Journal Entry transaction) ",
"COMBO BOX",
null,
275,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"1C6FBDD4-5C3C-4975-B867-353069964A43",
null,
null,
null,
1,
"I",
"One page per shipping location",
"Shipping Location Style",
"Select how the individual shipping locations will be displayed on the PO form when the \u2018Drop Ship From Supplier\u2019 option is used and there is more than one shipping location. The \u201cSummarized\u201d option will create a table on the PO form for each line item sorted by shipping method.",
"COMBO BOX",
null,
242,
"avReport_poStyle",
"S",
"55F19C5D-5E7D-4DEA-A2F7-DC5F3F48A76B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
80
],
[
"C155232E-1207-4004-9EC0-358A3ED86249",
null,
null,
null,
1,
"I",
null,
"Include Estimate Comment on the Sales Invoice",
"Include/Exclude Comment from printing on the Sales Invoice",
"TEXT FIELD",
null,
29,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"06DEB3F1-18D9-4158-93C6-3629FA898796",
null,
"2018-03-28T11:15:16.987Z",
null,
1,
"N",
"0.03",
"No auto trimming on any fold where waste less than a %",
"This is used in estimates/orders. \r\
If the waste from a layout on a sheet is less than or equal to this %, \r\
then the engine will not attempt to trim off the waste.",
"TEXT FIELD",
"globals.avBase_percentageFormat",
38,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
29
],
[
"0E4F1206-FD6C-4195-8C32-365B74D0E24D",
null,
"2018-03-28T11:15:16.922Z",
null,
1,
"I",
"0",
"Line Item Type Required",
"If set to Yes, Line Item Type Required would require a selection be made in the Line Item Type field of a Sales Order \
before it can be Released. If a selection is not made a warning would appear.",
null,
null,
244,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"736D31C5-EE7F-48E6-8A56-36D2FCAFCE84",
null,
null,
null,
1,
"T",
null,
"Internal Customer Name associated to Variance Cash Receipt",
"The customer name to be assigned to the variance cash receipt.",
"TYPE AHEAD",
null,
146,
"vl_Customers",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
42
],
[
"BEDE2284-5E51-465E-AB83-373126BFC736",
null,
null,
null,
1,
"I",
null,
"Include Comment on the Sales Invoice",
"Include/Exclude Comment on the Sales Invoice",
"TEXT FIELD",
null,
35,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"70063436-92A0-4218-A3C3-38C88B343430",
null,
"2016-12-17T17:41:53.086Z",
null,
1,
"T",
null,
"Standard Quote Message",
"Paste a block of text into the default value field. If there is a value, this information will appear at the bottom of the quote.",
"TEXT AREA",
null,
74,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"8925A421-DA96-4EC0-A345-393F411F860F",
null,
"2022-07-27T14:06:10.763Z",
null,
1,
"I",
"0",
"Add Department to the PO",
"Toggle to make the \"Department\" field available in the purchase order.",
"COMBO BOX",
null,
350,
"vl_YesNo",
"S",
"742AA30C-A420-4554-B841-F29070801890",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
155
],
[
"BCA391CD-1195-4944-A181-3945815637D0",
null,
"2016-10-19T00:45:48.220Z",
null,
1,
"T",
null,
"\"Vacation\" Exception Type",
null,
"TYPE AHEAD",
null,
154,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"1ACAD1B2-13A2-42A9-B0E7-3A2D59D7F290",
null,
null,
null,
1,
"I",
"0",
"Show/Use section number and operation code in both the job ticket barcodes and Shop Floor fields",
"Select 'Yes' to show \
1. Section number and Operation code in Shop Floor\
2. Job Tickets (Style 1 and 2) contain barcodes to use Section Number and Operation Code instead of description.",
"COMBO BOX",
null,
296,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
112
],
[
"69A0B3A2-69AF-4A27-866F-3A4D9CDFEA75",
null,
"2023-09-26T19:19:18.355Z",
null,
1,
"T",
null,
"CSV file to use in integration",
"Please specify the filename from the batch invoice file to be used in the CSV integration. \
The batch invoice file must be set to comma-delimited format.",
"TYPE AHEAD",
null,
371,
"vl_CSVIntegrationInvoiceFileNames",
"S",
"7EADD92C-9764-46C4-A8F4-50917044076F",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
164
],
[
"80AC0525-70ED-4D5C-9B6D-3AA8F0B95845",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Credit Organization",
null,
"TEXT FIELD",
null,
323,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
137
],
[
"1E830A8D-C309-4596-B828-3B64C6257E55",
null,
"2017-09-28T19:51:48.901Z",
null,
1,
"I",
"1",
"Press Details - Show #Up",
"Display the \"# Up\" field in the Press results.",
"TEXT FIELD",
null,
86,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
41
],
[
"EDC9CE3B-3177-4910-BC99-3C1F701C972B",
null,
null,
null,
1,
"I",
"0",
"Show Sales Order Description on Sales Order Line level Invoice report",
null,
null,
null,
300,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
116
],
[
"E8D68737-6F29-495D-899D-3C6D52F32E8E",
null,
"2017-03-06T15:42:04.976Z",
null,
1,
"I",
"null",
"Number of characters in picking tote ID",
"Enter the number of characters to be used for your tote ID so that the mobile application will be able to distinguish between totes and picked boxes. The tote ID must be fewer characters than the box ID. If left blank, the default will be sequential numbering.",
null,
null,
198,
null,
null,
"4102A78F-28B6-4C07-8F8A-5AFDDEF926BC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
67
],
[
"EB9D93D4-CA72-4D4D-8DD9-3DA260F45170",
null,
"2017-09-28T19:51:48.859Z",
null,
1,
"I",
"1",
"Substrate - Parent Sheets Required",
"Display parent sheet required info (Grand Format only)",
"COMBO BOX",
null,
185,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
20
],
[
"A8864209-006D-4F5E-9A69-3E4F6455F6EA",
null,
"2021-09-09T16:45:19.356Z",
null,
1,
"I",
"0",
"Consuming Partial Sheets for Grand Format presses",
"Current Partial sheets  functionality \u2013 calculates the difference between the sq. in. of the press sheet and \
the sq. in. of the images area on the press sheet. \
\
Calculate cost and price of full and partial GF press sheets \u2013 Calculates the sq. in. area of the press sheet \
and the area in sq. in. of the items up on each press sheet. Then compares the sheet utilization of each \
press sheet to a predetermined \u201c % sheet utilization for partial sheets\u201d on the estimate and press sheets \
that exceed the threshold are charged out as full press sheets, press sheets that are less that or \
equal to the threshold are charged out as partial press sheets.\
",
"TEXT FIELD",
null,
338,
"vl_ConsumePartialSheets",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
148
],
[
"FD3812BB-877E-46AE-B0DE-3E53286379B1",
null,
"2017-09-29T16:20:07.416Z",
null,
1,
"I",
"0",
"Style 1: Show Job Title",
"Show Job Title  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
224,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
13
],
[
"2884DF68-75E9-43D2-9E11-3E9B0EA9712F",
null,
"2017-09-28T19:51:48.867Z",
null,
1,
"I",
"1",
"Press Details - Variable",
"Display the variable info (Grand Format only)",
"COMBO BOX",
null,
181,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
24
],
[
"0B2A0CB0-BB39-4953-9198-3F2B1A35FF86",
null,
"2024-02-27T21:53:54.624Z",
null,
1,
"I",
null,
"Add Laminating/Mounting material caliper to Cutting lift calculation.",
"Add the caliper of any materials used on the Laminating or Mounting task (1 side and/or side 2) to the lift based cutting calculation of the cutting task, but only when the Laminating or Mounting task occurs before the Cutting task.\
\
The default is No.",
"COMBO BOX",
null,
380,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
172
],
[
"82E0A72C-034E-4CC3-A988-3F7CD5290416",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Credit Account",
"Enter the default credit account to use for the \u201cAccount Distribution\u201d Chargeback File detail level when there is no Chargeback Credit Account defined at the Work Type or Estimating Standardlevels for production sales order lines and at the Item Class or Item levels for item sales order lines.",
null,
null,
283,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
103
],
[
"C94AEB78-3772-408A-B0EC-400149219DC6",
null,
"2017-10-04T21:57:52.265Z",
null,
1,
"I",
"0",
"Style 1: Press Details - Run Spoils",
"Press Details - Run Spoils (Job Ticket Style 1 only)",
"COMBO BOX",
null,
234,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"C9DE9645-4D22-4E71-9B06-4026F1BA66A7",
null,
"2018-03-23T14:50:34.012Z",
null,
1,
"I",
"1",
"Contract Count the BTO Kit as an Item",
"If this option is true then BTO kit item will be counted as Line Item and Line charges will be applied to the Customer.",
"COMBO BOX",
null,
258,
"vl_YesNo",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
90
],
[
"EC0D1B6D-A4FA-4B8C-8C21-4122F38DBBC6",
null,
null,
null,
1,
"I",
null,
"Number of records in Ready To Schedule to trigger use of additional processor.",
null,
"TEXT FIELD",
null,
369,
null,
null,
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
163
],
[
"383ACE97-8F53-46F9-A313-4187817A22BA",
null,
null,
null,
1,
"I",
"0",
"Always title the Statement and Past Due Notice report as \u201cStatement\u201d only",
"Always title the Statement and Past Due Notice report as \u201cStatement\u201d only.",
"COMBO BOX",
null,
412,
"vl_YesNo",
"S",
"8F6869F7-C964-4351-B638-96F29E44B656",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
183
],
[
"D1900E58-40C0-42F1-9924-42C9C2D284BC",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Prompt to retain carrier punch out details when canceling a shipment",
"When this System Preference is set to Yes and a Shipment is canceled, the user will be prompted to indicate whether \
they want to retain the carrier details after confirming they want to cancel the shipment.",
"COMBO BOX",
null,
334,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"1D8203D6-A05B-433C-BA9B-441ACAA694DB",
null,
"2017-09-28T19:51:48.893Z",
null,
1,
"I",
"1",
"Paper Details: Show Parent Sheet Size",
"Display the \"Parent Sheet Size\" field in the Paper details.",
"TEXT FIELD",
null,
90,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
37
],
[
"7A1038B5-9A39-432C-BEDD-45A30BC8EEED",
null,
"2018-03-28T11:15:17.022Z",
null,
1,
"N",
".375",
"Minimum Bottom Gutter",
"The minimum size of the bottom gutter to use for the imposition.",
"TEXT FIELD",
".0000",
6,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
44
],
[
"3D622ADD-9A81-4C96-9867-465C14B08FCC",
null,
"2025-05-12T19:17:28.067Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"T",
null,
"Redistribute revenue and book costs on invoice and credit Notes",
"When this preference is active, a journal entry tab will become visible on invoices and credit notes.  \
A journal entry will be automatically created to redistribute revenue distribution lines against the revenue account from the work type.\
Costs will also be added to the journal entry.  This journal entry will be available for posting during the invoice and credit note accounting integrations.\
",
"COMBO BOX",
null,
390,
"vl_invoiceJournalEntryOptions",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"4C39440D-99A5-4C3F-8508-4670DE6C9318",
null,
"2024-08-09T15:51:03.776Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"1",
"Use Cash Receipts Register to post cash receipts and cancelled receipts to accounting system.",
"Selecting 'Yes' will use the Cash Receipts Register program to post cash receipts and cancelled receipt transactions to the selected accounting system.\
Selecting 'No' will enable the feature to post cash receipts and cancelled receipts to accounting system based on 'Ready To Post' status.",
"COMBO BOX",
null,
387,
"vl_YesNo",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"3D2C4098-573E-4980-A13A-46B5581AF149",
null,
"2016-09-20T15:33:16.276Z",
null,
1,
"N",
"10",
"Number of days per pay period",
null,
"TEXT FIELD",
null,
152,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"F73CB823-5C3C-49C4-8E1A-4700C9C9CA1B",
null,
null,
null,
1,
"I",
"0",
"Number of minutes to round time after Shift Ends",
"Number of minutes to round time after Shift Ends",
null,
null,
396,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
180
],
[
"D057508C-5E60-471C-9233-489DCEF11112",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Set packing slip number to sales order number",
"When 'Yes' is selected, the packing slip number will be set to the first sales order number on the packing slip. If there are\
multiple shipments for the same order a suffix will be added to the packing slip number. Example: 0000123-002\
\
When 'No' is selected, the packing slip numbering will be based on the standard numbering logic.",
"COMBO BOX",
null,
280,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"9ED629C2-823E-44EB-9807-48AF7E28AF21",
null,
"2018-03-28T11:15:16.957Z",
null,
1,
"I",
"0",
"Exclude bleeds from feed length calculation?",
null,
"COMBO BOX",
null,
132,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
16
],
[
"38DC78C6-168B-426C-89B1-48B139E01CD8",
null,
"2022-07-06T16:34:11.101Z",
null,
1,
"I",
"0",
"Allow the creation of the inventory issue transaction for Picked (Fulfillment) orders to occur at either Shipping or at Picking.",
null,
null,
null,
351,
"vlPref347",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
155
],
[
"321A4687-7441-4D1C-A6E6-496544118FDE",
null,
null,
null,
1,
"I",
"0",
"Force the entry of quantity picked",
"Show an additional \"Picked\" field on the item pick screen in the mobile application and to force the user to enter the quantity for each item picked. Turning this preference on also makes \"Qty\" field read-only.",
"COMBO BOX",
null,
194,
"vl_YesNo",
"S",
"4102A78F-28B6-4C07-8F8A-5AFDDEF926BC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
62
],
[
"69E5DA06-7530-47A1-B124-4A48E93E27E8",
null,
"2022-09-08T19:01:41.191Z",
null,
1,
"I",
"0",
"Detect and Fix Inventory Problems",
"If turned on, this will detect and fix Inventory problems in various places in the system.\
\
This is off by default.",
"COMBO BOX",
null,
343,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
152
],
[
"BD6954D5-96C3-4179-BDA3-4A7134C1CA60",
null,
"2017-10-20T16:06:14.777Z",
null,
1,
"I",
"0",
"Check daily for Expired Inventory Items",
"If this preference is set to \"Yes\" the system will check for Inventory Items older than the \
current date every day at 12:00am  (as per the Server Time & Timezone Setting) and mark them \"Expired\".\
\"Expired\" Items will not be available for new Estimates, Sales Order and / or Invoices.",
null,
null,
241,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
79
],
[
"9C75B2D0-6F33-4DAE-835E-4A9CA5B69EDD",
null,
null,
null,
1,
"I",
null,
"Distribute Ink Revenue and Cost Based on Ink Type",
"When 'Yes' is selected, the sales and cost of sales accounts for Ink task will be based on the Ink Type.\
",
"COMBO BOX",
null,
330,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
142
],
[
"E3002989-5312-4CB0-ADBC-4B5966F630F3",
null,
"2018-03-28T11:15:16.947Z",
null,
1,
null,
"0",
"Grand Format dimensions naming convention",
"By switching this setting from the default to the alternative option, the system will substitute the field labels on \
a print section, such that wherever the system would display the labels \u2018Finish Size\u2019 and Flat Size\u2019 by default, \
will instead display \u2018Live Area\u2019 and \u2018Overall Size. \
This will apply only to sections where the work type specifies a Grand Format press.)",
"COMBO BOX",
null,
140,
"vl_grandFormatDimensionsNaming",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
12
],
[
"ADE9BD94-A5A0-430C-92A6-4C8F49F780F2",
null,
null,
null,
1,
"I",
"0",
"Include a page break by section for Job Tickets style 1 and 2",
null,
"COMBO BOX",
null,
337,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
147
],
[
"A630316B-1124-46DB-A980-4CD3E4F1AC84",
null,
null,
null,
1,
"I",
"0",
"Print Sales Order Number Barcode on 4x4 Shipping Labels",
null,
"TEXT FIELD",
null,
374,
"vl_YesNo",
"S",
"685683E9-65D2-4137-9D85-6DF00BD5FED7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
167
],
[
"2C462FA9-F5C4-4845-9E3A-4D37674FB09E",
null,
"2017-09-28T19:51:48.908Z",
null,
1,
"I",
"1",
"Press Details - Show Imposition Type",
"Display the \"Imposition Type\" field in the Press results.",
"TEXT FIELD",
null,
82,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
45
],
[
"02A2387C-DA0D-485B-B692-4DFACDDD5D11",
null,
"2017-07-18T17:36:44.775Z",
null,
1,
"I",
null,
"Reservation Expiry Notification",
"Define the number of days before a reservation is due to expire that the CSR will be automatically notified (maximum is 7)",
null,
null,
207,
null,
null,
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"BEBA1B0E-78FF-4EDC-BF1A-4E73D616393B",
null,
"2024-05-13T16:21:09.554Z",
null,
1,
"I",
"0",
"Verify that the landed cost and FOB cost on the purchase receipt matches the purchase order",
"Activating this option will display a warning message if the landed cost or FOB cost on the purchase receipt doesn't correspond with the landed cost or FOB cost specified in the purchase order. \
The warning message will display upon clicking the 'Update' button on the purchase receipt.",
"COMBO BOX",
null,
377,
"vl_YesNo",
"S",
"742AA30C-A420-4554-B841-F29070801890",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
170
],
[
"F5EF546A-2AC0-43AA-AFBE-4F1F84029711",
null,
"2020-01-26T16:27:10.180Z",
null,
1,
"I",
"0",
"Send \"Credit Note Apply Records\" to MS Dynamics GP when posting the Invoice Register",
"Send \"Credit Note Apply Records\" to MS Dynamics GP when posting the Invoice Register",
"COMBO BOX",
null,
271,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"A2CD4B0F-D6FE-40C0-B295-4F923DA84B9B",
null,
"2017-11-06T17:13:13.962Z",
null,
1,
"I",
"0",
"Print item Bin Locations on the packing slip",
"Toggle whether to show the allocated bin locations of picked items on the packing slip.",
"COMBO BOX",
null,
245,
"vl_YesNoReversed",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"A59F3193-2D0B-4A14-BAED-51873C773951",
null,
null,
null,
1,
"N",
"0.00",
"Matching tolerance dollar value when matching purchases to invoices.",
"An invoice will only be considered matched, and therefore available to post via the AP Invoice Register, when the remaining unmatched amount of the invoice is less than the lesser of the Matching Tolerance Amount or the Matching Tolerance %.",
"TEXT FIELD",
null,
105,
null,
null,
"C1C5A89C-F11B-4E24-A17A-3B360B346DE7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"B49D9FC3-A13F-4F75-83D6-518827BDF776",
null,
"2019-04-03T18:52:54.324Z",
null,
1,
"I",
"0",
"Show Customer PO on Sales Order level Invoice report",
null,
null,
null,
301,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
115
],
[
"012F6738-2E81-42C2-ACE3-52ADFA15D928",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
53,
null,
null,
"6A0131C4-5FFE-4088-8283-4C40E7179878",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"D867D64D-F47B-4C3C-95B8-52CA4D340451",
null,
null,
null,
1,
"I",
"0",
"Use Division and Plant Filter?",
null,
"COMBO BOX",
null,
79,
"vl_YesNo",
"S",
"1770AA2D-06BE-4AF9-9606-CDA589056F7C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"D49399A5-A1AD-4C78-B361-53D67619BCA4",
null,
null,
null,
1,
"I",
null,
"Include Estimate Comment on the Packing Slip",
"Include/Exclude the comment from appearing on the Packing Slip.",
"TEXT FIELD",
null,
28,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"9D410508-A9FD-4AA8-AC3F-54A431E1DD0E",
null,
"2018-08-22T22:52:59.167Z",
"899250C7-A418-4B87-BF10-3C536EEC04DA",
1,
"I",
"0",
"Automatically calculate off the schedule reservation",
"If enabled, the system will calculate the release date based on the schedule reservation, otherwise a button in the\
project plan details tab will need to be pressed to populate.",
"COMBO BOX",
null,
278,
"vl_YesNo",
"S",
"29BE48E4-7A86-4347-8608-9C58871CC38C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
99
],
[
"3C489ADF-6BCE-4CDC-83DC-552C82547EBC",
null,
"2019-07-22T18:03:16.710Z",
null,
1,
"I",
"0",
"Limit Postage task to one postage item",
"When value = \u201cYes\u201d only one postage item per postage task can be created on a Sales order or Estimate.",
null,
null,
312,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
128
],
[
"CB3F9159-7A7D-49D6-B690-5631D8D71F26",
null,
"2016-10-06T17:04:36.172Z",
null,
1,
"I",
"0",
"Hide number of Sheets - Quote Letter 2",
"Hides the # Sheets info on Quote Letter 2",
"CHECK BOX",
null,
164,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
47
],
[
"F3218181-AD37-40E9-8611-56A3B7FA0635",
null,
"2017-02-15T15:44:03.339Z",
null,
1,
"I",
"0",
"When a sales order is released mark the first milestone on each job to 'Ready'",
"When you check this preference the first milestone on each Job will be marked as 'Ready' when the sales order is released.\r\
Note: This does not automatically schedule the job, you will still have to schedule the job.",
"CHECK BOX",
null,
110,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"543844C8-D93E-418D-86DF-56C52257F39A",
null,
"2024-07-10T18:12:35.951Z",
null,
1,
"I",
"0",
"Number of minutes to round time before Shift Starts",
"Number of minutes to round time before Shift Starts",
null,
null,
393,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
177
],
[
"C50F0344-18BA-409F-BB3D-57219C00BD55",
null,
"2018-03-28T11:15:16.970Z",
null,
1,
"T",
"S",
"Basis for press makeready spoils algorithm",
"The following applies to all litho, web, and flexo estimates:\r\
\r\
The \u2018Press setup once per section\u2019 option will factor in the 1st plate spoils once only for a particular section on an estimate, using the following formula:\r\
MR spoils = 1st plate spoils + (# of add\u2019l plates x per add\u2019l plate spoils)\r\
\r\
The \u2018Press setup once per form\u2019 option will factor in the 1st plate spoils once for each form on a section, using the following formula:\r\
MR spoils = (1st plate spoils + (# of add\u2019l plates per form x per add\u2019l plate spoils)) x (#of forms)",
"TEXT FIELD",
null,
103,
"avSales_mrCalculationMethods",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
22
],
[
"5D73709D-D3A1-45B9-9435-58066C779C43",
null,
"2017-09-29T16:16:22.567Z",
null,
1,
"I",
"0",
"Style 1: Show Shipping - Expected Ship Date",
"Show Shipping - Expected Ship Date  (Job Ticket Style 1 only)\
",
"COMBO BOX",
null,
238,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"789CD78F-EC76-4DB5-A509-588FC4A0C17B",
null,
"2016-12-08T17:30:22.021Z",
null,
1,
"I",
"0",
"Show Images linked to Sections",
"This option will force images that are linked to a section to be displayed on Quote Letter #1. \r\
You link the images to the section in the Documents tab on an Order or Estimate, and select\r\
the option to \"Show On Quote\".",
"COMBO BOX",
null,
59,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"25102A17-DE01-4B58-8367-59BA1DEB0D6F",
null,
"2016-12-17T20:48:56.265Z",
null,
1,
"I",
"0",
"Display Estimator's Info - Quote Letter 2",
"Prints the Estimator's name on the quote (Quote Letter #2 only)",
"COMBO BOX",
null,
176,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
56
],
[
"4FA97269-D6DA-412F-974B-59E70035B7BC",
null,
"2018-03-28T11:15:16.985Z",
null,
1,
"T",
"S",
"Outsourced Services Default Freight-In Type",
"Sets the default freight in type for outsourced services. Options are Supplier or Internal.",
"TEXT FIELD",
null,
71,
"vl_OutsourcedServicesFreightMethods",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
28
],
[
"FB20C11C-D8BD-4068-9581-5A37D484484F",
null,
"2018-03-28T11:15:16.959Z",
null,
1,
"N",
"0.125",
"Pre-Trim Cut Size",
"This is the size of the pre-trim cut (in inches) that is done to square up sheet before running it through the press.",
"TEXT FIELD",
null,
119,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
17
],
[
"EF09E7C2-D90D-42C6-A229-5A437CB831AE",
null,
null,
null,
1,
"I",
null,
"Include Comment on the Packing Slip",
"Include/Exclude Comment on the Packing Slip",
"TEXT FIELD",
null,
34,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"19BC2E57-981D-49CC-B909-5A686C928F66",
null,
null,
null,
1,
"I",
"0",
"Use department/operation category from press task to create gl account for ink tasks in Invoicing GL Distribution?",
null,
"COMBO BOX",
null,
281,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
102
],
[
"CD92E14A-E6AF-47FD-8A92-5B28B0950598",
null,
"2017-09-16T17:05:07.204Z",
null,
1,
"T",
"Budget",
"Type of Costing for Receipts From Production",
"Use the budgeted cost for the line item on the sales order or the actual cost collected against the job as the cost on the receipt from production.",
"COMBO BOX",
null,
188,
"vl_CostingType",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"A07F31B6-8710-4C42-9E6A-5D2FAE683B26",
null,
"2017-12-11T21:34:19.787Z",
null,
1,
"I",
"1",
"Show Images linked to Detail Lines and Sections",
"This option will force images that are linked to a detail line and section to be displayed on the Job Ticket. \r\
Images with no section selected (detail line only) will appear on the job ticket before the first section.\
You link the images to the detail/section in the Documents tab on an Order or Estimate, and select\r\
the option to \"Show On Job Ticket\".",
"COMBO BOX",
null,
57,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
47
],
[
"66561E6A-0AA5-471A-A6A5-5DF17101D681",
null,
"2017-09-29T16:19:59.293Z",
null,
1,
"I",
"0",
"Style 1: Show Work Type",
"Show Work Type  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
225,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
12
],
[
"3E5FF427-1783-4C0D-8140-5F06CC30A075",
null,
"2017-02-15T15:44:03.352Z",
null,
1,
"I",
"1",
"Action to take when credit limit has been exceeded by a customer",
"Upon saving a new or existing sales order, the system will check to see if the customer's outstanding balance, which includes all unpaid invoices and un-invoiced released sales orders, has exceeded their credit limit. When the integration with Microsoft Dynamics GP is being used (system preference #68), the credit limit may come from either Avanti Slingshot or GP. Otherwise, the credit limit is set on the customer record in Avanti Slingshot. If the current open invoice amount (outstanding balance from GP or Avanti Slingshot) plus the value of the current order exceeds the credit limit, take the action set in this system preference. This also applies to the creation of orders through the XML import.",
"TEXT FIELD",
null,
111,
"vl_exceedCreditLimitActions",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"0C86D83F-8CF6-405E-9460-5F7E828CDCA1",
null,
"2017-09-28T19:51:48.903Z",
null,
1,
"I",
"1",
"Press Details - Show Finished Size",
"Display the \"Finished Size\" field in the Press results.",
"TEXT FIELD",
null,
85,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
42
],
[
"C3A071B8-3F35-4263-9B66-6074090A4B3D",
null,
"2017-02-15T15:44:03.331Z",
null,
1,
"I",
"0",
"Use Change Orders",
"Turns on the Change Order system, which enforces a workflow process to obtain customer approval for\r\
all changes being made to the Order (that affect $), before the changes can be merged into the released\r\
Order and Shop Floor is notified of the changes.",
"TEXT FIELD",
null,
102,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"42204FAE-5A99-4050-9BDF-60E057C38E9E",
null,
"2017-10-04T21:57:52.276Z",
null,
1,
"I",
"0",
"Style 1: Show Shipping Method",
"Show Shipping Method  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
228,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"ECCC4630-BD24-4BC5-B05A-60E3330BA519",
null,
null,
null,
1,
"T",
null,
"Standard Purchase Order Message",
"Standard text that prints before the purchase order totals area.",
"TEXT AREA",
null,
69,
null,
null,
"55F19C5D-5E7D-4DEA-A2F7-DC5F3F48A76B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"F97A54BE-3BC0-4828-BB3E-614FE7353F0B",
null,
"2018-04-18T15:14:36.136Z",
null,
1,
"I",
"0",
"Allow selection of purchase receipt records to post",
"If this is selected to Yes, users will see an extra column under Purchase Order Receipts Table view called Post Record.\
This will allow them to select 1 or more PO Receipts to process in Purchase Receipts Register.",
"COMBO BOX",
null,
261,
"vl_YesNo",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
92
],
[
"114EE631-8E33-4388-A04F-61F7B165861E",
null,
"2022-01-18T17:05:07.201Z",
null,
1,
"I",
"1",
"Fulfillment PickList Report",
null,
"COMBO BOX",
null,
190,
"vl_FulfillmentPickListReportTypes",
"S",
"6A0131C4-5FFE-4088-8283-4C40E7179878",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"CCC6D267-5DD2-4EB5-977B-637372823C6F",
null,
null,
null,
1,
"I",
"0",
"Add postage cost to postage task cost",
"Automatically add the cost of postage (from postage detail lines) to the cost of the postage task itself. \
This applies when the postage task is created or updated in Estimating, Sales Orders, and XML Loader.",
"COMBO BOX",
null,
318,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
132
],
[
"38F32304-7CF1-4F31-B5E0-64AA31DA01CE",
null,
"2018-03-28T11:15:16.931Z",
null,
1,
"N",
null,
"Envelope - Gap for seam glue",
"Specify the gap for the seam glue formula.",
null,
null,
217,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"66F643D4-8717-408D-8CB4-64B7C0E01E03",
null,
null,
null,
1,
"T",
"D",
"Method to Allocate Freight Variance",
null,
null,
null,
262,
"vl_DollarQuantity",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
91
],
[
"FCC77DE1-4111-4413-A791-658E4176F572",
null,
"2022-08-18T18:01:27.811Z",
null,
1,
"N",
"0",
"Additional shipping markup % for domestic shipments.",
null,
"TEXT FIELD",
null,
353,
null,
null,
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
156
],
[
"CF1DF105-DE20-43DE-A45A-659A6C944EA1",
null,
null,
null,
1,
"T",
null,
"Revenue gl account Sage 300",
"This account will be used as the revenue account in the Sage 300 export.",
"TYPE AHEAD",
null,
407,
"avGL_ChartOfAccounts_All_withDesc_Active",
"S",
"599445EC-CD18-40FC-BBF2-5B0AE90547C7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
182
],
[
"29FE3625-7F51-406D-A9B1-6669EA816814",
null,
"2017-09-28T19:51:48.934Z",
null,
1,
"I",
"0",
"Show Job Total $",
"Displays the Job Total $ on the Job Ticket.",
"TEXT FIELD",
null,
41,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
58
],
[
"8EAB0F1E-C0B6-4369-B437-678F22C92694",
null,
"2018-03-28T11:15:17.006Z",
null,
1,
"T",
"A",
"Default Section Difficulty",
"The default section difficulty that should be used when adding a new section to an estimate or order.\r\
This setting will be ignored if a Work Template is used on a Section, where the Difficulty has been specified.",
"COMBO BOX",
null,
13,
"avSales_difficulty_long",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
37
],
[
"8F3A989F-33F2-4C73-8E3E-67D5A57E9DBF",
null,
"2020-09-04T15:53:26.286Z",
null,
1,
"I",
null,
"Number of addresses to display at a time in Quantity Allocation table on the Multi Ship dialog",
"If a number is entered here paging will be added to the Quantity Allocation table on the Multi Ship dialog. EG: \
\
- If 100 is entered it will only show 100 addresses at a time, you will have to scroll using Next and Previous buttons to see the next or previous 100 addresses. \
- If zero is entered then the Quantity Allocation table won't display. \
- If no value is entered then it will use the default behaviour, which is show the Quantity Allocation table without any paging.",
null,
null,
331,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
143
],
[
"949CE701-B254-43E3-9839-67F325A02454",
null,
"2018-03-28T11:15:16.975Z",
null,
1,
"I",
"0",
"Override # forms for Section",
"EXPERIMENTAL: Override the # forms for a Section. \r\
Setting this to YES will show the field in the UI allowing you to override the number of forms\r\
computed by the modelling.",
"TEXT FIELD",
null,
77,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
24
],
[
"B4E0357B-A354-485C-A0FD-68428D967A5F",
null,
"2018-03-28T11:15:17.001Z",
null,
1,
"N",
"20",
"Standard Ink Coverage (%)",
"This is the default ink coverage that should be used for a section. \r\
If you specify 4/4 color, then 4 colors for the front and 4 colors for the back will be added with this % ink coverage.\r\
The % ink coverage can be changed for a given section in the Section dialog on the Inks tab.",
"TEXT FIELD",
"#0",
15,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
35
],
[
"1EE72EAF-D7A1-4BFB-BFA8-6A6984B74BDC",
null,
"2021-01-06T18:51:36.645Z",
null,
1,
"I",
"Yes",
"Allow Edit and Delete capabilities for Invoices/Credit Notes with a Printed-Final/Printed status.",
"'Yes' - Will allow editing and deleting capabilities for invoices with a Printed-Final status and credit notes with a Printed status.\
\
'No' - Will not allow editing or deleting of invoices with a Printed-Final status and credit notes with a Printed status.\
\
'No, except invoice/credit note status' - Will only allow editing of the invoice status field on invoices with a Printed-Final status and credit note status field on credit notes with a Printed status. \
Invoice status options are based on the Invoice Entry privileges, 'Set Invoice status to Open' and \
'Set Invoice status to Printed - Final'\
Credit Note status options are based on the Credit Note Entry privileges, 'Set Credit Note status to Open'",
"COMBO BOX",
null,
197,
"vl_YesNoExceptInvoiceStatus",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
66
],
[
"FD9706B6-1B19-4BE6-B70C-6A8200121E0E",
null,
"2018-03-28T11:15:16.934Z",
null,
1,
"I",
"0",
"Use section paper?",
"For use with the \"Other\" Estimating Standard.\
\
If \"No\" is selected (the default) the \"Materials Needed?\" field will have the 2 options: Yes and No. \
If \"Yes\" is selected a 3rd option will be added called \"Use section paper\".\
\
When the \"Use section paper\" option is selected the substrate cost from the paper task in the same print section \
will be used as the cost on this \"Other\" task. The paper cost and sheets used on the \"Other\" task will not show in the \
cost summary.",
"COMBO BOX",
null,
209,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"E53586E6-8169-4F8F-B84D-6A91A07F6BBE",
null,
"2018-03-28T11:15:16.995Z",
null,
1,
"I",
"0",
"Use Minimum Order Quantities for Paper Costing",
"This is the default that will be inherited by all new sections created in estimates. \r\
If this is set to \"Yes\", then the sections will use the minimum reorder qtys for their paper costing.\r\
For example, if your pack size is set to 500, and the estimating engine calculates 1336 parent sheets\r\
are needed for a particular section / quantity / press, then 1500 parent sheets will be used for the parent sheets.\r\
This has no other affect on the tasks for the sections; they will still use 1336 parent sheets for their calculations.\r\
\r\
This setting can be changed for the section, and the individual press for each quantity on the estimate.",
"CHECK BOX",
null,
20,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
32
],
[
"4976C75F-8F3D-4C25-9751-6B6DAF919839",
null,
"2021-09-08T15:05:09.319Z",
null,
1,
"I",
"0",
"Track Currency on Estimates, Orders, Project Plans, and Invoices",
"When \u201cNo,\u201d the Currency does not allow the editing or tracking the currency rate from Estimating, Sales Order, and Invoicing. When \u201cYes,\u201d The currency rate on the Estimating is compared to the current rate, a message to update the current rate is asked. This comparison is done when Invoices are created.  The blue \u201cI\u201d is now editable to get the currency rate and is editable with privileges. In Estimating and Sales Order on the line extended price, when the right mouse clicks, you can set the Customer price in the Customers Currency, and the Organization price is adjusted based on the rate.",
"COMBO BOX",
null,
335,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
146
],
[
"138208C5-4B7B-410D-8C92-6BED03845FAF",
null,
null,
null,
1,
"I",
"0",
"Use Revision Field",
"If \u2018Yes\u201d is selected, a new field called Revision is created in the Item - Table View. When duplicating an item, the revision will be populated in the Revision Field. This also applies when using the Obsolete and Replace in the item Status. The Revision Code is required when importing XML Sales Orders. \
\
If \u2018No\u201d is selected, the existing functionality will apply where the revision is appended to the end of the item code.",
"COMBO BOX",
null,
413,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"5CE5A262-735D-4616-B775-6C47D3D5EDCB",
null,
"2017-09-28T19:51:48.928Z",
null,
1,
"I",
"0",
"Show Estimate Number",
"Shows the Estimate Number on the Job Ticket, in addition to the Order Number.",
"COMBO BOX",
null,
44,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
55
],
[
"8E83DEB1-D76B-4CF6-98DF-6C6A806F1E1D",
null,
"2016-10-07T15:29:08.552Z",
null,
1,
"I",
"0",
"Maximum number of customers in valuelist",
"The maximum number of customers to load in the valuelist. Please keep in mind that as the value exceeds 1000 customers, you may experience performance issues.  If the value is set to 0, it will load 1000 records.",
"TEXT FIELD",
null,
166,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
47
],
[
"DF1EF6E5-9504-4227-8AAF-6CBC4A815B10",
null,
"2022-08-18T18:01:11.763Z",
null,
1,
"N",
"0",
"Additional shipping markup % for international shipments.",
null,
"TEXT FIELD",
null,
354,
null,
null,
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
157
],
[
"4E790696-4D57-4843-BDE0-6CD9FA33871E",
null,
null,
null,
1,
"I",
"0",
"Style 2: Show Schedule Priority",
"Show the sales order schedule priority info (style 2 only)",
"COMBO BOX",
null,
248,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
85
],
[
"FE7CD9B4-9A5E-4675-A382-6F9E7144CC43",
null,
"2018-03-28T11:15:16.982Z",
null,
1,
"T",
"I",
"Outsourced Services Default Freight-Out Type",
"Sets the default freight-out type for outsourced services. Options are Supplier or Internal.",
"TEXT FIELD",
null,
72,
"vl_OutsourcedServicesFreightMethods",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
27
],
[
"20974C97-105A-4769-BA51-701BA45C3873",
null,
null,
null,
1,
"I",
"0",
"Set all customers not in import to Inactive.",
null,
"COMBO BOX",
null,
121,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
19
],
[
"29B18F68-8571-46FB-B216-70CFD47AF375",
null,
null,
null,
1,
"T",
"C",
"How do you record payments on Sales Invoices",
"Select the method used to record payments on sales invoices, the cash receipts module or the \
'Record Payment' button on the sales invoice.",
"COMBO BOX",
null,
162,
"vl_InvoicePaymentMethod",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
45
],
[
"EFA8A21D-448D-459E-8C33-7109E08B7310",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
52,
null,
null,
"55F19C5D-5E7D-4DEA-A2F7-DC5F3F48A76B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"CD019180-9FC6-4F36-AE4F-715774300C14",
null,
"2020-05-05T19:05:55.788Z",
null,
1,
"I",
"0",
"Use Project for Lot Control?",
"This system preference allows the project field to be used to filter FIFO records for Finished Goods items based on project. When use Project for Lot Control is set to \"No\" (default setting) the system will always select the oldest FIFO record ignoring the project field. When use Project for lot control is set to \"Yes\"\"on the Finished Goods item's inventory is filtered by project, then the oldest FIFO record for the project is selected.",
"COMBO BOX",
null,
328,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
141
],
[
"06F5139B-CC1D-4945-BD9E-7293A611250F",
null,
null,
null,
1,
"I",
"0",
"Quantity to use for Material Tasks",
"When a work template is used either manually in a new estimate/sales order or \
automatically through the XML import, set the quantity of any material tasks not \
related to other tasks (e.g. printing, plating, etc.) to either \"1\" or to the section quantity.",
"COMBO BOX",
null,
293,
"vl_sectionQty",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
109
],
[
"5D2A067C-81C6-4A4C-9CF6-72DEA4DE3F91",
null,
"2017-09-22T15:20:52.336Z",
null,
1,
"T",
"Open",
"Transaction Status",
"Set the status of transactions created through the Avanti Mobile Application. \u201cOpen\u201d will require a user to update all transactions manually in Avanti Slingshot. \u201cUpdated\u201d will automatically apply all transactions to inventory.",
"COMBO BOX",
null,
192,
"Open,Updated",
"M",
"4102A78F-28B6-4C07-8F8A-5AFDDEF926BC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"89DEB138-630E-4D27-8D48-72E615C0485B",
null,
"2017-10-04T21:57:52.274Z",
null,
1,
"I",
"0",
"Style 1: Barcode Order Number or Job Number",
"Barcode order number or job number  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
229,
"vl_reportBarcodeNumber",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"991F0AEB-5E27-471B-90D0-732BBDCE9DA7",
null,
null,
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"1",
"Invoice and Credit Note Integration Method",
"We have three methods available for how we create the NetSuite invoice and credit note.\
\
1. Use Slingshot Work Types.  Each Slingshot Invoice line is related to a Work Type.  We automatically create NetSuite Service Items,\
which will show up on the NetSuite invoice and credit note. \
\
2. Use Slignshot Distribution.  Each Slingshot revenue distribution account will automatically create NetSuite Service accounts, \
which will show up on the NetSuite invoice and credit note.\
\
3. Use Slingshot Billing Codes.  Each Slingshot estimating standard will be populated with a Billing Code.  These billing codes will automatically \
create NewSuite Service items, which will show up on the NetSuite invoice and credit Note.",
"COMBO BOX",
null,
404,
"avInvoiceIntegrationMethod_NetSuite",
"S",
"91A8CC18-DEE8-42FB-AF8C-9A2EDA7D5C65",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"70FC5AB8-00ED-4D47-8948-732EC72B788B",
null,
"2024-11-14T20:10:11.038Z",
null,
1,
"I",
"0",
"Automatically round up or down Start and End shift times",
"When this preference is set to 'yes,' the start or end shift times will be rounded up or down.",
"COMBO BOX",
null,
392,
"vl_YesNo",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
176
],
[
"EB6F80BD-9577-43E9-A222-73ECBE5B6058",
"2018-03-28T11:15:17.018Z",
null,
null,
1,
"I",
"0",
"Use Chargeback Code in Reference field of Packing Slip",
"Populate reference field in a Packing Slip with the Chargeback Code of Sales Orders in slip.",
"COMBO BOX",
null,
127,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
24
],
[
"FE28D84C-402A-4C5C-9A6F-745ECF2C563F",
null,
null,
null,
1,
"I",
"0",
"Number of minutes to round time before Shift Ends",
"Number of minutes to round time before Shift Ends",
null,
null,
395,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
179
],
[
"FF486840-00DA-4618-A200-75F65EFD00CE",
null,
"2018-03-28T11:15:17.018Z",
null,
1,
"N",
".25",
"Minumum Color Bar Gutter",
null,
"TEXT FIELD",
".000",
8,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
42
],
[
"20ACAB08-A83F-4987-A0B6-76A3A39E1EE4",
null,
"2017-02-15T15:44:03.319Z",
null,
1,
"I",
"1",
"Copy estimate notes by default when copying estimate to sales order",
"This preference sets the default for copying estimate notes when copying estimate to sales order.  The user has the option of overriding\r\
the default value.",
"COMBO BOX",
null,
19,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"4BDA05AA-DD7A-42DA-A01F-785F2DA5A943",
null,
"2016-10-19T00:44:10.344Z",
null,
1,
"T",
null,
"\"Sick Time\" Exception Type",
null,
"TYPE AHEAD",
null,
156,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"1C0546C6-9EC2-41BF-8FD7-788EDBD310F2",
null,
"2022-11-24T18:53:17.967Z",
null,
1,
"I",
"0",
"Show the Gang Form Summary on Job tickets; Standard Job Ticket, Standard Job Ticket Style 2 and JobTicket by Form",
"Option 1: No (default)  do not print Gang Form Summary on job tickets.\
Option 2: Yes \u2013Print  Gang Form Summary with Job tickets -Standard Job Ticket and Standard Job Ticket Style 2. ",
"COMBO BOX",
null,
362,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
159
],
[
"B104FFE6-5552-449C-AF1B-790C740D114B",
null,
"2019-08-19T14:46:19.987Z",
null,
1,
"T",
"N",
"Finished Size Area Calculation Rounding Factor",
"None = No adjustment made (default). Value displayed is the actual area of the section. \
Round Up = Area calculation is rounded up to the next whole number based upon the UOM.",
"COMBO BOX",
null,
315,
"avSales_areaCalculationRounding",
"S",
"011E4871-EA2E-4765-AD5C-42AFCB5AC39D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"C54829F2-3A05-4C66-A17F-790CCE550124",
null,
null,
null,
1,
"I",
"0",
"Waybill Number Required",
"Toggle to make the \u201cWaybill Number\u201d field in the Purchase Receipts program and function of the mobile application optional or required for a PO receipt transaction.",
"COMBO BOX",
null,
287,
"vl_YesNo",
"S",
"742AA30C-A420-4554-B841-F29070801890",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
103
],
[
"DAC88EC3-4B3B-428C-941F-797FFDB503F0",
null,
null,
null,
1,
"T",
null,
"Standard Invoice Message",
"The standard text that prints on the invoice after the line items.",
"TEXT AREA",
null,
65,
null,
null,
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"D0220896-13E7-4E9D-9339-79E1061D66E1",
null,
"2023-09-22T14:42:07.166Z",
null,
1,
"I",
"0",
"Make Cost Center a required field in time sheet material entry.",
"When set to 'Yes', the system will require a valid cost centre in order to confirm materials in time sheet entry.  ",
"COMBO BOX",
null,
170,
"vl_YesNo",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"4204F4F6-5798-409A-B18A-7A132698F751",
null,
"2018-03-28T11:15:16.954Z",
null,
1,
"I",
"0",
"Use staggered layouts",
"Setting this to YES means that all new Sections added to Estimates/Orders \
will have the \"use staggered layouts\" checkbox in the Section dialog initially ON.",
"CHECK BOX",
null,
137,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
15
],
[
"3013424B-9ACF-435C-9F00-7A66EEBCA728",
null,
"2017-02-15T15:44:03.335Z",
null,
1,
"I",
"0",
"Validate External Orders",
"This preference is used to validate an order from an external system.  The external system will call the Slingshot API Web Service to update the quantity and validate the sales order.",
"TEXT FIELD",
null,
108,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"AE9E38EE-2951-4E9F-87A7-7AA83E3B0445",
null,
"2018-03-16T16:27:05.269Z",
null,
1,
"I",
"0",
"Enable Advanced Billing",
"'Yes' to allow the system to show the Advance Billing check box on an invoice and to be able to use the Advance Billing\
functionality. 'No' to hide the Advance Billing check box on an invoice and to turn off the Advance Billing functionality",
"COMBO BOX",
null,
252,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
87
],
[
"ADE4DAB1-20BB-4DEF-AA5C-7B07F53D6D61",
null,
"2016-10-19T00:45:31.872Z",
null,
1,
"T",
null,
"\"Leave (Paid)\" Exception Type",
null,
"TEXT FIELD",
null,
157,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"4AD5FE27-BF20-4F39-AC08-7B4A3D7A05DB",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Credit Business Unit",
null,
"TEXT FIELD",
null,
321,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
135
],
[
"B67B9B02-896E-4B76-937E-7B5DEB600FAD",
null,
null,
null,
1,
"T",
null,
"GL Account to use for POS Reconciliation variance cash receipts",
null,
"TYPE AHEAD",
null,
143,
"avGL_AccountSegments_Active",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
39
],
[
"8350A67E-6DB4-42C8-BB2C-7CF9822BB7CD",
null,
"2017-09-29T16:19:52.404Z",
null,
1,
"I",
"0",
"Style 1: Show Add'l Info / Notes",
"Style 1: Show Add'l Info (Job Ticket Style 1 only)",
"COMBO BOX",
null,
226,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
11
],
[
"E1B61C2C-CC5D-44C3-97DA-7D11DCFB60F2",
null,
"2018-03-28T11:15:16.942Z",
null,
1,
"I",
"0",
"Line Item Difficulty Required",
"Require the entry of a \u201cDifficulty\u201d for every line item on an estimate or sales order.",
"CHECK BOX",
null,
169,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
10
],
[
"E416F97A-D9E6-434A-A7FD-7D1C52E09616",
null,
"2020-02-29T20:43:18.600Z",
null,
1,
"I",
"0",
"Switch Customer Address To 'ONE-TIME' Address when creating GP Sales Invoice Transaction",
"When 'Yes' is selected and the GP Invoice Integration option to send invoices and credit notes as Sales Invoice Transactions \
is active, the system will switch any address code that is not 'PRIMARY' to 'ONE-TIME'.\
",
"COMBO BOX",
null,
325,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
138
],
[
"D17522C5-74C3-41CB-BF74-7D5DDA1913CA",
null,
"2018-03-28T11:15:16.990Z",
null,
1,
"I",
"0",
"Use Estimate/Sales Order Contact address as the default Billing and ShipTo Address",
"If the customer default Bill-To and Ship-To address is 'PRIMARY' and you set this preference to 'Yes', the address for the selected \r\
customer contact on the Estimate/Sales Order will be used for the default Billing and Shipping Address on the Estimate/Sales Order.",
"COMBO BOX",
null,
37,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
30
],
[
"88F7915F-F0B5-42B9-AC84-7E501F4706A0",
null,
null,
null,
1,
"I",
"0",
"Show label printing details on Job Ticket by Form",
"Show additional details related to label printing on the Job Ticket by Form as a new line at the bottom of each task. This will include footage in the staging section, the run speed in the press section, and labels, footage, and count per roll for the Rewind/Slitter task. ",
"COMBO BOX",
null,
399,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"5DBDA72D-7F2C-4C49-9C8F-7F0027277BB9",
null,
"2017-09-28T19:51:48.935Z",
null,
1,
"I",
"0",
"Show Order Total $",
"Displays the Order total $ on the Job Ticket.",
"COMBO BOX",
null,
40,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
59
],
[
"F2D806FD-3769-4335-B55F-7FB53F10B464",
null,
"2017-09-28T19:51:48.865Z",
null,
1,
"I",
"1",
"Press Details - Delivery Mode",
"Display the delivery mode info (Grand Format only)",
"COMBO BOX",
null,
182,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
23
],
[
"D32D6BB0-0B72-4EAE-B53F-819C319A2858",
null,
"2018-02-01T18:37:20.502Z",
null,
1,
"I",
"0",
"Enable weight for postage task to use max weight as per Postage Method",
"Only use when importing postage tasks from a Web to Print system where a postage task is automatically created and you want to default the weight input to be based on the maximum weight defined by the Postage Method. The default is to derive the weight for the postage task based on the section details. ",
"COMBO BOX",
null,
253,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
87
],
[
"DAD8CE74-A86E-4AD2-96D8-838F1BC617BF",
null,
"2016-12-17T17:53:06.044Z",
null,
1,
"I",
"0",
"Show Bleeds - Quote Letter 2",
"Will print \"Bleeds\" information on the quote (Quote Letter 2 only)",
"COMBO BOX",
null,
173,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
53
],
[
"0E633FD3-4C9F-40B5-AC47-83D972A72C9E",
null,
null,
null,
1,
"I",
"0",
"Show Value Added Grid",
"Displays a value added grid for every detail line and quantity.",
"COMBO BOX",
null,
219,
"vl_YesNo",
"S",
"E7DC3CAF-2A90-42C4-8062-82AE6760682D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
75
],
[
"BC097FD0-7C5F-424A-9157-83E478F90DBB",
null,
null,
null,
1,
"I",
"0",
"Automatically confirm shipments from Postage Reconciliation",
"When the Postage Reconciliation is confirmed, automatically \u201cConfirm\u201d all shipments for the sales orders included in the reconciliation batch. This eliminates the need to manually mark those shipments as confirmed",
"COMBO BOX",
null,
276,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
99
],
[
"E83CA3E2-C561-4807-A447-854CB2A2909C",
null,
"2019-11-27T20:52:12.530Z",
null,
1,
"T",
null,
"Default Chargeback Debit Business Unit",
null,
"TEXT FIELD",
null,
320,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
134
],
[
"3DDA5856-9FF4-42E4-93BD-8570B610C69F",
null,
"2024-08-09T15:51:03.776Z",
"4C02950B-9DDC-49C7-9B64-D864C6AB7071",
1,
"I",
"1",
"Use Invoice Register to post invoices to accounting system",
"Selecting 'Yes' will use the Invoice Register program to post invoice transactions to the selected accounting system. \
Selecting 'No' will enable the feature to post invoices to accounting system based on 'Ready To Post' status.",
"COMBO BOX",
null,
386,
"vl_YesNo",
"S",
"A5CF794F-0CDF-4AD8-8130-9EC753BE1886",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"BE6F61D2-9160-4C3D-92C1-860F093C7C7F",
null,
"2017-09-28T19:51:48.873Z",
null,
1,
"I",
"0",
"Print section, operation and milestone bar codes on job ticket style 1 & 2.",
"Select 'Yes' to print section, operation and milestone barcodes on the job ticket.\
Only applies to style 1 and 2.",
"COMBO BOX",
null,
134,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
27
],
[
"894050C5-3D8F-446D-8967-863FDE2F881F",
null,
null,
null,
1,
"I",
"0",
"Show Section Description",
null,
"COMBO BOX",
null,
332,
"vl_YesNoReversed",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
144
],
[
"CBA2166B-D12B-4C48-BA54-864E8B4FEF05",
null,
null,
null,
1,
"T",
null,
"Alternate 'Ship-To' label",
"If you wish to override the system address naming convention for 'Ship-To' address",
"TEXT FIELD",
null,
63,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"ABB8E59A-D6EC-4A53-A6CF-86B1383B0E41",
null,
"2024-02-07T17:14:28.065Z",
null,
1,
"I",
"0",
"JDF export based on budget or schedule",
"Base the JDF export to integrated systems on either the budget from the sales order (section and tasks) or the milestones generated from the section, which may be modified in scheduling.",
"COMBO BOX",
null,
378,
"vl_BudgetSchedule",
"S",
"E4740F51-07DB-4006-9320-39D35F7D512A",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
171
],
[
"E2590BB2-7596-49D2-9992-86E83F2FB7C7",
null,
"2018-03-28T11:15:17.029Z",
null,
1,
"N",
".005",
"Minimum Paper Caliper",
"The minimum paper caliper is used in conjunction with the \"Maximum Folds based on Minimum Paper Caliper\".",
"TEXT FIELD",
".000",
3,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
47
],
[
"BEDDF2E5-87E0-427C-9973-86FD210DA477",
null,
"2017-09-28T19:51:48.924Z",
null,
1,
"I",
"0",
"Show Org Name on Header",
"Shows the Organization Name on the Job Ticket header.",
"COMBO BOX",
null,
46,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
53
],
[
"1FADAD33-05D7-4016-A01F-87253B4F8FF6",
null,
null,
null,
1,
"I",
null,
"Dynamics GP Receivable Invoices include the Sales Order number in the Invoice Description Field.",
"By default, the GP Dynamics Receivable invoice description field displays the Slingshot Invoice Register Number.\
When this preference is set to 'Yes', all sales order numbers referenced on the Slingshot invoice will be appended to the current invoice description to a maximum of 30 characters.",
"COMBO BOX",
null,
408,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"07120C83-7088-435F-B426-8735356FA6D1",
null,
"2017-07-18T17:36:44.785Z",
null,
1,
"I",
"",
"Enable reservation scheduling",
"Select if the ability to set up a reservation for machine time and production time is available.  When you want users to be able to schedule a reservation in an estimate and/or in a sales order, you can enable this function.",
null,
null,
206,
"vl_YesNo",
"S",
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"840C7F57-EB6C-48AE-9966-89F87D517D4D",
null,
null,
null,
1,
"I",
null,
"Shipping Label Format",
"Select the label format to print with shipping labels. Note: Not applicable if using shipping integrations.",
"COMBO BOX",
null,
81,
"vl_ShippingLabelOptions",
"S",
"685683E9-65D2-4137-9D85-6DF00BD5FED7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"0A0A1022-9954-4BF2-B65C-8A1F24CFAE74",
null,
null,
null,
1,
"I",
null,
"Number of days to look ahead for a free spot on the schedule (default is 365)",
null,
null,
null,
411,
null,
null,
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"D4533B79-A6A7-47C2-9EA0-8A626E8CCDF8",
null,
"2016-10-19T00:45:59.410Z",
null,
1,
"T",
null,
"\"Leave (Unpaid)\" Exception Type",
null,
"TYPE AHEAD",
null,
158,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"22F52514-9834-4246-90E3-8A7BB1BCBA30",
null,
"2017-11-06T17:13:11.522Z",
null,
1,
"I",
"0",
"Print box references on the packing slip",
"Setting this preference will print a distinct comma delimted list of box references on the packing slip at the line level.",
"COMBO BOX",
null,
128,
"vl_YesNo",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"8C490706-EED6-44BE-9A62-8ADDB040B1EA",
null,
"2017-09-28T19:51:48.875Z",
null,
1,
"I",
"0",
"Show milestone section on standard job ticket, Style 1 & 2.",
"Select 'Yes' to show the job milestones on the job ticket.",
"COMBO BOX",
null,
133,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
28
],
[
"D3497253-8F3C-482E-BEB5-8C5DA6A37918",
null,
"2017-09-16T17:05:07.207Z",
null,
1,
"I",
"0",
"Maximum number of items in valuelist",
"The maximum number of items to load in the valuelist. If you exceed the limit, it will load the Item Lookup dialog to improve performance.",
"TEXT FIELD",
null,
165,
null,
null,
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"B6672BF0-A339-45B2-9E04-8C6A696E8331",
null,
null,
null,
1,
"I",
"0",
"Allow cash receipt adjustments to be included in the amount applied",
"Set to true if you want to Allow cash receipt adjustments to be included in the amount applied",
"TEXT FIELD",
null,
288,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
104
],
[
"E2718F8B-AF53-423E-B115-8C818435F849",
null,
"2024-12-30T14:59:16.783Z",
null,
1,
"I",
"0",
"Remove Century from Dynamics GP Transaction Batch Numbers",
"Selecting 'Yes' will remove the century from the year portion of the batch number in the following registers.\
1. Sales Invoice and Credit Note\
2. Register based Journal Entries\
3. P/O Receipt",
"COMBO BOX",
null,
406,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"50728CE7-EFBD-46BE-9038-8DB5AD639D5F",
null,
"2023-09-22T14:42:07.172Z",
null,
1,
"I",
null,
"Quantity Produced Comments are mandatory if a Quantity is entered.",
null,
"TEXT FIELD",
null,
117,
"vl_YesNo",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"7958C004-9634-4616-9548-8E01D14A1765",
null,
"2023-08-25T19:58:28.828Z",
null,
1,
"I",
"0",
"Turn on Dev Logging for Velocity Web Server",
"Turns on requests logging for velocity end point",
"COMBO BOX",
null,
370,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
164
],
[
"5B2FCB78-15F9-42E9-8966-8E17E4B0C87A",
null,
"2017-11-06T17:13:13.956Z",
null,
1,
"I",
"1",
"Print Original Quantity Ordered From Sales Order",
"When 'Yes' is selected, the original quantity ordered will print on the packing slip",
"COMBO BOX",
null,
129,
"vl_YesNo",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"336BAFB3-6AF9-45C0-9371-8ED0A3335D3E",
null,
"2022-11-18T15:58:35.509Z",
null,
1,
"I",
null,
"Number of Line Items in shipping batch to trigger use of additional processor.",
"For large batches in Batch Shipping, an additional processor (a.k.a. \u201cheadless client\u201d) can be started to improve performance of the \u201cCreate Shipments\u201d and \u201cConfirm Shipments\u201d functions and allow the user to leave Batch Shipping while the batch is processed. This setting is a threshold, based on the total number of line items in the sales orders that have been added to the batch, to trigger the use of this additional processor. This additional processor will consume two concurrent user licenses while it is running. Default is blank, meaning the additional processor will never be used.",
"TEXT FIELD",
null,
361,
null,
null,
"356DD7E5-A82F-4A2E-8596-2F686105F66F",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
159
],
[
"CEDF3471-A53E-4948-8DCB-8F320B1D712F",
null,
"2018-03-28T11:15:17.025Z",
null,
1,
"N",
".125",
"Minimum Top Gutter",
"The minimum size of the top gutter to use for the imposition.",
"TEXT FIELD",
".000",
5,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
45
],
[
"C1DA6C2D-8D21-4ACD-8A93-8F49ACC737F7",
null,
null,
null,
1,
"T",
null,
"Invoice Text Description",
"This field is assigned to the column: TEXTDESC in the Sage 300 invoice export.",
null,
null,
405,
null,
null,
"599445EC-CD18-40FC-BBF2-5B0AE90547C7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"4390B7CF-8395-4022-8C4F-8F75340CA7AA",
null,
null,
null,
1,
"I",
"0",
"Add Operations to Production Job View",
null,
"COMBO BOX",
null,
417,
"vl_YesNo",
"S",
"E4740F51-07DB-4006-9320-39D35F7D512A",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"8F1E28F2-DD2D-4550-94EC-90128C251D1B",
null,
"2019-04-11T14:52:40.788Z",
null,
1,
"I",
"0",
"Enable on VAT Invoice Form Freight to be included on a line",
"\u2022 Yes - will enable Freight as a line item on UK VAT Invoice\
\u2022 No (default) -  will not diplay freight details as a line item on UK VAT Invoice",
null,
null,
303,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
121
],
[
"CB8673C2-836C-44AA-8F6B-907333A4C76E",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"T",
"P",
"Method to automatically allocate Freight Cost to Jobs",
"Method to automatically allocate Freight Cost to Jobs",
"COMBO BOX",
null,
267,
"vl_quantityPrice",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"8B84DCC8-4C33-4F35-A96A-91500AF64E93",
null,
"2020-10-22T19:29:34.688Z",
null,
1,
"I",
"0",
"Vouch Sales Tax on A/P Invoice",
null,
"COMBO BOX",
null,
333,
"vl_YesNo",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
145
],
[
"FBE9E05F-6A93-4BE3-B01C-91613A070969",
null,
"2017-09-28T19:51:48.883Z",
null,
1,
"I",
"0",
"Style 2: Address Info - Show address",
"Show the Customer address info on the Job ticket.\r\
Job Ticket Style 2 Only.",
"TEXT FIELD",
null,
100,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
32
],
[
"3C64EFAC-5627-4D9E-9B1A-91C0BDCCE5F0",
null,
"2017-09-16T17:05:07.205Z",
null,
1,
"T",
"ASC",
"Pick Route Order",
"Select how the items in a pick list should be sorted based on the setup of bin locations in the warehouse.The items will be sorted in ascending or descending order for each alphanumeric bin location segment ",
"TEXT FIELD",
null,
177,
"vl_sortDir",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"2E5601D0-95C6-4DC5-B042-91CE691BFA3E",
null,
null,
null,
1,
"T",
null,
"Consolidated Revenue Account",
"This account will be used as the revenue account in the Sage 200 export file for both Invoices and Credit Notes.\
Note: This account has no impact on the Invoice or Credit Note distributions.",
"TYPE AHEAD",
null,
344,
"avGL_ChartOfAccounts_All_withDesc_Active",
"S",
"F75662F3-7D9C-42FC-A8F9-5AA2FFC62A43",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
153
],
[
"2E792415-055F-4029-A0BA-93EBE64CC086",
null,
"2017-10-04T21:57:52.262Z",
null,
1,
"I",
"1",
"Style 1: Press Details - Plex/Color",
"Press Details - Plex/Color  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
235,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"CE741A01-7632-4D2A-BBB9-941CD58E906D",
null,
null,
null,
1,
"T",
"2:00 AM",
"Customer Upload Time",
null,
"TEXT FIELD",
null,
93,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"FEF63471-8DC1-46AD-9010-95EDCFC01A12",
null,
"2020-01-26T16:27:10.164Z",
null,
1,
"I",
"0",
"Include Credit Limit when updating the customer record in MS Dynamics GP",
"Include the Credit Limit when updating the customer record in MS Dynamics GP. \
Setting this option to \"Yes\" requires that the credit limit in Avanti Slingshot already exist in the GP system. \
They will not be created on the fly. \
If they do not exist in GP, the user in Avanti Slingshot will receive an error when the update is triggered.",
"COMBO BOX",
null,
292,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"DA381AD5-6EF5-4C1E-8BF0-95F11E5DCA13",
null,
"2018-03-28T11:15:17.008Z",
null,
1,
"T",
"4/4",
"Section Color Default",
"The default colors to use when adding a new section to an estimate. \r\
This can be changed for any given section (except a version which inherits this from the parent section).\r\
If a Work Template is used, then this setting is ignored.",
"TEXT FIELD",
null,
12,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
38
],
[
"44210E69-9F09-4A34-ACD0-96092DE04156",
null,
null,
null,
1,
"T",
"",
"Postage Request",
"We have estimated your postage costs and require a postage deposit of: <br>\r\
$<taskPrice><br>\r\
<br>\r\
Our estimate #: <estNr><br>\r\
# pieces being mailed: <taskQty><br>\r\
<br>\r\
Please send payment to:<br>\r\
Ref #: <estNr><br>\r\
<orgName><br>\r\
<orgAdd1><br>\r\
<orgCity>, <orgState><br>\r\
<orgZip><br>\r\
<br>\r\
Sincerely,<br>\r\
<br>\r\
<empFull>\r\
\r\
Fields you can use are:\r\
<orgName><orgAdd1><orgCity><orgState><orgZip>\r\
<empFirst><empLast><empFull><empTitle>\r\
<contactFirst><contactLast><contactFull><contactTitle>\r\
<companyName><companyAdd1><companyCity><companyState><companyZip>\r\
<taskQty><taskPrice><taskSpec>",
"TEXT FIELD",
null,
60,
null,
null,
"66D49C45-D474-423D-B190-E7E27678B023",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"0FD4242A-E24B-4304-A4B7-9633482DD6B7",
null,
null,
null,
1,
"I",
null,
"Include Estimate Comment on Quote letter",
"When a new estimate comment is created, do you want the comment included on the quote letter by default.",
"CHECK BOX",
null,
23,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"AA039DC1-3D55-4E84-A6A6-9745AAF0FD1B",
null,
"2017-09-28T19:51:48.879Z",
null,
1,
"I",
"1",
"Show Due at Customer Date",
"Display the 'Due at Customer' date field on the job ticket.",
"TEXT FIELD",
null,
122,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
30
],
[
"F3FC31AE-3C82-4244-BF0F-97A7F6DCCAC4",
null,
"2017-09-28T19:51:48.853Z",
null,
1,
"I",
"1",
"Show ink details on Job Ticket",
"Show ink details on Job Ticket (default is Yes).",
"COMBO BOX",
null,
204,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
17
],
[
"D2829F13-52DC-434A-BE1C-983C2520E294",
null,
null,
null,
1,
"I",
"0",
"Enable Child customers to use Parent customer price rules",
"Turning this 'On' enhances the price rules logic so all price types that include \u201cCustomer\u201d as a\
segment will apply price rules from a Parent customer where no price rule has been applied \
from the estimate/sales order customer. That is, price rules from a Parent customer apply \
as a default. \
\
When a Child customer uses a Parent, the price rule for the Child will maintain and use its own \
Customer Category and Customer Class values and not inherit those from the Parent. Only \
price segments from the customer down will apply. \
",
"COMBO BOX",
null,
317,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
131
],
[
"57BED1C3-D517-4B2F-AABC-99FA780ABEEF",
null,
null,
null,
1,
"I",
"0",
"Allow creating credit notes for invoices with zero balance owing",
"When 'No' selected, the system will not allow creating a credit note for an invoice with a zero balance owing.\
Selecting 'Yes', will allow creating a credit note for zero balance owning invoices.\
",
"COMBO BOX",
null,
414,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"03E6BA10-6A4E-4830-945D-9A3F11ED2C6C",
null,
null,
null,
1,
"N",
"0.00",
"Matching tolerance percentage when matching purchases to invoices.",
"An invoice will only be considered matched, and therefore available to post via the AP Invoice Register, when the remaining unmatched amount of the invoice is less than the lesser of the Matching Tolerance Amount or the Matching Tolerance % ",
"TEXT FIELD",
null,
104,
null,
null,
"BAE50AC0-5230-470D-861A-14620D4F73F9",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"C188FA7D-FBC9-4A17-AD50-9AF17B97F6E8",
null,
"2018-03-28T11:15:16.999Z",
null,
1,
"T",
null,
"Standard Ink Type",
"This is the standard ink type that should be added for any section added.\r\
For example, if you specify 4/4 color, then 4 inks for the front and 4 inks for the back will be added, and all the\r\
inks added will be of this type.\r\
The ink types can be changed for a given section in the Section dialog, on the Inks tab.",
"COMBO BOX",
null,
16,
"avInkTypes",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
34
],
[
"DC40629A-C95A-44FF-9B30-9B8AD12C5389",
null,
"2018-06-21T17:57:49.333Z",
null,
1,
"I",
"0",
"Allocate Purchase Variances to Received Items",
null,
"COMBO BOX",
null,
264,
"vl_YesNo",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
93
],
[
"B04DCAD8-A694-4794-B503-9BA64851CBD5",
null,
"2017-02-15T15:44:03.376Z",
null,
1,
"T",
"F",
"Schedule automatically released jobs forwards or backwards",
"This setting applies to those jobs created from imported (XML) sales orders that have been released automatically, with Work Types selected to release to schedule automatically.",
"COMBO BOX",
null,
147,
"avScheduleDirection",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"CA027AC3-36AE-48CF-9E65-9CBD3DF7B727",
null,
null,
null,
1,
"I",
"0",
"Use Warehouse Default Issue location to allocate remaining quantity to pick",
"When a pick is created, automatically allocate the \u201cRemaining Qty\u201d that could not otherwise be allocated for an item (i.e. unusable or unavailable, not backordered) to its Warehouse Default Issue Location. \
This assumes that when the pick is completed, the item will have been replenished there in the meantime.",
"COMBO BOX",
null,
360,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
158
],
[
"9E8ACDEE-FC88-48BE-BE00-9CF586AE7C77",
null,
"2017-09-28T19:51:48.889Z",
null,
1,
"T",
null,
"Select Job Ticket to Print on Order release and in Shop Floor",
"If you specify the standard job ticket an option will be provided to print all the job tickets when releasing a sales order.",
"COMBO BOX",
null,
92,
"vl_Reports$JobView",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
35
],
[
"01C9657F-8347-4234-B72A-9E0247535F19",
null,
"2018-03-28T11:15:17.013Z",
null,
1,
"N",
".125",
"Section Bleed Size",
"The size of the bleed to be used for the imposition.",
"TEXT FIELD",
".0000",
10,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
40
],
[
"769661D1-6D88-430F-8BBD-9E39DADD1F66",
null,
"2018-03-28T11:15:17.011Z",
null,
1,
"I",
"1",
"Section Color Bar Active",
"When adding a new section to an estimate, this determines if the color bar should be on or off. \r\
If you have specified a color bar on/off in a Task or a Work Template is used, then this setting will be ignored.",
"COMBO BOX",
null,
11,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
39
],
[
"660C69D5-779B-4D22-8E8E-9EBE201B8605",
null,
"2017-09-28T19:51:48.910Z",
null,
1,
"I",
"0",
"Show Payment Method",
"Will display the payment method on the Job Ticket billing section.",
"TEXT FIELD",
null,
61,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
46
],
[
"8C29BA37-F6ED-49D5-A63D-9F023C1F7B98",
null,
null,
null,
1,
"T",
"DATE",
"Select \"Due at Customer\" field format in Estimates",
"Allow the \u201cDue at Customer\u201d field in Estimates to show the date only (default) or date and time the order is due at customer",
"COMBO BOX",
null,
355,
"vl_dueAtCustomerBehavior",
"S",
"07A7E161-29E7-4497-8771-D77FC37F2D8A",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
156
],
[
"379A200A-30CB-433A-8CC5-9F32F6FC4777",
null,
"2017-10-04T21:57:52.269Z",
null,
1,
"I",
"0",
"Style 1: Show Section Description and Quantity",
"Show Section Description and Quantity  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
231,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"60BDF258-EB10-42ED-8625-A084B8A7D81E",
null,
"2023-04-03T16:25:30.266Z",
null,
1,
"T",
null,
"Custom Invoice Message II",
"",
"TEXT AREA",
null,
366,
null,
null,
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
161
],
[
"67B6616D-4EFF-4E6D-A51A-A0E611A0EFD4",
null,
null,
null,
1,
"I",
"0",
"Print Summary Work Order report when Pick List report is selected",
null,
"COMBO BOX",
null,
375,
"vl_YesNo",
"S",
"6A0131C4-5FFE-4088-8283-4C40E7179878",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
168
],
[
"DB46779B-1165-47B6-BA38-A1CA3CE7113D",
null,
"2018-03-28T11:15:16.944Z",
null,
1,
"T",
"1",
"Show Workflow Group in \u2018Task\u2019 drop-down on Sections tab",
"This preference controls whether the Workflow Group appears as a pre-fix to the Estimating Standard description",
null,
null,
148,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
11
],
[
"989B1CF8-64F3-4E9C-B364-A1DFEF6E084C",
null,
null,
null,
1,
"I",
null,
"Include Comment on Job Ticket",
"Include/Exclude Comment on the Job Ticket",
"TEXT FIELD",
null,
32,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"A5C85E41-BCBA-4593-9EAE-A233EF66F914",
null,
null,
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
64,
null,
null,
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"6E48B34D-9874-4FA7-80EC-A3699EDEC61E",
null,
"2017-09-28T19:51:48.904Z",
null,
1,
"I",
"1",
"Press Details - Show Flat Size",
"Display the \"Flat Size\" field in the Press results.",
"TEXT FIELD",
null,
84,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
43
],
[
"A39469B8-74FB-4B33-BF15-A381008E6CE6",
null,
null,
null,
1,
"I",
"1",
"Include Tax calculation value on Advance Billing",
"Select 'Yes' to allow the system to automatically bring in the tax values onto the Advance Bill.  \
Select 'No' to turn off the tax values on the Advance Billing",
"COMBO BOX",
null,
257,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
90
],
[
"0C325004-82BF-497E-831D-A4B50EDE37C1",
null,
"2023-04-03T16:25:20.671Z",
null,
1,
"T",
null,
"Custom Invoice Message I",
"",
"TEXT AREA",
null,
365,
null,
null,
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
160
],
[
"3247EFFB-C1C4-4AAA-9C47-A66747511F46",
null,
null,
null,
1,
"I",
null,
"Include Estimate Comment on Sales Order Acknowledgment",
"When a new estimate comment is created, do you want to include by default on the Sales Order Acknowledgment.",
"TEXT FIELD",
null,
25,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"CEC8A836-E775-4461-8366-A671690052D7",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
null,
"Maximum number of records to display in the orders waiting to ship dialog",
"This allows you to set the maximum number of records to appear in the orders waiting to ship dialog. This is only required\
if you have a large number of sales order to ship.\
\
Example: 500 - This would query a maximum of 500 records. If your sales order is not in the list \
                       then use the filters to narrow down the search.\
\
Note: A value of zero will return ALL orders that are ready to ship.",
null,
"#,###",
216,
null,
null,
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"4A67C1FA-179A-4F06-8FCF-A6DF5E4CC4A8",
null,
"2018-03-28T11:15:17.034Z",
null,
1,
"N",
".40",
"Default Markup %",
"The default markup on Estimates and Orders.",
"TEXT FIELD",
"globals.avBase_percentageFormat",
1,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
49
],
[
"E6FCDFA9-02D8-4C6D-8B6D-A831D70C3480",
null,
null,
null,
1,
"T",
"DATE",
"Select \"Due at Customer\" field format in Sales Orders",
"Allow the \u201cDue at Customer\u201d field in Sales Orders to show the date only (default) or date and time the order is due at customer",
"COMBO BOX",
null,
356,
"vl_dueAtCustomerBehavior",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
157
],
[
"B5CBD1D4-A7FF-4F0F-8827-A8B34B06414C",
null,
null,
null,
1,
"I",
"0",
"Use POS System?",
null,
null,
null,
144,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
40
],
[
"75538130-009C-443B-AAD5-A90F0986B78A",
null,
null,
null,
1,
"T",
null,
"Contract Minimum Charge Service Item",
"Link the inventory service item associated with minimum order charges. This item will appear on sales order invoices when minimum order charges are billed.",
"TYPE AHEAD",
null,
212,
"avItem_ServiceItems",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
65
],
[
"7A58050D-F84A-4166-8581-AA0F5CF0B048",
null,
"2019-08-12T18:19:31.259Z",
null,
1,
"I",
"0",
"Recalculate Production Time When Moving Milestones from One Resource to Another",
"The Time Needed for a Milestone that is moved to a different Resource, either manually or automatically with Load Balancing functionality, will be recalculated using the speed of the Resource it is being moved to based on the corresponding Estimating Standard. If multiple Tasks share the same Cost Links / Operations, the Time Needed will be calculated using the default Estimating Standard selected on the Equipment record. \
If the speed for the Resource the Milestone is being moved to is based on a Variable and there are multiple Variables on the Estimating Standard, the Variable with the slowest speed will be used when recalculating.\
Note there could be an impact on performance when using this functionality depending on the number of Work Pools used in a Section and the number of Resources within those Work Pools.",
"COMBO BOX",
null,
308,
"vl_YesNo",
"S",
"D4A04BDB-4FE1-4767-904E-01250E66C7AE",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
126
],
[
"AF4AC055-FA97-4F83-B652-AA69507C87D8",
null,
"2021-11-30T17:34:35.199Z",
null,
1,
"T",
null,
"Return Freight Service Item",
null,
null,
null,
341,
"avItem_ServiceItems",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
150
],
[
"11143B92-A693-4247-8D05-AB163B8ABC92",
null,
"2016-12-17T17:53:36.872Z",
null,
1,
"I",
"0",
"Show Front & Back Colors - Quote Letter 2",
"Will print \"Front & Back Colors\" information on the quote (Quote Letter 2 only)",
"COMBO BOX",
null,
174,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
54
],
[
"F697A203-9566-4DBF-B26F-AC11DC7753F4",
null,
"2016-09-20T15:33:54.587Z",
null,
1,
"T",
null,
"Is overtime calculated based upon hours over in a regular shift or hours over in a regular pay period",
null,
"COMBO BOX",
null,
153,
"vl_overtimeCalcType",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"DE71A6D7-4CE3-412E-A72B-AD244B9F6955",
null,
null,
null,
1,
"I",
"0",
"Show up to 15 Task Level Comments on Job Tickets Style 1 and 2",
null,
"COMBO BOX",
null,
384,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
174
],
[
"5C506981-7437-4D87-8A55-AD2719CADF90",
null,
"2020-01-26T16:27:10.173Z",
null,
1,
"I",
"0",
"Include Customer Class when updating customer record in MS Dynamics GP",
"Include the Customer Class when updating the customer record in MS Dynamics GP. \
Setting this option to \"Yes\" requires that the customer class IDs in Avanti Slingshot already exist in the GP system. \
They will not be created on the fly. \
If they do not exist in GP, the user in Avanti Slingshot will receive an error when the update is triggered.",
"COMBO BOX",
null,
290,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"DA6A5983-F58B-4870-AF2D-AF22C5B01DDE",
null,
"2018-03-28T11:15:16.939Z",
null,
1,
"I",
"0",
"Grand Format only \u201cRestrict imposition to use narrowest material when laminating or mounting\u201d",
null,
"COMBO BOX",
null,
195,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"128523BF-4750-4D7D-8DC3-AF541264360E",
null,
"2020-04-15T20:35:55.148Z",
null,
1,
"T",
"Shipped Qty",
"Default Quantity for Invoice",
"If a shipment exists and the user has set this preference to default to the \"Ordered Qty\", \
then the shipment will be ignored and the ordered quantity will be used instead for the invoice quantity.",
"COMBO BOX",
null,
203,
"Shipped Qty,Ordered Qty",
"M",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
57
],
[
"A44829D3-EBB2-4EC9-B054-B0A79C2B06BA",
null,
"2022-06-06T17:50:35.336Z",
null,
1,
"T",
"MM/dd/yyyy",
"Date Format",
"Specify the date format to be used in the Sage 200 export file.",
"TEXT FIELD",
null,
345,
null,
null,
"F75662F3-7D9C-42FC-A8F9-5AA2FFC62A43",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
154
],
[
"66ECA497-3841-4AA6-A4FE-B18A77AA9D04",
null,
"2018-03-28T11:15:16.961Z",
null,
1,
"N",
".25",
"Adhesive: Envelope flap glue width",
"The default width of glue that will be used for the envelope flap (gumming), if no value is specified on the die itself.",
"TEXT FIELD",
null,
114,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
18
],
[
"16A1DE55-7991-4B0C-BA09-B1F93B178505",
null,
"2022-08-10T18:23:16.790Z",
null,
1,
"I",
"0",
"Show up to 15 Section Comments on Job Ticket Style 1 and 2",
null,
"COMBO BOX",
null,
352,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
153
],
[
"DE13DE4E-1479-456D-B237-B21D559A9C25",
null,
"2017-10-04T21:57:52.272Z",
null,
1,
"I",
"0",
"Style 1: Show Client Info (contact, phone, email, dept.)",
"Show Client Info (contact, phone, email, dept.)  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
230,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"1F025A57-D166-4C9D-8BD9-B35EAD6AD9A5",
null,
"2017-09-28T19:51:48.932Z",
null,
1,
"I",
"0",
"Show Chargeback Code",
"Shows the Customer Chargeback Code on the Job Ticket.",
"COMBO BOX",
null,
42,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
57
],
[
"66C91284-B52A-4419-90E6-B37E9EFCD338",
null,
"2018-08-22T22:52:51.705Z",
"899250C7-A418-4B87-BF10-3C536EEC04DA",
1,
"I",
"1",
"Average Warehouse Picking Lead Time ",
"Enter the lead time, in days, for the warehouse to complete picking of raw materials for production picks or fulfilment picks.\
The lead time would include the time from when the sales order is released to when the pick is accepted. Do not\
include time for purchasing of backorders or processing of the purchase receipt.\
\
The default is 1 day.",
"TEXT FIELD",
"##0",
273,
null,
null,
"29BE48E4-7A86-4347-8608-9C58871CC38C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
100
],
[
"DDBD337F-CE89-4583-A72A-B4FAF3461799",
null,
"2017-10-16T19:31:21.471Z",
null,
1,
"I",
"0",
"Disable G/L account number field account type restrictions",
null,
"CHECK BOX",
null,
220,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
76
],
[
"9481554E-FCBC-4623-8013-B5AF35E8B3F1",
null,
"2018-03-28T11:15:17.031Z",
null,
1,
"N",
".20",
"Default Bottom Line Markup Percentage",
"The default bottom line markup percentage used for the Estimate or Order.\r\
This appears shows in the Section Calculator, and can be changed for a given section.",
"TEXT FIELD",
"globals.avBase_percentageFormat",
2,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
48
],
[
"7D023B2B-99FC-4536-B88F-B6BC1221BAF6",
null,
"2020-01-26T16:27:10.170Z",
null,
1,
"I",
"0",
"Include Sales Person when updating customer record in MS Dynamics GP",
"Include the Sales Person when updating the customer record in MS Dynamics GP. \
Setting this option to \"Yes\" requires that the sales person IDs in Avanti Slingshot already exist in the GP system. \
They will not be created on the fly. \
If they do not exist in GP, the user in Avanti Slingshot will receive an error when the update is triggered.",
"COMBO BOX",
null,
291,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"7B95C3ED-B7AF-4F79-8978-B75FC8F13938",
null,
"2017-09-28T19:51:48.914Z",
null,
1,
"T",
null,
"Certification Form Number",
"Input a Certification Form Number to appear at the bottom of the printed form. This is typically used to hold the FSC number.",
"TEXT FIELD",
null,
51,
null,
null,
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
48
],
[
"343BEA4D-DA35-4F7B-B8F3-B8B0765895C5",
null,
null,
null,
1,
"I",
null,
"Include Comment on Sales Order Acknowledgment",
"Include/Exlude Comment from Sales Order Acknowledgment",
"TEXT FIELD",
null,
31,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"51D9E5CB-5615-4C4A-B970-B8C788213E4C",
null,
"2017-09-28T19:51:48.930Z",
null,
1,
"I",
"1",
"Show PO #",
"Shows the Customer PO# on the Job Ticket.",
"COMBO BOX",
null,
43,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
56
],
[
"43EA776F-9C18-4FCC-9F29-B8E790788852",
null,
"2017-11-20T14:48:23.379Z",
null,
1,
"I",
"0",
"Enable Sales Tax details on Invoice",
"This option will force Sales Tax Details that are linked to a section to be displayed on Sales Invoice. ",
null,
null,
246,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
83
],
[
"A34D10EA-F028-47DD-83AE-B917B412B3CB",
null,
"2019-07-08T13:24:40.459Z",
null,
1,
"T",
null,
"Chargeback Debit Control Account",
"Enter the default debitControl Account to use for the \u201cAccount Distribution\u201d Chargeback File detail level.",
"TEXT FIELD",
null,
310,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
126
],
[
"AC641A6E-AE67-4D11-8622-BB9D15110542",
null,
"2018-06-21T17:57:57.097Z",
null,
1,
"I",
"0",
"Allocate Freight Variances to Received Items",
null,
"COMBO BOX",
null,
266,
"vl_YesNo",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
98
],
[
"F2DB5348-514A-4FB2-89BB-BBEEAF11F95B",
null,
"2023-06-13T17:49:26.723Z",
null,
1,
"T",
"AUTO",
"Select bin allocation behavior for rolls in production pick list",
null,
"COMBO BOX",
null,
367,
"vl_binAllocationBehaviorForRolls",
"S",
"E4740F51-07DB-4006-9320-39D35F7D512A",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
160
],
[
"FF8A98C9-1DBD-496C-845D-BC0F3A138987",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Prevent confirmation of shipment until all shipment lines have been confirmed",
"Setting this preference to \u201cYes\u201d will require a user to scan/enter the item code or mark each line on every shipment \
as \u201cConfirmed\u201d before the shipment as a whole can be confirmed and packing slip printed. Default is \u201cNo\u201d.",
"COMBO BOX",
null,
277,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"FAFDDAB5-5AA6-4B4D-A413-BCA74D1AF9BF",
null,
null,
null,
1,
"I",
"0",
"Enable Register Auto Scheduler",
null,
null,
null,
202,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
71
],
[
"09DB100B-B487-4EC6-9923-BCC1FBAEE634",
null,
null,
null,
1,
"T",
"AVANTI",
"System data to use for credit check",
"This setting determines whether the credit check will be performed based on the data in Avanti Slingshot or Microsoft Dynamics GP in the case that the integration with GP is in use (system preference #68). If the integration with GP is not in use, the credit check will be performed based on the data in Avanti Slingshot (orders, invoices, cash receipts, and customer credit limit).",
"COMBO BOX",
null,
189,
"vl_pref_dataForCreditCheck",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
59
],
[
"B9E6C71C-5721-4340-A8A3-BD845136DC68",
null,
"2020-01-26T16:27:10.178Z",
null,
1,
"I",
"0",
"Auto-apply Cash Receipts to associated Invoices within MS Dynamics GP",
"Auto-apply Cash Receipts to associated Invoices within MS Dynamics GP. ",
"COMBO BOX",
null,
272,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"5F7F3ED0-F6C4-460C-BC54-BE0DAB6AA41E",
null,
"2017-11-06T17:13:13.959Z",
null,
1,
"I",
"1",
"Print Backorder Quantity on Packing Slip",
"When 'Yes' is selected, the backorder quantity will print on the packing slip",
"CHECK BOX",
null,
131,
"vl_YesNo",
"S",
"703F0C69-8A29-48BA-AC44-D18EE9409448",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"98724A95-7210-4F6B-84DB-BE1D29B3B989",
null,
null,
null,
1,
"I",
null,
"Transfer Comment to the Purchase Order",
"Transfer the comment to the Purchase Order",
"TEXT FIELD",
null,
36,
"vl_YesNo",
"S",
"7B5DE02F-A86F-4D92-84C1-45ACDEA1DC6C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"C3C2F857-91F2-42B9-BEB0-BE2C1463FFB2",
null,
null,
null,
1,
"T",
"M",
"Default EOQ/ROP Control Method",
"When using EOQ/ROP, this preference will be used to set the method of contolling the calculations.\r\
Manual - allows the user to tweak the control values\r\
Automatic - allow the system to control the values using the recalulation routine.",
"TEXT FIELD",
null,
67,
"avItemReorderControlMethod",
"S",
"1B7B552E-DB54-4564-B840-A9D7C3CD609E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"1DAC867B-88D2-4E77-A791-BE892FFF5E83",
null,
"2022-09-19T21:40:37.192Z",
null,
1,
"I",
"0",
"Add \"xml\" root element for Import Sales Order from Xml",
"Make API call response a valid Xml. Add \"xml\" root element for Import Sales Order from Xml.\
By default there is no \"xml\" root element in response xml.",
"COMBO BOX",
null,
358,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
157
],
[
"E1FDBB76-CCE9-4FAB-BABF-BEC9ADA940A3",
null,
"2016-10-19T00:46:11.501Z",
null,
1,
"T",
null,
"\"Unpaid Time Off\" Exception Type",
null,
"TYPE AHEAD",
null,
155,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
6
],
[
"C8BDE0D2-202E-4BE1-811F-C194BEDAB936",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"I",
"0",
"Automatically Apply Customer Details As Shipper",
" Automatcially set the customer, contact, and bill-to address of the first sales order on the shipment. ",
null,
null,
240,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"0C7AA32D-B7D4-440D-905D-C1DA7D5B5DA1",
null,
"2018-11-21T16:41:20.953Z",
null,
1,
"I",
"0",
"Disable Cost Accounting On Sales Invoice",
"By default, cost of goods sold accounting entries are included on the sales invoice based on production costs.\
If you do not want to include these accounting entries, disable this system preference by selecting the 'Yes' option.",
null,
null,
294,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
110
],
[
"50D43751-1C06-4123-AEA1-C221249D060B",
null,
"2016-12-17T20:51:43.426Z",
null,
1,
"T",
null,
"Show Terms and Conditions - Quote Letter 2",
"Will add a page break and then print the Terms and Conditions on a separate page. \
\
Format the Terms and Conditions using something like Notepad, then paste them into the Pref Default Value field. If\
there is text in the Pref Default Value field, then a page break will be added and all the text will begin printing on a\
new page. \
\
You can use HTML markup with this field.\
\
For example, you can try this in Pref Default Value:\
\
<html>\
<b>Terms and Conditions<\/b><br>\
<br>\
<b>1. Use Bold:<\/b> This was an example of bold text<br>\
<br>\
<b><i>2. Use Bold Italic:<\/b><\/i> This was an example of bold italic text<br>\
<br>\
<b>3. Use HTML Markup:<\/b> You can use most html markup tags to control the look of the terms and conditions<br>\
<\/html>",
"TEXT AREA",
null,
175,
null,
null,
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
55
],
[
"7F618EED-5DC9-49A0-97D6-C2264FAF46CA",
null,
"2018-03-28T11:15:16.929Z",
null,
1,
"N",
null,
"Envelope - Minimum patch material height",
"The minimum height to be used for patch material.",
null,
null,
218,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"44ED6413-BBD6-40E6-803C-C2711FF865F4",
null,
"2020-01-26T16:27:10.183Z",
null,
1,
"I",
"0",
"Create an Avalara Tax Transaction",
"Set to true if you want the Avalara Tax Integration to create a Transaction in AvaTax.",
"TEXT FIELD",
null,
193,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
9
],
[
"85DB1090-DBE1-410C-9B68-C2B0FE50154E",
null,
"2023-09-22T14:42:07.174Z",
null,
1,
"T",
"Allow",
"Allow, warn, or prevent operations from being started before predecessors have been marked as complete",
null,
"COMBO BOX",
null,
136,
"Allow,Warn,Prevent",
"M",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"C9DC5596-B9E8-4054-BE1C-C2B50C1E4481",
null,
"2017-09-28T19:51:48.916Z",
null,
1,
"I",
"1",
"Show Estimate Time for Tasks",
"Shows the estimated time in minutes for the tasks on each section.",
"COMBO BOX",
null,
50,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
49
],
[
"41F0DE7A-D6F3-44B2-A504-C335326EDE47",
null,
"2021-01-06T21:24:10.721Z",
null,
1,
"T",
"P",
"Method to automatically allocate Freight Revenue to Jobs",
"Method to automatically allocate Freight Revenue to Jobs",
"COMBO BOX",
null,
256,
"vl_quantityPrice",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
3
],
[
"2B2BC664-95C7-44B3-A1CB-C342182CE480",
null,
null,
null,
1,
"I",
"1",
"Distribution of quantity produced over multiple jobs in shop floor",
"Pro-rate based on section quantity (default setting):\
\
In Shop Floor, when there are multiple operations, the Quantity Produce is pro-rated based the Section Quantity\
\
Pro-rate based on line item order quantity: \
\
In Shop Floor, when there are multiple operations, the Quantity Produce is pro-rated based the Line item Ordered Quantity",
null,
null,
391,
"vl_shopfloorQtyProducedDistMethodPrefOptions",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
176
],
[
"AEA9B64A-4757-479C-81C6-C44C72814494",
null,
"2018-02-23T17:05:36.633Z",
null,
1,
"T",
"I",
"Are orders selected for Postage Reconciliation by invoice date (default) or ship date?",
null,
null,
null,
255,
"vl_invoiceAndShipDate",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
89
],
[
"83867D6A-8DBA-4163-B75C-C61D35CD3CAF",
null,
"2017-09-28T19:51:48.877Z",
null,
1,
"I",
"0",
"Job Ticket By Form: Suppress 'Net Quantity' field",
"Set this preference to 'Yes', if you wish to suppress printing the 'Net Quantity' field.",
"COMBO BOX",
null,
125,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
29
],
[
"EA16B82A-2271-40F8-8A61-C717EEED6191",
null,
"2017-10-12T14:12:16.740Z",
null,
1,
"I",
"0",
"Allow overriding division and plant G/L segment codes for balance sheet accounts",
"By Default, the plant and division g/l segment codes are defined at the division and plant level for all account types; however, if you turn on this \
system preference you will be able to override the plant and division segment codes for balance sheet accounts in plant maintenance.",
"COMBO BOX",
null,
239,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
77
],
[
"873E365C-0D34-4E01-BE66-C84C592E21FA",
null,
"2016-10-19T00:44:46.372Z",
null,
1,
"T",
null,
"\"Lunch\" Operation Code",
null,
"TYPE AHEAD",
null,
160,
"vl_CostCenter",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
11
],
[
"4D6D9234-5F95-4301-AE77-C9BD0409AEC0",
null,
null,
null,
1,
"I",
null,
"Transfer Comment to the Purchase Order",
null,
"TEXT FIELD",
null,
30,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"6D2B1451-ED51-45AF-B38D-CA4133E58099",
null,
null,
null,
1,
"T",
"Yes",
"Populate default location",
"Automatically populate the default (or only) warehouse/bin location in a new transaction started in the Avanti Mobile Application.",
"COMBO BOX",
null,
191,
"Yes,No",
"M",
"4102A78F-28B6-4C07-8F8A-5AFDDEF926BC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
59
],
[
"FBB27F4A-F834-493A-9434-CA63ECDB7C0E",
null,
null,
null,
1,
"N",
"4",
"Override the Register numbering values",
"Default is 4 digits, and you can not go below 4 digits once a register has active for the company, and you can not exceed 16 digits.",
null,
null,
364,
"vl_RegisterNumLength",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
160
],
[
"CD796EBB-313C-4751-BAEB-CAA75206CB0E",
null,
"2019-08-19T14:46:09.295Z",
null,
1,
"I",
"0",
"Enable Finished Size Area Calculation",
"Enabling this option will allow for the addition of a Finished Size Area Calculation to be made visible \
in the Work Template, Section Details and \"Enter Details\" and Wizard screens that will calculate and \
display the single unit area in square feet.",
"COMBO BOX",
null,
314,
"vl_YesNo",
"S",
"011E4871-EA2E-4765-AD5C-42AFCB5AC39D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"9A31FBC1-495C-4D1E-BBDE-CAF0A773E8AC",
null,
null,
null,
1,
"I",
"0",
"Filtering non-standard Ship-to-address from the Standard Invoice Printout",
"This preference allows the user to select the address (in the Multi-ship functionality) or filter the non-standard Ship-to-address from the Standard Invoice Printout.",
"COMBO BOX",
null,
347,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
155
],
[
"C1F64D7E-AACD-4294-983B-CB969C4A1293",
null,
null,
null,
1,
"I",
"0",
"Include Non-Standard packing slips in invoicing",
"When set to \u201cYes\u201d the Revenue and Cost on Non-Standard Packing Slips are included in Invoicing.",
"COMBO BOX",
null,
402,
"vl_YesNo",
"S",
"98BEDB9A-BC49-4B27-A16D-7AC469AEAE5C",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
177
],
[
"FD9A6ADB-EB53-4A2E-BB53-CCB745EA03E5",
null,
"2022-07-25T16:40:36.481Z",
null,
1,
"I",
"1",
"How the Minimum Order charge is to appear on printed invoice (Customer facing)",
"Option 1 - Show each job as a separate line item on the invoice with unit price and extended price showing. Then show the Minimum Order charge as a separate line, the amount showing will be the difference between the order subtotal and the minimum order charge on the customer record.\
Option 2 - Show each job on the invoice as a separate line item with unit price and extended price supressed.\
Minimum Order charge as a separate line showing the whole minimum order charge from the customer record.\
",
"COMBO BOX",
null,
349,
"vl_Options",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
156
],
[
"979F4EAD-10D3-43D8-90AB-CD06B818BA18",
null,
null,
null,
1,
"I",
"0",
"Press sections of the same parent section to use same press",
"Selecting \u201cYes\u201d for the system preference will change estimate modeling behavior so jobs having \
multiple press sections calculating from a common parent section, that the press calculated on the \
1st child section will be the press used on all subsequent child sections.\
(This system preference only applies to presses of type Conventional Press and Digital sheet).",
"COMBO BOX",
null,
270,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
97
],
[
"A1484571-9A92-4599-8615-CECE032E2034",
null,
"2016-12-17T17:53:25.397Z",
null,
1,
"I",
"0",
"Show Caliper - Quote Letter 2",
"Will print \"Caliper\" information on the quote (Quote Letter 2 only)",
"COMBO BOX",
null,
172,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
52
],
[
"22AF776A-6328-4D61-9E9C-CF38FDD20769",
null,
"2020-01-26T16:27:10.182Z",
null,
1,
"T",
null,
"Default Tax Detail ID to send to GP with Avalara AvaTax Integration",
"The Tax Detail ID to be used for ALL taxes in the Invoice Register post to GP when the system preference for creating a transaction in AvaTax (#193) is set to \"Yes\".",
"TEXT FIELD",
null,
205,
null,
null,
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"E482E876-75A5-4995-A925-D0B738B0C9C2",
null,
"2017-06-02T14:06:22.741Z",
null,
1,
"I",
"0",
"Automatically validate all multi-ship addresses via USPS",
"Automatically validate all multi-ship addresses on an order, using the USPS Address Standardization Web Tool, when the sales order or change order is released and also allow the user to manually initiate the address validation.",
"COMBO BOX",
null,
200,
"vl_YesNo",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
69
],
[
"A78A517E-0EC0-4216-960B-D10FC4F8E7BF",
null,
null,
null,
1,
"I",
"0",
"Show Substrate Type (Stock Type)",
"Will display the substrate type in the description on the quote letter, like:\r\
<Description 1>, <Substrate Type>, <Finish>. For example: \u201c80# Futura Laser Digital Cover \u2013 19 x 13, Cover, Gloss\u201d\r\
* See separate preference to display the Finish.",
"TEXT FIELD",
null,
97,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"CAAB8FCB-7614-4B6A-A788-D2D6211DA741",
null,
null,
null,
1,
"I",
"0",
"Lock Invoice Line Item",
null,
"COMBO BOX",
null,
279,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
102
],
[
"3E84442C-186A-4645-9683-D2D9BC042783",
null,
"2017-09-28T19:51:48.887Z",
null,
1,
"I",
"0",
"Style 2: Show finished size on Post Press Cutting task",
"Shows the finished size on the Post Press Cutting task.\r\
Job Ticket Style 2 Only.",
"TEXT FIELD",
null,
95,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
34
],
[
"EBC73F7C-4D5B-4ABA-9E01-D341F610D9A1",
null,
null,
null,
1,
"I",
null,
"Transfer Estimate Comments to the Sales Order",
null,
"TEXT FIELD",
null,
24,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"98C55FE8-7B3C-4B99-ACE7-D356623A3C33",
null,
null,
null,
1,
"I",
"0",
"Print Summary Work Order report with Job Ticket report",
null,
"COMBO BOX",
null,
383,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
173
],
[
"706CE8A6-9611-4C0B-B1BD-D3829431B534",
null,
"2024-07-05T16:29:54.772Z",
null,
1,
"I",
"1",
"Distribution of total time and material over multiple jobs in shop floor",
"Even Distribution (default setting):\
\
For multiple jobs/sections entered into shop floor at once for a select operation, apply the time and materials for that \
operation evenly across all selected jobs.\
\
Pro-rate based on budgeted time of each operation:\
\
For multiple jobs/sections entered into shop floor at once for a select operation, pro-rate the time and material based \
on budgeted time of the operations.\
\
Pro-rate based on volume (budgeted impressions) on individual sections: \
\
For multiple jobs/sections entered into shop floor at once for a select operation, pro-rate the time and material based \
on budgeted impressions of operations.\
\
Pro-rate based on Line item order quantity:\
\
For multiple jobs/sections entered in shop floor at once for a select operation, pro-rate the time and material based \
on line item order quantity.",
null,
null,
309,
"vl_shopfloorTimeDistMethodPrefOptions",
"S",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"97246741-0664-44C7-BC10-D4A68E075259",
null,
"2017-09-16T17:05:34.299Z",
null,
1,
"I",
"0",
"Automatically select oldest partial roll first",
null,
"COMBO BOX",
null,
106,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"D4BAAEE4-743C-4F12-AE22-D6618C7A1643",
null,
"2017-09-28T19:51:48.885Z",
null,
1,
"I",
"0",
"Style 2: Show open size on Post Press Cutting task",
"Shows the \"open size\" on the Post Press Cutting task. This is simply the flat size - the bleeds.\r\
Job Ticket Style 2 Only.",
"TEXT FIELD",
null,
96,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
33
],
[
"7BBD783A-8825-4D4C-9230-D83FAE4718AB",
null,
null,
null,
1,
"I",
"0",
"Print Tracking Number barcodes 4x4 on Shipping Labels",
null,
"TEXT FIELD",
null,
269,
"vl_YesNo",
"S",
"685683E9-65D2-4137-9D85-6DF00BD5FED7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
96
],
[
"9C85B792-109B-4CD4-8443-D8F64D883663",
null,
"2017-09-29T16:16:29.335Z",
null,
1,
"I",
"0",
"Style 1: Show Section Weight",
"Show Section Weight  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
237,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"2B83D77F-4959-4BDC-9D05-D91EF47E7B6C",
null,
"2018-03-28T11:15:16.968Z",
null,
1,
"N",
"1",
"When is a job marked as 'Complete'?",
null,
"TEXT FIELD",
null,
107,
"vl_JobCompleteOptions",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
21
],
[
"0E5C5B37-C21B-4F6D-A1E8-DA2933976D44",
null,
null,
null,
1,
"T",
null,
"Alternate 'Bill-To' label",
"If you wish to override the system address naming convention for 'Bill-To' address",
"TEXT FIELD",
null,
62,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"43F5FB7C-0E19-491C-9F3D-DA823F19A53D",
null,
null,
null,
1,
"T",
null,
"Chargeback Credit Control Account",
"Enter the default credit Control Accountto use for the \u201cAccount Distribution\u201d Chargeback File detail level.",
"TEXT FIELD",
null,
311,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
127
],
[
"CE6E06A5-2C0F-401D-9BA5-DA98A7B0AAD9",
null,
null,
null,
1,
"I",
"0",
"Display system errors to user?",
"Setting this to Yes will result in system errors being displayed to the user in a dialog. With it set to No (the default), errors are written to a log file.",
null,
null,
149,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
44
],
[
"54E9275F-6933-4FF0-ABF5-DACC93C9A1FE",
null,
null,
null,
1,
"T",
"I",
"Base Units",
"Set the default base units for weight and dimension fields.",
"TEXT FIELD",
null,
120,
"avBaseUnits",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"7AE5B91E-30F2-450C-BBD4-DB8002549EED",
null,
null,
null,
1,
"I",
"0",
"Print Summary Work Order report when Pick List report is selected",
null,
"COMBO BOX",
null,
376,
"vl_YesNo",
"S",
"F50163B3-CE82-44E3-9225-870496144011",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
169
],
[
"31A22E64-0A86-4330-8A46-DB9E315CD211",
null,
null,
null,
1,
"T",
"D",
"Method to Allocate Purchase Variance",
null,
null,
null,
263,
"vl_DollarQuantity",
"S",
"06A3CF4A-4496-44E8-B1C0-D68E77211B36",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
92
],
[
"BE397DB1-2D76-4E2F-8DCA-DBC5C29614B8",
null,
null,
null,
1,
"I",
"25",
"Maximum number of jobs in range for Job Costing reports",
"Maximum number of jobs that can be selected in the Job Number range in the Job Costing reports (Job Costing - Actual vs Budget, Job Costing - Actual vs Estimate, Job Costing - Actual vs Estimate by Dept). This limit will reduce the likelihood of a user accidentally selecting a large range that will cause a degradation in performance of the report. Default is 25.",
"TEXT FIELD",
null,
372,
null,
null,
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
165
],
[
"197F6FB6-DD54-4D44-BE4C-DC6CC2432000",
null,
null,
null,
1,
"T",
"DATE",
"Select \"Expected Ship Date\" field format in Sales Orders",
"Allow the \u201cExpected Ship Date\u201d field in Sales Orders to show the date only (default) or date and time the order is expected to be shipped.",
"COMBO BOX",
null,
403,
"vl_dueAtCustomerBehavior",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
181
],
[
"72BD12B2-4F78-43CE-9E99-DD4291583D74",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Debit Organization",
null,
"TEXT FIELD",
null,
322,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
136
],
[
"15473C62-BC72-419B-8CA9-DD5821B0A4A6",
null,
"2017-09-28T19:51:48.920Z",
null,
1,
"I",
"0",
"Show Staging Location",
"Shows the Staging Location on the Job Ticket",
"COMBO BOX",
null,
48,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
51
],
[
"D746B458-9E69-4776-97D8-DE6BCFC45D46",
null,
"2018-03-28T11:15:17.003Z",
null,
1,
"T",
"P",
"Default Orientation",
"This is the default orientation for the imposition, either Portrait or Landscape.\r\
This forces the modelling to look for only Landscape JDF patterns for example. \r\
Normally, only Portrait should be used here, and only in rare layout requirements would Landscape \r\
ever be used.",
"COMBO BOX",
null,
14,
"avSales_pageOrientation",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
36
],
[
"1F0F9920-1752-49DA-8C84-DED94AAA1D88",
null,
"2018-03-28T11:15:16.937Z",
null,
1,
"I",
"0",
"Envelope - Dimensions Naming Convention",
"By switching this setting from the default to the alternative option, the system will\
substitute the field labels on a print section such as where the system would display\
the labels \"Width x Length\" by default and will instead display \"Height x Width\".\
\
Also, this will reverse the position of the 2 fields in the UI (where necessary) so that it \
is always displays as \"Height\" and then \"Width\". \
\
Other labels will be changed to reflect the same convention, these are Work \
Template - Section flat and finished size, die details, window die details, \
Est/Ord: - Section table finish size, and Section dialog finish/flat size,\
and the Wizard.",
"TYPE AHEAD",
null,
196,
"avSales_envelopeDimensions",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
8
],
[
"68D76EE8-64D5-4D25-B600-DF16AAEA639C",
null,
"2017-09-28T19:51:48.869Z",
null,
1,
"I",
"1",
"Press Details - Run Time",
"Display the run time (Grand Format only)",
"COMBO BOX",
null,
179,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
25
],
[
"B0CFEA55-9057-41AA-B205-DF509E7D99F6",
null,
"2023-12-15T16:49:19.874Z",
null,
1,
"I",
"0",
"Add a \"Scheduled Date\" field to the line items inside the Planned Purchasing - Source details window",
"Enabling this system preference will add a \"Sales Order Date\" Column to the Source Window in Planned Purchasing. This new field will be populated from the \u201cscheduled date\u201d of the first press listed in job view column in the Ready to Schedule module for that job.",
"COMBO BOX",
null,
319,
"vl_YesNo",
"S",
"D698BF2E-4246-47A3-9E3E-BFAA22216291",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"58C9D46A-00A1-476F-9C1C-E11B4F3B4BC3",
null,
"2023-12-15T16:49:19.880Z",
null,
1,
"I",
"0",
"Allow purchasing for single warehouse only",
"By Default, a user can create a purchase order in planned purchasing for more than one warehouse. If you set this\
preference to 'Yes', the system will only allow purchasing for a single warehouse at a time.",
"COMBO BOX",
null,
126,
"vl_YesNo",
"S",
"D698BF2E-4246-47A3-9E3E-BFAA22216291",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
2
],
[
"85A4752D-B6D2-417B-A28F-E25D88ECD3E3",
null,
"2022-09-08T14:20:52.817Z",
null,
1,
"I",
"0",
"Turn on Dev Logging",
"Turning this on will enable logging in different places in the system. This is used by Avanti Development to troubleshoot problems.",
"COMBO BOX",
null,
357,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
156
],
[
"165AEFF8-56E0-4033-BD2A-E289E1113872",
null,
"2017-02-15T15:44:03.366Z",
null,
1,
"I",
"1",
"Create orders for non-customers",
"Action to take if a non-customer (prospect, suspect, etc.) tries to create a sales order. ",
"TEXT FIELD",
null,
118,
"vl_nonCustomerSalesOrderActions",
"S",
"4E7CD55F-ADAE-4672-88B8-B54A6970C2CC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
7
],
[
"42EF8650-0A0B-47D4-8AFE-E2B729ECC472",
null,
null,
null,
1,
"I",
"1",
"Enable sending estimate test results to Avanti",
null,
null,
null,
135,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
32
],
[
"99EE90D0-E7E1-4D7E-8C61-E38428885D03",
null,
"2020-03-26T15:12:12.561Z",
null,
1,
"T",
"M",
"Cash Receipt Status for Sales Order Deposits",
"Changing the status to \u201con Hold\u201d requires the user to release the record for it to be available for posting in the Cash Receipts Register. \
Using \u201cReady to Post\u201d Status enables the deposit record to be available immediately for the Cash Receipt Register. ",
null,
null,
326,
"vl_DefaultCashReceiptStatus",
"S",
"1E0879E6-E410-48AD-B8A5-5ABF3740B20B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
139
],
[
"41B5A76D-26A3-48D0-87E3-E38D07228E24",
null,
null,
null,
1,
"I",
"0",
"Allow Edit of Invoice Lines When Customer is Invoice Complete",
null,
null,
null,
313,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
129
],
[
"B58655D7-FE5F-4605-AD4E-E4B1A0A9424D",
null,
"2021-11-30T17:34:29.973Z",
null,
1,
"T",
null,
"Return Unapplied Credit Note Service Item",
null,
null,
null,
342,
"avItem_ServiceItems",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
151
],
[
"F7435B21-6901-4888-91C6-E579895AA77C",
null,
"2016-12-17T17:53:18.059Z",
null,
1,
"I",
"0",
"Show Stock Color - Quote Letter 2",
"Will print \"Stock Color\" information on the quote (Quote Letter 2 only)",
"COMBO BOX",
null,
171,
"vl_YesNo",
"S",
"0B66DC6D-126D-4CE5-9AF9-835430FB8BEC",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
51
],
[
"98B2941C-DBAB-4D56-B9DF-E7026CA5B591",
null,
null,
null,
1,
"I",
"0",
"Apply duplicated items to price rules",
"When an item that is applied to a price rule is duplicated, automatically apply the new item to the same price rule(s) as the source item.",
"COMBO BOX",
null,
299,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
115
],
[
"F32F4842-E91F-4071-9651-E71EFBBC95E0",
null,
null,
null,
1,
"T",
null,
"Contract Storage Charge Service Item",
"Link the inventory service item associated with storage charges. This item will appear on contract invoices when storage charges are billed.",
"TYPE AHEAD",
null,
214,
"avItem_ServiceItems",
"S",
"DDAEA3A7-FF9C-4188-BBDC-A08B0C4499D6",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
67
],
[
"99CDFCAB-2BD2-4529-9344-E9BF1BA7AD74",
null,
"2016-10-19T00:45:05.271Z",
null,
1,
"T",
null,
"\"Break\" Operation Code",
null,
"TYPE AHEAD",
null,
161,
"vl_CostCenter",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
12
],
[
"57121C39-1389-4696-8A1F-EA697686A406",
null,
null,
null,
1,
"I",
"0",
"Task Detail Report - Include System Tasks ",
"When set to 'Yes', system tasks will appear on Task Detail Report.",
"COMBO BOX",
null,
346,
"vl_YesNo",
"S",
"2C1E4D34-F258-42DE-85F3-50716F9C7FEB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
153
],
[
"0AADCC56-77A0-4E60-9D8F-EACF75D483BA",
null,
"2017-09-29T16:20:21.731Z",
null,
1,
"I",
"0",
"Style 1: Show Order Description",
"Show Order Description (Job Ticket Style 1 only)",
"COMBO BOX",
null,
222,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
15
],
[
"A7D85D81-14A4-4826-88EC-EC212917D911",
null,
"2018-02-06T16:10:42.662Z",
null,
1,
"I",
"0",
"Default Register 'Employee' to the name of the currently logged in User",
null,
null,
null,
254,
"vl_YesNo",
"S",
"763C9B19-5BA4-4EBA-A392-731FF632969B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
88
],
[
"3F09ED6D-9952-4B3F-B12B-EC4E9C93CD7F",
null,
"2016-10-19T00:43:53.981Z",
null,
1,
"T",
null,
"\"Holiday\" Exception Type",
null,
"TYPE AHEAD",
null,
159,
"vl_ExceptionTypes",
"S",
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
10
],
[
"25631C09-7325-4C51-BE7A-EE75F54B1608",
null,
"2020-01-26T16:27:10.185Z",
null,
1,
"T",
"None",
"Integration Type",
"Select the Accounting Integration Type",
"TYPE AHEAD",
null,
68,
"vlAccountingIntegrations",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
10
],
[
"78D883E4-C49E-464C-AA77-EEE5DF701D84",
null,
"2017-09-28T19:51:48.855Z",
null,
1,
"I",
"1",
"Substrate - Date Ordered",
"Display parent sheet ordered date (Grand Format only)",
"COMBO BOX",
null,
187,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
18
],
[
"3C717E26-9A4B-41EF-BE05-EF05B2EBD1AA",
null,
null,
null,
1,
"I",
"0",
"Allow editing of defined Sq. Ft. value",
"Whether or not the looked up value from the defined Square Foot table can be edited",
"COMBO BOX",
null,
316,
"vl_YesNo",
"S",
"011E4871-EA2E-4765-AD5C-42AFCB5AC39D",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
130
],
[
"409D86B2-11B6-4B02-A062-EF62DC989FBB",
null,
"2018-10-29T17:42:14.432Z",
null,
1,
"I",
"0",
"Receipts From Production - Default Quantity",
"This setting will control what quantity will appear automatically for a line item when a reecipt from production is created. \
\
There are 2 choices: (1) Production Quantity (default), or (2) Zero quantity. \
\
When Production Quantity is selected all Receipts from Production, when created, will default to using the Sales Order Line Item Production Quantity. This is the default setting.\
\
When Zero quantity is selected all Receipts from Production, when created, will default the quantity to zero.\
\
",
"COMBO BOX",
null,
289,
"vl_pref_recFromProdDefQty",
"S",
"E6426985-9D7B-4935-8751-A28105D52761",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
107
],
[
"541FDC57-D23F-4D31-BCA7-EFEB4E05C57A",
null,
"2024-09-04T13:21:50.856Z",
null,
1,
"T",
"Substrate Quantity Used",
"Value to use to calculate number of clicks",
"This setting will be used to determine how the number of clicks will be calculated for Shop Floor operations for digital presses. The number of clicks is used to create one or more cost entries for click charges.",
"COMBO BOX",
null,
142,
"Substrate Quantity Used,Quantity Produced",
"M",
"693D5182-EABD-4D13-A0E4-7C3F7DF3B771",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
5
],
[
"98C6D2A7-7EF4-43C8-9F15-F10DF8339D63",
null,
"2017-09-28T19:51:48.851Z",
null,
1,
"I",
"1",
"Show color info on Job Ticket",
"This will show the color info (4/4) on the Job Ticket (default is Yes).",
"COMBO BOX",
null,
208,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
16
],
[
"71C85E90-C93B-4BF5-808A-F14274152DC2",
null,
"2017-09-28T19:51:48.891Z",
null,
1,
"I",
"1",
"Paper Details - Show # From Parent",
"Display the \"# From Parent\" field in the Paper details.",
"TEXT FIELD",
null,
91,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
36
],
[
"EC23FEC6-0D6F-4721-AB1C-F1C956BC9E28",
null,
null,
null,
1,
"I",
"0",
"Include fully backordered items on pick lists",
null,
"COMBO BOX",
null,
340,
"vl_YesNo",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
150
],
[
"ABA7335D-D63C-4034-8F15-F1FE3873550F",
null,
"2018-03-28T11:15:16.966Z",
null,
1,
"I",
"0",
"Use TVE Estimating Calculator",
"This is a special TVE estimating calculator. It requires a set of custom tables to be populated\r\
by data from your accounting system, and does not permit individual price category overrides, \r\
and only allows an override on the Price Per UOM, which is applied to the bottomline markup.",
"TEXT FIELD",
null,
109,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
20
],
[
"2F66AF12-C938-4D39-913F-F5A5AE5B203B",
null,
null,
null,
1,
"I",
null,
"Inlcude Estimate comment on Picking Slip",
"When creating a new estimate comment, do you want to make it avilable to print on the picking slip",
"TEXT FIELD",
null,
27,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"0A28B25D-9A7E-48E3-B122-F7EB2A7F9BF0",
null,
"2018-05-30T15:25:21.492Z",
null,
1,
"I",
"0",
"Print Tracking Numbers on 4x4 Shipping Labels",
null,
"TEXT FIELD",
null,
268,
"vl_YesNo",
"S",
"685683E9-65D2-4137-9D85-6DF00BD5FED7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
95
],
[
"F03E1725-3CBC-4C93-9FD8-F84D13C38B5B",
null,
"2017-09-28T19:51:48.863Z",
null,
1,
"I",
"1",
"Press Details - Helpers Required",
"Display number of helpers required (Grand Format only)",
"COMBO BOX",
null,
183,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
22
],
[
"600EE0EA-566B-4260-BE43-F9CA83ACD31A",
null,
"2018-03-28T11:15:16.952Z",
null,
1,
"I",
"0",
"Write postage source information to line item comments?",
null,
"COMBO BOX",
null,
138,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
14
],
[
"79F0F07B-94E7-4E8C-A706-F9D0FB94EDDE",
null,
"2021-07-15T13:54:05.767Z",
null,
1,
"I",
null,
"Unit of measure to be used when using the Pick List program, Fulfillment Pick List report and Picking in the mobile application.",
null,
"COMBO BOX",
null,
336,
"vlPref336",
"S",
"3B1C386F-C2B3-4BC0-BB02-41B9FF45562B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
146
],
[
"343AED49-2FC8-41FB-8090-FA03170D6D26",
null,
"2016-08-30T19:09:52.057Z",
null,
1,
"N",
"8",
"Number of hours in a regular shift",
null,
"TEXT FIELD",
null,
150,
null,
null,
"CC6E9050-3A92-42B4-8FC5-74EB151AD7C5",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"8F599FBB-59C1-471A-94FE-FA17FC58645F",
null,
null,
null,
1,
"T",
"no",
"Show Requester (Service Rep) by line",
"Show the service rep name, email address, and phone number from the source of outsourced service items on the purchase order. If there are multiple sources (jobs), each unique name will be listed, separated by commas.",
"COMBO BOX",
null,
168,
"vl_YesNo",
"S",
"55F19C5D-5E7D-4DEA-A2F7-DC5F3F48A76B",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
49
],
[
"F0163269-8910-4A1E-ABB0-FA6D2D91EF66",
null,
"2020-01-26T16:27:10.175Z",
null,
1,
"I",
"0",
"Send Invoice Salesperson to GP",
null,
null,
null,
282,
"vl_YesNo",
"S",
"2380FDFE-1AF8-48C5-BE4F-63C11F4CE37E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
4
],
[
"F618F633-35C3-48D3-98C1-FA6F1BCCF3C5",
null,
"2017-06-01T20:23:17.672Z",
null,
1,
"I",
"0",
"POS - Automatically populate Till #",
null,
"TEXT FIELD",
null,
199,
"vl_YesNo",
"S",
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
68
],
[
"F48E2685-0ECF-408A-AD0C-FADD1D0F3CB6",
null,
null,
null,
1,
"T",
"M",
"Default Stock Reorder Method",
"When a new inventory warehouse record is created it will use this preference to set the default stock reorder method of either\r\
Min/Max or EOQ/ROP. (Economic Order Quantity/Re-order Point).",
"TEXT FIELD",
null,
66,
"avItemReorderMethod",
"S",
"1B7B552E-DB54-4564-B840-A9D7C3CD609E",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"1B8F9D9A-D2DA-4A65-9C8C-FAF3598449C8",
null,
null,
null,
1,
"I",
"0",
"Add flexo linear footage and count information to Estimates/Sales Orders, Work Templates, Shopfloor and Estimating Standards.",
null,
null,
null,
385,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
1
],
[
"A8BFBCD9-AA14-4FCB-9664-FBBADA80964C",
null,
"2019-03-14T21:45:41.941Z",
null,
1,
"I",
"0",
"Minimum Charge - behavior when subtotal price (in estimating calculator) is lower than minimum charge on tasks.",
"- Maintain Minimum charge (Default setting: No)\
\
- Allow tasks with Minimum charge to be overwritten (Alt setting: Yes). This will\
only work when all other tasks are set to $0 by price rules.\
",
"CHECK BOX",
null,
297,
"vl_YesNo",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
113
],
[
"25950359-BD19-43CE-9F6C-FC29972742EF",
null,
"2017-09-28T19:51:48.906Z",
null,
1,
"I",
"1",
"Press Details - Show Imposition Model",
"Display the \"Imposition Model\" field in the Press results.",
"TEXT FIELD",
null,
83,
"vl_YesNo",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
44
],
[
"FDBC2ADA-1DBB-438B-BA7E-FD384691096A",
null,
"2018-03-28T11:15:16.963Z",
null,
1,
"N",
".25",
"Adhesive: Envelope seam glue width",
"The default width of glue that will be used for envelope side seams, if no value is specified on the die itself.",
"TEXT FIELD",
null,
112,
null,
null,
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
19
],
[
"CAA916E8-5D10-4A35-BA0B-FD7D28C83069",
null,
"2017-09-29T16:20:14.748Z",
null,
1,
"I",
"0",
"Style 1: Show Job #",
"Show Job #  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
223,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
14
],
[
"C30F0B1B-809E-4110-8E1C-FD8F9F20E803",
null,
"2018-03-28T11:15:17.016Z",
null,
1,
"I",
"1",
"Pre-Trim Cuts",
"This is the number of pre-trim cuts to be done before a piece can be run on a press doing \r\
work & tumble or perfecting. It is used for squaring one or more edges before\r\
running the paper through the press. This has no effect if the press is digital.",
"TEXT FIELD",
"#0",
9,
"vl_1or2",
"S",
"56D2CDF1-035C-430C-AF7D-D11B4DA87491",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
41
],
[
"BB09E17E-85D7-45E1-91CD-FE84B10C2B80",
null,
null,
null,
1,
"I",
null,
"Include Estimate Comment on the Job Ticket",
"When creating a new Estimate comment, do you want to include the comment on the Job Ticket by default.",
"TEXT FIELD",
null,
26,
"vl_YesNo",
"S",
"CEE47474-B07C-409D-A3EE-6B5A91BEE2A7",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
null
],
[
"90FD3F2A-65B1-458B-9DA9-FF137D551CEB",
null,
"2017-09-29T16:19:46.247Z",
null,
1,
"I",
"0",
"Style 1: Show Date of Order",
"Show Date of Order  (Job Ticket Style 1 only)",
"COMBO BOX",
null,
227,
"vl_YesNoReversed",
"S",
"2056BECD-EE05-4FB4-9FD9-EF23BB6C7B06",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
10
],
[
"51CCBD58-491D-4045-9298-FF481E116652",
null,
null,
null,
1,
"T",
null,
"Default Chargeback Debit Account",
"Enter the default debit account to use for the \u201cAccount Distribution\u201d Chargeback File detail level when there is no Chargeback Debit Account defined atthe Work Type or Estimating Standardlevels for production sales order lines and at the Item Class or Item levels for item sales order lines.",
null,
null,
285,
null,
null,
"6BB35920-17CB-4FC4-9B16-CB4E84472648",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
105
],
[
"********-A34A-449B-AFF7-FFBFA06A808F",
null,
"2019-04-11T14:52:28.136Z",
null,
1,
"I",
"0",
"Enable on VAT Invoice Form Postage to be included on a line",
"\u2022 Yes - will enable postage as a line item on UK VAT Invoice\
\u2022 No (default) -  will not diplay postage details as a line item on UK VAT Invoice",
null,
null,
302,
"vl_YesNo",
"S",
"291CCFF5-D834-44E7-AD9A-EF30F4AAD3FB",
"<html> <head><\/head> <body><img src=\"media:///UpdownGrey.png\"><\/body><\/html>",
120
]
]