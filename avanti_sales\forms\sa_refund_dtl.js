/**
 * @param {<PERSON><PERSON><PERSON>} _firstShow
 * @param {JSEvent} _event
 * @override
 *
 * @properties={typeid:24,uuid:"52977B28-5AB1-4160-9471-09A99567B7EF"}
 */
function onShowForm(_firstShow, _event) {
    _super.onShowForm(_firstShow, _event);
    elements.ref_refund_date.format = globals.avBase_dateFormat;
    elements.ref_trans_date.format = globals.avBase_dateFormat;
    elements.ref_amount.format = globals.avBase_currencyFormat;
    setRefundTab();
}

/**
 * <PERSON>le changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B0662EA8-2B84-40A4-BFA8-E7A030A61706"}
 */
function onDataChange_refundType(oldValue, newValue, event) {
    clearRefundType(oldValue);
    setRefundType(newValue);
    return true;
}

/**
 * Clear Refund Type
 * 
 * @param {String} oldValue
 *
 * @properties={typeid:24,uuid:"A1E2C232-B0BE-4973-8C80-DF52277F4496"}
 */
function clearRefundType(oldValue) {
    switch (oldValue) {
        case scopes.avUtils.ENUM_REFUND_TYPES.CustomerDeposit:
            sa_refund_to_sa_refund_customer_deposit.deleteAllRecords();
            break;
        case scopes.avUtils.ENUM_REFUND_TYPES.CustomerPostage:
            sa_refund_to_sa_refund_customer_postage.deleteAllRecords();
            break;
        case scopes.avUtils.ENUM_REFUND_TYPES.CreditNote:
            sa_refund_to_sa_refund_credit_note.deleteAllRecords();
            break;
    }
}

/**
 * Create the Refund Type
 * 
 * @param {String} newValue
 *
 * @properties={typeid:24,uuid:"46B5E701-299E-4333-9099-549955CC30A4"}
 */
function setRefundType(newValue) {

    if (utils.hasRecords(sa_refund_to_sa_customer)) {
        switch (newValue) {
            case scopes.avUtils.ENUM_REFUND_TYPES.CustomerDeposit:
                createCustomerDeposits();
                break;
            case scopes.avUtils.ENUM_REFUND_TYPES.CustomerPostage:
                createCustomerPostage();
                break;
            case scopes.avUtils.ENUM_REFUND_TYPES.CreditNote:
                createCreditNoteRefund();
                break;
        }
    }
    
    setRefundTab();

}

/**
 * Create Customer Deposit Data
 *
 * @properties={typeid:24,uuid:"E416F7A8-8184-4202-B127-3784CB6720D2"}
 */
function createCustomerDeposits() {
    
    //Make sure the current data has been cleared.
    sa_refund_to_sa_refund_customer_deposit.deleteAllRecords();
    
    for (var i = 1; i <= sa_refund_to_sa_customer_deposit$withbalance.getSize(); i++) {
        var rCustomerDeposit = sa_refund_to_sa_customer_deposit$withbalance.getRecord(i);
        
        var rRefundCustomerDeposit = sa_refund_to_sa_refund_customer_deposit.getRecord(sa_refund_to_sa_refund_customer_deposit.newRecord());
        rRefundCustomerDeposit.sa_cust_deposit_id = rCustomerDeposit.sa_cust_deposit_id;
        rRefundCustomerDeposit.refcd_applied_ttd = rCustomerDeposit.applied_amount;
        rRefundCustomerDeposit.refcd_balance_ttd = rCustomerDeposit.balance_at_time;
    }
}

/**
 * Create Customer Postage Refund
 *
 * @properties={typeid:24,uuid:"BCBC5963-C449-4544-B7E9-7CAFEA3272CB"}
 */
function createCustomerPostage() {
    
    //Make sure the current data has been cleared.
    sa_refund_to_sa_refund_customer_postage.deleteAllRecords();
    
    for (var i = 1; i <= sa_refund_to_sa_customer_postage$withbalance.getSize(); i++) {
        var rCustomerPostage = sa_refund_to_sa_customer_postage$withbalance.getRecord(i);
        
        var rRefundCustomerPostage = sa_refund_to_sa_refund_customer_postage.getRecord(sa_refund_to_sa_refund_customer_postage.newRecord());
        rRefundCustomerPostage.sa_cust_postage_id = rCustomerPostage.sa_cust_postage_id;
        rRefundCustomerPostage.refcp_applied_ttd = rCustomerPostage.applied_amount;
        rRefundCustomerPostage.refcp_balance_ttd = rCustomerPostage.balance_at_time;
    }
}

/**
 * Create Credit Note Refund
 *
 *
 * @properties={typeid:24,uuid:"15D54D39-97C4-4AE7-B778-22DEEDB5DC9A"}
 */
function createCreditNoteRefund() {
    
    //Make sure the current data has been cleared.
    sa_refund_to_sa_refund_credit_note.deleteAllRecords();
    
    if (utils.hasRecords(sa_refund_to_sa_customer)){
        /***@type {{sql:String,
         *          args:Array,
         *          server:String,
         *          maxRows:Number,
         *          table:String}}*/
        var oSQL = {},
            /***@type {JSDataSet} ***/
            dsData,
            /***@type {Number} ***/
            iMax = 0,
            i = 0;

        oSQL.sql = "SELECT  inv.inv_id, \
                            ABS(inv.inv_total_amt) AS creditAmt, \
                            ISNULL (a.appliedAmt, 0) AS appliedAmt, \
                            ISNULL (r.refundAmt, 0) AS refundAmt, \
                            ABS(inv.inv_total_amt) - ISNULL (a.appliedAmt, 0) - ISNULL (r.refundAmt, 0) AS balanceAmt \
                    FROM sa_invoice AS inv \
                    LEFT JOIN (SELECT credit_note_id, SUM (sa_apply_credit_note_payment) AS appliedAmt FROM sa_apply_credit_note GROUP BY credit_note_id) AS a ON inv.inv_id = a.credit_note_id \
                    LEFT JOIN (SELECT inv_id, SUM (refcn_refund_amount) AS refundAmt FROM sa_refund_credit_note GROUP BY inv_id) AS r ON inv.inv_id = r.inv_id \
                    WHERE (inv.org_id = ?) \
                        AND (inv.inv_cust_id = ?) \
                        AND (inv.inv_record_type = 'C') \
                        AND (inv.inv_credit_inv_id IS NULL) \
                        AND (inv.inv_status = 'U') \
                        AND ISNULL (ABS(inv.inv_total_amt) - ISNULL (a.appliedAmt, 0) - ISNULL (r.refundAmt, 0), 0) > 0";

        oSQL.args = [globals.org_id, cust_id.toString()];
        dsData = globals["avUtilities_sqlDataset"](oSQL);

        if (dsData) {
            iMax = dsData.getMaxRowIndex();
            for (i = 1; i <= iMax; i++) {
                var rRefundCreditNote = sa_refund_to_sa_refund_credit_note.getRecord(sa_refund_to_sa_refund_credit_note.newRecord());
                rRefundCreditNote.inv_id = dsData.getValue(i, 1);
                rRefundCreditNote.refcn_applied_ttd = dsData.getValue(i, 3) + dsData.getValue(i, 4);
                rRefundCreditNote.refcn_balance_ttd = dsData.getValue(i, 5);
            }
        }
    }
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"3E2C940A-6BC3-4D06-AE57-1D5831076BDC"}
 */
function onDataChange_customer(oldValue, newValue, event) {
    setRefundType(ref_trans_type);
    return true;
}

/**
 * Set refund tab based on refund type
 *
 * @properties={typeid:24,uuid:"48990DD2-0A4A-43BE-886B-9CD38593634D"}
 */
function setRefundTab() {
    //remove all tabs
    scopes.globals.avUtilities_tabRemoveAll(controller.getName(), "tabRefund");
    
    if (utils.hasRecords(sa_refund_to_sa_customer)) {
        //Add the tab based on refund type 
        switch (ref_trans_type) {
            case scopes.avUtils.ENUM_REFUND_TYPES.CustomerDeposit:
                scopes.globals.avUtilities_tabAdd(controller.getName(), "tabRefund", forms.sa_refund_customer_deposit_tbl, i18n.getI18NMessage("avanti.lbl.customerDeposit"), "sa_refund_to_sa_refund_customer_deposit", null, null, 1, null);
                scopes.globals.avUtilities_tabAdd(controller.getName(), "tabRefund", forms.sys_comment_refund, i18n.getI18NMessage("avanti.lbl.refundComments"), "sa_refund_to_sys_comment", null, null, 2, null);
                break;
            case scopes.avUtils.ENUM_REFUND_TYPES.CustomerPostage:
                scopes.globals.avUtilities_tabAdd(controller.getName(), "tabRefund", forms.sa_refund_customer_postage_tbl, i18n.getI18NMessage("avanti.lbl.customerPostage"), "sa_refund_to_sa_refund_customer_postage", null, null, 1, null);
                scopes.globals.avUtilities_tabAdd(controller.getName(), "tabRefund", forms.sys_comment_refund, i18n.getI18NMessage("avanti.lbl.refundComments"), "sa_refund_to_sys_comment", null, null, 2, null);
                break;
            case scopes.avUtils.ENUM_REFUND_TYPES.CreditNote:
                scopes.globals.avUtilities_tabAdd(controller.getName(), "tabRefund", forms.sa_refund_credit_note_tbl, i18n.getI18NMessage("avanti.lbl.creditNote"), "sa_refund_to_sa_refund_credit_note", null, null, 1, null);
                break;
        }
    }
}

/**
 * @param {JSEvent} [_event]
 * @param {String} [_form]
 * @override
 *
 * @return
 * @properties={typeid:24,uuid:"2A81CC79-033A-4850-B2A8-DF3ACDA6BDD7"}
 */
function onRecordSelection(_event, _form) {
    setRefundTab();
    forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
    return _super.onRecordSelection(_event, _form);
}

/**
 * @param {JSEvent} [_event]
 * @param {String} [_triggerForm]
 *
 * @override
 *
 * @properties={typeid:24,uuid:"192A5426-CD64-4A0F-A170-2D0B24106AE6"}
 */
function dc_edit(_event, _triggerForm) {
    _super.dc_edit(_event, _triggerForm);
    refreshUI();
//     elements.ref_reference.requestFocus();
}

/**
 * @override
 *
 * @properties={typeid:24,uuid:"7B7890FA-B437-48A5-B8AD-99B8DC74643F"}
 */
function gotoBrowse() {
    forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
}

/**
 * Refresh U/I
 *
 * @properties={typeid:24,uuid:"06672534-E795-4EB1-9CD5-5EBF949E4AC1"}
 */
function refreshUI() {
    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit 
            && (utils.hasRecords(sa_refund_to_sa_refund_credit_note) 
                    || utils.hasRecords(sa_refund_to_sa_refund_customer_deposit) 
                    || utils.hasRecords(sa_refund_to_sa_refund_customer_postage))) {
        elements.cust_id.enabled = false;
        scopes.globals.avUtilities_addBkgndColorStyle(elements.cust_id, scopes.avUtils.BACKGROUND_COLORS.White);
        elements.ref_trans_type.enabled = false;
        scopes.globals.avUtilities_addBkgndColorStyle(elements.ref_trans_type, scopes.avUtils.BACKGROUND_COLORS.White);
    }
    else {
        elements.cust_id.enabled = true;
        scopes.globals.avUtilities_addBkgndColorStyle(elements.cust_id, scopes.avUtils.BACKGROUND_COLORS.Yellow);
        elements.ref_trans_type.enabled = true;
        scopes.globals.avUtilities_addBkgndColorStyle(elements.ref_trans_type, scopes.avUtils.BACKGROUND_COLORS.Yellow);
    }
}

/**
 * @param {JSEvent} [_event]
 * @param {String} [_triggerForm]
 * @override
 *
 * @properties={typeid:24,uuid:"936F3FDB-A4DD-48EA-8E1F-B7FE8B52B0C0"}
 */
function dc_new(_event, _triggerForm) {
    _super.dc_new(_event, _triggerForm);
    refreshUI();
}
