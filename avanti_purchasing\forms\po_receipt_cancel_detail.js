/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"23BC9868-B3F8-49D3-8F69-EF0EBFC71B73",variableType:-4}
 */
var _TagFlag = false;

/**
 * refreshUI
 *
 * @properties={typeid:24,uuid:"50BBF56F-C2C5-4D95-BE97-9266B2E5B5E0"}
 */
function refreshSelect() 
{

	if (_TagFlag == true)
	{
		forms.po_receipt_cancel_detail_tbl.elements.btnTagUnTagAll.text = i18n.getI18NMessage('avanti.lbl.DeselectAll');
	}
	else
	{
		forms.po_receipt_cancel_detail_tbl.elements.btnTagUnTagAll.text = i18n.getI18NMessage('avanti.lbl.selectAll');
	}
}
