/*****************************************************************************************************************************
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"CCF8DAB1-01AB-40EC-A4EB-4A45A6E8D055",variableType:4}
 */
var MAX_NUM_ERRS = 200;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9D720303-3FFC-458C-99C5-2FC29D76AAA0"}
 */
var NO_VALUE = "NO-VALUE";

/**
 * @properties={typeid:35,uuid:"3125B049-4549-4922-9D21-CE518CE26927",variableType:-4}
 */
var bAutoImport = false;

/**
 * @properties={typeid:35,uuid:"F45710E4-AA1B-4B6B-BF0C-619E5C907411",variableType:-4}
 */
var _bFifoUpdated = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1F18B630-DAFE-480B-9904-A10D318CE562"}
 */
var _sSalesOrdersImport = scopes.avText.getLblMsg('SalesOrdersImport');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C7837CF3-B22D-471B-9E7D-A54530AD740F"}
 */
var sAppendReplace = '';

/**
 * @properties={typeid:35,uuid:"74132141-B71D-4E42-AEEA-BAC886658A72",variableType:-4}
 */
var _aCustsProcessed = [];

/**
 * @properties={typeid:35,uuid:"C6B7B3EE-9789-4DE3-A295-F13ABE00BCCF",variableType:-4}
 */
var _aItemsProcessed = [];

/**
 * @properties={typeid:35,uuid:"446A9F76-B2EC-4935-8F7A-3E71152C1CC5",variableType:-4}
 */
var aCustNoValueCols = [];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"0A42BC80-2D8B-4F32-9C86-5B722726FE53",variableType:-4}
 */
var aCustImportDBCols = ['cust_name', 'cust_type', 'custcat_id', 'custclass_id', 'terr_id', 'custgrp_id', 'cust_parent_cust_id', 'salesper_id', 'cust_status', 'curr_id', 'terms_id',
	'cust_website', 'custsic_id', 'shipmethod_id', 'cust_credit_limit', 'paymethod_id', 'custindustry_id', 'cust_min_order_amount', 'cust_po_required', 'cust_ship_complete', 
	'cust_amounts_on_pkgslip', 'cust_exclude_price_on_pkgslip', 'taxgroup_id', 'cust_date_created',	'cust_createdby_user_id', 'whse_id', 'cust_csr_empl_id', 'cust_accept_bo', 
	'cust_proof_contact_id', 'cust_samples_contact_id',	'cust_chargeback_code',	'cust_priority', 'cust_tax_reg_number',	'cust_invoice_detail_level', 'cust_account_code', 
	'clc_mtd_sales', 'clc_mtd_profit', 'clc_ytd_profit', 'clc_ytd_sales', 'cust_over_under_threshold', 'default_inv_freight_revenue', 'cust_project_required',
	'custaddr_use_default_shipper', 'cust_id_ship', 'custcontact_id_ship', 'custaddr_id_ship', 'custaddr_id', 'cust_primary_custaddr_id', 'cust_shipto_custaddr_id', 
	'cust_billto_custaddr_id'];

/**
 * @properties={typeid:35,uuid:"ED3EBD65-52E3-4E2A-B0A9-57FD70254E0D",variableType:-4}
 */
var aCustAddressNoValueCols = [];

/**
 * @properties={typeid:35,uuid:"5E4F9E28-CAB3-41BB-8C86-677A5ADC4A88",variableType:-4}
 */
var aAddressNoValueCols = [];

/**
 * @properties={typeid:35,uuid:"1ED9FE76-2A1F-43AF-B484-268FB8B92EFC",variableType:-4}
 */
var aCustContactNoValueCols = [];

/**
 * @properties={typeid:35,uuid:"A55BD1FC-E516-4102-91FC-DF5C4DD79566",variableType:-4}
 */
var aContactNoValueCols = [];

/**
 * @properties={typeid:35,uuid:"B37C4676-A669-4BCD-96E5-7DA81F1D9C9F",variableType:-4}
 */
var aTaxItemNoValueCols = [];

/**
 * @properties={typeid:35,uuid:"8BB1DB5A-BD38-4D16-B9CC-C583523D2201",variableType:-4}
 */
var aTaxGroupNoValueCols = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"B029434C-8B5E-4688-B0EF-E3EF1C69C2BE"}
 */
var _sInsertOrUpdate = '';

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"81A2285B-2666-42F9-8D7D-0FBCEAE92ACC",variableType:-4}
 */
var mbTurnOffHaltingAfterMaxErrors;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"A68F53A4-980F-4D16-90BE-C7E32F611B5E",variableType:-4}
 */
var maColNames = [''];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5E1E482A-128A-4FCA-AC2E-476D73EC6097"}
 */
var msSubType = '';

/**
 * @properties={typeid:35,uuid:"DBB30D8E-0100-4BA2-8253-E668CD0F9344",variableType:-4}
 */
var aCustomerSubTypes = application.getValueListArray('subType_Customers');

/**
 * @properties={typeid:35,uuid:"3917F5CF-20B0-4A52-B170-EDF81266331A",variableType:-4}
 */
var aSupplierSubTypes = application.getValueListArray('subType_Suppliers');

/**
 * @properties={typeid:35,uuid:"08BC7C81-E9BA-4DC3-8A68-1D8342C9C427",variableType:-4}
 */
var aCostCenterSubTypes = application.getValueListArray('subType_CostCenters');

/**
 * @properties={typeid:35,uuid:"F42D6E23-7BEB-46DF-A6F5-F7D47044563D",variableType:-4}
 */
var aInventorySubTypes = application.getValueListArray('subType_Inventory');

/**
 * @properties={typeid:35,uuid:"2E96347E-BD26-4E38-B8AC-82326E005F88",variableType:-4}
 */
var aCRMSubTypes = application.getValueListArray('dataImport_subType_CRM');

/**
 * @properties={typeid:35,uuid:"01E533D6-A456-44D0-A3C5-06B290A33334",variableType:-4}
 */
var aNoteSubTypes = application.getValueListArray('dataImport_subType_notes');

/**
 * @properties={typeid:35,uuid:"40144651-732E-4B01-983B-569517DD8D6F",variableType:-4}
 */
var aUDFAnswerSubTypes = application.getValueListArray('dataImport_subType_Udfs');

/**
 * @properties={typeid:35,uuid:"3B23522D-60C1-4098-B2FD-3290D9BEDEAE",variableType:-4}
 */
var aUDFQuestionSubTypes = application.getValueListArray('dataImport_subType_UdfQuestions');

/**
 * @properties={typeid:35,uuid:"74672FE0-91E9-470B-8A86-95B1FB09E913",variableType:-4}
 */
var aEmployeeSubTypes = application.getValueListArray('dataImport_subType_Emps');

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"973DB761-F287-4E3A-9515-E9E825241F5A",variableType:-4}
 */
var aSalesOrderSubTypes = ['1 - Sales Orders', '2 - Sales Order Details', '3 - Sales Order Multiship', '4 - Sales Order Multiship Qtys'];

/**
 * @properties={typeid:35,uuid:"D4F412B8-8681-4B25-A304-07955E381645",variableType:-4}
 */
var aTaxSubTypes = ['1 - ' + scopes.avText.getLblMsg('taxItems'), '2 - ' + scopes.avText.getLblMsg('taxGroups'), '3 - ' + scopes.avText.getLblMsg('TaxItemRates')];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"DEF36DEC-7717-441C-8ACC-A508D563C3C9",variableType:-4}
 */
var aAccountingSubTypes = ['1 - Chart of Accounts'];

/**
 * @type {JSFoundSet<db:/avanti/sa_customer_contact>}
 *
 * @properties={typeid:35,uuid:"274D49C1-F354-44F0-B0E0-9347767A84F7",variableType:-4}
 */
var mfsCustContacts;

/**
 * @type {JSFoundSet<db:/avanti/sa_division_plant>}
 *
 * @properties={typeid:35,uuid:"54ADDADA-F86C-448C-A794-EADF3A4AEBFE",variableType:-4}
 */
var mfsCustDivPlants;

/**
 * @type {JSFoundSet<db:/avanti/sys_sales_tax_item>}
 *
 * @properties={typeid:35,uuid:"04651E26-2754-4DC4-81AA-DF8CE5B6C940",variableType:-4}
 */
var mfsTaxItems;

/**
 * @type {JSFoundSet<db:/avanti/sys_sales_tax_group>}
 *
 * @properties={typeid:35,uuid:"9CD8F78F-D2E8-4A0E-BF36-8BD4B4C058F6",variableType:-4}
 */
var mfsTaxGroups;

/**
 * @type {JSFoundSet<db:/avanti/sys_sales_tax_group_item>}
 *
 * @properties={typeid:35,uuid:"A1B03850-92F5-45E7-B267-20A2724F7BF1",variableType:-4}
 */
var mfsTaxGroupItems;

/**
 * @type {JSFoundSet<db:/avanti/sys_sales_tax_rate>}
 *
 * @properties={typeid:35,uuid:"2D3E288D-2601-4187-9D26-2E0E09718A34",variableType:-4}
 */
var mfsTaxItemRates;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"3E328DD9-9D57-42B7-A4FA-59AFFD8ABA0C",variableType:-4}
 */
var mbImportHasSubType;

/**
 * @type {JSFoundSet<db:/avanti/ap_supplier_contact>}
 *
 * @properties={typeid:35,uuid:"7EAB5593-6134-4DEC-B46C-71A3BC2B3F1B",variableType:-4}
 */
var mfsSupplierContacts;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"A80CCA55-ACE6-49D9-8017-056592DADEC2",variableType:-4}
 */
var maSupplierContactColNames = ['Supplier Code', 'Supplier Address Code'];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"888EFDCE-5049-4791-A949-8E6D2FA0AA2D"}
 */
var sCashReceiptID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2F0E4255-4692-4A97-94A3-4111B16C25AC"}
 */
var sCashReceiptCustID = null;

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"E63A85C2-1792-4138-9D69-0436AF0698C3",variableType:-4}
 */
var maCashReceiptDetails = ['Invoice Number', 'Payment Amount'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D0B31D6F-825D-4694-B85F-63B4691A136E",variableType:-4}
 */
var maProjects = ['Project', 'Customer Code', 'Allow Project to control the tax', 'Sales Tax Option', 'Tax Group', 'Tax Exempt Reason'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"3FB65635-37E7-46C7-B813-EF792B63DF3F",variableType:-4}
 */
var maPriceRuleDetails = ['price type', 'rule description', 'price category', 'date from', 'date to', 'active', 'price segment #1', 'price segment #2', 
	'price segment #3', 'price segment #4', 'price segment #5', 'price segment #6', 'price method', 'break method', 
	'base markup % (price method = cost plus markup %)', 'price (price method = actual price)', 'selling units (price method = actual price)',
	'step pricing selling units (price method = actual price)'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"F5442537-0CCA-4E39-9333-241B7F0CB4BD",variableType:-4}
 */
var maItemDocs = ['Item Code', 'Title', 'File Path', 'BOM Item', 'Section', 'Category', 'Proof', 'For Job', 'OKD', 
	'OKD By', 'OKD Date', 'Show on Job Ticket', 'Show on Quote'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"CF4D179C-25F6-4AF2-9886-B48DEE657184",variableType:-4}
 */
var maBOM = ['Item Code', 'Bill of Material - Item Code', 'Bill of Material - Qty'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D7358433-BA76-4C4D-9214-D563B8017C21",variableType:-4}
 */
var maCashReceipts = ['Customer Code', 'Payment Number', 'Payment Date', 'Currency', 'Payment Method', 'Account', 'Invoice Number',	'Payment Amount'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"6556CD50-8C30-4343-8D16-722AE185D891",variableType:-4}
 */
var maInvoiceColNames = ['Customer Code', 'Invoice Date', 'Invoice Number', 'Item Code', 'Invoice Total'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"60E091F8-FDA0-4B51-BF06-55A04B6620C2",variableType:-4}
 */
var maSupplierColNames = ['Supplier Code', 'Supplier Name','Supplier Active','Currency Code','Supplier Website','Supplier Since','Buyer',
	'Minimim PO Value',	'Shipping Method','Account Number','Supplier Accepts Backorders','Supplier Confirmation Required','Freight Bill Method',
	'Freight Cost Method','Payment Terms','Default Lead Time','Primary Address Code', 'Matching Method'];

/**
 * @type {Array}
 * 
 * @properties={typeid:35,uuid:"55B4C8F9-C9BD-44BF-A478-C35A544C2F71",variableType:-4}
 */
var maChartOfAccountsColNames = ['Account','Description','Account Type'];

/**
 * @type {JSFoundSet<db:/avanti/ap_supplier>}
 *
 * @properties={typeid:35,uuid:"63D586E1-8701-42A1-87AB-B9EE33D795A8",variableType:-4}
 */
var mfsSuppliers;

/**
 * @type {JSFoundSet<db:/avanti/gl_account>}
 *
 * @properties={typeid:35,uuid:"84ED81D6-0A8D-4104-A0D8-7B2D7CAC3A03",variableType:-4}
 */
var mfsGlAccount;

/**
 * @type {JSFoundSet<db:/avanti/gl_account_segment>}
 *
 * @properties={typeid:35,uuid:"B2C13BED-FEE9-4C9F-A624-1B59F3AE6641",variableType:-4}
 */
var mfsGlAccountSegment;

/**
 * @type {JSFoundSet<db:/avanti/sys_department>}
 *
 * @properties={typeid:35,uuid:"CA3495CA-8538-4B97-BF5C-EF4AC7C9D00B",variableType:-4}
 */
var mfsDepts;

/**
 * @type {JSFoundSet<db:/avanti/in_paper_grade>}
 *
 * @properties={typeid:35,uuid:"4321F943-87A4-48BE-B61D-2118E64CA58A",variableType:-4}
 */
var mfsPaperGrades;

/**
 * @type {JSFoundSet<db:/avanti/in_paper_brand>}
 *
 * @properties={typeid:35,uuid:"BD334673-E87E-411A-A8F1-21CF7E45DA63",variableType:-4}
 */
var mfsPaperBrands;

/**
 * @type {JSFoundSet<db:/avanti/sa_sales_person>}
 *
 * @properties={typeid:35,uuid:"8A40AB1B-9170-4BB9-9AA6-0E5D03F34EC0",variableType:-4}
 */
var mfsSalesReps;

/**
 * @type {JSFoundSet<db:/avanti/sys_employee>}
 *
 * @properties={typeid:35,uuid:"617A253A-7FD9-4AA3-87CB-4AA8BCFF9926",variableType:-4}
 */
var mfsEmps;

/**
 * @type {JSFoundSet<db:/avanti/in_warehouse_location>}
 *
 * @properties={typeid:35,uuid:"ACE805F3-A89D-420A-80D3-533E0893E309",variableType:-4}
 */
var mfsBins;

/**
 * @type {JSFoundSet<db:/avanti/crm_activity>}
 *
 * @properties={typeid:35,uuid:"703F40A3-1FD5-4B92-9D02-722E2ADD8CBA",variableType:-4}
 */
var mfsActivities;

/**
 * @type {JSFoundSet<db:/avanti/crm_lead>}
 *
 * @properties={typeid:35,uuid:"FCA3A360-09A5-4408-A67E-EE42747A422B",variableType:-4}
 */
var mfsLeads;

/**
 * @type {JSFoundSet<db:/avanti/sys_udf_values>}
 *
 * @properties={typeid:35,uuid:"3FCA8BD2-2FD2-482F-A3E2-AC8717CE6EBE",variableType:-4}
 */
var mfsUdfAnswers;

/**
 * @type {JSFoundSet<db:/avanti/sys_udf_type>}
 *
 * @properties={typeid:35,uuid:"994DB694-20C6-40B4-8061-051C2BB82746",variableType:-4}
 */
var mfsUdfQuestions;

/**
 * @type {JSFoundSet<db:/avanti/sys_udf_type_table_values>}
 *
 * @properties={typeid:35,uuid:"10D93C18-5554-4D78-811A-FA458C2B8DC7",variableType:-4}
 */
var mfsUdfQuestionTableOptions;

/**
 * @type {JSFoundSet<db:/avanti/sys_note>}
 *
 * @properties={typeid:35,uuid:"5E06AFBC-688E-41E5-8E8D-5900B27A1FFD",variableType:-4}
 */
var mfsNotes;

/**
 * @type {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:35,uuid:"A819E432-CAD4-4C99-BD88-460ECF2D1F91",variableType:-4}
 */
var mfsIntItems;

/**
 * @type {JSFoundSet<db:/avanti/in_item_warehouse>}
 *
 * @properties={typeid:35,uuid:"01503B31-93CA-4800-A494-E8DB7FE25FB0",variableType:-4}
 */
var tfsItemWarehouse;

/**
 * @type {JSFoundSet<db:/avanti/sys_operation_category>}
 *
 * @properties={typeid:35,uuid:"C41BFDCC-F51A-43CF-8FE7-49048857D922",variableType:-4}
 */
var mfsCats;

/**
 * @type {JSFoundSet<db:/avanti/sys_cost_centre>}
 *
 * @properties={typeid:35,uuid:"24FBD367-A5BF-414C-85BC-C04A51B520BB",variableType:-4}
 */
var mfsOps;

/**
 * @type {JSFoundSet<db:/avanti/ap_supplier_address>}
 *
 * @properties={typeid:35,uuid:"7F674AEB-BB20-4563-ABE1-40D9DF8C6530",variableType:-4}
 */
var mfsSupplierAddresses;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"D65074C4-52B6-4255-8AA2-32353070873D",variableType:-4}
 */
var maSupplierAddressColNames = ['Supplier Code', 'Address Code','Address Name','Phone 1','Phone 1 Ext','Phone 2','Phone 2 Ext',
'Phone 3','Phone 3 Ext','Fax','Fax Ext','Supplier Contact','Address Active'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"5730B728-BFEF-4A25-84C2-6AD16F0F35F0",variableType:-4}
 */
var maCatColNames = ['Department Code','Category Code','Description','Short Description','Active','GL Cost of Sales','GL Sales','External Device'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"16D9EF85-5295-4903-B81D-6F79136B433D",variableType:-4}
 */
var maInkColorColNames = ['ink_type_code','name','coverage_coated','coverage_uncoated','cost_per_unit','setup_mix','GL Sales','External Device'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"CD5B5DBB-495D-4710-B0BB-7B59464E7121",variableType:-4}
 */
var maPaperGradeColNames = ['papergrade_name','papergrade_active','papergrade_width','papergrade_length','papergrade_is_roll','papergrade_weight_by','papergrade_charge_partial'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"97979100-EA25-4F3D-8DAE-8757B565D1AF",variableType:-4}
 */
var maPaperBrandColNames = ['papergrade_name','paperbrand_name','paperbrand_active','paperbrand_paper_full_desc','paperbrand_basis_weight','paperbrand_finish_back','paperbrand_finish_front','paperbrand_width','paperbrand_length'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"9AA56AA2-159A-41EC-91F6-83BFF75ABD2D",variableType:-4}
 */
var maSalesRepColNames = ['salesperson_code','territory_code','salesperson_name','salesperson_active','salesperson_commission','salesper_commission_type'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"6CCA2094-7DFB-480B-A77A-A48848541F85",variableType:-4}
 */
var maTaxItems = ['Tax Item Code','Tax Type','Tax Item Description','Tax Registration No.','G/L Account','Tax Based On','Based On Tax Item','Rounding Rule','Taxable Tax','Active','Tax Included','Tax Refundable'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"27681F51-5420-492A-9599-DF4EEC8E8E4E",variableType:-4}
 */
var maTaxGroups = ['Tax Group Code','Description','Active','Tax Shipping Charges','Add Tax Item Codes From Here (note: Tax Items must exist)'];

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"89CDA48E-AD98-41DF-8D33-D084307D3F8A",variableType:8}
 */
var miNumStaticTaxGroupCols = maTaxGroups.length - 1;

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"469008DF-5EB1-484E-A780-6BAE61E5E01B",variableType:-4}
 */
var maTaxItemRates = ['Tax Item Code','Effective Date','Tax Percentage'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"10C3691A-B7D1-4CA0-BB39-01766D8FB5C6",variableType:-4}
 */
var maEmpColNames = ['empl_code','empl_first_name','empl_last_name','emplclass_code','empl_active','empl_date_format','salesper_code','app_assignment_type_descr'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"E328FBA1-90A7-4F1C-A682-F7F69DD9A457",variableType:-4}
 */
var maBinColNames = ['Warehouse Code','Level 1','Level 2','Level 3','Level 4','Level 5','Minimum Qty','Maximum Qty','Active flag (Y/N)'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"9221E891-0E9D-45D0-8F32-629EC4D2456F",variableType:-4}
 */
var maActivityColNames = ['activity_type','activity_subject','activity_regarding','activity_assignment_empl_code','activity_customer_code',
	'activity_contact_first_and_last','activity_priority','activity_end_datetime','activity_estimate_number','activity_sales_order_number',
	'activity_job_number','activity_packing_slip_number','activity_status','activity_due_datetime','activity_objective_complete',
	'activity_objective','note','created_date','created_by_emp','activity_lead_number'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"F9824731-8E0A-4DDE-AD6F-AC92E5CD7495",variableType:-4}
 */
var maLeadColNames = ['lead_number','cust_code','lead_date_time','contact_first_and_last','lead_date_time_passed_to_rep','salesper_code',
'csr_code','is_active','est_number','crm_lead_stage_descr','created_date_time','created_by_emp_code','modified_date_time',
'modified_by_emp_code','crm_lead_rating_descr','crm_lead_rating_after_descr','lead_owner_emp_code','crm_lead_source_descr'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"D08EBFC6-534F-4659-B720-F1D4EB1450F1",variableType:-4}
 */
var maIntItemsColNames = ['item_code', 'Revision','description1','description2','item_class_code','item_type_code','status','lot_item','color',
'decimal_places','avg_lead_time','salestax_option','tax_group_code','allow_commissions','allow_discounts','allow_backorders','dimension_height',
'dimension_length','dimension_width','max_weight','uom_code','cust_code','worktype_code','cust_part_number','isbn_number','group_code',
'backorder_qty','committed_qty','inproduction_qty','intransit_qty','onhand_qty','onpo_qty','onporeq_qty','scheduled_qty','specifications',
'glacct_code_inventory_adj','glacct_code_cost_of_sales','glacct_code_inventory','glacct_code_sales_returns','glacct_code_sales','ink_type_code',
'cylinder_teeth','cylinder_diameter','plate_run_length','paper_grade_name','paper_brand_name','paper_first_dim','paper_second_dim',
'paper_caliper','item_number_of_tabs','paper_roll_weight','paper_m_weight','paper_weight','Warehouse Code','primary_supplier_code',
'primary_supplier_uom_code','primary_supplier_purchase_cost_uom_code','primary_supplier_list_price','selling_list_price','selling_uom_code',
'primary_supplier_leadtime_flag','selling_uom_conversion_factor','pricing_uom_code','primary_supplier_uom_conversion_factor', 
'substrate - brand name','Selling Units->Per Quantity Of', 'Warehouse Estimating Units (code)', 'Landed Cost Costing Units Factor', 'Supplier Details Item Number', 
'Supplier Details Item Description', 'Supplier Details Reorder Multiple', 'Supplier Details Minimum Order Qty', 'Warehouse Average Cost',
'Default Receipt Location', 'No Bin Location Tracking', 'creation_date','expiry_date','Default Issue Location','Project', 'Harmonized Code', 'Manufacture Country', 
'Virtual Item', 'Language'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"E0D93346-C048-4D88-9F52-946B1C525091",variableType:-4}
 */
var maIntItemsBinColNames = ['item_code','Warehouse Code', 'Warehouse Bin Location','onhand_qty','Estimating Units','Reorder Method','Min On Hand','Max On Hand','Default Receipt Location',
	'Default Issue Location','Project'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"5789B75F-18BD-4734-AD0A-33C90260EDAB",variableType:-4}
 */
var maCustomerColNames = ['Customer Code', 'Name','Type','Category','Class','Territory','Group','Parent  Customer','Salesperson','Status',
'Currency','Payment Terms','Website','SIC Code','Shipping Method','Credit Limit','Payment Method','Industry','Minimim Order Amount','PO Required',
'Ship Complete','Amounts On Packing Slip','Exclude Price On Packing Slip','Tax Group','Date Created','Created By Employee',
'Shipto Address Code','Billto Address Code','Warehouse Code','CSR','Accepts Back Orders','Proof Contact','Samples Contact','Chargeback Code',
'Priority','Tax Reg Number','Invoice Print Default','Account Code','MTD Sales','MTD Profit','YTD Profit','YTD Sales', 'Over/Under Threshold (%)',
'Default Invoice Freight Revenue', 'Project Required','Account - Default Shipper - Use Customer Address', 
'Account - Default Shipper - Customer Code', 'Account - Default Shipper - Ship-From Contact First And Last', 
'Account - Default Shipper - Ship-From Address Code', 'FOB Customer', 'FOB Address Code', 'FOB Address Customer Code','Default Shipping Markup %'];

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"56CB7566-7683-4925-821C-E5BEB97AECCC",variableType:4}
 */
var miNumStaticCustCols = 67;

/**
 * @type {Array<String>}
 * 
 * @properties={typeid:35,uuid:"840C0868-9F5D-4468-8DEB-722792C66D38",variableType:-4}
 */
var maCustomerColHeaders = [];

/**
 * @type {JSFoundSet<db:/avanti/sa_customer>}
 *
 * @properties={typeid:35,uuid:"83C2CB31-1C0F-4DD6-BADF-7ABFEAF662E7",variableType:-4}
 */
var mfsCustomers;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"FC7EAFF4-63F0-4945-82B0-22962C0B30D5",variableType:-4}
 */
var maCustContactColNames = ['Customer Code','Customer Address Code'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"98D6E5C2-09B3-47C6-AE07-B78B68C1CB39",variableType:-4}
 */
var maCustDivPlantColNames = ['Customer Code','Division Code','Plant Code'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"46859B6A-7930-4374-ACB3-4498625D259E",variableType:-4}
 */
var maCustAddressColNames = ['Customer Code','Address Code', 'Address Name','Phone 1','Phone 1 Ext','Phone 2','Phone 2 Ext','Phone 3','Phone 3 Ext','Fax','Fax Ext',
'Shipping Method','Tax Group Code','Salesperson','Territory','Address Active','Warehouse','Contact First And Last',
'Default Shipper - Use Customer Address', 'Default Shipper - Customer Code', 
'Default Shipper - Ship-From Contact First And Last', 'Default Shipper - Ship-From Address Code'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"380316EB-ECA5-40BF-ABC9-4DA9EE323D28",variableType:-4}
 */
var maDeptColNames = ['Department Code','Plant Code', 'Description','Short Description','Schedule','Active','Shift Code'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"5E82D517-655A-4530-8988-7D7EE5CF3540",variableType:-4}
 */
var maCustAddressColNamesForCustImport = ['Address Name','Phone 1','Phone 1 Ext','Phone 2','Phone 2 Ext','Phone 3','Phone 3 Ext','Fax','Fax Ext',
 'Shipping Method','Tax Group Code','Salesperson','Territory','Address Active','Warehouse','Contact First And Last',
 'Default Shipper - Use Customer Address', 'Default Shipper - Customer Code', 
 'Default Shipper - Ship-From Contact First And Last', 'Default Shipper - Ship-From Address Code'];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DBAB07CC-D09A-478C-A824-521DE29C0C61"}
 */
var msSourceFile = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9295621B-AF5F-4645-B1B6-9B2EC2F14BAC"}
 */
var msImportType = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"936BE71D-44F3-42C4-AF44-EADC240F092A"}
 */
var lblNumRecs = '';

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"77C087D2-3DB5-4F72-AE38-95A4DCB5AEF0",variableType:-4}
 */
var msLines;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"BDFE1991-1861-4A22-81F4-E74AE08705B9",variableType:4}
 */
var miNumInserts = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"0C1BC16F-3E9A-456F-9BE7-DB9F35AD999B",variableType:8}
 */
var miNumUpdates = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C5CF9D26-D328-4A87-8C6A-7BA8D487D017",variableType:4}
 */
var miNumDeletes = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"032392D7-BD38-4B24-AB03-48218CA3224C",variableType:4}
 */
var miNumErrors = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C82F2F19-192B-43F3-8E02-ADA6FE22780C"}
 */
var msNumErrors = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7F367BAB-8E54-4531-AB86-5988B1702434"}
 */
var msNumInserts = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2431DF14-037D-4BF2-8466-D5C7CB1EDC95"}
 */
var msNumUpdates = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"23BF0C50-81FB-400C-9D68-A613350C8250"}
 */
var msNumDeletes = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2CC3A102-F8C9-493F-A9C5-A0C276EF09ED"}
 */
var msNumRecsFailed = '';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2CA206A6-EC1E-4E11-9642-48ECA7466FF8",variableType:4}
 */
var miNumRecsFailed = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2D81478E-B39E-485B-96F2-ED890241BABE"}
 */
var msErrorMsgs = '';

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"96D130E1-AAA0-4B55-B53D-80A2741665CF",variableType:-4}
 */
var mbCurrRowErrored;

/**
 * @type {JSFoundSet<db:/avanti/sa_customer_address>}
 *
 * @properties={typeid:35,uuid:"2AE694D3-F23A-4830-BDEA-A189AE01D5DF",variableType:-4}
 */
var mfsCustomerAddresses;

/**
 * @type {JSFoundSet<db:/avanti/sys_address>}
 *
 * @properties={typeid:35,uuid:"D227CFC3-0669-48A1-A00C-E42E0AD73083",variableType:-4}
 */
var mfsAddresses;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"38CAC284-FB2A-4105-8C8B-7094A5A8519A",variableType:-4}
 */
var maAddressColNames = ['Address 1','Address 2','Address 3','City','State/Province Code','Zip/Postal Code','Country Code'];

/**
 * @type {JSFoundSet<db:/avanti/sys_contact>}
 *
 * @properties={typeid:35,uuid:"D8C8E771-5813-4B78-98D0-9A779CDF1DEE",variableType:-4}
 */
var mfsContacts;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"C284BAD7-CEA8-47AF-B137-566D66D4979E",variableType:-4}
 */
var maContactColNames = ['Title','First Name','Middle Name','Last Name','Suffix','Use Default Address',
'Business Phone','Business Ext','Mobile Phone','Home Phone','Other Phone','Business Fax',
'Home Fax','Business Email','Home Email','Other Email','Job Title','Contact Type','Department','Division','Contact Active'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"E5DD697A-8050-4C22-9112-1E653B44EDA5",variableType:-4}
 */
var maNoteColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"04AA0964-7276-4F17-9E36-E334DDF3B760",variableType:-4}
 */
var maUDFAnswerColNames = ['udf_descr','udf_answer'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"FC95C2BA-FEA9-416B-90BB-F277B5C7DB93",variableType:-4}
 */
var maUDFQuestionColNames = ['udf_category','udf_field','udf_field_type','udf_parent_field'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"A2F25FD7-833A-4172-853A-ACFA0E14CF06",variableType:-4}
 */
var maUdfQuestionTableOptionColNames = ['udf_category','udf_field','udf_table_option'];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"6FF0F6EF-8444-4480-8F6A-1C296CF35F0E",variableType:-4}
 */
var maOpColNames = ['Department Code','Category Code','Operation Code','Description','Short Description','Type',
'Base Rate','Overhead','Labor','Chargeable','Qty Required','Active',
'Division Code','Plant Code','Total Rate','Group by Category','Default Lag','Default Lag Units','Default Lag Type','Default Successor Lag',
'Default Successor Lag Units','Default Successor Lag Type','QA Check Field 1','QA Check Field 2','QA Check Field 3','QA Check Field 1 Enabled',
'QA Check Field 2 Enabled','QA Check Field 3 Enabled','JDF Type', 'Lag Time Based On'];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3B088CF3-67F7-4474-A090-B95BC14F5CB5"}
 */
var msAction = '';

/**
 * @type {plugins.file.JSFile}
 *
 * @properties={typeid:35,uuid:"52EBBAB2-4C49-46C0-9880-9C8AA9946D4B",variableType:-4}
 */
var _jsFile = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A05863D4-E629-4CA7-8A27-222BE3C6E560"}
 */
var _sFileName = '';

/**
 * @properties={typeid:35,uuid:"62F6BCEC-68DE-468C-BE30-0FEA686C10C8",variableType:-4}
 */
var _setCustsNotInImportAsInactive = false;

/**
 * @properties={typeid:35,uuid:"8FAA02F8-649A-4736-BF2F-0603A4CECFD8",variableType:-4}
 * 
 * @type {{
 * getIDFromCode: {}
 * }}
 */
var _oImportCache;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"CDE1AC03-D069-49E4-BA8C-575FD276B216",variableType:-4}
 */
var _bOverwriteChartOfAccounts;

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"174246E8-A3EC-49E0-A336-D6DFA1BF62D0"}
 */
function onLoad(event) {
	var rsult = _super.onLoad(event) 
	controller.readOnly = false
	databaseManager.setAutoSave(false)
	clearScreen()
	return rsult
	
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"C871D3CA-DF82-47E1-9392-7A8760AA31A2"}
 */
function onShowForm(firstShow, event) {
	var rsult = _super.onShowForm(firstShow, event); 

	controller.readOnly = false;
	
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
		// sl-2525 - have to remove div + plant filters so we can see all divs and plants here
		globals.avBase_removeTableFilter();
		
		// disable div + plant fields so they cant reapply filter while in data import 
		forms.svy_nav_fr_status_bar.elements.fldDivision.enabled = false;
		forms.svy_nav_fr_status_bar.elements.fldPlant.enabled = false;
	}

	elements.btnPurge.visible = importSupportsPurge();
	elements.optAppendReplace.visible = importSupportsAppendReplace();
	elements.btnExportData.visible = importSupportsExport();
	elements.chkOverwriteExistingChartOfAccounts.visible = importSupportChartOfAccountUpdateExistingRecs();
	
	if (!sAppendReplace) {
		sAppendReplace = 'A';
	}
	
	application.executeLater(enableForm, 1000, [true]);
	
	return rsult;
}

/**
 * @return
 * @properties={typeid:24,uuid:"F324C726-0DD0-401B-8575-3A0AC1087DF5"}
 */
function importSupportsPurge(){
	if(msImportType == 'Customers' && msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){
		return true;
	}
	else{
		return false;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"BB76F03B-6472-4C2E-B1E2-3241885042D1"}
 */
function importSupportsExport() {
	var aExportTypes = Object.keys(scopes.avDataExport.ENUM_IMPORT_TYPES).map(function(k){return scopes.avDataExport.ENUM_IMPORT_TYPES[k]});
	
	if (aExportTypes.indexOf(msImportType) > -1) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"B28BA673-42E4-419B-B997-3F53FF2D7093"}
 */
function importSupportsAppendReplace() {
	if (msImportType == 'Customers' && msSubType == '4 - Divisions/Plants') {
		return true;
	}
	else if (msImportType == 'Inventory' && msSubType == '6 - Item Documents') {
		return true;
	}
	else if (msImportType == 'Inventory' && msSubType == '7 - Bill of Materials') {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"0980666F-FE20-4FE4-8FF3-A65DAE782138"}
 */
function importSupportChartOfAccountUpdateExistingRecs() {
	if (scopes.avText.getLblMsg('accounting') && msSubType == '1 - Chart of Accounts') {
		return true;
	}
	return false;
}

/**
*
* @param {JSEvent} event
*
 * @return
* @properties={typeid:24,uuid:"********-8587-4E41-84BF-3844DBBD9EC5"}
*/
function onHide(event) {
	var ret = _super.onHide(event);
	
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
		//sl-2525 - restore div and plant filters that were removed in onShowForm()
		globals.avBase_setTableFilter();
		
		// re-enable div + plant fields 
		forms.svy_nav_fr_status_bar.elements.fldDivision.enabled = true;
		forms.svy_nav_fr_status_bar.elements.fldPlant.enabled = true;
	}
	
	return ret;
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"08C22344-**************-9506F924D7BA"}
 */
function cboSubType_onDataChange(oldValue, newValue, event) {
	msSourceFile='';
	clearScreen();
	_bOverwriteChartOfAccounts = false;
	
	elements.btnPurge.visible = importSupportsPurge();
	elements.chkOverwriteExistingChartOfAccounts.visible = importSupportChartOfAccountUpdateExistingRecs();

    forms.utils_dataImport_dtl_automatic.msSubType = msSubType;
    forms.utils_dataImport_dtl_automatic.onDataChange_subType(null, msSubType, event);

    // this has to run after utils_dataImport_dtl_automatic.onDataChange_subType above because we will set sAppendReplace 
    // from adi_replace_existing_records if there is one
	if (importSupportsAppendReplace()) {
		// sl-27466 - if there is no sys_auto_data_import record or adi_replace_existing_records is null then dont set sAppendReplace. 
		// it will just act as a ui element without an underlying db value, as it did before.
		if (utils.hasRecords(forms.utils_dataImport_dtl_automatic.foundset)) {
			if (forms.utils_dataImport_dtl_automatic.foundset.adi_replace_existing_records == 1) {
				sAppendReplace = "R";
			}
			else if (forms.utils_dataImport_dtl_automatic.foundset.adi_replace_existing_records == 0) {
				sAppendReplace = "A";
			}
		}
		
		elements.optAppendReplace.visible = true;
	}
	else {
		elements.optAppendReplace.visible = false;
	}
	return true;
}

/**
 * @properties={typeid:24,uuid:"03019369-FCC4-41C4-8E27-7A7E4E156FB5"}
 */
function btnFile_FileChosen(fileArray){
	/** @type {plugins.file.JSFile} */
	var file = fileArray[0]
	
	
	
	if (file){ 		
		msSourceFile = file.getAbsolutePath()
		loadFile()
	}
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"7E955BBB-EDAE-418B-BC94-1B674A8CD8B9"}
 */
function cboImportType_onDataChange(oldValue, newValue, event) {
    msSourceFile = '';
    msSubType = null;
    mbImportHasSubType = true;
    _bOverwriteChartOfAccounts = false;
    elements.cboSubType.visible = true;
	elements.btnPurge.visible = importSupportsPurge();
	elements.optAppendReplace.visible = importSupportsAppendReplace();
	elements.btnExportData.visible = importSupportsExport();
	elements.chkOverwriteExistingChartOfAccounts.visible = importSupportChartOfAccountUpdateExistingRecs();

    if (msImportType == 'Customers') {
        application.setValueListItems('dataImport_subType', aCustomerSubTypes);
    }
    else if (msImportType == 'Suppliers') {
        application.setValueListItems('dataImport_subType', aSupplierSubTypes);
    }
    else if (msImportType == 'Cost Centers') {
        application.setValueListItems('dataImport_subType', aCostCenterSubTypes);
    }
    else if (msImportType == 'Inventory') {
        application.setValueListItems('dataImport_subType', aInventorySubTypes);
    }
    else if (msImportType == 'CRM') {
        application.setValueListItems('dataImport_subType', aCRMSubTypes);
    }
    else if (msImportType == 'Notes') {
        application.setValueListItems('dataImport_subType', aNoteSubTypes);
    }
    else if (msImportType == 'UDF Answers') {
        application.setValueListItems('dataImport_subType', aUDFAnswerSubTypes);
    }
    else if (msImportType == 'UDF Questions') {
        application.setValueListItems('dataImport_subType', aUDFQuestionSubTypes);
    }
    else if (msImportType == 'Employees') {
        application.setValueListItems('dataImport_subType', aEmployeeSubTypes);
    }
    else if (msImportType == scopes.avText.getLblMsg('SalesOrdersImport')) {
        application.setValueListItems('dataImport_subType', aSalesOrderSubTypes);
    }
    else if (msImportType == scopes.avText.getLblMsg('Taxes')) {
        application.setValueListItems('dataImport_subType', aTaxSubTypes);
    }
    else if (msImportType == scopes.avText.getLblMsg('InvoicesHistorical') || msImportType == 'Projects' || msImportType == scopes.avText.getLblMsg('PriceRules')) {
        mbImportHasSubType=false;
        elements.lblSubType.visible = false;
        elements.cboSubType.visible = false
    }
    else if (msImportType == scopes.avText.getLblMsg('accounting')) {
        application.setValueListItems('dataImport_subType', aAccountingSubTypes);
    }
		
	clearScreen();
	
	forms.utils_dataImport_dtl_automatic.msImportType = msImportType;
    forms.utils_dataImport_dtl_automatic.mbSubType = mbImportHasSubType;
    forms.utils_dataImport_dtl_automatic.cboImportType_onAction(event);
    
    return true;
}

/**
 * @properties={typeid:24,uuid:"7ADE8763-9255-4681-B0D6-F699FB1BB824"}
 */
function loadFile(){
	msLines=['']
	lblNumRecs=''
	 plugins.file.showFileOpenDialog(1, null, false, new Array("txt"), callbackUploadFile, 'Upload Import File...');
}

/**
 * Process File
 *
 * @param {Array<plugins.file.JSFile>} file
 *
 * @properties={typeid:24,uuid:"EC4B651A-79E8-4735-9632-3FD0B5C97C5E"}
 */
function callbackUploadFile(file)
{
	
	var sPath = "",
		jsNew;

	var sServerPath = globals.avUtilities_serverGetPath('');
	// GD - Apr 29, 2014: After much difficulty, here is the code we need to use for moving files to the server
	sPath = forms["_docs_base"].setPathSep(sServerPath + "/server/webapps/root/uploads");
	
	_jsFile = file[0];
	sPath += "/" + globals.org_id + "_dataImports/" + _jsFile.getName();
	_sFileName = _jsFile.getName();
	
	jsNew = plugins.file.convertToJSFile(sPath);
	plugins.file.createFolder(jsNew.getParent()); // Need to create the folder, in case it does not exist
	plugins.file.writeFile(jsNew, _jsFile.getBytes());
	fileUploadDone()
	
//	plugins.file.streamFilesToServer(_jsFile, "/" + globals.org_id + "_dataImports/" + _jsFile.getName() , fileUploadDone);

	

}

/**
 * fileUploadDone
 *
 *
 * @properties={typeid:24,uuid:"B33150A4-4DCC-4651-9C3A-DFC1FA4A3A4D"}
 */
function fileUploadDone()
{
	// sl-2381 - calling new avUtilities_getFileServerServiceDefaultFolder() function - gets upload path from se
	//var sSourceDir = globals.avUtilities_getFileServerServiceDefaultFolder("/" + globals.org_id + "_dataImports/")	

	// HP - the above a previous fix for sevroy deciding to put our files in a different location - commented in favour of new fix in callbackUploadFile() above
	var sSourceDir = globals.avUtilities_serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_dataImports/");
	elements.btnImportData.enabled = true;
	
	var txt = plugins.file.readTXTFile(sSourceDir + _jsFile.getName());
	
	if(txt){
		msLines = txt.split("\n")
		if(msLines.length > 1){
			lblNumRecs = _jsFile.getName();
			lblNumRecs += ' (' + (msLines.length - 1) + ' ' + scopes.avText.getLblMsg('records') + ')';

			elements.lblFileNameAndRecs.enabled = true
			
			if(msImportType == 'Cash Receipt Details' || msImportType == 'Cash Receipts'){
				doImport();
				
				if(forms.utils_dataImport_dtl.miNumInserts > 0){
					plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_cash_receipt');
					plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_cash_receipt_detail');
					plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_invoice');

					if(msImportType == 'Cash Receipt Details'){
						forms['sa_cash_receipt_dtl'].refreshDetailsFromDB();
					}
					else{
						databaseManager.refreshRecordFromDatabase(forms['sa_cash_receipt_tbl'].foundset, -1);
					}
				}
			}
		}
	}
	
	_jsFile=null
}

/**
 * @return
 * @properties={typeid:24,uuid:"05E127E8-5DE9-450F-A4C3-1D73EF6EA58E"}
 */
function ValidEntry(){
	var msg = ''
	
	if(msImportType == '')
		msg = 'Please choose an Import Type'
	else if(mbImportHasSubType){
        if (msSubType == '') {
            msg = 'Please choose a Sub Type';
        }
	}
		
	if(msg=='')
		return true
	else
	{
	    showMsg('Incomplete Information', 'error', msg);
		return false;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"FF9098F6-1264-4C0A-A48B-22ABBFD4E1BB"}
 */
function ValidImportFile(){
	var retval = true
	
	if(msLines.length < 2){
        if (bAutoImport) {
            autoDataImportLog('There are no records to process.', 'Error', true);
        }
        else {
            showMsg('Import File', 'error', 'There are no records to process.');
        }
	    
		retval=false
	}
	else{
		msErrorMsgs = ValidateImportFileColNames();
		
		if(msErrorMsgs!=''){
	        if (bAutoImport) {
	            autoDataImportLog(msErrorMsgs, 'Error', true);
	        }
	        else {
	            elements.tabAutoImport.visible = false;
	            elements.fraResults.visible = true;
	            elements.lblFailedRecs.text = "Invalid Column Names:";
	            elements.lblFailedRecs.visible = true;
	            elements.txtFailedRecs.visible = true;
	        }
		    
		    
			msNumInserts = 'No records processed.'
			retval=false
		}
	}
	
	return retval
}

/**
 * @return
 * @properties={typeid:24,uuid:"D48BB1F9-1B65-49B4-8B88-EB2A53EBFEE6"}
 */
function ValidateImportFileColNames(){	
	var taCols = msLines[0].split('\t')
	var taImportFileCols1=0
	var taImportFileCols2=0
	var taImportFileCols3=0
	var tiNumColsExpected=0
	var msg=''
	var i=0
	var tiIdx = 0
	var bVariableNumCols = false

	// SUPPLIERS
	if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Vendors')){
		if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Vendors')){
			taImportFileCols1 = maSupplierColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - Addresses'){
			taImportFileCols1 = maSupplierAddressColNames
			taImportFileCols2 = maAddressColNames
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='3 - Contacts'){
			taImportFileCols1 = maSupplierContactColNames
			taImportFileCols2 = maContactColNames
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
	}
	
	// CUSTOMERS - IMPORT
	else if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers') && msAction == 'Import'){
		if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){
			taImportFileCols1 = maCustomerColNames
			taImportFileCols2 = maCustAddressColNamesForCustImport
			taImportFileCols3 = maAddressColNames
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length + taImportFileCols3.length
			bVariableNumCols = true
		}
		else if(msSubType=='2 - Addresses'){
			taImportFileCols1 = maCustAddressColNames
			taImportFileCols2 = maAddressColNames
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='3 - Contacts'){
			taImportFileCols1 = maCustContactColNames
			taImportFileCols2 = maContactColNames
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='4 - Divisions/Plants'){
			taImportFileCols1 = maCustDivPlantColNames;
			tiNumColsExpected = taImportFileCols1.length;
		}
	}
	// CUSTOMERS - PURGE
	else if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers') && msAction == 'Purge'){
		if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){
			taImportFileCols1 = ['Customer Code']
			tiNumColsExpected = taImportFileCols1.length 
		}
	}
	
	// COST CENTERS
	else if(msImportType=='Cost Centers'){
		if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Depts')){
			taImportFileCols1 = maDeptColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Categories')){
			taImportFileCols1 = maCatColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='3 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Operations')){
			taImportFileCols1 = maOpColNames
			tiNumColsExpected = taImportFileCols1.length
		}
	}

	// INVENTORY
	else if(msImportType=='Inventory'){
		if(msSubType=='1 - Substrate Types'){
			taImportFileCols1 = maPaperGradeColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - Substrate Finishes'){
			taImportFileCols1 = maPaperBrandColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='3 - Inventory Items'){
			taImportFileCols1 = maIntItemsColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='4 - Bin Locations'){
			taImportFileCols1 = maBinColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='5 - Inventory Items Multiple Locations'){
			taImportFileCols1 = maIntItemsBinColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='6 - Item Documents'){
			taImportFileCols1 = maItemDocs;
			tiNumColsExpected = taImportFileCols1.length;
		}
		else if(msSubType=='7 - Bill of Materials'){
			taImportFileCols1 = maBOM;
			tiNumColsExpected = taImportFileCols1.length;
		}
	}

	// EMPLOYEES
	else if(msImportType=='Employees'){
		if(msSubType=='1 - Sales Reps'){
			taImportFileCols1 = maSalesRepColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - Employees'){
			taImportFileCols1 = maEmpColNames
			tiNumColsExpected = taImportFileCols1.length
		}
	}

	// CRM
	else if(msImportType=='CRM'){
		if(msSubType=='1 - Leads'){
			taImportFileCols1 = maLeadColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - Activities'){
			taImportFileCols1 = maActivityColNames
			tiNumColsExpected = taImportFileCols1.length
		}
	}

	// NOTES
	else if(msImportType=='Notes'){
		if(msSubType=='Activity'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Customer Contact'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code', 'note_contact_first_and_last']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Customer'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Estimate'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code', 'note_estimate_number']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Lead'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code', 'note_lead_number']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Sales Order'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_cust_code', 'note_sales_order_number']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
		else if(msSubType=='Supplier'){
			taImportFileCols1 = maNoteColNames
			taImportFileCols2 = ['note_supplier_code']
			tiNumColsExpected = taImportFileCols1.length + taImportFileCols2.length
		}
	}

	// UDF Answers
	else if(msImportType=='UDF Answers'){
		if(msSubType =='Customer')	
			maUDFAnswerColNames = ['udf_descr','udf_answer','customer_code']
		else if(msSubType=='Customer Contact')
			maUDFAnswerColNames = ['udf_descr','udf_answer','customer_code','contact_first_and_last']
		else if(msSubType=='Job')
			maUDFAnswerColNames = ['udf_descr','udf_answer','job_number']
		else if(msSubType=='Sales Order')
			maUDFAnswerColNames = ['udf_descr','udf_answer','sales_order_number']
		else if(msSubType=='Item')
			maUDFAnswerColNames = ['udf_descr','udf_answer','item_code']
		else if(msSubType=='Suppliers')
			maUDFAnswerColNames = ['udf_descr','udf_answer','supplier_code']			

		tiNumColsExpected = maUDFAnswerColNames.length 
	}

	// UDF Questions
	else if(msImportType=='UDF Questions'){
		if(msSubType =='1 - UDF Questions'){
			taImportFileCols1 = maUDFQuestionColNames
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType=='2 - UDF Question Table Options'){
			taImportFileCols1 = maUdfQuestionTableOptionColNames
			tiNumColsExpected = taImportFileCols1.length
		}
	}

	// Sales Orders
	else if(msImportType == _sSalesOrdersImport){
		if(msSubType == '1 - Sales Orders')
			maColNames = ['ordh_document_num', 'cust_code', 'ordh_shipto_custaddr_code', 'ordh_billto_custaddr_code', 
				'ordh_description', 'currency_code', 'salesterr_code', 'ordh_order_date', 'ordh_customer_po', 'campaign_code', 'rush_code', 
				'custproj_code', 'ordtype_code', 'ordh_salesper_code', 'ordh_csr_empl_code', 'plant_code', 
				'ordh_document_stream', 'shipmethod_code', 'custcontact_first_and_last', 'ordh_priority', 'ordh_staging_location', 
				'paymethod_code', 'ordh_chargeback_code', 'ordrevh_order_status', 'ordrevh_promise_date', 'ordrevh_expected_date', 
				'ordrevh_exchange_rate', 'ordrevh_total_order_qty', 'ordrevh_total_ship_qty', 'ordrevh_total_backorder_qty', 
				'ordrevh_subtotal_amount', 'ordrevh_markup_pct', 'ordrevh_markup_amount', 
				'ordrevh_shipping_amount', 'ordrevh_warehouse_amount', 'ordrevh_rush_amount', 'ordrevh_total_amount', 'ordrevh_onhold', 
				'ordrevh_total_taxes', 'ordrevh_is_active', 'ordrevh_total_commission', 'bill_ordaddr_name', 'bill_ordaddr_phone1', 
				'bill_ordaddr_phone1_ext', 'bill_ordaddr_phone2', 'bill_ordaddr_phone2_ext', 'bill_ordaddr_phone3', 'bill_ordaddr_phone3_ext', 
				'bill_ordaddr_fax', 'bill_ordaddr_fax_ext', 'bill_ordaddr_fax_home', 'bill_ordaddr_email_business', 'bill_ordaddr_email_home',
				'bill_ordaddr_email_other', 'bill_custcontact_first_and_last', 'bill_addr_address1', 'bill_addr_address2', 'bill_addr_address3', 
				'bill_addr_city', 'bill_stateprov_code', 'bill_addr_postal', 'bill_country_code', 'ship_ordaddr_name', 'ship_ordaddr_phone1', 
				'ship_ordaddr_phone1_ext', 'ship_ordaddr_phone2', 'ship_ordaddr_phone2_ext', 'ship_ordaddr_phone3', 'ship_ordaddr_phone3_ext', 
				'ship_ordaddr_fax', 'ship_ordaddr_fax_ext', 'ship_ordaddr_fax_home', 'ship_ordaddr_email_business', 'ship_ordaddr_email_home', 
				'ship_ordaddr_email_other', 'ship_custcontact_first_and_last', 'ship_addr_address1', 'ship_addr_address2', 'ship_addr_address3', 
				'ship_addr_city', 'ship_stateprov_code', 'ship_addr_postal', 'ship_country_code', 'cust_addr_code']
		else if(msSubType == '2 - Sales Order Details')
			maColNames = ['sales_order_num', 'ordrevd_line_num', 'ordrevd_prod_desc', 'whse_code', 'uom_code', 'ordrevd_qty_ordered', 
				'ordrevd_qty_shipped', 'ordrevd_qty_backord', 'ordrevd_sell_price', 'ordrevd_disc_amt', 'ordrevd_extended_price', 
				'inventory_item_code', 'ordrevd_unit_cost', 'ordrevd_extended_cost', 'sysworktype_code', 'worktype_descr', 'ordrevd_unit_price', 
				'ordrevd_staging_location', 'ordrevd_ext_price_over'] 
		else if(msSubType == '3 - Sales Order Multiship')
			maColNames = ['sales_order_num', 'sequence_nr', 'cust_code', 'custaddr_code', 'shipmethod_code', 'ordrevhms_instructions', 'ordrevhms_event_date', 
				'ordrevhms_must_arrive_date', 'ordrevhms_ship_date', 'ordrevhms_shipping_charges', 'custcontact_first_and_last'] 
		else if(msSubType == '4 - Sales Order Multiship Qtys')
			maColNames = ['sales_order_num', 'detail_line_num', 'multi_ship_line_num', 'ordrevdms_qty', 'ordrevdms_qty_backord', 
			'ordrevdms_qty_shipped']

		taImportFileCols1 = maColNames
		tiNumColsExpected = maColNames.length
	}

	// TAXES
	else if(msImportType == scopes.avText.getLblMsg('Taxes')){
		if(msSubType == '1 - ' + scopes.avText.getLblMsg('taxItems')){
			taImportFileCols1 = maTaxItems
			tiNumColsExpected = taImportFileCols1.length
		}
		else if(msSubType == '2 - ' + scopes.avText.getLblMsg('taxGroups')){
			taImportFileCols1 = maTaxGroups
			tiNumColsExpected = taImportFileCols1.length
			bVariableNumCols=true
		}
		else if(msSubType == '3 - ' + scopes.avText.getLblMsg('TaxItemRates')){
			taImportFileCols1 = maTaxItemRates
			tiNumColsExpected = taImportFileCols1.length;
		}
	}
	
	// INVOICES
	else if(msImportType == scopes.avText.getLblMsg('InvoicesHistorical')){
		taImportFileCols1 = maInvoiceColNames;
		tiNumColsExpected = taImportFileCols1.length
	}
	
	// Cash Receipt Details
	else if(msImportType == 'Cash Receipt Details'){
		// long format
		if(taCols.length == maCashReceipts.length){
			taImportFileCols1 = maCashReceipts;
			tiNumColsExpected = maCashReceipts.length;
		}
		else{
			taImportFileCols1 = maCashReceiptDetails;
			tiNumColsExpected = maCashReceiptDetails.length;
		}
	}
	
	// Cash Receipts
	else if(msImportType == 'Cash Receipts'){
		taImportFileCols1 = maCashReceipts;
		tiNumColsExpected = taImportFileCols1.length
	}

	// Projects
	else if(msImportType == i18n.getI18NMessage('i18n:avanti.program.projects')){
		taImportFileCols1 = maProjects;
		tiNumColsExpected = taImportFileCols1.length
	}
	
	// Price Rules
	else if(msImportType == i18n.getI18NMessage('avanti.lbl.PriceRules')){
		taImportFileCols1 = maPriceRuleDetails;
		tiNumColsExpected = taImportFileCols1.length
		bVariableNumCols = true
	}
	
	// ACCOUNTING
	else if (msImportType == scopes.avText.getLblMsg('accounting')) {
		if (msSubType == '1 - Chart of Accounts') {
			taImportFileCols1 = maChartOfAccountsColNames;
			if (taCols.length == taImportFileCols1.length) {
				tiNumColsExpected = taImportFileCols1.length;
			}
			else {
				tiNumColsExpected = taImportFileCols1.length - 1;
			}
		}
	}

	if(taCols.length != tiNumColsExpected && !bVariableNumCols){
		msg = 'Incorrect number of columns in import file. Expecting: ' + tiNumColsExpected + '. Actual: ' + taCols.length
	}
	else{
		for(i=0;i<taImportFileCols1.length;i++)
			if(taCols[i] != taImportFileCols1[i]){
				if(msg != '')
					msg += '\n'
				msg += 'column ' + (i+1) + " is: '" + taCols[i] + "', should be: '" + taImportFileCols1[i] + "'"  
			}
		
		if(taImportFileCols2.length>0)	
			for(i=0;i<taImportFileCols2.length;i++){
				tiIdx = taImportFileCols1.length + i
				if(taCols[tiIdx] != taImportFileCols2[i]){
					if(msg != '')
						msg += '\n'
					msg += 'column ' + (tiIdx+1) + " is: '" + taCols[tiIdx] + "', should be: '" + taImportFileCols2[i] + "'"  
				}
			}

		if(taImportFileCols3.length>0)	
			for(i=0;i<taImportFileCols3.length;i++){
				tiIdx = taImportFileCols1.length + taImportFileCols2.length + i
				if(taCols[tiIdx] != taImportFileCols3[i]){
					if(msg != '')
						msg += '\n'
					msg += 'column ' + (tiIdx+1) + " is: '" + taCols[tiIdx + i] + "', should be: '" + taImportFileCols3[i] + "'"  
				}
			}
				
		if(msg !='')
			msg = 'The following column names in the import file are incorrect: ' + '\n\n' + msg
		
	}

	return msg
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3B6F7B25-67A4-46F1-8401-53B77693791D"}
 */
function btnSelectFile_onAction(event) {
	clearScreen()
	loadFile()
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {Boolean} [bBypassMsg]
 *
 * @properties={typeid:24,uuid:"7CF231B7-3573-456C-B819-D07BE01F891F"}
 */
function btnImportData_onAction(event, bBypassMsg) {
	msAction = 'Import'
	clearResults()
	
	_oImportCache = {}; //initialize cache object
	_oImportCache.getIDFromCode = {}; // iniitalize the code lookup object
		
	if (!msLines || msLines.length <= 1) {
        if (!bAutoImport) {
            scopes.avText.showWarning('nothingToProcess');
        }
	}
	else {
		if(ValidEntry()){
			if(ValidImportFile()){
				enableForm(false)
				processFile()
				_oImportCache = null;
				
				if(bBypassMsg){
					showResults(false);				
				}
				else{
					showResults(true);				
				}
				
				enableForm(true);
			}
		}

		lblNumRecs=''
			
		msLines=['']
	}
}

/**
 * @properties={typeid:24,uuid:"590569BF-A74E-4DCF-9122-1BA948F04ACB"}
 */
function processFile(){
	try{
		if(msAction == 'Import') {
			var oStopWatch = new scopes.avUtils.StopWatch();
			oStopWatch.reset()
			oStopWatch.start()
			
            if (bAutoImport) {
                autoDataImportLog("Data Import Started", "Info", true);
            }

			// SUPPLIERS
			if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Vendors')){
				if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Vendors'))
					processSupplierRecords()
				else if(msSubType=='2 - Addresses')
					processSupplierAddressRecords()
				else if(msSubType=='3 - Contacts')
					processSupplierContactRecords()
			}
			
			// CUSTOMERS
			else if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){
				
				if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers'))
					processCustomerRecords()
				else if(msSubType=='2 - Addresses')
					processCustomerAddressRecords()
				else if(msSubType=='3 - Contacts')
					processCustomerContactRecords()
				else if(msSubType=='4 - Divisions/Plants')
					processCustomerDivPlantRecords();
			}
			
			// COST CENTERS
			else if(msImportType=='Cost Centers'){
				if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Depts'))
					processDeptRecords()
				else if(msSubType=='2 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Categories'))
					processCatRecords()
				else if(msSubType=='3 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Operations'))
					processOpRecords()
			}
	
			// INVENTORY
			else if(msImportType=='Inventory'){
				if(msSubType=='1 - Substrate Types')
					processPaperGradeRecords()
				else if(msSubType=='2 - Substrate Finishes')
					processPaperBrandRecords()
				else if(msSubType=='3 - Inventory Items')
					processIntItemRecords()						
				else if(msSubType=='4 - Bin Locations')
					processBinRecords();
				else if(msSubType=='5 - Inventory Items Multiple Locations')
					processIntItemMultipleLocationRecords();
				else if(msSubType=='6 - Item Documents'){
					processItemDocumentRecords();
				}
				else if(msSubType=='7 - Bill of Materials'){
					processItemBOMRecords();
				}
			}
	
			// EMPLOYEES
			else if(msImportType=='Employees'){
				if(msSubType=='1 - Sales Reps')
					processSalesRepRecords()
				else if(msSubType=='2 - Employees')
					processEmpRecords()						
			}
				
			// CRM
			else if(msImportType=='CRM'){
				if(msSubType=='1 - Leads')
					processLeadRecords()
				else if(msSubType=='2 - Activities')
					processActivityRecords()						
			}
	
			// NOTES
			else if(msImportType=='Notes' && msSubType != '')
				processNoteRecords()
		
			// UDF Answers
			else if(msImportType=='UDF Answers' && msSubType != '')
				processUDFAnswerRecords()
	
			// UDF QUESTIONS
			else if(msImportType=='UDF Questions'){
				if(msSubType =='1 - UDF Questions')
					processUDFQuestionRecords()						
				else if(msSubType=='2 - UDF Question Table Options')
					processUDFQuestionTableOptionRecords()
			}
			
			// SALES ORDERS
			else if(msImportType == _sSalesOrdersImport){
				if(msSubType =='1 - Sales Orders')
					processRecords(processSalesOrderRecord)
				else if(msSubType =='2 - Sales Order Details')
					processRecords(processSalesOrderDetailRecord)
				else if(msSubType == '3 - Sales Order Multiship')
					processRecords(processSalesOrderMultishipRecord)
				else if(msSubType == '4 - Sales Order Multiship Qtys')
					processRecords(processSalesOrderMultishipQtyRecord)
			}
			
			// TAXES
			else if(msImportType == scopes.avText.getLblMsg('Taxes')){
				if(msSubType == '1 - ' + scopes.avText.getLblMsg('taxItems')){
					processTaxItemRecords()						
				}
				if(msSubType == '2 - ' + scopes.avText.getLblMsg('taxGroups')){
					processTaxGroupRecords()						
				}
				else if(msSubType == '3 - ' + scopes.avText.getLblMsg('TaxItemRates')){
					processTaxRateRecords()						
				}
			}
			
			// INVOICES
			else if(msImportType == scopes.avText.getLblMsg('InvoicesHistorical')){
				processInvoiceRecords();					
			}
			
			// Cash Receipt Details
			else if(msImportType == 'Cash Receipt Details' && sCashReceiptID){
				processCashReceiptDetailRecords();					
			}
			
			// Cash Receipt Details
			else if(msImportType == 'Cash Receipts'){
				processCashReceiptRecords();					
			}

			// Projects
			else if(msImportType == i18n.getI18NMessage('i18n:avanti.program.projects')){
				processProjectRecords();
			}

			// Projects
			else if(msImportType == i18n.getI18NMessage('avanti.lbl.PriceRules')){
				importPriceRuleDetails();
			}
			
			// ACCOUNTING
			else if(msImportType == i18n.getI18NMessage('i18n:avanti.lbl.accounting')) {
				if (msSubType == '1 - Chart of Accounts') {
					processChartOfAccountRecords();
				}
			}
			
			oStopWatch.stop()
			application.output('######  TIME: ' + oStopWatch.total())
		} 
		else if(msAction == 'Purge') {
			if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){
				if(msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers') && msAction == 'Purge') {
					purgeCustomerRecords()
				}
			}
		}
		
	}
	catch(ex){
        showMsg('Processing Halted', 'error', ex.message);
		showResults(false);
	}
}

/**
 * @properties={typeid:24,uuid:"D31ED426-2DCA-49AD-8154-379B08E5A88F"}
 */
function showResults(pbShowMsg){
    elements.tabAutoImport.visible = false;
	elements.fraResults.visible = true
    elements.lblFailedRecs.visible = true;
	elements.txtFailedRecs.visible = true;
	
	if(miNumInserts > 0)
		msNumInserts = miNumInserts + ' records inserted'
	if(miNumUpdates > 0)
		msNumUpdates = miNumUpdates + ' records updated'
	if(miNumDeletes > 0)
		msNumDeletes = miNumDeletes + ' records deleted'
	if(miNumErrors > 0){
		msNumRecsFailed = miNumRecsFailed + ' records failed'
		msNumErrors = miNumErrors + ' errors found'
		elements.lblFailedRecs.text = "Failed Records:"
		elements.lblFailedRecs.visible = true
		elements.txtFailedRecs.visible = true	
	}
	
    if (bAutoImport) {
        autoDataImportLog("Data Import Finished", "Info");
        
        if (msNumInserts) {
            autoDataImportLog(msNumInserts, "Info");
        }
        if (msNumUpdates) {
            autoDataImportLog(msNumUpdates, "Info");
        }
        if (msNumRecsFailed) {
            autoDataImportLog(msNumRecsFailed, "Info");
        }
        if (msNumErrors) {
            autoDataImportLog(msNumErrors, "Info");
        }
    }
    else if (pbShowMsg) {
        scopes.avText.showInfo('ImportFinished');
    }
}

/**
 * @return
 * @properties={typeid:24,uuid:"7D31534F-89CD-4FCB-8C1F-757C85A38682"}
 */
function importUsesSql(){
	if(msImportType==i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers') &&	
		msSubType=='1 - ' + i18n.getI18NMessage('i18n:avanti.utils.dataImport.vl_dataImportType.Customers')){

		return true;
	}
	
	return false;
}

/**
 * 
 * @param {Boolean} on
 *
 * @properties={typeid:24,uuid:"439724BB-3925-4A31-A257-89FBDE1867B4"}
 */
function enableForm(on){
	controller.readOnly = !on;
	elements.btnSelectFile.enabled = on;
	elements.btnImportData.enabled = on;
	elements.btnExportData.enabled = on;
}

/**
 * @properties={typeid:24,uuid:"E1997179-FDCF-46B4-988D-B257288EA285"}
 */
function clearResults(){
	miNumInserts=0
	miNumUpdates=0
	miNumDeletes=0
	miNumRecsFailed=0
	miNumErrors=0
	
	msNumInserts=''
	msNumUpdates=''
	msNumDeletes=''
	msNumRecsFailed=''
	msNumErrors=''
	
	msErrorMsgs=''	

	elements.lblFailedRecs.visible = false;
	elements.txtFailedRecs.visible = false;
	elements.fraResults.visible = false;
    elements.tabAutoImport.visible = true;
	
	mbCurrRowErrored=false
	_sInsertOrUpdate = ''
}

/**
 * @properties={typeid:24,uuid:"51373C74-C70C-4DC9-911A-395DEDB4E605"}
 */
function clearScreen(){
	clearResults()
	lblNumRecs = ''
	//mbTurnOffHaltingAfterMaxErrors=false
}

/**
 * @properties={typeid:24,uuid:"E03EE7C2-730A-4674-BD1D-45747F1517EF"}
 */
function processSupplierRecords(){
	mfsSuppliers = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processSupplierRecord(msLines[i], i)
		}
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"DE3E3584-6456-4897-827E-313F5FA3E499"}
 * @AllowToRunInFind
 */
function processSupplierRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column 'Supplier Code' is blank. This is a required field.", tiRecNum, '')
		else if(mfsSuppliers.find() || mfsSuppliers.find()){
			mfsSuppliers.supplier_code = removeCommaQuotes(taCols[0].toUpperCase());			
			if(mfsSuppliers.search() > 0){ // found it do an update
				WriteSupplierToDB(taCols, 'U', tiRecNum)
			}
			else{ // do an insert
				mfsSuppliers.newRecord()
				mfsSuppliers.supplier_code = taCols[0].toUpperCase()
				WriteSupplierToDB(taCols, 'I', tiRecNum)
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"425B51EC-ACD8-4976-8D51-E7EC795B1624"}
 */
function WriteSupplierToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

//		var maSupplierColNames = ['Supplier Code', 'Supplier Name','Supplier Active','Currency Code','Supplier Website','Supplier Since','Buyer',
//		'Minimim PO Value',	'Shipping Method','Account Number','Supplier Accepts Backorders','Supplier Confirmation Required','Freight Bill Method',
//		'Freight Cost Method','Payment Terms','Default Lead Time']
		
		/*
		 * @param {JSFoundSet} toFoundset
		 * @param {String} colName
		 * @param {Number} tiRecNum
		 * @param {String} niceColName
		 * @param {String} colValue
		 * @param {Boolean} mandatory
		 * @param {Number} maxLen
		 * @param {String} stdValidationType
		 * @param {String} validVals
		 * @param {String} translatedVals
		 * @param {Function} validationFunc
		 * @param {Object} [otherValidationFuncValue]
		 * @param {Object} [otherValidationFuncValue2]
		 */
		
		Validate(mfsSuppliers, 'supplier_name', tiRecNum, maSupplierColNames[1], taCols[1], true, 64, '', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_active', tiRecNum, maSupplierColNames[2], taCols[2], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsSuppliers, 'curr_id', tiRecNum, maSupplierColNames[3], taCols[3], true, 0, '', '', '', GetCurrencyIDFromCode, '')
		Validate(mfsSuppliers, 'supplier_website', tiRecNum, maSupplierColNames[4], taCols[4], false, 120, '', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_since', tiRecNum, maSupplierColNames[5], taCols[5], true, 0, 'DATE', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_buyer_id', tiRecNum, maSupplierColNames[6], taCols[6], false, 0, '', '', '', GetBuyerIDFromCode, '')		
		Validate(mfsSuppliers, 'supplier_minimim_po_value', tiRecNum, maSupplierColNames[7], taCols[7], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_shipmethod_id', tiRecNum, maSupplierColNames[8], taCols[8], false, 0, '', '', '', GetShipMethodIDFromCode, '')		
		Validate(mfsSuppliers, 'supplier_our_acct_number', tiRecNum, maSupplierColNames[9], taCols[9], false, 50, '', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_accept_bo', tiRecNum, maSupplierColNames[10], taCols[10], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsSuppliers, 'supplier_confirmation_req', tiRecNum, maSupplierColNames[11], taCols[11], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsSuppliers, 'supplier_freight_bill_method', tiRecNum, maSupplierColNames[12], taCols[12], true, 0, '', 'Supplier, Other', 'S,O', null, '')
		Validate(mfsSuppliers, 'supplier_freight_cost_method', tiRecNum, maSupplierColNames[13], taCols[13], true, 0, '', 'Landed, Expensed', 'L,E', null, '')
		Validate(mfsSuppliers, 'terms_id', tiRecNum, maSupplierColNames[14], taCols[14], false, 0, '', '', '', GetTermsIDFromCode, '')		
		Validate(mfsSuppliers, 'supplier_default_leadtime', tiRecNum, maSupplierColNames[15], taCols[15], false, 0, 'INTEGER', '', '', null, '')
		Validate(mfsSuppliers, 'supplier_matching_method', tiRecNum, maSupplierColNames[17], taCols[17], false, 0, '', 'None, 3 Ways', '0,3', null, '');

			/***@type {String} */
			var tsSuppID = "" + mfsSuppliers.supplier_id + "" // getting weird errs in Query() because it didnt regard custid as a string
			var tsAddrId = ''
			
			tsAddrId = GetSuppAddrIDFromCode('PRIMARY', tsSuppID, false) 
			if(!isBlank(tsAddrId)) 
				mfsSuppliers.supplier_primary_custaddr_id = tsAddrId
			else // didnt find it - create placeholder rec with that code
				mfsSuppliers.supplier_primary_custaddr_id = CreateNewSupplierAddress(tsSuppID, 'PRIMARY')
		
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsSuppliers, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsSuppliers.getSelectedRecord())		
			CheckForSaveErrors(mfsSuppliers, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsSuppliers, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @properties={typeid:24,uuid:"45923F60-1677-425E-B8FF-05CC5A4F5469"}
 */
function processSupplierContactRecords(){
	mfsSupplierContacts = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier_contact')
	mfsContacts = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i])
			processSupplierContactRecord(msLines[i], i)		
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"AB20B829-C984-4064-92ED-626D541115CE"}
 * @AllowToRunInFind
 */
function processSupplierContactRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')

	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError("Column 'Supplier Code' is blank. This is a required field.", tiRecNum, '')
		}
		else if(mfsSupplierContacts.find() || mfsSupplierContacts.find()){
			var tsFirstAndLast = taCols[3] + ' ' + taCols[5]			
			var tsSuppID = GetSupplierIDFromCode(taCols[0], false)

			//sl-5881
			var contactTypeID = 'empty';
			if(!isBlank(taCols[19])){
				contactTypeID = GetContactTypeIDFromDescr(taCols[19], false, 'contact type');
			}
			
			if(isBlank(tsSuppID))
				LogError("Column 'Supplier Code' has an invalid value ('" + taCols[0] + "'). Please check Supplier setup for valid values.", tiRecNum, '')
			else if(isBlank(contactTypeID))
				LogError("Column 'Contact Type' has an invalid value ('" + taCols[19] + "')", tiRecNum, '')
			else{
				var tiFoundRecs = 0
				var tsSuppContactID = GetSuppContactIDFromFirstAndLast(tsFirstAndLast, tsSuppID, false, '', contactTypeID)
				if(tsSuppContactID != ''){
					mfsSupplierContacts.suppcontact_id = tsSuppContactID 
					tiFoundRecs = mfsSupplierContacts.search() 
				}
				else{
					// sl-3396 - if tsSuppContactID wasnt found, then no search was done - have to do a clear to get out of find mode or we cant create new record
					mfsSupplierContacts.clear()
				}
				
				if(tiFoundRecs > 0){ // found it do an update
					WriteSupplierContactToDB(taCols, 'U', tiRecNum)
				}
				else{ // do an insert
					mfsSupplierContacts.newRecord()
					mfsContacts.newRecord() 
					mfsSupplierContacts.supplier_id = tsSuppID
					mfsContacts.suppcontact_id = mfsSupplierContacts.suppcontact_id 
					mfsSupplierContacts.contact_id = mfsContacts.contact_id 
					WriteSupplierContactToDB(taCols, 'I', tiRecNum)
				}
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"2F54D6E8-51FA-43C0-AEC2-32320E59C2D4"}
 */
function WriteSupplierContactToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
		
		Validate(mfsSupplierContacts, 'supplier_id', tiRecNum, maSupplierContactColNames[0], taCols[0], true, 0, '', '', '', GetSupplierIDFromCode, '')
		Validate(mfsSupplierContacts, 'suppaddr_id', tiRecNum, maSupplierContactColNames[1], taCols[1], false, 0, '', '', '', GetSuppAddrIDFromCode, mfsSupplierContacts.supplier_id) 
		
		var uAddrID;
		var sAddrName;
		if(utils.hasRecords(mfsSupplierContacts.ap_supplier_contact_to_ap_supplier_address)){
			uAddrID = mfsSupplierContacts.ap_supplier_contact_to_ap_supplier_address.addr_id;
			sAddrName = mfsSupplierContacts.ap_supplier_contact_to_ap_supplier_address.suppaddr_address_name;
		}
		
		if(tsInsertOrUpdate=="I")
			FillContactFoundset(taCols, tiRecNum, 2, mfsContacts, uAddrID, sAddrName); // sl-3422 - add addrID and addrName params 
		else
			FillContactFoundset(taCols, tiRecNum, 2, mfsSupplierContacts.ap_supplier_contact_to_sys_contact, uAddrID, sAddrName); // sl-3422 - add addrID and addrName param
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsSupplierContacts, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsSupplierContacts.getSelectedRecord())		
			CheckForSaveErrors(mfsSupplierContacts, tsInsertOrUpdate)
			
			if(tsInsertOrUpdate=='I'){
				databaseManager.saveData(mfsContacts.getSelectedRecord())
				CheckForSaveErrors(mfsContacts, tsInsertOrUpdate)
				miNumInserts=miNumInserts+1
			}
			else{
				databaseManager.saveData(mfsSupplierContacts.ap_supplier_contact_to_sys_contact.getSelectedRecord())
				CheckForSaveErrors(mfsSupplierContacts.ap_supplier_contact_to_sys_contact, tsInsertOrUpdate)
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsSupplierContacts, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * this func doesnt pop the db directly, is called by both WriteSupplierContactToDB() and WriteCustContactToDB(), which write to db
 * tiStartIdx may vary depending on where this func is being called from 
 * @param {Array<String>} taCols
 * @param {Number} tiRecNum
 * @param {Number} tiStartIdx
 * @param {JSFoundSet<db:/avanti/sys_contact>} toFoundset
 * @param {UUID} addrID
 * @param {String} addrName
 *
 * @properties={typeid:24,uuid:"1E691CEA-DF01-4708-9A36-6BA03C8FF42A"}
 */
function FillContactFoundset(taCols, tiRecNum, tiStartIdx, toFoundset, addrID, addrName){
	Validate(toFoundset, 'contact_title', tiRecNum, maContactColNames[0], taCols[tiStartIdx], false, 10, '', 'Dr., Miss., Mr., Mrs., Ms., Prof.', '', null, '')
	Validate(toFoundset, 'contact_first_name', tiRecNum, maContactColNames[1], taCols[tiStartIdx+1], false, 30, '', '', '', null, '')
	Validate(toFoundset, 'contact_middle_name', tiRecNum, maContactColNames[2], taCols[tiStartIdx+2], false, 30, '', '', '', null, '')
	Validate(toFoundset, 'contact_last_name', tiRecNum, maContactColNames[3], taCols[tiStartIdx+3], false, 30, '', '', '', null, '')
	Validate(toFoundset, 'contact_suffix', tiRecNum, maContactColNames[4], taCols[tiStartIdx+4], false, 10, 'I, II, II, Jr., Sr.', '', '', null, '')
	
	toFoundset.contact_full_name = Trim(toFoundset.contact_title + ' ' + toFoundset.contact_first_name + ' ' + 
		toFoundset.contact_middle_name +  ' ' + toFoundset.contact_last_name +  ' ' +	toFoundset.contact_suffix).replace('  ', ' ')
	toFoundset.contact_file_as = Trim(toFoundset.contact_last_name + ', ' + toFoundset.contact_first_name + ' ' + toFoundset.contact_middle_name).replace('  ', ' ')
	toFoundset.contact_first_and_last = Trim(toFoundset.contact_first_name + ' ' +	toFoundset.contact_last_name).replace('  ', ' ')
	
	if(addrID){
		// sl-3432 poorly named column, suggests that if on youy are using PRIMARY address, thats not what it means, it means use address psecified in custaddr_id
		toFoundset.contact_use_default_address = 1;
		toFoundset.addr_id = addrID;
		toFoundset.contact_address_name = addrName;
	}
	else{
		toFoundset.contact_use_default_address = 0;
	}
	
	Validate(toFoundset, 'contact_business_phone', tiRecNum, maContactColNames[6], taCols[tiStartIdx+6], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_business_ext', tiRecNum, maContactColNames[7], taCols[tiStartIdx+7], false, 10, '', '', '', null, '')
	Validate(toFoundset, 'contact_mobile_phone', tiRecNum, maContactColNames[8], taCols[tiStartIdx+8], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_home_phone', tiRecNum, maContactColNames[9], taCols[tiStartIdx+9], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_other_phone', tiRecNum, maContactColNames[10], taCols[tiStartIdx+10], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_business_fax', tiRecNum, maContactColNames[11], taCols[tiStartIdx+11], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_home_fax', tiRecNum, maContactColNames[12], taCols[tiStartIdx+12], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'contact_business_email', tiRecNum, maContactColNames[13], taCols[tiStartIdx+13], false, 128, '', '', '', null, '')
	Validate(toFoundset, 'contact_home_email', tiRecNum, maContactColNames[14], taCols[tiStartIdx+14], false, 128, '', '', '', null, '')
	Validate(toFoundset, 'contact_other_email', tiRecNum, maContactColNames[15], taCols[tiStartIdx+15], false, 128, '', '', '', null, '')
	
//	Validate(toFoundset, 'contact_jobtitle_id', tiRecNum, maContactColNames[16], taCols[tiStartIdx+16], false, 0, '', '', '', GetJobTitleIDFromDescr, '')
	if(Trim(taCols[tiStartIdx+16]) != ''){
		var tsJobTitleID = GetJobTitleIDFromDescr(taCols[tiStartIdx+16], false, '')
		if(isBlank(tsJobTitleID))
			tsJobTitleID = CreateJobTitle(taCols[tiStartIdx+16])
		toFoundset.contact_jobtitle_id = tsJobTitleID
	}
	
	Validate(toFoundset, 'contacttype_id', tiRecNum, maContactColNames[17], taCols[tiStartIdx+17], false, 0, '', '', '', GetContactTypeIDFromDescr, '')
	
//	Validate(toFoundset, 'contactdept_id', tiRecNum, maContactColNames[18], taCols[tiStartIdx+18], false, 0, '', '', '', GetDeptIDFromDescr, '')
	if(Trim(taCols[tiStartIdx+18]) != ''){
		var tsDeptID = GetDeptIDFromDescr(taCols[tiStartIdx+18], false, '')
		if(isBlank(tsDeptID))
			tsDeptID = CreateContactDept(taCols[tiStartIdx+18])
		toFoundset.contactdept_id = tsDeptID
	}
	
//	Validate(toFoundset, 'contactdiv_id', tiRecNum, maContactColNames[19], taCols[tiStartIdx+19], false, 0, '', '', '', GetDivisionIDFromDescr, '')
	if(Trim(taCols[tiStartIdx+19]) != ''){
		var tsDivID = GetDivisionIDFromDescr(taCols[tiStartIdx+19], false, '')
		if(isBlank(tsDivID))
			tsDivID = CreateContactDiv(taCols[tiStartIdx+19])
		toFoundset.contactdiv_id = tsJobTitleID
	}
	
	// theyre all active - there isnt an option to de=activate
	//Validate(toFoundset, 'contact_active', tiRecNum, maContactColNames[20], taCols[tiStartIdx+20], false, 0, '', 'Y, N', '1,0', null, '')
	toFoundset.contact_active = 1

}

/**
 * @properties={typeid:24,uuid:"CD0B0F7B-7401-49BE-ACA6-9C6C1412CB9E"}
 */
function processSupplierAddressRecords(){
	mfsSupplierAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier_address')
	mfsAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i])
			processSupplierAddressRecord(msLines[i], i)		
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"1A73188F-C325-470B-AE64-FFF1A37048B8"}
 */
function processSupplierAddressRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError("Column 'Supplier Code' is blank. This is a required field.", tiRecNum, '')
			if(isBlank(taCols[1]))
				LogError("Column 'Address Code' is blank. This is a required field.", tiRecNum, '')
		}
		else if(isBlank(taCols[1]))
			LogError("Column 'Address Code' is blank. This is a required field.", tiRecNum, '')
		else if(taCols[1].length > 10)
			LogError("The value in the Column 'Address Code' is too long. The maximum is 10 characters.", tiRecNum, '')
		else if(mfsSupplierAddresses.find() || mfsSupplierAddresses.find()){
			var tsSupplierID =  GetSupplierIDFromCode(taCols[0], false)
			
			if(isBlank(tsSupplierID))
				LogError("Column 'Supplier Code' has an invalid value: '" + taCols[0] + "'. Please consult Suppliers setup for valid values.", tiRecNum, '')
			else{
				// record identifier is supplier_id + suppaddr_code
				mfsSupplierAddresses.supplier_id = tsSupplierID
				mfsSupplierAddresses.suppaddr_code = taCols[1]
				
				if(mfsSupplierAddresses.search() > 0){ // found it do an update
					WriteSupplierAddressToDB(taCols, 'U', tiRecNum)
				}
				else{ // do an insert
					mfsSupplierAddresses.newRecord()
					mfsSupplierAddresses.supplier_id = tsSupplierID
					mfsSupplierAddresses.suppaddr_code = taCols[1]
					mfsAddresses.newRecord()
					mfsSupplierAddresses.addr_id = mfsAddresses.addr_id
					WriteSupplierAddressToDB(taCols, 'I', tiRecNum)
				}
			}
		}
	}
}

/**
 * @param {String} s
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"8931E11C-09EB-4BD1-A5A6-318913B43DDC"}
 */
function removeCommaQuotes(s) {
    // if there is a comma in the string then excel puts quotes around the whole string
    if (s.indexOf(',') > -1 && s.charAt(0) == '"' && s.charAt(s.length - 1) == '"') {
        s = s.substr(1, s.length - 2);
    }

    return s;
}

/**
 * @param {String} s
 *
 * @return
 * @properties={typeid:24,uuid:"0E852ECE-81AA-411C-888F-6EB449FB7819"}
 */
function removeCommaQuotesItem(s) {
    // if there is a quote/comma/bracket in the string then excel put quotes around the whole string and also put additional quote next to each quote in the string
    
	// replace 2 doubles quotes with 1 double quote
    s = scopes.avText.replaceAll(s, '""', '"');
	
    // remove leading and trailing double quotes if we have both
    if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"') {
    	s = s.slice(1, s.length - 1)
    }

    return s;
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"485ACB24-3C56-449A-A478-9DF5B3F5586F"}
 */
function WriteSupplierAddressToDB(taCols, tsInsertOrUpdate, tiRecNum){		
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
		
		Validate(mfsSupplierAddresses, 'suppaddr_address_name', tiRecNum, maSupplierAddressColNames[2], taCols[2], false, 64, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone1', tiRecNum, maSupplierAddressColNames[3], taCols[3], false, 32, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone1_ext', tiRecNum, maSupplierAddressColNames[4], taCols[4], false, 10, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone2', tiRecNum, maSupplierAddressColNames[5], taCols[5], false, 32, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone2_ext', tiRecNum, maSupplierAddressColNames[6], taCols[6], false, 10, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone3', tiRecNum, maSupplierAddressColNames[7], taCols[7], false, 32, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_phone3_ext', tiRecNum, maSupplierAddressColNames[8], taCols[8], false, 10, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_fax', tiRecNum, maSupplierAddressColNames[9], taCols[9], false, 32, '', '', '', null, '')
		Validate(mfsSupplierAddresses, 'suppaddr_fax_ext', tiRecNum, maSupplierAddressColNames[10], taCols[10], false, 10, '', '', '', null, '')

		//function SetSupplierContactIDFromFirstAndLast(toFoundset, colName, psFirstAndLast, psSupplierID, tiRecNum){
		SetSupplierContactIDFromFirstAndLast(mfsSupplierAddresses, 'suppcontact_id', taCols[11], globals.UUIDtoStringNew(mfsSupplierAddresses.supplier_id))
		Validate(mfsSupplierAddresses, 'suppaddr_active', tiRecNum, maSupplierAddressColNames[12], taCols[12], true, 64, '', 'Y, N', '1,0', null, '')
		
		if(tsInsertOrUpdate=="I"){
			mfsSupplierAddresses.sequence_nr = GetNextSupplierAddressSeqNum(mfsSupplierAddresses.supplier_id)
			FillAddressFoundset(taCols, tiRecNum, 13, mfsAddresses)
		}
		else
			FillAddressFoundset(taCols, tiRecNum, 13, mfsSupplierAddresses.ap_supplier_address_to_sys_address)
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsSupplierAddresses, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsSupplierAddresses.getSelectedRecord())		
			CheckForSaveErrors(mfsSupplierAddresses, tsInsertOrUpdate)
			
			if(tsInsertOrUpdate=='I'){
				miNumInserts=miNumInserts+1
				databaseManager.saveData(mfsAddresses.getSelectedRecord())
				CheckForSaveErrors(mfsAddresses, tsInsertOrUpdate)
			}
			else{
				miNumUpdates=miNumUpdates+1
				databaseManager.saveData(mfsSupplierAddresses.ap_supplier_address_to_sys_address.getSelectedRecord())
				CheckForSaveErrors(mfsSupplierAddresses.ap_supplier_address_to_sys_address, tsInsertOrUpdate)
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsSupplierAddresses, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * // this func doesnt pop the db directly, is called by both WriteSupplierAddressToDB() and WriteCustAddressToDB(), which write to db
 *    tiStartIdx may vary depending on where this func is being called from
 * @param {Array<String>} taCols
 * @param {Number} tiRecNum
 * @param {Number} tiStartIdx
 * @param {JSFoundSet<db:/avanti/sys_address>} toFoundset
 *
 * @properties={typeid:24,uuid:"A32C4CDF-9FE6-4498-AEC6-CC1A2275D483"}
 */
function FillAddressFoundset(taCols, tiRecNum, tiStartIdx, toFoundset){	
	Validate(toFoundset, 'addr_address1', tiRecNum, maAddressColNames[0], taCols[tiStartIdx], false, 64, '', '', '', null, '')
	Validate(toFoundset, 'addr_address2', tiRecNum, maAddressColNames[1], taCols[tiStartIdx+1], false, 64, '', '', '', null, '')
	Validate(toFoundset, 'addr_address3', tiRecNum, maAddressColNames[2], taCols[tiStartIdx+2], false, 64, '', '', '', null, '')
	Validate(toFoundset, 'addr_city', tiRecNum, maAddressColNames[3], taCols[tiStartIdx+3], false, 64, '', '', '', null, '')
	Validate(toFoundset, 'stateprov_id', tiRecNum, maAddressColNames[4], taCols[tiStartIdx+4], false, 0, '', '', '', GetStateIDFromCode, '')
	Validate(toFoundset, 'addr_postal', tiRecNum, maAddressColNames[5], taCols[tiStartIdx+5], false, 20, '', '', '', null, '')
	Validate(toFoundset, 'country_id', tiRecNum, maAddressColNames[6], taCols[tiStartIdx+6], false, 0, '', '', '', GetCountryIDFromCode, '')
}

/**
 * 
 * @param {String} psNiceCodeColName
 * @param {String} psCodeColName
 * @param {String} psCodeVal
 * @param {String} psIDTable
 * @param {String} psIDColName
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} setupPgm
 *
 * @return
 * @properties={typeid:24,uuid:"B8F3E45F-9F86-496B-8EAB-44823F0B657C"}
 */
function GetIDFromCode(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound, setupPgm){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	psCodeVal = removeCommaQuotes(psCodeVal);
		
	if (_oImportCache && _oImportCache.getIDFromCode && _oImportCache.getIDFromCode[psIDTable + "|" + psCodeVal]){
		tsRetVal = _oImportCache.getIDFromCode[psIDTable + "|" + psCodeVal];
		
	}else
	{
		if(psCodeVal != ''){
			// wasnt returning anything when using params (sometimes) - was giving me a 'cant convert unknown to unknown' err
//			tsRetVal = Query('select ' + psIDColName + ' from ' + psIDTable + ' where org_id = ? and ' + psCodeColName + " = ?", [globals.org_id, psCodeVal])
			tsRetVal = Query('select ' + psIDColName + ' from ' + psIDTable + " where " + psCodeColName + " = '" + FixApo(psCodeVal) + "'" + " and org_id = '" + globals.org_id + "'", null)
			if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
				oErr.message = "Column '" + psNiceCodeColName + "' has an invalid value ('" + psCodeVal + "')."
				if(setupPgm!='')
					oErr.message += ' Please check ' + setupPgm + ' for valid values.'
				throw oErr		
			}
		}
		
		_oImportCache.getIDFromCode[psIDTable + "|" + psCodeVal] = tsRetVal;
		
	}


	
	return tsRetVal
}

/**
 * @param {String} psValue
 *
 * @return
 * @properties={typeid:24,uuid:"3B368811-C23E-4B07-9755-1F94C497CA32"}
 */
function FixApo(psValue){
	try{
		if(psValue == null || psValue == "")
			return ''
		else
			return psValue.toString().replace("'","''")
	}
	catch(ex){ // have been getting errs on replace, not sure why
		return psValue
	}
}

/**
 * 
 * @param {String} psNiceCodeColName
 * @param {String} psCodeColName
 * @param {String} psCodeVal
 * @param {String} psIDTable
 * @param {String} psIDColName
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} setupPgm
 *
 * @return
 * @properties={typeid:24,uuid:"C1CF2504-B6D0-474E-A58F-95C6BBFC0BD9"}
 */
function GetIDFromCode_NoOrgID(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound, setupPgm){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''

	if(psCodeVal != ''){
		tsRetVal = Query('select ' + psIDColName + ' from ' + psIDTable + ' where ' + psCodeColName + " = ?", [psCodeVal])
		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + psNiceCodeColName + "' has an invalid value ('" + psCodeVal + "')."
			if(setupPgm!='')
				oErr.message += ' Please check ' + setupPgm + ' for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 *
 * @param {String} psTypeDescr
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"229C62B2-7E20-4F64-AE9D-63F14823A633"}
 */
function GetAppAssignTypeIDFromDescr(psTypeDescr, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode_NoOrgID(niceColName, 'assignment_desc', psTypeDescr, 'app_assignment_type', 'app_assignment_type_id', pbThrowErrorIfNotFound, 'Assignment Type setup') 
}

/**
 * @param {String} psStatus
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"DD7A825B-B2AC-49F0-873D-E86E1B8AF6BF"}
 */
function ValidateOrderStatus(psStatus, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode_NoOrgID(niceColName, 'ordstat_id', psStatus, 'app_order_status', 'ordstat_id', pbThrowErrorIfNotFound, 'Order Status setup') 
}

/**
 * 
 * @param {String} psIDTable
 * @param {String} psCol1Name
 * @param {String} psCol1Val
 * @param {String} psCol2Name
 * @param {String} psCol2Val
 * @param {String} psIDColName
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} setupPgm
 * @param {String} psNiceCodeColName
 * @param {Boolean} pbBypassOrgID
 *
 * @return
 * @properties={typeid:24,uuid:"36759C9F-6B64-4B63-9177-65382F99F689"}
 */
function GetIDFrom2Codes(psNiceCodeColName, psIDTable, psCol1Name, psCol1Val, psCol2Name, psCol2Val, psIDColName, pbThrowErrorIfNotFound, setupPgm, pbBypassOrgID){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psCol1Val != '' && psCol2Val != ''){
		// wasnt returning anything when using params - was giving me a 'cant convert unknown to unknown' err
//		var tsSQL = "select " + psIDColName + " from " + psIDTable + " where org_id = ? and " + psCol1Name + " = ? and " + psCol2Name + " = ?"
//		tsRetVal = Query(tsSQL, [globals.org_id, psCol1Val, psCol2Val])

		var tsSQL = "select " + psIDColName + " from " + psIDTable + " where " + psCol1Name + " = '" + FixApo(psCol1Val) + "' and " + 
			psCol2Name + " = '" + FixApo(psCol2Val) + "'"
		if(pbBypassOrgID == false)
			tsSQL += " and org_id = '" + globals.org_id + "'"
			
		tsRetVal = Query(tsSQL, null)

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + psNiceCodeColName + "' has an invalid value ('" + psCol1Val + "')."
			if(setupPgm!='')
				oErr.message += ' Please check ' + setupPgm + ' for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * @param {String} psIDTable
 * @param {String} psCol1Name
 * @param {String} psCol1Val
 * @param {String} psCol2Name
 * @param {String} psCol2Val
 * @param {String} psCol3Name
 * @param {String} psCol3Val
 * @param {String} psIDColName
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} setupPgm
 * @param {String} psNiceCodeColName
 * @param {Boolean} pbBypassOrgID
 *
 * @return
 * @properties={typeid:24,uuid:"E70CEE0C-32FE-478C-80D9-5AF4E25114B9"}
 */
function GetIDFrom3Codes(psNiceCodeColName, psIDTable, psCol1Name, psCol1Val, psCol2Name, psCol2Val, psCol3Name, psCol3Val, psIDColName, pbThrowErrorIfNotFound, setupPgm, pbBypassOrgID){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psCol1Val != '' && psCol2Val != '' && psCol3Val != ''){
		var tsSQL = "select " + psIDColName + " from " + psIDTable + " where " + psCol1Name + " = '" + FixApo(psCol1Val) + "' and " + 
			psCol2Name + " = '" + FixApo(psCol2Val) + "' and " + psCol3Name + " = '" + FixApo(psCol3Val) + "'"
		if(pbBypassOrgID == false)
			tsSQL += " and org_id = '" + globals.org_id + "'"
			
		tsRetVal = Query(tsSQL, null)

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + psNiceCodeColName + "' has an invalid value ('" + psCol1Val + "')."
			if(setupPgm!='')
				oErr.message += ' Please check ' + setupPgm + ' for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * 
 * @param {String} psAddrCode
 * @param {String} psSuppCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"FE2F002D-0655-453F-B827-681BDD005499"}
 */
function GetSuppAddrIDFromCode(psAddrCode, psSuppCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'ap_supplier_address', 'suppaddr_code', psAddrCode, 'supplier_id', psSuppCode, 'suppaddr_id', pbThrowErrorIfNotFound, 'Supplier Address setup', false) 
}

/**
 * @param {String} psStream
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"00A53796-**************-D4A0DE6CA1AC"}
 */
function ValidateOrderStream(psStream, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sys_document_number', 'docnum_stream', psStream, 'doctype_code', 'ORD', 'docnum_stream', pbThrowErrorIfNotFound, 'Sales Order Stream setup', false) 
}

/**
 * @param {String} psCategory
 * @param {String} psField
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"0D000BB2-F37E-4DFC-95D0-6B6680AEC00B"}
 */
function GetUdfTypeIDFromFieldNameAndCategory(psField, psCategory, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sys_udf_type', 'udf_field', psField, 'udf_code', psCategory, 'sys_udf_type_id', pbThrowErrorIfNotFound, 'UDF setup', false) 
}

/**
 *
 * @param {String} psCategoryCode
 * @param {String} psUdfQuestion
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"FF14080E-6E2B-421D-A329-BEC0E23021A4"}
 */
function GetUdfTypeIDFromCategoryAndDescr(psCategoryCode, psUdfQuestion, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sys_udf_type', 'udf_code', psCategoryCode, 'udf_field', psUdfQuestion, 'sys_udf_type_id', pbThrowErrorIfNotFound, 'UDF setup', false) 
}

/**
 *
 * @param {String} psUdfAnswer
 * @param {String} psUdfTypeID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"FA038B43-41E6-4AAD-AB69-5FA71577F43E"}
 */
function GetUdfTableValueIDFromTypeIDAndAnswer(psUdfAnswer, psUdfTypeID, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sys_udf_type_table_values', 'udf_table_value', psUdfAnswer, 'sys_udf_type_id', psUdfTypeID, 'sys_udf_type_table_value_id', pbThrowErrorIfNotFound, 'UDF setup', false) 
}

/**
 * 
 * @param {JSFoundSet} toFoundset
 * @param {String} colName
 * @param {String} psFirstAndLast
 * @param {String} psSupplierID
 *
 * @properties={typeid:24,uuid:"CDB6A691-19ED-4E32-A2A9-4CCBC62E9113"}
 */
function SetSupplierContactIDFromFirstAndLast(toFoundset, colName, psFirstAndLast, psSupplierID){
	var tsRetVal = null
	
	if(!isBlank(psFirstAndLast)){
		tsRetVal = Query("select suppcontact_id from sys_contact where org_id = '" + globals.org_id + "' and contact_first_and_last = ? and suppcontact_id in " + 
			"(select suppcontact_id from ap_supplier_contact where supplier_id = '" + psSupplierID + "')", [psFirstAndLast])
		if((tsRetVal=='' || tsRetVal==null)){
			tsRetVal = CreateNewSupplierContact(psSupplierID, psFirstAndLast)
		}
	}
	
	if(psFirstAndLast != 'NO-VALUE'){
		toFoundset.setDataProviderValue(colName, tsRetVal)
	}
}

/**
 * @param {String} psSuppID
 * @param {String} psFirstAndLast
 *
 * @return
 * @properties={typeid:24,uuid:"388AE219-19BE-46C0-A6C8-E409287BDF15"}
 */
function CreateNewSupplierContact(psSuppID, psFirstAndLast){
	var taNames = psFirstAndLast.split(" ")
	var tsFstName = taNames[0]
	if(taNames.length > 1)
		var ts2ndName = taNames[1]
	
	/***@type {JSFoundSet<db:/avanti/sys_contact>} */
	var tfsContact
	tfsContact = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact')
	tfsContact.newRecord()
	tfsContact.contact_first_and_last = psFirstAndLast
	tfsContact.contact_first_name = tsFstName
	tfsContact.contact_last_name = ts2ndName
	tfsContact.contact_full_name = psFirstAndLast

	/***@type {JSFoundSet<db:/avanti/ap_supplier_contact>} */
	var tfsSupplierContact
	tfsSupplierContact = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier_contact')
	tfsSupplierContact.newRecord()
	tfsSupplierContact.supplier_id = psSuppID
	tfsSupplierContact.contact_id = tfsContact.contact_id
	tfsContact.suppcontact_id = tfsSupplierContact.suppcontact_id
	
	databaseManager.saveData(tfsContact.getSelectedRecord())
	CheckForSaveErrors(tfsContact, 'I')

	databaseManager.saveData(tfsSupplierContact.getSelectedRecord())	
	CheckForSaveErrors(tfsSupplierContact, 'I')
	
	return tfsSupplierContact.suppcontact_id
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"333026C6-B60E-4B95-A794-C66674180F27"}
 */
function GetStateIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'stateprov_code', psCode, 'sys_state_province', 'stateprov_id', pbThrowErrorIfNotFound, 'State/Provinces setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"49DFCC7E-0432-4FA5-8592-6F4C06F34B5A"}
 */
function GetCountryIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'country_code', psCode, 'sys_country', 'country_id', pbThrowErrorIfNotFound, 'Country setup')
}

/**
 * 
 * @param {String} psDescr
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"A2500EEB-114A-4285-A417-ABDC7900D70C"}
 */
function GetJobTitleIDFromDescr(psDescr, pbThrowErrorIfNotFound, niceColName){
	// GetIDFromCode(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound)
	return GetIDFromCode(niceColName, 'jobtitle_desc', psDescr, 'sys_contact_job_title', 'jobtitle_id', pbThrowErrorIfNotFound, 'Job Title setup')
}

/**
 * 
 * @param {String} psDescr
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"86E76405-8902-419F-926E-878EBF598BA3"}
 */
function GetContactTypeIDFromDescr(psDescr, pbThrowErrorIfNotFound, niceColName){
	// GetIDFromCode(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound)
	return GetIDFromCode(niceColName, 'contacttype_desc', psDescr, 'sys_contact_type', 'contacttype_id', pbThrowErrorIfNotFound, 'Contact Type setup')
}

/**
 * 
 * @param {String} psDescr
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"9307D200-6CD1-444D-A9A8-92D79D9A8C24"}
 */
function GetDeptIDFromDescr(psDescr, pbThrowErrorIfNotFound, niceColName){
	// GetIDFromCode(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound)
	return GetIDFromCode(niceColName, 'contactdept_desc', psDescr, 'sys_contact_department', 'contactdept_id', pbThrowErrorIfNotFound, 'Contact Department setup')
}

/**
 * 
 * @param {String} psDescr
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"537E5A10-3D36-40F7-AD8C-A15332BC2EFD"}
 */
function GetDivisionIDFromDescr(psDescr, pbThrowErrorIfNotFound, niceColName){
	// GetIDFromCode(psNiceCodeColName, psCodeColName, psCodeVal, psIDTable, psIDColName, pbThrowErrorIfNotFound)
	return GetIDFromCode(niceColName, 'contactdiv_desc', psDescr, 'sys_contact_division', 'contactdiv_id', pbThrowErrorIfNotFound, 'Contact Division setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"F2117E88-30FA-43B7-A6EA-0E83F1F84CDB"}
 */
function GetDivisionIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'div_code', psCode, 'sys_division', 'div_id', pbThrowErrorIfNotFound, 'Division setup')
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"D7A5F9AC-CD97-4B69-87E5-CCF1BC2B925D"}
 */
function GetBuyerIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetEmpIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"BA3CA4C7-0A40-4391-B77C-DCBD0B2C758A"}
 */
function GetSalesrepIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'salesper_code', psCode, 'sa_sales_person', 'salesper_id', pbThrowErrorIfNotFound, 'Salesperson setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"6BDCDB50-B1C8-4181-90EE-15527AE9CA29"}
 */
function GetCSRIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetEmpIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"9FEA7047-EDEA-4E58-9042-7D339442AB66"}
 */
function GetCreatedByIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetEmpIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"45FF90C7-26E8-4BFF-A6D8-703EA211EBA5"}
 */
function GetEmpIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'empl_code', psCode, 'sys_employee', 'empl_id', pbThrowErrorIfNotFound, 'Employee setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"F19D0CEF-BCBB-4F12-96A5-2341BE032B53"}
 */
function GetNoteTypeIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'notetype_desc', psCode, 'sys_note_type', 'notetype_id', pbThrowErrorIfNotFound, 'Note Type setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"8EF95E1B-7622-43D9-9726-C0095471B622"}
 */
function GetEstIDFromNumber(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sa_order', 'ordh_document_num', psCode, 'ordh_document_type', 'EST', 'ordh_id', pbThrowErrorIfNotFound, 'Estimate setup', false) 
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"85A8F36D-2F2A-48B9-A176-8F7243A9BA6C"}
 */
function GetLeadStageIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'lead_stage_desc', psCode, 'crm_lead_stage', 'crm_lead_stage_id', pbThrowErrorIfNotFound, 'Lead Stage setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('crm_lead_stage', 'lead_stage_desc', psCode, 'crm_lead_stage_id')

	return tsID
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"FDCE704B-E2A8-4617-84E0-2DF2FBD24D08"}
 */
function GetLeadRatingIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'lead_rating_desc', psCode, 'crm_lead_rating', 'crm_lead_rating_id', pbThrowErrorIfNotFound, 'Lead Rating setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('crm_lead_rating', 'lead_rating_desc', psCode, 'crm_lead_rating_id')

	return tsID
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"004DDF12-3B8F-49E9-9D76-0F19E893FC1D"}
 */
function GetLeadSourceIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'lead_source_desc', psCode, 'crm_lead_source', 'crm_lead_source_id', pbThrowErrorIfNotFound, 'Lead Source setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('crm_lead_source', 'lead_source_desc', psCode, 'crm_lead_source_id')

	return tsID
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"9B53568D-93A6-4E46-80A5-7C9ABE550840"}
 */
function GetSalesOrderIDFromNumber(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom2Codes(niceColName, 'sa_order', 'ordh_document_num', psCode, 'ordh_document_type', 'ORD', 'ordh_id', pbThrowErrorIfNotFound, 'Sales Order setup', false) 
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"39D0F951-A851-4D61-9FD4-C4E908296275"}
 */
function GetJobIDFromNumber(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'job_number', psCode, 'prod_job', 'job_id', pbThrowErrorIfNotFound, 'Job setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"0F65D683-46B8-441E-9435-A990B1E92B7F"}
 */
function GetPackingSlipIDFromNumber(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'pack_doc_number', psCode, 'sa_pack', 'pack_id', pbThrowErrorIfNotFound, 'Packing Slip setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"1A11B679-AF05-434E-B168-AFA88B527091"}
 */
function GetLeadIDFromNumber(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'lead_number', psCode, 'crm_lead', 'crm_lead_id', pbThrowErrorIfNotFound, 'CRM Lead setup')
}

/**
 * @param {String} psCurCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"41CE3C46-79DF-459F-A30A-9BFD6C53DB77"}
 * @AllowToRunInFind
 */
function GetCurrencyIDFromCode(psCurCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'curr_iso_code', psCurCode, 'sys_currency', 'curr_id', pbThrowErrorIfNotFound, 'Currency setup')
}

/**
 * 
 * @param {String} psName
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"92967446-F495-4D0E-B0CE-91D026834BE9"}
 */
function GetPaperGradeIDFromName(psName, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'papergrade_name', psName, 'in_paper_grade', 'papergrade_id', pbThrowErrorIfNotFound, 'Substrate Standards setup')
}

/**
 * 
 * @param {String} psName
 * @param {Number} pnWeight
 * @param {String} tsGradeID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"DF75F50B-4C15-48C2-8B4A-910AE63AB3C9"}
 */
function GetPaperBrandIDFromNameAndWeightAndGrade(psName, pnWeight, tsGradeID, pbThrowErrorIfNotFound, niceColName){
	return GetIDFrom3Codes(niceColName, 'in_paper_brand', 'paperbrand_name', psName, 'paperbrand_basis_weight', pnWeight.toString(), 'papergrade_id', tsGradeID, 'paperbrand_id', pbThrowErrorIfNotFound, 'Substrate Standards setup', false) 
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"CB17713D-2F5E-4C6A-8625-7DDC08D60FAE"}
 */
function GetShipMethodIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'shipmethod_code', psCode, 'sys_shipping_method', 'shipmethod_id', pbThrowErrorIfNotFound, 'Shipping Method setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"212B61CF-9771-4FCF-8A85-A56337B0E9BB"}
 */
function GetPlantIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'plant_code', psCode, 'sys_plant', 'plant_id', pbThrowErrorIfNotFound, 'Plant setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"EEEDD035-1CE7-4450-974A-24D1851FD03C"}
 */
function GetShiftIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'shift_code', psCode, 'sch_shift', 'shift_id', pbThrowErrorIfNotFound, 'Shift setup')
}

/**
 * 
 * @param {String} psJDFType
 * @param {String} psCCType
 * @param {Boolean} pbThrowErrorIfNotFound
 *
 * @return
 * @properties={typeid:24,uuid:"EA8A9CB4-1C45-4F4E-A65A-EEFD6BC8339E"}
 */
function IsJDFTypeValid(psJDFType, psCCType, pbThrowErrorIfNotFound){
//  GetIDFrom2Codes(psNiceCodeColName,        psIDTable,            psCol1Name, psCol1Val, psCol2Name, psCol2Val, psIDColName, pbThrowErrorIfNotFound, setupPgm){
	return GetIDFrom2Codes('JDF Type', 'app_cost_center_jdf_type', 'cc_jdf_type_desc', psJDFType, 'cc_type', psCCType, 'cc_jdf_type_desc', pbThrowErrorIfNotFound, 'JDF Type setup', true) 
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"5AB27F00-D7DF-4372-82F6-B3BBC0380F5B"}
 */
function GetDeptIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'dept_code', psCode, 'sys_department', 'dept_id', pbThrowErrorIfNotFound, 'Department setup')
}

/**
 * 
 * @param {String} psDeptID
 * @param {String} psCatCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"57E70E82-1A3F-4E2A-BE17-6CE4181B44C0"}
 */
function GetCatIDFromCode(psDeptID, psCatCode, pbThrowErrorIfNotFound, niceColName){
//  GetIDFrom2Codes(psNiceCodeColName,        psIDTable,            psCol1Name, psCol1Val, psCol2Name, psCol2Val, psIDColName, pbThrowErrorIfNotFound, setupPgm){
	return GetIDFrom2Codes(niceColName, 'sys_operation_category', 'opcat_code', psCatCode, 'dept_id', psDeptID, 'opcat_id', pbThrowErrorIfNotFound, 'Operation Category setup', false) 
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"E103F777-798E-4B78-9B8D-CE2BB0D3F3D0"}
 */
function GetTermsIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'terms_code', psCode, 'sys_payment_terms', 'terms_id', pbThrowErrorIfNotFound, 'Payment Terms setup')
}

/**
 * @AllowToRunInFind
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-13
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"1EF55FE3-7533-41C6-8D1D-0017FF6367AD"}
 */
function GetItemIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'item_code', psCode, 'in_item', 'item_id', pbThrowErrorIfNotFound, 'Item setup')
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"5E45A568-B256-43F5-A4FE-9C6020DA345E"}
 */
function GetSupplierIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'supplier_code', psCode, 'ap_supplier', 'supplier_id', pbThrowErrorIfNotFound, 'Supplier setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"5DA59208-8066-44A5-8EF6-2FAF55D15BBA"}
 */
function GetPrimarySupplierIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'supplier_code', psCode, 'ap_supplier', 'supplier_id', pbThrowErrorIfNotFound, 'Supplier setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"5C98C13E-5650-486C-AAA1-66FC763B02C2"}
 */
function GetCustCatIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'custcat_code', psCode, 'sa_customer_category', 'custcat_id', pbThrowErrorIfNotFound, 'Customer Category setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"CDEEC826-B6DA-4216-BFC6-88460FFE68EC"}
 */
function GetCustClassIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'custclass_code', psCode, 'sa_customer_class', 'custclass_id', pbThrowErrorIfNotFound, 'Customer Class setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"ADFD8254-48C1-47EE-8F24-B35E405DEFD7"}
 */
function GetTerritoryIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'terr_code', psCode, 'sa_territory', 'terr_id', pbThrowErrorIfNotFound, 'Territory setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"51DFB804-ECC5-42ED-826D-8F0D3E44CEC9"}
 */
function GetEmplClassIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'emplclass_code', psCode, 'sys_employee_class', 'emplclass_id', pbThrowErrorIfNotFound, 'Employee Class setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"584DD88D-1C04-4A69-9B49-BE0CA596F826"}
 */
function GetCustGroupIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'custgrp_code', psCode, 'sa_customer_sales_group', 'custgrp_id', pbThrowErrorIfNotFound, 'Customer Group setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"7B944CEF-D794-4BE1-BF22-C8F00F15EF10"}
 */
function GetCustIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'cust_code', psCode, 'sa_customer', 'cust_id', pbThrowErrorIfNotFound, 'Customer setup')
}

/**
 *
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"8677EB0E-4ECB-4851-88DE-68B400A918B7"}
 */
function GetContactIDFromFirstAndLast(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'contact_first_and_last', psCode, 'sys_contact', 'contact_id', pbThrowErrorIfNotFound, 'Contact setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"413DBC7A-45BA-4A0B-99F0-7A78B1055587"}
 */
function GetIntCustIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetCustIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"0B18D129-A7B6-4993-968B-7C563D9FED54"}
 */
function GetParentCustIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetCustIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"34572CAF-93D9-40B8-9430-47E9685A251C"}
 */
function GetCustSicIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'custsic_code', psCode, 'sa_customer_sic', 'custsic_id', pbThrowErrorIfNotFound, 'Customer SIC setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"66474CBB-EAF3-4B03-A07A-27FA6EE960C7"}
 */
function GetPayMethodIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'paymethod_code', psCode, 'sa_payment_method', 'paymethod_id', pbThrowErrorIfNotFound, 'Payment Method setup')
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"E7773C6A-7217-43C6-B867-1C80E0A280D3"}
 */
function GetOrderTypeIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'ordtype_desc', psCode, 'sa_order_type', 'ordtype_id', pbThrowErrorIfNotFound, 'Order Type setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"0850F2CB-542D-42E4-9671-12A1B994B4E4"}
 */
function GetCustIndustryIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'custindustry_code', psCode, 'sa_customer_industry', 'custindustry_id', pbThrowErrorIfNotFound, 'Customer Industry setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"8079DC2D-AE52-43A3-BC08-740F5CF2F90A"}
 */
function GetTaxGroupIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'taxgroup_code', psCode, 'sys_sales_tax_group', 'taxgroup_id', pbThrowErrorIfNotFound, 'Tax Group setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"8BFBE6B0-2363-41FC-8051-8E892E506003"}
 */
function GetIntClassIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'itemclass_code', psCode, 'in_item_class', 'itemclass_id', pbThrowErrorIfNotFound, 'Item Class setup')
}

/**
 * @private 
 * 
 * @param {String} sLanguage
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * 
 * @properties={typeid:24,uuid:"90F71841-6142-4B69-BEDA-76832F1FE0A2"}
 */
function GetLanguageID(sLanguage, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'sys_org_language', sLanguage, 'sys_org_language', 'sys_org_language_uuid', pbThrowErrorIfNotFound, 'Organization Language Setup');
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"60906CE6-D1FD-4764-958A-4AAFE49ECBF9"}
 */
function GetUOMIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'uom_code', psCode, 'sys_unit_of_measure', 'uom_id', pbThrowErrorIfNotFound, 'Unit of Measure setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"D06725B9-8A41-4E62-8422-11698F36260B"}
 */
function GetPrimarySupplierCostUOMIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'uom_code', psCode, 'sys_unit_of_measure', 'uom_id', pbThrowErrorIfNotFound, 'Unit of Measure setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"DC913250-A612-46B1-81E7-1304AD82F27F"}
 */
function GetPrimarySupplierUOMIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'uom_code', psCode, 'sys_unit_of_measure', 'uom_id', pbThrowErrorIfNotFound, 'Unit of Measure setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"DD294777-B6DB-45E3-83ED-BE34642A6F8C"}
 */
function GetSellingUOMIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'uom_code', psCode, 'sys_unit_of_measure', 'uom_id', pbThrowErrorIfNotFound, 'Unit of Measure setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"A814BE6A-EC52-48C2-9091-E40765A67011"}
 */
function GetPriceUOMIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'uom_code', psCode, 'sys_unit_of_measure', 'uom_id', pbThrowErrorIfNotFound, 'Unit of Measure setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"3745B0BA-E78E-48A1-B686-3D1C1A2EE15B"}
 */
function GetWorkTypeIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'worktype_code', psCode, 'sa_task_worktype', 'worktype_id', pbThrowErrorIfNotFound, 'Work Template setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"B3BB8CE1-CAB9-4D98-8DDE-2DB5217E4C3A"}
 */
function GetIntGroupIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'ingroup_code', psCode, 'in_group', 'ingroup_id', pbThrowErrorIfNotFound, 'Item Group setup')
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} psNiceColName
 *
 * @return
 * @properties={typeid:24,uuid:"A4195F87-FA78-4B29-B0EA-2C75FFC96228"}
 */
function GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, psNiceColName){
	return GetIDFromCode(psNiceColName, 'glacct_number', psNumber, 'gl_account', 'glacct_id', pbThrowErrorIfNotFound, 'GL Account setup')
}

/**
 * @param {String} sSegNumber
 * @param {Boolean} bThrowErrorIfNotFound
 * @param {String} sNiceColName
 *
 * @return
 * @properties={typeid:24,uuid:"7530BED6-07A2-4C6A-B273-D09FEE6E97B4"}
 */
function GetGLAccountSegmentIDFromNumber(sSegNumber, bThrowErrorIfNotFound, sNiceColName){
    return GetIDFromCode(sNiceColName, 'glacctseg_number', sSegNumber, 'gl_account_segment', 'glacctseg_id', 
        bThrowErrorIfNotFound, scopes.avText.getDlgMsg('GLAccountSegmentSetup'));
}

/**
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} psNiceColName
 *
 * @return
 * @properties={typeid:24,uuid:"654F764A-C156-403E-97E5-33333866ABD2"}
 */
function GetTaxItemIDFromCode(psNumber, pbThrowErrorIfNotFound, psNiceColName){
	return GetIDFromCode(psNiceColName, 'taxitem_code', psNumber, 'sys_sales_tax_item', 'taxitem_id', pbThrowErrorIfNotFound, 'Tax Items setup')
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"969C43D0-D966-4600-AE23-A52A1DC3EEC8"}
 */
function GetInkTypeIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'inktype_code', psCode, 'in_ink_type', 'inktype_id', pbThrowErrorIfNotFound, 'Ink Type Setup')
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"6E638C46-72FB-4973-97FE-AA9121BA7477"}
 */
function GetGLAccountIDFromNumber_IntAdj(psNumber, pbThrowErrorIfNotFound, niceColName){
	return GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"5326F43A-D76E-4F32-A7E8-976C5B661B28"}
 */
function GetGLAccountIDFromNumber_COS(psNumber, pbThrowErrorIfNotFound, niceColName){
	return GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"C1224AD6-BE2F-4AA3-BB8B-DCAABD30E461"}
 */
function GetGLAccountIDFromNumber_Invent(psNumber, pbThrowErrorIfNotFound, niceColName){
	return GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"76B6DE45-EBA0-445F-86BD-BBE5C924416F"}
 */
function GetGLAccountIDFromNumber_SR(psNumber, pbThrowErrorIfNotFound, niceColName){
	return GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psNumber
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"CB51FB01-8A17-4A05-B5FD-56D7C83F23A9"}
 */
function GetGLAccountIDFromNumber_Sales(psNumber, pbThrowErrorIfNotFound, niceColName){
	return GetGLAccountIDFromNumber(psNumber, pbThrowErrorIfNotFound, niceColName)
}

/**
 * 
 * @param {String} psAddrCode
 * @param {String} psCustID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} [niceColName]
 *
 * @return
 * @properties={typeid:24,uuid:"46A66826-F0C4-4E56-BBA6-EFDE0E3DC758"}
 */
function GetCustAddrIDFromCode(psAddrCode, psCustID, pbThrowErrorIfNotFound, niceColName){
//         GetIDFrom2Codes(psNiceCodeColName,        psIDTable,            psCol1Name, psCol1Val, psCol2Name, psCol2Val, psIDColName, pbThrowErrorIfNotFound, setupPgm){
	return GetIDFrom2Codes(niceColName, 'sa_customer_address', 'custaddr_code', psAddrCode, 'cust_id', psCustID, 'custaddr_id', pbThrowErrorIfNotFound, 'Customer Address setup', false) 
}

/**
 * 
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"F1024DE2-89B7-428A-A114-289D5B0AC7F2"}
 */
function GetWarehouseIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'whse_code', psCode, 'in_warehouse', 'whse_id', pbThrowErrorIfNotFound, 'Warehouse setup')
}

/**
 * 
 * @param {String} sBin
 * @param {String} sWareID
 * @param {Boolean} bThrowErrorIfNotFound
 * @param {String} sNiceColName
 *
 * @return
 * @properties={typeid:24,uuid:"FB62C87A-4481-46E5-9122-94B0C628BA28"}
 */
function GetWarehouseLocationIDFromBinLocAndWareID(sBin, sWareID, bThrowErrorIfNotFound, sNiceColName){
	return GetIDFrom2Codes(sNiceColName, 'in_warehouse_location', 'whseloc_bin_location', sBin, 'whse_id', sWareID, 'whseloc_id', bThrowErrorIfNotFound, 'Warehouse location setup', false)
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"EE2C4BBB-A47A-4EBE-B9D6-715BBE6AB776"}
 */
function GetIntItemIdFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'item_code', psCode, 'in_item', 'item_id', pbThrowErrorIfNotFound, 'Inventory Item setup')
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"3FF5DAB4-EEC7-44CE-B76F-A3BF5997BCD1"}
 */
function GetSysWorkTypeIdFromCode(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'sysworktype_code', psCode, 'sys_task_worktype', 'sysworktype_id', pbThrowErrorIfNotFound, 'Work Type setup')
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"243CB58E-826D-4842-B627-6312691A88CF"}
 */
function GetSAWorkTypeIdFromDescr(psCode, pbThrowErrorIfNotFound, niceColName){
	return GetIDFromCode(niceColName, 'worktype_desc', psCode, 'sa_task_worktype', 'worktype_id', pbThrowErrorIfNotFound, 'Work Type setup')
}

/**
 * 
 * @param {JSFoundSet} toFoundset
 * @param {String} colName
 * @param {String} psFirstAndLast
 * @param {String} psCustID
 * @param {String} psCustAddrID
 * @param {String} psContactAddrName
 * @param {String} psAddrID
 * @param {String} psBusinessPhone
 *
 * @properties={typeid:24,uuid:"C3BFA41E-3AFB-48D8-A932-746949F788EF"}
 */
function SetCustContactIDFromFirstAndLast(toFoundset, colName, psFirstAndLast, psCustID, psCustAddrID, psContactAddrName, psAddrID, psBusinessPhone){
	var tsRetVal = null
	
	if(!isBlank(psFirstAndLast)){
		tsRetVal = Query("select custcontact_id from sys_contact where org_id = '" + globals.org_id + "' and contact_first_and_last = ? and custcontact_id in " + 
		"(select custcontact_id from sa_customer_contact where cust_id = '" + psCustID + "')", [psFirstAndLast])
		if((tsRetVal=='' || tsRetVal==null)){
			tsRetVal = CreateNewCustContact(psCustID, psFirstAndLast, psCustAddrID, psContactAddrName, psAddrID, psBusinessPhone)
		}
	}
	
	if(psFirstAndLast != 'NO-VALUE'){
		toFoundset.setDataProviderValue(colName, tsRetVal)
	}
}

/**
 *
 * @param {String} psCustID
 * @param {String} psFirstAndLast
 * @param {String} psCustAddressID
 * @param {String} psAddrName
 * @param {String} psAddrID
 * @param {String} psBusinessPhone
 *
 * @return
 * @properties={typeid:24,uuid:"2B1EEBDF-E95B-4B91-868C-0326789C549F"}
 */
function CreateNewCustContact(psCustID, psFirstAndLast, psCustAddressID,psAddrName, psAddrID, psBusinessPhone){
	var taNames = psFirstAndLast.split(" ");
    var tsFstName = taNames[0];
    if (taNames.length > 1) {
        var ts2ndName = taNames[1];
    }

	/***@type {JSFoundSet<db:/avanti/sys_contact>} */
	var tfsContact;
	tfsContact = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact');
	tfsContact.newRecord();
	tfsContact.contact_first_and_last = psFirstAndLast;
	tfsContact.contact_first_name = tsFstName;
	tfsContact.contact_last_name = ts2ndName;
	tfsContact.contact_full_name = psFirstAndLast;
	tfsContact.contact_address_name = psAddrName;
	tfsContact.addr_id = psAddrID;
	tfsContact.contact_business_phone = psBusinessPhone;
	tfsContact.contact_file_as = Trim(tfsContact.contact_last_name + ', ' + tfsContact.contact_first_name).replace('  ', ' ');

	/***@type {JSFoundSet<db:/avanti/sa_customer_contact>} */
	var tfsCustContact;
	tfsCustContact = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_contact');
	tfsCustContact.newRecord();
	tfsCustContact.cust_id  = psCustID;
	tfsCustContact.contact_id = tfsContact.contact_id;
	tfsCustContact.custaddr_id = psCustAddressID;
	tfsContact.custcontact_id = tfsCustContact.custcontact_id;

	writeWithSQL(tfsContact, [], [], 'I');
	writeWithSQL(tfsCustContact, [], [],  'I');
	
	return tfsCustContact.custcontact_id;
}

/**
 * 
 * @param {String} psFirstAndLast
 * @param {String} psCustID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {UUID|String} [contactTypeID]
 *
 * @return
 * @properties={typeid:24,uuid:"86D4DA52-BF04-439F-9CF7-B820554F30B7"}
 */
function GetCustContactIDFromFirstAndLast(psFirstAndLast, psCustID, pbThrowErrorIfNotFound, niceColName, contactTypeID){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psCustID != '' && psFirstAndLast != ''){
		var sql = "select custcontact_id from sys_contact where org_id = '" + globals.org_id + "' and contact_first_and_last = '" + FixApo(psFirstAndLast) + "'" +
			" and custcontact_id in (select custcontact_id from sa_customer_contact where cust_id = '" + psCustID + "')";
		
		if(contactTypeID){
			if(contactTypeID == 'empty'){
				sql += " and contacttype_id IS NULL";
			}
			else{
				sql += " and contacttype_id = '" + contactTypeID + "'";
			}
		}
		
		tsRetVal = Query(sql);

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + niceColName + "' has an invalid value ('" + psFirstAndLast + "')."
			oErr.message += ' Please check Customer Contact setup for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * @param {String} psFirstAndLast
 * @param {String} psCustID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {UUID|String} [contactTypeID]
 *
 * @return
 * @properties={typeid:24,uuid:"593BA80A-2EFB-4704-B087-AFD894FC8EAF"}
 */
function GetContactIDAndCustContactIDFromFirstAndLast(psFirstAndLast, psCustID, pbThrowErrorIfNotFound, niceColName, contactTypeID){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psCustID != '' && psFirstAndLast != ''){
		var sql = "select contact_id, custcontact_id from sys_contact where org_id = '" + globals.org_id + "' and contact_first_and_last = '" + FixApo(psFirstAndLast) + "'" +
			" and custcontact_id in (select custcontact_id from sa_customer_contact where cust_id = '" + psCustID + "')";
		
		if(contactTypeID){
			if(contactTypeID == 'empty'){
				sql += " and contacttype_id IS NULL";
			}
			else{
				sql += " and contacttype_id = '" + contactTypeID + "'";
			}
		}
		
		tsRetVal = Query(sql, null, 2);

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + niceColName + "' has an invalid value ('" + psFirstAndLast + "')."
			oErr.message += ' Please check Customer Contact setup for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * @param {String} psFirstAndLast
 * @param {String} psCustID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 *
 * @return
 * @properties={typeid:24,uuid:"A58662EB-E62F-45A4-9901-3EBF7ABA43BB"}
 */
function GetContactIDFromCustAndFirstAndLast(psFirstAndLast, psCustID, pbThrowErrorIfNotFound, niceColName){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psCustID != '' && psFirstAndLast != ''){
		tsRetVal = Query('select contact_id from sys_contact where org_id = ? and contact_first_and_last = ? and custcontact_id in ' + 
			'(select custcontact_id from sa_customer_contact where cust_id = ?)', [globals.org_id, psFirstAndLast, psCustID])

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + niceColName + "' has an invalid value ('" + psFirstAndLast + "')."
			oErr.message += ' Please check Customer Contact setup for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * 
 * @param {String} psFirstAndLast
 * @param {String} psSuppID
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {UUID|String} [contactTypeID]
 *
 * @return
 * @properties={typeid:24,uuid:"BF04C608-002F-4A82-9B31-6E896838E579"}
 */
function GetSuppContactIDFromFirstAndLast(psFirstAndLast, psSuppID, pbThrowErrorIfNotFound, niceColName, contactTypeID){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();	
	var tsRetVal = ''
		
	if(psSuppID != '' && psFirstAndLast != ''){
		var sql = 'select suppcontact_id from sys_contact where org_id = ? and contact_first_and_last = ? and suppcontact_id in ' + 
			'(select suppcontact_id from ap_supplier_contact where supplier_id = ?)';
	
		if(contactTypeID){
			if(contactTypeID == 'empty'){
				sql += " and contacttype_id IS NULL";
			}
			else{
				sql += " and contacttype_id = '" + contactTypeID + "'";
			}
		}
		
		tsRetVal = Query(sql, [globals.org_id, psFirstAndLast, psSuppID])

		if((tsRetVal=='' || tsRetVal==null) && pbThrowErrorIfNotFound){
			oErr.message = "Column '" + niceColName + "' has an invalid value ('" + psFirstAndLast + "')."
			oErr.message += ' Please check Supplier Contact setup for valid values.'
			throw oErr		
		}
	}
	
	return tsRetVal
}

/**
 * @return
 * @properties={typeid:24,uuid:"5B28809B-1B73-4781-B3E8-1C1810F2F29B"}
 */
function GetNextSupplierAddressSeqNum(psSuppID){
	var tiRetVal

	tiRetVal = Query("SELECT max(sequence_nr) from ap_supplier_address where supplier_id = '" + psSuppID + "' and org_id = '" + globals.org_id + "'")
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * 
 * @param {String} psCustID
 *
 * @return
 * @properties={typeid:24,uuid:"9609778E-E4F0-4292-B676-53DB6D7251B8"}
 */
function GetNextCustAddressSeqNum(psCustID){
	var tiRetVal

	tiRetVal = Query("SELECT max(sequence_nr) from sa_customer_address where cust_id = '" + psCustID + "' and org_id = ?", [globals.org_id])
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * @param {String} psTable
 * @param {String} psQueryField
 * @param {String} psQueryValue
 *
 * @return
 * @properties={typeid:24,uuid:"57E25F5B-B9AA-4972-9B51-DDF4251653B1"}
 */
function GetNextSeqNumFor(psTable, psQueryField, psQueryValue){
	var tiRetVal

	tiRetVal = Query("SELECT max(sequence_nr) from " + psTable + " where " + psQueryField + " = '" + psQueryValue + "' and org_id = '" + globals.org_id + "'")
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * 
 * @param {String} psItemID
 *
 * @return
 * @properties={typeid:24,uuid:"D788F999-1D32-4C3D-AA9F-9975C6641A42"}
 */
function GetNextItemSellingUOMSeqNum(psItemID){
	var tiRetVal

	tiRetVal = Query("SELECT max(sequence_nr) from in_item_selling_uom where item_id = '" + psItemID + "' and org_id = '" + globals.org_id + "'", null)
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * 
 * @param {String} vsTable
 *
 * @return
 * @properties={typeid:24,uuid:"BE5CD3E1-51E0-437B-BC69-6AE7ECD99C89"}
 */
function GetNextSeqNum(vsTable){
	var tiRetVal

	tiRetVal = Query("SELECT max(sequence_nr) from " + vsTable + " where org_id = ?", [globals.org_id])
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * // this func is an easy way of returing a single col from a single row.
 * @param {String} psSQL
 * @param {Array} [paArgs]
 * @param {Number} [nNumCols]
 *
 * @return
 * @properties={typeid:24,uuid:"B3E04E0C-C925-4204-BD81-25249BBE35D1"}
 */
function Query(psSQL, paArgs, nNumCols){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object()
	var tsRetVal=''

	oSQL.sql = psSQL
	oSQL.args = paArgs
	oSQL.server = globals.avBase_dbase_avanti 

	var dsData = globals.avUtilities_sqlDataset(oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0){		
		/** @type {Array} */
		var taColVals;
		
		if(nNumCols > 1){
			for(var i=0;i<nNumCols;i++){
				taColVals = dsData.getColumnAsArray(i+1)
				if(tsRetVal){
					tsRetVal += ','
				}
				tsRetVal += taColVals[0]
			}
		}
		else{
			taColVals = dsData.getColumnAsArray(1)
			tsRetVal = taColVals[0]
		}
	}
	
	return tsRetVal
}

/**
 * 
 * @param {String} psAvantiDate - its in the format 'YYYYMMDD'
 *
 * @return
 * @properties={typeid:24,uuid:"29ACE0CE-03B7-4375-91B9-4B1F2B534E6E"}
 */
function MakeDate(psAvantiDate){
	if(psAvantiDate){
		/***@type {Number} */
		var tiYYYY = psAvantiDate.substr(0,4)
		/***@type {Number} */
		var tiMM = psAvantiDate.substr(4,2)-1
		/***@type {Number} */
		var tiDD = psAvantiDate.substr(6,2)	
		var myDate = new Date(tiYYYY,tiMM, tiDD)
		return myDate
	}
	
	return null
}

/**
 *
 * @param {String} psAvantiDateTime
 *
 * @return
 * @properties={typeid:24,uuid:"520338A0-5F5A-44EB-817C-936784E8E86A"}
 */
function MakeDateTime(psAvantiDateTime){
	/***@type {Number} */
	var tiYYYY = psAvantiDateTime.substr(0,4)
	/***@type {Number} */
	var tiMM = psAvantiDateTime.substr(4,2)-1
	/***@type {Number} */
	var tiDD = psAvantiDateTime.substr(6,2)
	/***@type {Number} */
	var tiHH = psAvantiDateTime.substr(9,2)
	/***@type {Number} */
	var tiNN = psAvantiDateTime.substr(11,2)
	
	var myDate = new Date(tiYYYY,tiMM, tiDD, tiHH, tiNN, 0)
	return myDate
}

/**
 * 
 * @param {String} errMsg
 * @param {Number} tiRecNum
 * @param {String} [tsInsertOrUpdate]
 * @param {String} [sColName]
 * @param {Array<String>} [aValidValues]
 *
 * @properties={typeid:24,uuid:"33CB4DB1-D1C8-4382-B428-5A30ECBD179C"}
 */
function LogError(errMsg, tiRecNum, tsInsertOrUpdate, sColName, aValidValues) {
	if (!errMsg && sColName && aValidValues && aValidValues.length > 0) {
		errMsg = sColName + " " + scopes.avText.getLblMsg('isInvalid') + ". " + scopes.avText.getLblMsg("ValidValuesAre");
		
		for (var i = 0; i < aValidValues.length; i++) {
			if (i > 0) {
				errMsg += ", ";
			}
			errMsg += aValidValues[i];
		}
	}
	
	if (msErrorMsgs != '') {
		msErrorMsgs = msErrorMsgs + '\n';
	}

	if (tsInsertOrUpdate == 'I') {
		errMsg = 'File record # ' + tiRecNum + '. Insert Failed. Error: ' + errMsg;
	}
	else if (tsInsertOrUpdate == 'U') {
		errMsg = 'File record # ' + tiRecNum + '. Update Failed. Error: ' + errMsg;
	}
	// comes here if the key used to find rec is missing. so we dont know yet if its an insert or an update
	else {
		errMsg = 'File record # ' + tiRecNum + '. ' + errMsg;
	}

	miNumErrors += 1;

	if (bAutoImport) {
		autoDataImportLog(errMsg, "Error");
	}
	else {
		msErrorMsgs += errMsg;

		if (miNumErrors % MAX_NUM_ERRS == 0) {
			if (mbTurnOffHaltingAfterMaxErrors) {
				msErrorMsgs = msErrorMsgs; // DECIDED not to clear out result screen
			}
			else {
				/***@type {{name:String, message:String}}*/
				var oErr = new Object();
				oErr.name = "ERROR LIMIT REACHED"
				oErr.message = "Too many validation errors have been encountered (" + MAX_NUM_ERRS + "). Please see the results below, correct the problems with the data and try again."
				throw oErr
			}
		}
	}
}

/**
 * @param {String} sMsg
 * @param {String} sMsgType
 * @param {Boolean} [bAddPreamble]
 *
 * @properties={typeid:24,uuid:"BFF2E2B4-ECB9-4DBB-909B-1ED7DB341165"}
 */
function autoDataImportLog(sMsg, sMsgType, bAddPreamble) {
    if (bAddPreamble) {
        sMsg = 'Import type/subtype: ' + msImportType + '/' + msSubType + '. Filename: ' + _sFileName + '. ' + sMsg;
    }
    
    globals.dbLog(sMsg, 'autoDataImport', 'import', null, 'ftp', globals.org_id, 'ftp', 'Summary', null, sMsgType, null, null);
}

/**
 * 
 * @param {String} psDate
 *
 * @return
 * @properties={typeid:24,uuid:"14B700DC-81D5-4530-81A9-DADDE9B0D2E8"}
 */
function IsValidDate(psDate){
	var retval = false
	
	if(psDate.length == 8){
		/***@type {Number} */
		var tiYYYY = psDate.substr(0,4)
		/***@type {Number} */
		var tiMM = psDate.substr(4,2)-1
		/***@type {Number} */
		var tiDD = psDate.substr(6,2)		
		
		if(tiYYYY>0 && tiYYYY<=9999)
			if(tiMM>=0 && tiMM<12) // month is 0-11
				retval = IsValidMonthDay(tiYYYY, tiMM+1, tiDD)
	}
	
	return retval
}

/**
 *
 * @param {String} psTime
 *
 * @return
 * @properties={typeid:24,uuid:"7B3D6E1D-1619-44A5-8C9D-F6FBE3C77C3D"}
 */
function IsValidTime(psTime){
	var retval = false
	
	if(psTime.length == 4){
		/***@type {Number} */
		var tiHH = psTime.substr(0,2)
		/***@type {Number} */
		var tiNN = psTime.substr(2,2)
		
		if(tiHH>0 && tiHH<25)
			if(tiNN>=0 && tiNN<61)
				retval = true
	}
	
	return retval
}

/**
 *
 * @param {String} psDateTime
 *
 * @return
 * @properties={typeid:24,uuid:"34B55047-16A8-4502-9BD5-222FC66D406B"}
 */
function IsValidDateTime(psDateTime){
	var retval = false
	var tsDate = psDateTime.substr(0,8)
	var tsTime = psDateTime.substr(9,4)
	
	if(IsValidDate(tsDate)){
		if(tsTime.length == 0) // they dont really have to provide a time
			retval=true
		else if(IsValidTime(tsTime))
			retval=true
	}

	return retval
}

/**
 * 
 * @param {Number} tiYear
 * @param {Number} tiMonth
 * @param {Number} tiDay
 *
 * @return
 * @properties={typeid:24,uuid:"8C90E274-D79F-405F-9A2D-967E47A46950"}
 */
function IsValidMonthDay(tiYear, tiMonth, tiDay){
	switch(tiMonth)
	{
		case 9, 4, 6, 11:
			return tiDay <= 30
			break
		case 2:
			if(isLeapYear(tiYear))
				return tiDay <= 29
			else
				return tiDay <= 28						
			break				
		default: 
			return tiDay <= 31
			break				
	}
}

/**
 * 
 * @param {Number} tiYear
 *
 * @return
 * @properties={typeid:24,uuid:"98DAE836-7E4B-4E9F-909D-DBD6B8F5BF73"}
 */
function isLeapYear(tiYear){
	return (tiYear % 4 == 0 && tiYear % 100 != 0) || tiYear % 400 == 0
}

/**
 * 
 * @param {JSFoundSet} toFoundset
 * @param {String} colName
 * @param {Number} tiRecNum
 * @param {String} niceColName
 * @param {String} colValue
 * @param {Boolean} mandatory
 * @param {Number} maxLen
 * @param {String} stdValidationType
 * @param {String} validVals
 * @param {String} translatedVals
 * @param {Function} validationFunc
 * @param {Object|UUID} [otherValidationFuncValue]
 * @param {Object} [otherValidationFuncValue2]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"958FF49C-8B40-4C99-9493-7A7AE5925B87"}
 */
function Validate(toFoundset, colName, tiRecNum, niceColName, colValue, mandatory, maxLen, stdValidationType, validVals, translatedVals, validationFunc, otherValidationFuncValue, otherValidationFuncValue2){
	try{
		/***@type {{name:String, message:String}}*/
		var oErr = new Object();

		if(colValue == null || colValue == undefined){
			colValue = '';
		}

		var retVal = colValue	
		
		if(mandatory){
			if(colValue == ''){
				oErr.message = "Column '" + niceColName + "' is blank. This is a required field"
				throw oErr		
			}

			if(colValue == 'NO-VALUE' && _sInsertOrUpdate == 'I'){ // sl-5051 - only object to NO-VALUE for mandatory col if doing an insert
				oErr.message = "Column '" + niceColName + "' is 'NO-VALUE'. This is a required field"
				throw oErr		
			}
		}

		// had to do this separate test for num cols because '0' == '' was evaluating as true 
		var bValidNum = false;
		if(stdValidationType == 'NUMBER' || stdValidationType == 'INTEGER' || stdValidationType == 'NUM-NO-NEGATIVES'){
			if(scopes.avUtils.isNumber(colValue)){
				bValidNum = true;
			}
		}
		
		// if we accept a limited set of values, and we left col blank then we are assuming they dont want to update the field
		if(validVals && Trim(colValue) == ''){
			colValue = 'NO-VALUE';
		}
		
		// if colValue == 'NO-VALUE' then we arent updating this col, also not updating if blank string - but only if non-char col
		if(colValue != 'NO-VALUE' && (Trim(colValue) != '' || stdValidationType=='' || bValidNum)){ 
			if(maxLen > 0)
				if(colValue.length > maxLen){
					oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It cannot be more than " + maxLen + ' characters.'
					throw oErr			
				}

			if(stdValidationType !=''){ // empty string means no update - only for non char field tho
				if(stdValidationType == 'DATE'){
					if(IsValidDate(colValue)== false){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. This is not a valid date."
						throw oErr		
					}
					else
						retVal = MakeDate(colValue)
				}
				else if(stdValidationType == 'DATETIME'){
					if(IsValidDateTime(colValue)== false){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. This is not a valid date-time."
						throw oErr		
					}
					else
						retVal = MakeDateTime(colValue)
				}
				else if(stdValidationType == 'NUMBER'){
					if(!bValidNum){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It must be a number."
						throw oErr		
					}
				}
				else if(stdValidationType == 'INTEGER'){
					if(!bValidNum){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It must be an integer."
						throw oErr		
					}
					else if(isInt(colValue) == false){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It must be an integer.'"
						throw oErr		
					}
				}
				else if(stdValidationType == 'NUM-NO-NEGATIVES'){
					if(!bValidNum){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It must be a number.";
						throw oErr;
					}
					else if(colValue < 0){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It cannot be negative.'";
						throw oErr;
					}
				}
			}
			
			if(validVals){
				var taValidVals = validVals.split(", ")
				var taTranslatedVals = translatedVals.split(",")
				var tbFoundOne=false

				if(taValidVals.length > 0){
					colValue=colValue.toUpperCase()
					for(var i=0;i< taValidVals.length;i++)
						if(colValue==taValidVals[i].toUpperCase()){
							tbFoundOne=true
							if(translatedVals != '' && taTranslatedVals.length-1 >=  i)
								retVal=taTranslatedVals[i]
							break
						}
					if(tbFoundOne==false){
						oErr.message = "Column '" + niceColName + "' is '" + colValue + "'. It must be one of: " + validVals
						throw oErr		
					}
				}
			}
			
			// validation callback func - it will throw an error if it fails
            if (validationFunc) {
                if (otherValidationFuncValue2 != null && otherValidationFuncValue2 != '')
                    retVal = validationFunc(colValue, otherValidationFuncValue, otherValidationFuncValue2, true, niceColName);
                else if (otherValidationFuncValue != null && otherValidationFuncValue != '')
                    retVal = validationFunc(colValue, otherValidationFuncValue, true, niceColName);
                else if (colValue != null && colValue != '')
                    retVal = validationFunc(colValue, true, niceColName);
            }
			
			// dealing with a string - probably
			if(!stdValidationType){
				retVal = Trim(retVal);
				
				retVal = removeCommaQuotesItem(retVal);
				// sl-5616
				if(retVal.indexOf('<br>') > -1){
					retVal = scopes.avText.replaceAll(retVal.toString(), '<br>', '\r\n');
				}
			}
			
			if(toFoundset==null) // no foundset passed - just using this func for validation
				return retVal.toString()
			else
				toFoundset.setDataProviderValue(colName, retVal)
				
		}
		else if(toFoundset != null){ // NO-VALUE - NO UPDATE
			var tableName = scopes.avDB.getTableName(toFoundset);
			
			if(tableName == 'sa_customer'){
				aCustNoValueCols.push(colName);
			}
			else if(tableName == 'sa_customer_address'){
				aCustAddressNoValueCols.push(colName);
			}
			else if(tableName == 'sys_address'){
				aAddressNoValueCols.push(colName);
			}
			else if(tableName == 'sa_customer_contact'){
				aCustContactNoValueCols.push(colName);
			}
			else if(tableName == 'sys_contact'){
				aContactNoValueCols.push(colName);
			}
			else if(tableName == 'sys_sales_tax_item'){
				aTaxItemNoValueCols.push(colName);
			}
			else if(tableName == 'sys_sales_tax_group'){
				aTaxGroupNoValueCols.push(colName);
			}
		}
	}
	catch(ex){
		mbCurrRowErrored=true
		LogError(ex.message, tiRecNum, '')
	}
	return null
}

/**
 * @param {String} vs
 * @return
 * @properties={typeid:24,uuid:"8A71732C-59B4-4B65-AC44-795185E68AC5"}
 */
function Trim(vs){
	try{
		if(vs == null)
			return ''
		else{
		    while (vs.substring(0,1) == ' ') // check for white spaces from beginning
		    {
		    	vs = vs.substring(1, vs.length);
		    }
		    while (vs.substring(vs.length-1, vs.length) == ' ') // check white space from end
		    {
		    	vs = vs.substring(0,vs.length-1);
		    }
		    
			return vs
		}
	}
	catch(ex){ // have been getting errs on replace, not sure why
		return vs
	}
	
}

/**
 * 
 * @param {Object} n
 *
 * @return
 * @properties={typeid:24,uuid:"C8A9E38D-EF37-46AC-9358-DEB1EE227A08"}
 */
function isInt(n) {
	var tbRetVal = false
	
	if(n != '')
		if(n % 1 == 0)
			tbRetVal = true
		
	return tbRetVal
}

/**
 * @properties={typeid:24,uuid:"0EA62DFA-80B1-4AAB-84AE-EBE6A98430BA"}
 */
function processCustomerRecords(){
	mfsCustomers = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer')
	mfsCustomerAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_address')
	mfsAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')
	
	// sl-3444 - set all custs not in import file to inactive
	_setCustsNotInImportAsInactive = globals.avBase_getSystemPreference_String(121) == 1 
	if(_setCustsNotInImportAsInactive){
		mfsCustomers.loadAllRecords()
		scopes.avDB.updateFS(mfsCustomers, ['cust_status'], ['I'])
		mfsCustomers.clear();
	}

	// fst rec is col headings	
	maCustomerColHeaders = msLines[0].split('\t') 

	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processCustomerRecord(msLines[i], i)
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsCustomers, -1)
	databaseManager.refreshRecordFromDatabase(mfsCustomerAddresses, -1)
	databaseManager.refreshRecordFromDatabase(mfsAddresses, -1)
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_customer');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_customer_address')
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_address');
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"6356559B-449E-4D2F-B51C-E33D06648714"}
 * @AllowToRunInFind
 */
function processCustomerRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t');

	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column 'Customer Code' is blank. This is a required field.", tiRecNum, '');
		else{
			mfsCustomers.newRecord();
			mfsCustomerAddresses.newRecord();
			mfsAddresses.newRecord();

			mfsCustomers.cust_code = taCols[0].toUpperCase();
			
			var dsData = scopes.avDB.SQLQueryDataset('select cust_id, cust_name from sa_customer where cust_code = ?', true, [removeCommaQuotes(mfsCustomers.cust_code)]);
			var cust_id = null;
			var cust_name = null;
			
			if (dsData && dsData.getMaxRowIndex() == 1) {
				var arr = dsData.getColumnAsArray(1);				
				if(arr && arr.length > 0){
					cust_id = arr[0];
				}

				arr = dsData.getColumnAsArray(2);				
				if(arr && arr.length > 0){
					cust_name = arr[0];
				}
			}
			else if (dsData && dsData.getMaxRowIndex() > 1) {
				LogError("There is more than 1 record in the database (" + dsData.getMaxRowIndex() + ") with Customer Code: " + taCols[0] + '. This is not allowed. Please delete the extras.', tiRecNum, '');
				return;
			}
				
			if(cust_id){
				// if doing an update, replace the cust_id of this new rec with the one from the rec in db we want to update. it is used to find the right sa_customer_address rec to update
				mfsCustomers.cust_id = cust_id;
				mfsCustomerAddresses.cust_id = cust_id;
				mfsCustomerAddresses.addr_id = mfsAddresses.addr_id;
				WriteCustomerToDB(taCols, 'U', tiRecNum);

				// code called from onRecordUpdate_sa_customer() 
				if(Trim(taCols[1]) != Trim(cust_name)){
					cust_id = cust_id.toString();
					var sql = "select det.priceruledtl_id \
						from sa_price_rule_detail det \
						inner join sa_price_rule rul on rul.pricerule_id = det.pricerule_id \
						inner join sa_price_type_segment seg on seg.pricetype_id = rul.pricetype_id \
						where seg.pricetypeseg_code = 'A' and rul.pricerule_active = 1 and det.org_id = ? and ( \
							det.priceruledtl_repsonse_seg1 = ? or \
							det.priceruledtl_repsonse_seg2 = ? or \
							det.priceruledtl_repsonse_seg3 = ? or \
							det.priceruledtl_repsonse_seg4 = ? or \
							det.priceruledtl_repsonse_seg5 = ? or \
							det.priceruledtl_repsonse_seg6 = ?)";

					if(scopes.avDB.SQLQuery(sql, null, [globals.org_id.toString(), cust_id, cust_id, cust_id, cust_id, cust_id, cust_id])){
						globals.setPriceSegmentDisplayValue('A', cust_id, taCols[1]); // Call Price update
					}
				}
			}
			else{
				mfsCustomerAddresses.cust_id = mfsCustomers.cust_id;
				mfsCustomerAddresses.addr_id = mfsAddresses.addr_id;
				WriteCustomerToDB(taCols, 'I', tiRecNum);
			}
			
			mfsCustomers.getSelectedRecord().revertChanges();
			mfsCustomerAddresses.getSelectedRecord().revertChanges();
			mfsAddresses.getSelectedRecord().revertChanges();
			
			aCustNoValueCols = [];
			aCustAddressNoValueCols = [];
			aAddressNoValueCols = [];
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"AE7DB1CA-0754-4A10-BF86-E80094CB3535"}
 */
function WriteCustomerToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
		
		// *** IMPORTANT - WHENEVER A NEW COL IS ADDED TO THE CUSTOMER TABLE IMPORT IT NEEDS TO BE ADDED TO THE aCustImportDBCols ARRAY OR IT WONT BE IMPORTED *** // 
		
		Validate(mfsCustomers, 'cust_name', tiRecNum, maCustomerColNames[1], taCols[1], true, 64, '', '', '', null, '')
		Validate(mfsCustomers, 'cust_type', tiRecNum, maCustomerColNames[2], taCols[2], true, 0, '', 'Customer, Prospect, Suspect', 'C, P, S',null, '')
		Validate(mfsCustomers, 'custcat_id', tiRecNum, maCustomerColNames[3], taCols[3], false, 0, '', '', '', GetCustCatIDFromCode, '')
		Validate(mfsCustomers, 'custclass_id', tiRecNum, maCustomerColNames[4], taCols[4], true, 0, '', '', '', GetCustClassIDFromCode, '')
		Validate(mfsCustomers, 'terr_id', tiRecNum, maCustomerColNames[5], taCols[5], false, 0, '', '', '', GetTerritoryIDFromCode, '')
		Validate(mfsCustomers, 'custgrp_id', tiRecNum, maCustomerColNames[6], taCols[6], false, 0, '', '', '', GetCustGroupIDFromCode, '')
		Validate(mfsCustomers, 'cust_parent_cust_id', tiRecNum, maCustomerColNames[7], taCols[7], false, 0, '', '', '', GetParentCustIDFromCode, '')
		Validate(mfsCustomers, 'salesper_id', tiRecNum, maCustomerColNames[8], taCols[8], false, 0, '', '', '', GetSalesrepIDFromCode, '')
		
		// sl-3444 - if using pref set this cust as active - assuming they havent entered a val in the status col here
		if(_setCustsNotInImportAsInactive && Trim(taCols[9]) == ''){
			taCols[9] = 'Active'
		}
		
		Validate(mfsCustomers, 'cust_status', tiRecNum, maCustomerColNames[9], taCols[9], true, 0, '', 'Active, Inactive, Flagged For Deletion', 'A,I,F', null, '')
		Validate(mfsCustomers, 'curr_id', tiRecNum, maCustomerColNames[10], taCols[10], true, 0, '', '', '', GetCurrencyIDFromCode, '')
		Validate(mfsCustomers, 'terms_id', tiRecNum, maCustomerColNames[11], taCols[11], false, 0, '', '', '', GetTermsIDFromCode, '')
		Validate(mfsCustomers, 'cust_website', tiRecNum, maCustomerColNames[12], taCols[12], false, 256, '', '', '', null, '')
		Validate(mfsCustomers, 'custsic_id', tiRecNum, maCustomerColNames[13], taCols[13], false, 0, '', '', '', GetCustSicIDFromCode, '')
		Validate(mfsCustomers, 'shipmethod_id', tiRecNum, maCustomerColNames[14], taCols[14], false, 0, '', '', '', GetShipMethodIDFromCode, '')		
		Validate(mfsCustomers, 'cust_credit_limit', tiRecNum, maCustomerColNames[15], taCols[15], false, 0, 'INTEGER', '', '', null, '')
		Validate(mfsCustomers, 'paymethod_id', tiRecNum, maCustomerColNames[16], taCols[16], false, 0, '', '', '', GetPayMethodIDFromCode, '')
		Validate(mfsCustomers, 'custindustry_id', tiRecNum, maCustomerColNames[17], taCols[17], false, 0, '', '', '', GetCustIndustryIDFromCode, '')
		Validate(mfsCustomers, 'cust_min_order_amount', tiRecNum, maCustomerColNames[18], taCols[18], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsCustomers, 'cust_po_required', tiRecNum, maCustomerColNames[19], taCols[19], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsCustomers, 'cust_ship_complete', tiRecNum, maCustomerColNames[20], taCols[20], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsCustomers, 'cust_amounts_on_pkgslip', tiRecNum, maCustomerColNames[21], taCols[21], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsCustomers, 'cust_exclude_price_on_pkgslip', tiRecNum, maCustomerColNames[22], taCols[22], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsCustomers, 'taxgroup_id', tiRecNum, maCustomerColNames[23], taCols[23], false, 0, '', '', '', GetTaxGroupIDFromCode, '')
		Validate(mfsCustomers, 'cust_date_created', tiRecNum, maCustomerColNames[24], taCols[24], false, 0, 'DATE', '', '', null, '')
		Validate(mfsCustomers, 'cust_createdby_user_id', tiRecNum, maCustomerColNames[25], taCols[25], false, 0, '', '', '', GetCreatedByIDFromCode, '')

		Validate(mfsCustomers, 'whse_id', tiRecNum, maCustomerColNames[28], taCols[28], false, 0, '', '', '', GetWarehouseIDFromCode, '')
		Validate(mfsCustomers, 'cust_csr_empl_id', tiRecNum, maCustomerColNames[29], taCols[29], false, 0, '', '', '', GetCSRIDFromCode, '')
		Validate(mfsCustomers, 'cust_accept_bo', tiRecNum, maCustomerColNames[30], taCols[30], true, 0, '', 'Y, N', '1,0', null, '')

		// these funcs may call CreateNewCustContact() which inserts rec thru foundset - but no find is done
		SetCustContactIDFromFirstAndLast(mfsCustomers, 'cust_proof_contact_id', taCols[31], globals.UUIDtoStringNew(mfsCustomers.cust_id),null, null, null, null)
		SetCustContactIDFromFirstAndLast(mfsCustomers, 'cust_samples_contact_id', taCols[32], globals.UUIDtoStringNew(mfsCustomers.cust_id), null, null, null, null)
		
		Validate(mfsCustomers, 'cust_chargeback_code', tiRecNum, maCustomerColNames[33], taCols[33], false, 64, '', '', '', null, '')
		Validate(mfsCustomers, 'cust_priority', tiRecNum, maCustomerColNames[34], taCols[34], false, 0, 'INTEGER', '', '', null, '')
		Validate(mfsCustomers, 'cust_tax_reg_number', tiRecNum, maCustomerColNames[35], taCols[35], false, 32, '', '', '', null, '')
		Validate(mfsCustomers, 'cust_invoice_detail_level', tiRecNum, maCustomerColNames[36], taCols[36], false, 0,'', 'Sales Order, Sales Order Line', 'O,L', null, '')
		Validate(mfsCustomers, 'cust_account_code', tiRecNum, maCustomerColNames[37], taCols[37], false, 32, '', '', '', null, '')
		Validate(mfsCustomers, 'clc_mtd_sales', tiRecNum, maCustomerColNames[38], taCols[38], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsCustomers, 'clc_mtd_profit', tiRecNum, maCustomerColNames[39], taCols[39], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsCustomers, 'clc_ytd_profit', tiRecNum, maCustomerColNames[40], taCols[40], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsCustomers, 'clc_ytd_sales', tiRecNum, maCustomerColNames[41], taCols[41], false, 0, 'NUMBER', '', '', null, '')
		
		var nOverUnder = taCols[42]; 
		if(scopes.avUtils.isNumber(nOverUnder)){
			nOverUnder = nOverUnder / 100;
		}
		
		Validate(mfsCustomers, 'cust_over_under_threshold', tiRecNum, maCustomerColNames[42], nOverUnder, false, 0, 'NUMBER', '', '', null, '');
		Validate(mfsCustomers, 'default_inv_freight_revenue', tiRecNum, maCustomerColNames[43], taCols[43], false, 0, '', 'Negotiated Carrier Rate, Published Carrier Rate, Sales Order Shipping Cost', '1,2,3', null, '')
		Validate(mfsCustomers, 'cust_project_required', tiRecNum, maCustomerColNames[44], taCols[44], false, 0, '', 'Y, N', '1,0', null, '');
		
		// Default Shipper fields
		Validate(mfsCustomers, 'custaddr_use_default_shipper', tiRecNum, maCustomerColNames[45], taCols[45], false, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsCustomers, 'cust_id_ship', tiRecNum, maCustomerColNames[46], taCols[46], false, 0, '', '', '', GetCustIDFromCode, '');
		if(mfsCustomers.cust_id_ship){
			Validate(mfsCustomers, 'custcontact_id_ship', tiRecNum, maCustomerColNames[47], taCols[47], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, mfsCustomers.cust_id_ship);
			Validate(mfsCustomers, 'custaddr_id_ship', tiRecNum, maCustomerColNames[48], taCols[48], false, 0, '', '', '', GetCustAddrIDFromCode, mfsCustomers.cust_id_ship);
		}
		
		// FOB Customer
		Validate(mfsCustomers, 'cust_is_fob', tiRecNum, maCustomerColNames[49], taCols[49], true, 0, '', 'Y, N', '1,0', null, '');
		
		// Default Shipping Markup %
		Validate(mfsCustomers, 'default_shipping_markup', tiRecNum, maCustomerColNames[52], taCols[52], false, 0, 'NUMBER', '', '', null, '');
		
		if (Trim(taCols[49]) == "Y") {
			mfsCustomers.taxgroup_id = null;
			
			// FOB Address Code
			if (!isBlank(taCols[50])) {
				var sSQL = "SELECT CA.custaddr_id \
							FROM sa_customer_address CA \
							INNER JOIN sa_customer C ON CA.cust_id = C.cust_id \
							WHERE \
								CA.org_id = ? \
								AND (CA.cust_id = ? OR CA.globaladdr_active = 1) \
								AND ISNULL(CA.custaddr_one_time, 0) = 0 \
								AND CA.custaddr_code = ? ";
				var aArgs = [globals.org_id, mfsCustomers.cust_id.toString(), taCols[50]];
				
				// FOB Address Customer Code - could be a different customer 
				if (!isBlank(taCols[51])) {
					sSQL += " AND C.cust_code = ?";
					aArgs.push(taCols[51]);
				}
				
				mfsCustomers.cust_fob_address_id = scopes.avDB.SQLQuery(sSQL, null, aArgs);
				
				if (!mfsCustomers.cust_fob_address_id) {
					LogError(scopes.avText.getLblMsg("importBadFOBAddress"), tiRecNum, tsInsertOrUpdate);
					mbCurrRowErrored = true;
				}
			}
		}
		else {
			mfsCustomers.cust_fob_address_id = null;
		}
		

		FillCustomerAddressFoundset(taCols, tiRecNum, 53, mfsCustomerAddresses, tsInsertOrUpdate);
		FillAddressFoundset(taCols, tiRecNum, 73, mfsAddresses); // 20 cols in FillCustomerAddressFoundset()
		mfsCustomerAddresses.custaddr_code = 'PRIMARY'
		
		if(tsInsertOrUpdate=="I"){			
			pullInCustClassDefaults(mfsCustomers) // pull in cust class defaults - but only for insert
		}
		else{
			var bNoPrimaryAddressFound = false;
			var custAddrID = scopes.avDB.SQLQuery('select custaddr_id from sa_customer_address where cust_id = ? and custaddr_code = ?', true, [mfsCustomers.cust_id.toString(), mfsCustomerAddresses.custaddr_code]);
		
			// if we are doing an update there should already be a primary addr, theyre mandatory. big mountain somehow didnt have one for many recs tho. dont know how - so create a skeleton primary if nec
			if(custAddrID){
				mfsCustomerAddresses.custaddr_id = custAddrID;
			}
			else{
				bNoPrimaryAddressFound = true;
			}
		}

		mfsCustomers.cust_primary_custaddr_id = mfsCustomerAddresses.custaddr_id;
		
		// if no billto or shipto specified then assign primary
		if(isBlank(taCols[26]) || taCols[26]=='PRIMARY')
			mfsCustomers.cust_shipto_custaddr_id = mfsCustomerAddresses.custaddr_id;
		if(isBlank(taCols[27]) || taCols[27]=='PRIMARY')
			mfsCustomers.cust_billto_custaddr_id = mfsCustomerAddresses.custaddr_id;
		
		// i put this right at the end because it could create new address recs and we dont want to do that if any validation failed
		if(mbCurrRowErrored==false){
			/***@type {String} */
			var tsCustCode = "" + mfsCustomers.cust_id + "" // getting weird errs in Query() because it didnt regard custid as a string
			var tsAddrId = ''
			
			///// SHIPTO ADDRESS
			if(!isBlank(taCols[26]) && taCols[26]!='PRIMARY'){ // sl-2102 - added PRIMARY condition 
				tsAddrId = GetCustAddrIDFromCode(taCols[26], tsCustCode, false) 
				if(!isBlank(tsAddrId)) 
					mfsCustomers.cust_shipto_custaddr_id = tsAddrId
				else // didnt find it - create placeholder rec with that code
					mfsCustomers.cust_shipto_custaddr_id = CreateNewCustAddress(tsCustCode, taCols[26])
			}
			
			///// BILLTO ADDRESS
			if(!isBlank(taCols[27]) && taCols[27]!='PRIMARY'){
				tsAddrId = GetCustAddrIDFromCode(taCols[27], tsCustCode, false) 
				if(!isBlank(tsAddrId)) 
					mfsCustomers.cust_billto_custaddr_id = tsAddrId
				else // didnt find it - create placeholder rec with that code
					mfsCustomers.cust_billto_custaddr_id = CreateNewCustAddress(tsCustCode, taCols[27])
			}
			
			//sl-4501 - cust udf cols
			if(taCols.length > miNumStaticCustCols){
				for(var u=miNumStaticCustCols;u<taCols.length;u++){
					if(maCustomerColHeaders[u].indexOf('UDF|') > -1 && taCols[u] != 'NO-VALUE'){
						var udfName = maCustomerColHeaders[u].split('|')[1]
						updateCustUDF(udfName, taCols[u], mfsCustomers.cust_id)
					}
				}
			}
		}
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
//			revertChanges(mfsCustomers, tsInsertOrUpdate)
		}
		else{
//			databaseManager.saveData(mfsCustomers.getSelectedRecord())
//			CheckForSaveErrors(mfsCustomers, tsInsertOrUpdate)
//
//			if(tsInsertOrUpdate=="I"){
//				databaseManager.saveData(mfsAddresses.getSelectedRecord())
//				CheckForSaveErrors(mfsAddresses, tsInsertOrUpdate)
//				databaseManager.saveData(mfsCustomerAddresses.getSelectedRecord())
//				CheckForSaveErrors(mfsCustomerAddresses, tsInsertOrUpdate)
//
//				miNumInserts=miNumInserts+1
//			}
//			else{
//				miNumUpdates=miNumUpdates+1
//			}
			
			
			if(tsInsertOrUpdate=="I"){
				mfsCustomers.created_by_import = 1;
				mfsCustomerAddresses.created_by_import = 1;
				mfsAddresses.created_by_import = 1;
				
				writeWithSQL(mfsCustomers, [], [], tsInsertOrUpdate, tiRecNum);
				writeWithSQL(mfsCustomerAddresses, [], [], tsInsertOrUpdate, tiRecNum);
				writeWithSQL(mfsAddresses, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				writeWithSQL(mfsCustomers, ['cust_code'], aCustNoValueCols, tsInsertOrUpdate, tiRecNum);

				// have to get cust id from cust rec so we can find the right customer address rec to update
				mfsCustomerAddresses.cust_id = mfsCustomers.cust_id;

				if(bNoPrimaryAddressFound){
					mfsCustomerAddresses.addr_id = mfsAddresses.addr_id;
					writeWithSQL(mfsCustomerAddresses, [], [], 'I', tiRecNum);
					writeWithSQL(mfsAddresses, [], [], 'I', tiRecNum);
				}
				else{
					var addrID = scopes.avDB.SQLQuery('select addr_id from sa_customer_address where cust_id = ? and custaddr_code = ?', true, [mfsCustomers.cust_id.toString(), mfsCustomerAddresses.custaddr_code])
					
					mfsCustomerAddresses.addr_id = addrID;
					writeWithSQL(mfsCustomerAddresses, ['custaddr_id'], aCustAddressNoValueCols, tsInsertOrUpdate, tiRecNum);
					
					// have to get addr_id from customer address rec so we can find the right address rec to update
					mfsAddresses.addr_id = addrID;
					writeWithSQL(mfsAddresses, ['addr_id'], aAddressNoValueCols, tsInsertOrUpdate, tiRecNum);
				}
			}

			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
//			revertChanges(mfsCustomers, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @param {JSFoundset} fs
 * @param {Array<String>} keyCols
 * @param {Array<String>} updateExcludeCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} [recNum]
 *
 * @properties={typeid:24,uuid:"********-6C39-47AB-8D5F-6CD4D01F97C6"}
 */
function writeWithSQL(fs, keyCols, updateExcludeCols, tsInsertOrUpdate, recNum){
	if(!scopes.avDB.writeRecWithSQL(fs, keyCols, updateExcludeCols, tsInsertOrUpdate)){
		if(recNum){
			throwDBWriteError(recNum);
		}
		else{
			throwDBWriteError();
		}
	}
}

/**
 * @param {String} udfName
 * @param {String} udfValue
 * @param {UUID} custID
 *
 * @properties={typeid:24,uuid:"2F74BEB1-9704-48F0-915F-DB5640A2F7C1"}
 */
function updateCustUDF(udfName, udfValue, custID){
	/**@type {JSRecord<db:/avanti/sys_udf_type>} */
	var rUdfType = scopes.avDB.getRec('sys_udf_type', ['udf_field'], [udfName])
	
	if(rUdfType){
		/**@type {JSFoundset<db:/avanti/sys_udf_values>} */
		var fs = scopes.avDB.getFS('sys_udf_values', ['sys_udf_type_id', 'unique_id'], [rUdfType.sys_udf_type_id, custID])
		/**@type {JSRecord<db:/avanti/sys_udf_values>} */
		var r;
		
		if(fs.getSize() == 1){
			r = fs.getRecord(1) 
		}
		else{
			r = fs.getRecord(fs.newRecord()) 
			r.sys_udf_type_id = rUdfType.sys_udf_type_id
			r.unique_id = custID
		}
		
		if(r){
			switch (rUdfType.udf_field_type) {
				case 'TEXT':
					r.udf_answer = udfValue
					r.udf_answer_text = udfValue 
					break;
				case 'NUMBER':
					r.udf_answer = udfValue
					r.udf_answer_number = parseFloat(udfValue) 
					break;
				case 'INTEGER':
					r.udf_answer = udfValue
					r.udf_answer_integer = parseInt(udfValue) 
					break;
				case 'DATETIME':
					if(IsValidDate(udfValue)){
						r.udf_answer_datetime = MakeDate(udfValue)  
						r.udf_answer = r.udf_answer_datetime.toDateString()
					}
					break;
				case 'TABLE_VALUE':
					r.udf_answer_table_value = scopes.avDB.getVal('sys_udf_type_table_values', ['udf_table_value'], [udfValue], 'sys_udf_type_table_value_id')
					if(r.udf_answer_table_value){
						r.udf_answer = udfValue 
					}
					break;
				case 'MULTI_SELECT':
					var aVals = udfValue.split('|')

					r.udf_answer = null
					r.udf_answer_multi_select = null
					r.udf_answer_multi_select_seq = null
					
					for(var m=0;m<aVals.length;m++){
						var mval = aVals[m]
						
						/**@type {JSRecord<db:/avanti/sys_udf_type_table_values>} */
						var rTabVal = scopes.avDB.getRec('sys_udf_type_table_values', ['udf_table_value'], [mval])
						if(rTabVal){
							if(r.udf_answer){
								r.udf_answer += ', '
								r.udf_answer_multi_select += ', '
								r.udf_answer_multi_select_seq += ', '
							}

							r.udf_answer += mval 
							r.udf_answer_multi_select += rTabVal.sys_udf_type_table_value_id
							r.udf_answer_multi_select_seq += rTabVal.sequence_nr.toString()
						}
					}
					break;
			}
			
			databaseManager.saveData(r)
		}
	}
}

/**
 * @param {JSFoundSet<db:/avanti/sa_customer>} fsCust
 *
 * @properties={typeid:24,uuid:"85D60C3B-5A41-4B44-A949-C53904A92200"}
 */
function pullInCustClassDefaults(fsCust){
	if(fsCust.custclass_id && utils.hasRecords(fsCust.sa_customer_to_sa_customer_class)){
		if(!fsCust.curr_id && fsCust.sa_customer_to_sa_customer_class.curr_id){
			fsCust.curr_id = fsCust.sa_customer_to_sa_customer_class.curr_id
		}

		if(!fsCust.taxgroup_id && fsCust.sa_customer_to_sa_customer_class.taxgroup_id){
			fsCust.taxgroup_id = fsCust.sa_customer_to_sa_customer_class.taxgroup_id
		}

		if(!fsCust.salesper_id && fsCust.sa_customer_to_sa_customer_class.salesper_id){
			fsCust.salesper_id = fsCust.sa_customer_to_sa_customer_class.salesper_id
		}

		if(!fsCust.terr_id && fsCust.sa_customer_to_sa_customer_class.terr_id){
			fsCust.terr_id = fsCust.sa_customer_to_sa_customer_class.terr_id
		}

		if(!fsCust.shipmethod_id && fsCust.sa_customer_to_sa_customer_class.shipmethod_id){
			fsCust.shipmethod_id = fsCust.sa_customer_to_sa_customer_class.shipmethod_id
		}

		if(!fsCust.terms_id && fsCust.sa_customer_to_sa_customer_class.terms_id){
			fsCust.terms_id = fsCust.sa_customer_to_sa_customer_class.terms_id
		}

		if(!fsCust.form_lang_code && fsCust.sa_customer_to_sa_customer_class.form_lang_code){
			fsCust.form_lang_code = fsCust.sa_customer_to_sa_customer_class.form_lang_code
		}

		if(!fsCust.chargebackcustomer && fsCust.sa_customer_to_sa_customer_class.chargeback_customer){
			fsCust.chargebackcustomer = fsCust.sa_customer_to_sa_customer_class.chargeback_customer
		}
	}	
}

/**
 * @param {JSFoundSet} toFoundset
 * @param {String} [tsInsertOrUpdate]
 * 
 * @properties={typeid:24,uuid:"BDA2DE1A-426F-4DE8-BF09-D4F618CE00C8"}
 */
function CheckForSaveErrors(toFoundset, tsInsertOrUpdate){
	var array = databaseManager.getFailedRecords(toFoundset)			

	if(array.length>0) {
		if(tsInsertOrUpdate){
			revertChanges(toFoundset, tsInsertOrUpdate)
		}

		var record = array[0]
		/***@type {{name:String, message:String}}*/
		var oErr = new Object();
		oErr.name = "database save error"
		oErr.message = record.exception
		throw oErr		
	}
	
}

/**
 * @param {Number} [recNum]
 *
 * @properties={typeid:24,uuid:"495B93AA-9798-4E23-8D93-4623A7C6DB4E"}
 */
function throwDBWriteError(recNum){
	/***@type {{name:String, message:String}}*/
	var oErr = new Object();
	oErr.name = "database write error";
	if(recNum){
		oErr.message = 'error writing record #' + recNum + ' to database.';
	}
	throw oErr;
}

/**
 * 
 * @param {String} psCustID
 * @param {String} psAddrCode
 *
 * @return
 * @properties={typeid:24,uuid:"A86B3FBD-3CE5-4D89-8F67-EABD2B4EF5DD"}
 */
function CreateNewCustAddress(psCustID, psAddrCode){
	/***@type {JSFoundSet<db:/avanti/sys_address>} */
	var tfsAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')
	tfsAddress.newRecord()

//	databaseManager.saveData(tfsAddress.getSelectedRecord())
//	CheckForSaveErrors(tfsAddress, 'I')

	writeWithSQL(tfsAddress, [], [], 'I');
	
	/***@type {JSFoundSet<db:/avanti/sa_customer_address>} */
	var tfsCustomerAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_address')
	tfsCustomerAddress.newRecord()
	tfsCustomerAddress.cust_id = psCustID
	tfsCustomerAddress.custaddr_code = psAddrCode
	tfsCustomerAddress.addr_id = tfsAddress.addr_id
	// sl-2102 - added getting sequence_nr
	tfsCustomerAddress.sequence_nr = GetNextCustAddressSeqNum(globals.UUIDtoStringNew(tfsCustomerAddress.cust_id))
	if(tfsCustomerAddress.sequence_nr < 2){ // has to be at least 2 because there has to be a primary addr that is num 1, it might not have been saved yet tho
		tfsCustomerAddress.sequence_nr = 2
	}

//	databaseManager.saveData(tfsCustomerAddress.getSelectedRecord())
//	CheckForSaveErrors(tfsCustomerAddress, 'I')

	writeWithSQL(tfsCustomerAddress, [], [], 'I');
	
	return tfsCustomerAddress.custaddr_id
}

/**
 * 
 * @param {String} psSuppID
 * @param {String} psAddrCode
 *
 * @return
 * @properties={typeid:24,uuid:"D82DC004-640E-4AEF-BC44-572A5832D17C"}
 */
function CreateNewSupplierAddress(psSuppID, psAddrCode){
	/***@type {JSFoundSet<db:/avanti/sys_address>} */
	var tfsAddress
	tfsAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')
	tfsAddress.newRecord()
	databaseManager.saveData(tfsAddress.getSelectedRecord())
	CheckForSaveErrors(tfsAddress, 'I')

	/***@type {JSFoundSet<db:/avanti/ap_supplier_address>} */
	var tfsSupplierAddress
	tfsSupplierAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier_address')
	tfsSupplierAddress.newRecord()
	tfsSupplierAddress.supplier_id = psSuppID
	tfsSupplierAddress.suppaddr_code = psAddrCode
	tfsSupplierAddress.addr_id = tfsAddress.addr_id

	// sl-2102 - added getting sequence_nr
	tfsSupplierAddress.sequence_nr = GetNextSupplierAddressSeqNum(tfsSupplierAddress.supplier_id)
	
	databaseManager.saveData(tfsSupplierAddress.getSelectedRecord())
	CheckForSaveErrors(tfsSupplierAddress, 'I')
	
	return tfsSupplierAddress.suppaddr_id
}

/**
 * @properties={typeid:24,uuid:"9D63AF7B-006C-4BF1-B747-7987BD07D424"}
 */
function processCustomerAddressRecords(){
	mfsCustomerAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_address')
	mfsAddresses = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processCustomerAddressRecord(msLines[i], i)
		}

	databaseManager.refreshRecordFromDatabase(mfsCustomerAddresses, -1)
	databaseManager.refreshRecordFromDatabase(mfsAddresses, -1)
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_customer_address');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_address')
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"A7BF174B-15A0-4944-B51D-86226D417EE8"}
 */
function processCustomerAddressRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError("Column 'Customer Code' is blank. This is a required field.", tiRecNum, '')
			if(isBlank(taCols[1]))
				LogError("Column 'Address Code' is blank. This is a required field.", tiRecNum, '')
		}
		else if(isBlank(taCols[1]))
			LogError("Column 'Address Code' is blank. This is a required field.", tiRecNum, '')
		else if(taCols[1].length > 10)
			LogError("The value in the Column 'Address Code' is too long. The maximum is 10 characters.", tiRecNum, '')
		
		// OLD FS CODE
//		else if(mfsCustomerAddresses.find() || mfsCustomerAddresses.find()){
//			var tsCustID = GetCustIDFromCode(taCols[0], false, 'Customer Code')
//			
//			if(isBlank(tsCustID))
//				LogError("Column 'Customer Code' has an invalid value: '" + taCols[0] + "'. Please consult Customers setup for valid values.", tiRecNum, '')
//			else{
//				// record identifier is supplier_id + suppaddr_code
//				mfsCustomerAddresses.cust_id = tsCustID
//				mfsCustomerAddresses.custaddr_code = taCols[1]
//				
//				if(mfsCustomerAddresses.search() > 0){ // found it do an update
//					WriteCustomerAddressToDB(taCols, 'U', tiRecNum)
//				}
//				else{ // do an insert
//					mfsCustomerAddresses.newRecord()
//					mfsCustomerAddresses.cust_id = tsCustID
//					mfsCustomerAddresses.custaddr_code = taCols[1]
//					mfsAddresses.newRecord()
//					mfsCustomerAddresses.addr_id = mfsAddresses.addr_id
//					WriteCustomerAddressToDB(taCols, 'I', tiRecNum)
//				}
//			}
//		}
		
		// NEW SQL CODE
		else{
			var tsCustID = GetCustIDFromCode(taCols[0], false, 'Customer Code')

			if(isBlank(tsCustID))
				LogError("Column 'Customer Code' has an invalid value: '" + taCols[0] + "'. Please consult Customers setup for valid values.", tiRecNum, '')
			else{
				mfsCustomerAddresses.newRecord()
				mfsAddresses.newRecord()
				
				mfsCustomerAddresses.cust_id = tsCustID;
				mfsCustomerAddresses.custaddr_code = taCols[1];
				
				var tscustaddrID = scopes.avDB.getVal('sa_customer_address', ['cust_id', 'custaddr_code'], [mfsCustomerAddresses.cust_id, mfsCustomerAddresses.custaddr_code], 'custaddr_id');
				
				if(tscustaddrID){ // UPDATE
					mfsCustomerAddresses.custaddr_id = tscustaddrID;
					WriteCustomerAddressToDB(taCols, 'U', tiRecNum);
				}
				else{ // INSERT
					mfsCustomerAddresses.addr_id = mfsAddresses.addr_id
					WriteCustomerAddressToDB(taCols, 'I', tiRecNum)
				}
				
				mfsCustomerAddresses.getSelectedRecord().revertChanges()
				mfsAddresses.getSelectedRecord().revertChanges()
				
				aCustAddressNoValueCols = [];
				aAddressNoValueCols = [];
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"BB6A2FED-9385-4F47-92C1-850C1DDA0056"}
 */
function WriteCustomerAddressToDB(taCols, tsInsertOrUpdate, tiRecNum){		
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
		
		FillCustomerAddressFoundset(taCols, tiRecNum, 2, mfsCustomerAddresses, tsInsertOrUpdate);
		FillAddressFoundset(taCols, tiRecNum, 22, mfsAddresses); // 20 cols in FillCustomerAddressFoundset()
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
		}
		else{
			if(tsInsertOrUpdate=="I"){
				writeWithSQL(mfsCustomerAddresses, [], [], tsInsertOrUpdate, tiRecNum);
				writeWithSQL(mfsAddresses, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				// have to get addr_id from customer address rec so we can find the right address rec to update
				var addrID = scopes.avDB.SQLQuery('select addr_id from sa_customer_address where cust_id = ? and custaddr_code = ?', true, [mfsCustomerAddresses.cust_id.toString(), mfsCustomerAddresses.custaddr_code]);
				
				if(addrID){
					mfsAddresses.addr_id = addrID;
					mfsCustomerAddresses.addr_id = addrID; 

					writeWithSQL(mfsCustomerAddresses, ['custaddr_id'], aCustAddressNoValueCols, tsInsertOrUpdate, tiRecNum);
					writeWithSQL(mfsAddresses, ['addr_id'], aAddressNoValueCols, tsInsertOrUpdate, tiRecNum);
				}
				else{
					mfsCustomerAddresses.addr_id = mfsAddresses.addr_id; 
					writeWithSQL(mfsCustomerAddresses, ['custaddr_id'], aCustAddressNoValueCols, tsInsertOrUpdate, tiRecNum);
					writeWithSQL(mfsAddresses, [], [], 'I', tiRecNum);
				}
			}
			
			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {Number} tiRecNum
 * @param {Number} tiStartIdx
 * @param {JSFoundSet<db:/avanti/sa_customer_address>} toFoundset
 * @param {String} tsInsertOrUpdate
 *
 * @properties={typeid:24,uuid:"DD54A49E-87D9-4173-9794-2A5EEECB4815"}
 */
function FillCustomerAddressFoundset(taCols, tiRecNum, tiStartIdx, toFoundset, tsInsertOrUpdate){
	Validate(toFoundset, 'custaddr_address_name', tiRecNum, maCustAddressColNames[2], taCols[tiStartIdx], false, 64, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone1', tiRecNum, maCustAddressColNames[3], taCols[tiStartIdx+1], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone1_ext', tiRecNum, maCustAddressColNames[4], taCols[tiStartIdx+2], false, 10, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone2', tiRecNum, maCustAddressColNames[5], taCols[tiStartIdx+3], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone2_ext', tiRecNum, maCustAddressColNames[6], taCols[tiStartIdx+4], false, 10, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone3', tiRecNum, maCustAddressColNames[7], taCols[tiStartIdx+5], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_phone3_ext', tiRecNum, maCustAddressColNames[8], taCols[tiStartIdx+6], false, 10, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_fax', tiRecNum, maCustAddressColNames[9], taCols[tiStartIdx+7], false, 32, '', '', '', null, '')
	Validate(toFoundset, 'custaddr_fax_ext', tiRecNum, maCustAddressColNames[10], taCols[tiStartIdx+8], false, 10, '', '', '', null, '')
	Validate(toFoundset, 'shipmethod_id', tiRecNum, maCustAddressColNames[11], taCols[tiStartIdx+9], false, 10, '', '', '', GetShipMethodIDFromCode, '')
	Validate(toFoundset, 'taxgroup_id', tiRecNum, maCustAddressColNames[12], taCols[tiStartIdx+10], false, 10, '', '', '', GetTaxGroupIDFromCode, '')
	Validate(toFoundset, 'salesper_id', tiRecNum, maCustAddressColNames[13], taCols[tiStartIdx+11], false, 10, '', '', '', GetSalesrepIDFromCode, '')
	Validate(toFoundset, 'terr_id', tiRecNum, maCustAddressColNames[14], taCols[tiStartIdx+12], false, 10, '', '', '', GetTerritoryIDFromCode, '')
	Validate(toFoundset, 'custaddr_active', tiRecNum, maCustAddressColNames[15], taCols[tiStartIdx+13], true, 64, '', 'Y, N', '1,0', null, '')
	Validate(toFoundset, 'whse_id', tiRecNum, maCustAddressColNames[16], taCols[tiStartIdx+14], false, 0, '', '', '', GetWarehouseIDFromCode, '')
	
	if(taCols[tiStartIdx+15] != '' && taCols[tiStartIdx+15] != 'NO-VALUE'){
		//function SetCustContactIDFromFirstAndLast(toFoundset, colName, psFirstAndLast, psCustID){
		SetCustContactIDFromFirstAndLast(toFoundset, 'custcontact_id', taCols[tiStartIdx+15], globals.UUIDtoStringNew(toFoundset.cust_id), globals.UUIDtoStringNew(toFoundset.custaddr_id), toFoundset.custaddr_address_name, globals.UUIDtoStringNew(toFoundset.addr_id), toFoundset.custaddr_phone1)
	}
	
	// Default Shipper fields
	Validate(toFoundset, 'custaddr_use_default_shipper', tiRecNum, maCustAddressColNames[tiStartIdx+16], taCols[tiStartIdx+16], false, 0, '', 'Y, N', '1,0', null, '');
	Validate(toFoundset, 'cust_id_ship', tiRecNum, maCustAddressColNames[tiStartIdx+17], taCols[tiStartIdx+17], false, 0, '', '', '', GetCustIDFromCode, '');
	if(toFoundset.cust_id_ship){
		Validate(toFoundset, 'custcontact_id_ship', tiRecNum, maCustAddressColNames[tiStartIdx+18], taCols[tiStartIdx+18], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, toFoundset.cust_id_ship);
		Validate(toFoundset, 'custaddr_id_ship', tiRecNum, maCustAddressColNames[tiStartIdx+19], taCols[tiStartIdx+19], false, 0, '', '', '', GetCustAddrIDFromCode, toFoundset.cust_id_ship);
	}
	
	if(tsInsertOrUpdate=="I")
		toFoundset.sequence_nr = GetNextCustAddressSeqNum(globals.UUIDtoStringNew(toFoundset.cust_id))
}

/**
 * @properties={typeid:24,uuid:"830B2D32-24B5-48B4-AEEB-B93A8D7FA4EB"}
 */
function processCustomerContactRecords(){
	mfsCustContacts = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_contact')
	mfsContacts = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processCustomerContactRecord(msLines[i], i)		
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsCustContacts, -1)
	databaseManager.refreshRecordFromDatabase(mfsContacts, -1)
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_customer_contact');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_contact')
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"020E260E-5435-4978-AFB6-FB426A9CD583"}
 */
function processCustomerContactRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError("Column 'Customer Code' is blank. This is a required field.", tiRecNum, '')
		}
		
		// OLD FS CODE	
//		else if(mfsCustContacts.find() || mfsCustContacts.find()){
//			var tsFirstAndLast = Trim(taCols[3] + ' ' +	taCols[5]).replace('  ', ' ');
//			var tsCustID = GetCustIDFromCode(taCols[0], false, '')
//
//			//sl-5881
//			var contactTypeID = 'empty';
//			if(!isBlank(taCols[19])){
//				contactTypeID = GetContactTypeIDFromDescr(taCols[19], false, 'contact type');
//			}
//			
//			if(isBlank(tsCustID))
//				LogError("Column 'Customer Code' has an invalid value ('" + taCols[0] + "'). Please check Customer setup for valid values.", tiRecNum, '')
//			else if(isBlank(contactTypeID))
//				LogError("Column 'Contact Type' has an invalid value ('" + taCols[19] + "')", tiRecNum, '')
//			else{
//				var tiFoundRecs = 0
//				var tsCustContactID = GetCustContactIDFromFirstAndLast(tsFirstAndLast, tsCustID, false, '', contactTypeID)
//				if(tsCustContactID != ''){
//					mfsCustContacts.custcontact_id = tsCustContactID 
//					tiFoundRecs = mfsCustContacts.search() 
//				}
//				else{
//					// sl-3396 - if tsCustContactID wasnt found, then no search was done - have to do a clear to get out of find mode or we cant create new record
//					mfsCustContacts.clear()
//				}
//				
//				if(tiFoundRecs > 0){ // found it do an update
//					WriteCustContactToDB(taCols, 'U', tiRecNum)
//				}
//				else{ // do an insert
//					mfsCustContacts.newRecord()
//					mfsContacts.newRecord() 
//					mfsCustContacts.cust_id = tsCustID
//					mfsCustContacts.contact_id = mfsContacts.contact_id 
//					mfsContacts.custcontact_id = mfsCustContacts.custcontact_id 
//					WriteCustContactToDB(taCols, 'I', tiRecNum)
//				}
//			}
//		}

		// NEW SQL CODE
		else{
			var tsFirstAndLast = Trim(taCols[3] + ' ' +	taCols[5]).replace('  ', ' ');
			var tsCustID = GetCustIDFromCode(taCols[0], false, '')

			mfsCustContacts.newRecord()
			mfsContacts.newRecord()
			mfsCustContacts.cust_id = tsCustID

			//sl-5881
			var contactTypeID = 'empty';
			if(!isBlank(taCols[19])){
				contactTypeID = GetContactTypeIDFromDescr(taCols[19], false, 'contact type');
			}
			
			if(isBlank(tsCustID))
				LogError("Column 'Customer Code' has an invalid value ('" + taCols[0] + "'). Please check Customer setup for valid values.", tiRecNum, '')
			else if(isBlank(contactTypeID))
				LogError("Column 'Contact Type' has an invalid value ('" + taCols[19] + "')", tiRecNum, '')
			else{
				var sIDs = GetContactIDAndCustContactIDFromFirstAndLast(tsFirstAndLast, tsCustID, false, '', contactTypeID);
				
				if(sIDs.indexOf(',') > 0){ // UPDATE
					var aIDs = sIDs.split(',')
					var tsContactID = aIDs[0]
					var tsCustContactID = aIDs[1]
					
					mfsCustContacts.custcontact_id = tsCustContactID
					mfsCustContacts.contact_id = tsContactID
					mfsContacts.contact_id = tsContactID
					mfsContacts.custcontact_id = mfsCustContacts.custcontact_id
					
					WriteCustContactToDB(taCols, 'U', tiRecNum)
				}
				else{ // INSERT
					mfsCustContacts.contact_id = mfsContacts.contact_id 
					mfsContacts.custcontact_id = mfsCustContacts.custcontact_id
					
					WriteCustContactToDB(taCols, 'I', tiRecNum)
				}
				
				try{
					mfsCustContacts.getSelectedRecord().revertChanges();
					mfsContacts.getSelectedRecord().revertChanges();
				}catch (ex) {
					LogError('Revert changes customer contacts failed: ' + taCols[0] + ' it is associated to an order.', tiRecNum, '');
				}
				
				
				aCustContactNoValueCols = [];
				aContactNoValueCols = [];
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"619E30D3-BE36-40C4-8D02-0C193163152C"}
 */
function WriteCustContactToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
																																			 
		Validate(mfsCustContacts, 'custaddr_id', tiRecNum, maCustContactColNames[1], taCols[1], false, 0, '', '', '', GetCustAddrIDFromCode, mfsCustContacts.cust_id) 

		var uAddrID;
		var sAddrName;
		if(utils.hasRecords(mfsCustContacts.sa_customer_contact_to_sa_customer_address$custaddr_id)){
			uAddrID  = mfsCustContacts.sa_customer_contact_to_sa_customer_address$custaddr_id.addr_id;
			sAddrName  = mfsCustContacts.sa_customer_contact_to_sa_customer_address$custaddr_id.custaddr_address_name;
		}
		
		FillContactFoundset(taCols, tiRecNum, 2, mfsContacts, uAddrID, sAddrName); // sl-3422 - add addrID and addrName param
			
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
		}
		else{
			if(tsInsertOrUpdate=="I"){
				writeWithSQL(mfsCustContacts, [], [], tsInsertOrUpdate, tiRecNum);
				writeWithSQL(mfsContacts, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				writeWithSQL(mfsCustContacts, ['custcontact_id'], aCustContactNoValueCols, tsInsertOrUpdate, tiRecNum);
				writeWithSQL(mfsContacts, ['contact_id'], aContactNoValueCols, tsInsertOrUpdate, tiRecNum);
			}
			
			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * @properties={typeid:24,uuid:"4019C1EA-6F31-418C-A21D-AFBCDE44828B"}
 */
function processCustomerDivPlantRecords(){
	mfsCustDivPlants = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_division_plant');

	if(sAppendReplace == 'R'){
		_aCustsProcessed = [];
	}
	
	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processCustomerDivPlantRecord(msLines[i], i)		
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsCustDivPlants, -1);
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_division_plant');
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"BEB602F6-444C-42B5-BD54-3A28BD22F8F7"}
 */
function processCustomerDivPlantRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t');
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError("Column 'Customer Code' is blank. This is a required field.", tiRecNum, '');
		}
		else if(isBlank(taCols[1])){
			LogError("Column 'Division Code' is blank. This is a required field.", tiRecNum, '');
		}
		else if(isBlank(taCols[2])){
			LogError("Column 'Plant Code' is blank. This is a required field.", tiRecNum, '');
		}
		else{
			var tsCustID = GetCustIDFromCode(taCols[0], false, '');
			var tsDivID = GetDivisionIDFromCode(taCols[1], false, null);
			var tsPlantID = GetPlantIDFromCode(taCols[2], false, null);

			if(isBlank(tsCustID))
				LogError("Column 'Customer Code' has an invalid value ('" + taCols[0] + "'). Please check Customer setup for valid values.", tiRecNum, '')
			else if(isBlank(tsDivID))
				LogError("Column 'Division Code' has an invalid value ('" + taCols[1] + "'). Please check Division setup for valid values.", tiRecNum, '')
			else if(isBlank(tsPlantID))
				LogError("Column 'Plant Code' has an invalid value ('" + taCols[2] + "'). Please check Plant setup for valid values.", tiRecNum, '')
			else{
				if(sAppendReplace == 'R' && _aCustsProcessed.indexOf(tsCustID) == -1){
					scopes.avDB.RunSQL("delete from sa_division_plant where object_id = ?", true, [tsCustID]);
					_aCustsProcessed.push(tsCustID)
				}
				
				var bRecExists = false;
				if(sAppendReplace == 'A'){
					bRecExists = scopes.avDB.SQLExists("select * from sa_division_plant where object_id = ? and div_id = ? and plant_id = ?", true, [tsCustID, tsDivID, tsPlantID]); 
				}
				
				if(!bRecExists){
					mfsCustDivPlants.newRecord();

					mfsCustDivPlants.object_id = tsCustID;
					mfsCustDivPlants.object_type = 'CUST';
					mfsCustDivPlants.div_id = tsDivID; 
					mfsCustDivPlants.plant_id = tsPlantID;
					mfsCustDivPlants.created_date = application.getTimeStamp();
					
					WriteCustDivPlantToDB(taCols, 'I', tiRecNum)
					
					mfsCustDivPlants.getSelectedRecord().revertChanges()
				}
			}
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"B20BC4C9-88B5-4B82-8418-835838C65A67"}
 */
function WriteCustDivPlantToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		writeWithSQL(mfsCustDivPlants, [], [], tsInsertOrUpdate, tiRecNum);
		miNumInserts=miNumInserts+1;
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex;
		else{
			miNumRecsFailed += 1;
			LogError(ex.message, tiRecNum, tsInsertOrUpdate);
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"14EFF0FD-7C86-446F-99A6-BE740B22597F"}
 */
function processDeptRecords(){
	mfsDepts = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_department')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processDeptRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"9E84468E-2DA1-44C9-AA6B-029038FE87ED"}
 */
function processDeptRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column 'Department Code' is blank. This is a required field.", tiRecNum, '')
		else if(mfsDepts.find() || mfsDepts.find()){
			mfsDepts.dept_code = taCols[0]			
			if(mfsDepts.search() > 0){ // found it do an update
				WriteDeptToDB(taCols, 'U', tiRecNum)
			}
			else{ // do an insert
				mfsDepts.newRecord()
				mfsDepts.dept_code = taCols[0]
				WriteDeptToDB(taCols, 'I', tiRecNum)
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"D9155CFE-A801-4B88-B11A-9BA7664AB6B3"}
 */
function WriteDeptToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsDepts, 'dept_code', tiRecNum, maDeptColNames[0], taCols[0], true, 4, '', '', '', null, '')
		Validate(mfsDepts, 'plant_id', tiRecNum, maDeptColNames[1], taCols[1], true, 0, '', '', '', GetPlantIDFromCode, '')
		Validate(mfsDepts, 'dept_desc', tiRecNum, maDeptColNames[2], taCols[2], true, 50, '', '', '', null, '')
		Validate(mfsDepts, 'dept_shortdesc', tiRecNum, maDeptColNames[3], taCols[3], true, 12, '', '', '', null, '')
		Validate(mfsDepts, 'dept_schedule_flag', tiRecNum, maDeptColNames[4], taCols[4], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsDepts, 'dept_active', tiRecNum, maDeptColNames[5], taCols[5], true, 0, '', 'Y, N', '1,0', null, '')
		
		// removed map to shift as it doesnt appear to be used
//		Validate(mfsDepts, 'shift_id', tiRecNum, maDeptColNames[6], taCols[6], false, 0, '', '', '', GetShiftIDFromCode, '')
		if(tsInsertOrUpdate=="I")
			mfsDepts.sequence_nr = GetNextSeqNum('sys_department')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsDepts, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsDepts.getSelectedRecord())		
			CheckForSaveErrors(mfsDepts, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsDepts, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"79AF8BDD-1836-4684-AB26-9833D0791035"}
 */
function processCatRecords(){
	mfsCats = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_operation_category')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processCatRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"936D2981-FC41-4426-A1A1-0970526E65B3"}
 */
function processCatRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column 'Department Code' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[1]))
			LogError("Column 'Category Code' is blank. This is a required field.", tiRecNum, '')
		else{
			var tsDeptID = GetDeptIDFromCode(taCols[0], false)			
			if(isBlank(tsDeptID))
				LogError("'Department Code' is invalid. Please check Department setup for valid options.", tiRecNum, '')
			else if(mfsCats.find() || mfsCats.find()){				
				mfsCats.dept_id = tsDeptID			
				mfsCats.opcat_code = taCols[1]			
				if(mfsCats.search() > 0){ // found it do an update
					WriteCatToDB(taCols, 'U', tiRecNum)
				}
				else{ // do an insert
					mfsCats.newRecord()
					mfsCats.dept_id = tsDeptID			
					mfsCats.opcat_code = taCols[1]
					WriteCatToDB(taCols, 'I', tiRecNum)
				}
			}			
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"68890FD6-BE72-46D7-90F7-04C2167597E8"}
 */
function WriteCatToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate=="I"){
			Validate(mfsCats, 'opcat_code', tiRecNum, maCatColNames[1], taCols[1], true, 4, '', '', '', null, '')
			mfsCats.sequence_nr = GetNextSeqNum('sys_operation_category') 
		}
			
		Validate(mfsCats, 'opcat_desc', tiRecNum, maCatColNames[2], taCols[2], true, 50, '', '', '', null, '')
		Validate(mfsCats, 'opcat_shortdesc', tiRecNum, maCatColNames[3], taCols[3], true, 12, '', '', '', null, '')
		Validate(mfsCats, 'opcat_active', tiRecNum, maCatColNames[4], taCols[4], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsCats, 'opcat_gl_cost_of_sales', tiRecNum, maCatColNames[5], taCols[5], false, 50, '', '', '', null, '')
		Validate(mfsCats, 'opcat_gl_sales', tiRecNum, maCatColNames[6], taCols[6], false, 50, '', '', '', null, '')
		Validate(mfsCats, 'opcat_external_device_id', tiRecNum, maCatColNames[7], taCols[7], false, 100, '', '', '', null, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsCats, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsCats.getSelectedRecord())		
			CheckForSaveErrors(mfsCats, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsCats, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"8AFF2296-ED62-47F0-A900-7DE1D0BBC1C0"}
 */
function processOpRecords(){
	mfsOps = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_cost_centre')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processOpRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"CAF3519E-D67D-4131-A32C-E516D1F0547F"}
 */
function processOpRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column 'Department Code' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[1]))
			LogError("Column 'Category Code' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[2]))
			LogError("Column 'Operation Code' is blank. This is a required field.", tiRecNum, '')
		else{
			var tsDeptID = GetDeptIDFromCode(taCols[0], false)			
			if(isBlank(tsDeptID))
				LogError("'Department Code' is invalid. Please check Department setup for valid options.", tiRecNum, '')
			else{
				var tsCatID = GetCatIDFromCode(tsDeptID, taCols[1], false)			
				if(isBlank(tsCatID))
					LogError("'Category Code' is invalid. Please check Operation Category setup for valid options.", tiRecNum, '')
				else if(mfsOps.find() || mfsOps.find()){				
					mfsOps.dept_id = tsDeptID			
					mfsOps.opcat_id = tsCatID
					mfsOps.cc_op_code = taCols[2]
					if(mfsOps.search() > 0){ // found it do an update
						WriteOpToDB(taCols, 'U', tiRecNum)
					}
					else{ // do an insert
						mfsOps.newRecord()
						mfsOps.dept_id = tsDeptID			
						mfsOps.opcat_id = tsCatID			
						mfsOps.cc_op_code = taCols[2]
						WriteOpToDB(taCols, 'I', tiRecNum)
					}
				}			
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"2A2B1DCD-2EF4-44A7-B33B-8A08F79484C8"}
 */
function WriteOpToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate=="I"){
			Validate(mfsOps, 'cc_op_code', tiRecNum, maOpColNames[2], taCols[2], true, 4, '', '', '', null, '')
			mfsOps.sequence_nr = GetNextSeqNum('sys_cost_centre') 
		}
		Validate(mfsOps, 'cc_desc', tiRecNum, maOpColNames[3], taCols[3], true, 50, '', '', '', null, '')
		Validate(mfsOps, 'cc_short_desc', tiRecNum, maOpColNames[4], taCols[4], true, 12, '', '', '', null, '')
		Validate(mfsOps, 'cc_type', tiRecNum, maOpColNames[5], taCols[5], true, 0, '', 'L, M, P', '', null, '')//
		Validate(mfsOps, 'cc_base_rate', tiRecNum, maOpColNames[6], taCols[6], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_overhead', tiRecNum, maOpColNames[7], taCols[7], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_labour', tiRecNum, maOpColNames[8], taCols[8], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_chargable', tiRecNum, maOpColNames[9], taCols[9], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_qty_required', tiRecNum, maOpColNames[10], taCols[10], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_active', tiRecNum, maOpColNames[11], taCols[11], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'div_id', tiRecNum, maOpColNames[12], taCols[12], true, 0, '', '', '', GetDivisionIDFromCode, '')
		Validate(mfsOps, 'plant_id', tiRecNum, maOpColNames[13], taCols[13], true, 0, '', '', '', GetPlantIDFromCode, '')
		Validate(mfsOps, 'cc_total_rate', tiRecNum, maOpColNames[14], taCols[14], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_group_by_category', tiRecNum, maOpColNames[15], taCols[15], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_dflt_lag', tiRecNum, maOpColNames[16], taCols[16], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_dflt_lag_units', tiRecNum, maOpColNames[17], taCols[17], false, 0, '', 'M, H', 'm,h', null, '')
		Validate(mfsOps, 'cc_dflt_lag_type', tiRecNum, maOpColNames[18], taCols[18], false, 0, '', 'FF, SS, SF, FS', '', null, '')
		Validate(mfsOps, 'cc_dflt_succr_lag', tiRecNum, maOpColNames[19], taCols[19], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsOps, 'cc_dflt_succr_lag_units', tiRecNum, maOpColNames[20], taCols[20], false, 0, '', 'M, H', 'm,h', null, '')
		Validate(mfsOps, 'cc_dflt_succr_lag_type', tiRecNum, maOpColNames[21], taCols[21], false, 0, '', 'FF, SS, SF, FS', '', null, '')
		Validate(mfsOps, 'cc_qa_check_field_1', tiRecNum, maOpColNames[22], taCols[22], false, 150, '', '', '', null, '')
		Validate(mfsOps, 'cc_qa_check_field_2', tiRecNum, maOpColNames[23], taCols[23], false, 150, '', '', '', null, '')
		Validate(mfsOps, 'cc_qa_check_field_3', tiRecNum, maOpColNames[24], taCols[24], false, 150, '', '', '', null, '')
		Validate(mfsOps, 'cc_qa_check_field_1_enabled', tiRecNum, maOpColNames[25], taCols[25], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_qa_check_field_2_enabled', tiRecNum, maOpColNames[26], taCols[26], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_qa_check_field_3_enabled', tiRecNum, maOpColNames[27], taCols[27], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsOps, 'cc_jdf_type', tiRecNum, maOpColNames[28], taCols[28], false, 0, '', '', '', IsJDFTypeValid, taCols[5])
		Validate(mfsOps, 'cc_lag_uses_shift_time', tiRecNum, maOpColNames[29], taCols[29], true, 0, '', 'Passage of Time, Shift Time', '0,1', null, '');
		
		// vlLagBasedOn uses null
		if (mfsOps.cc_lag_uses_shift_time == 0) {
			mfsOps.cc_lag_uses_shift_time = null;
		}
		
		//Setting the default laf type, time and units when type is not set
		if(!mfsOps.getSelectedRecord().cc_dflt_lag_type){
			mfsOps.getSelectedRecord().cc_dflt_lag_type = 'FS';
			mfsOps.getSelectedRecord().cc_dflt_lag = 0;
			mfsOps.getSelectedRecord().cc_dflt_lag_units = 'm';
		}

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsOps, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsOps.getSelectedRecord())		
			CheckForSaveErrors(mfsOps, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsOps, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @properties={typeid:24,uuid:"42AE1EE1-9886-4E89-86B5-************"}
 */
function processIntItemRecords() {
    mfsIntItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
    tfsItemWarehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse');
    _bFifoUpdated = false;

    // fst rec is col headings
    for (var i = 1; i < msLines.length; i++) {
        if (msLines[i]) {
            processIntItemRecord(msLines[i], i);
        }
    }

    if (_bFifoUpdated) {
        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_item_fifo');
        _bFifoUpdated = false;
    }
}

/**
 * @properties={typeid:24,uuid:"8B9DE28C-8FBB-4144-94B6-65B22A01B7ED"}
 */
function processIntItemMultipleLocationRecords() {
	mfsIntItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
	tfsItemWarehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse');
	_bFifoUpdated = false;
	
    for (var i = 1; i < msLines.length; i++) {
        if (msLines[i]) {
            processIntItemMultipleLocationsRecord(msLines[i], i);
        }
    }
		
    if (_bFifoUpdated) {
        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_item_fifo');
        _bFifoUpdated = false;
    }
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"CFE61136-305C-4996-B5C1-38A24FBF3E7A"}
 */
function processIntItemRecord(tsRecord, tiRecNum) {
	var taCols = tsRecord.split('\t');
	
	if (taCols.length > 0) {
		var nUseItemRevision = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseItemRevisionField);
		var sItemCode = Trim(removeCommaQuotesItem(taCols[0])).toUpperCase();
		var sRevision = nUseItemRevision ? Trim(removeCommaQuotesItem(taCols[1])).toUpperCase() : null;
		var aIntItemsColNames = scopes.avUtils.arrayCopy(maIntItemsColNames); 
		
		// they wanted to insert Revision as the 2nd col, but that would all the inexes in WriteIntItemToDB would need to shift down.
		// so extract sRevision, then remove the index from taCols and aIntItemsColNames so nothing in WriteIntItemToDB has to change 
		taCols.splice(1, 1);
		aIntItemsColNames.splice(1, 1);

		if (isBlank(sItemCode)) {
			LogError("Column '" + maIntItemsColNames[0] + "' is blank. This is a required field.", tiRecNum, '');
		}
		else {
			var sSQL = "SELECT item_id FROM in_item WHERE org_id = ?";
			var aArgs = [globals.org_id, sItemCode];
			
			if (nUseItemRevision) {
				sSQL += " AND (item_code_no_revision = ? OR (item_code_no_revision IS NULL AND item_code = ?))";
				aArgs.push(sItemCode);
				
				if (sRevision != null && sRevision != "") {
					sSQL += " AND item_revision = ?";
					aArgs.push(sRevision);
				}
				else {
					sSQL += " AND ISNULL(item_revision, '') = ''";
				}
			}
			else {
				sSQL += " AND item_code = ?";
			}
			
			mfsIntItems = scopes.avDB.getFSFromSQL(sSQL, "in_item", aArgs);
			
			if (utils.hasRecords(mfsIntItems)) { // found it do an update
				WriteIntItemToDB(taCols, 'U', tiRecNum, sItemCode, aIntItemsColNames, nUseItemRevision, sRevision);
			}
			else { // do an insert
				mfsIntItems.newRecord();
				
				if (nUseItemRevision) {
					mfsIntItems.item_code_no_revision = sItemCode;

					if (sRevision != null && sRevision != "") {
						mfsIntItems.item_revision = sRevision;
						mfsIntItems.item_code = sItemCode + "-" + sRevision;
					}
					else {
						mfsIntItems.item_code = sItemCode;
					}
				}
				else {
					mfsIntItems.item_code = sItemCode;
				}
				
				WriteIntItemToDB(taCols, 'I', tiRecNum, sItemCode, aIntItemsColNames, nUseItemRevision, sRevision);
			}
		}
	}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"187134F4-131F-42F3-9FE2-31E84BAC7176"}
 */
function processIntItemMultipleLocationsRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	var oErr = new Object();
	
	try {
		if(taCols.length > 0){
			if(isBlank(taCols[0])) {
				oErr.message = "Column '" + maIntItemsBinColNames[0]+ "' is blank. This is a required field."
				throw oErr
			}
			else if(isBlank(taCols[1])) {
				oErr.message = "Column '" + maIntItemsBinColNames[1]+ "' is blank. This is a required field."
				throw oErr
			}
			else if(mfsIntItems.find() || mfsIntItems.find()){
				// escape the pound (case insensitive search)
				mfsIntItems.item_code = "\\" + Trim(removeCommaQuotes(taCols[0]))			
				if(mfsIntItems.search() > 0){ // found it do an update
					WriteIntItemMultipleLocationsToDB(taCols, tiRecNum)
				}
				else{ 
					oErr.message = "Item with code "  + taCols[0]+ " doesn't exist in the database."
					throw oErr
				}
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED"){
			throw ex;
		}
		else{
			miNumRecsFailed += 1;
			LogError(ex.message, tiRecNum, _sInsertOrUpdate); 
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"84722B71-7D26-4A95-91F4-1E9D07420DF1"}
 * @AllowToRunInFind
 */
function WriteIntItemMultipleLocationsToDB(taCols, tiRecNum) {
	try {
		var oErr = new Object();
		mbCurrRowErrored = false;
		_sInsertOrUpdate = null;

		var tsWarehouseID = Validate(null, 'Warehouse Code', tiRecNum, maIntItemsBinColNames[1], taCols[1], true, 0, '', '', '', GetWarehouseIDFromCode, '');
		
		if (tsWarehouseID) {
			var tsWarehouseLocId = Validate(null, 'Warehouse Bin Location', tiRecNum, maIntItemsBinColNames[2], taCols[2], false, 0, '', '', '', GetWarehouseLocationIDFromBinLocAndWareID, tsWarehouseID);
		}

		Validate(null, 'onhand_qty', tiRecNum, maIntItemsBinColNames[3], taCols[3], false, 0, 'NUM-NO-NEGATIVES', null, null, null);

		var tsWareEstUomID = Validate(null, 'Warehouse Estimating Units (code)', tiRecNum, maIntItemsBinColNames[4], taCols[4], false, 0, '', '', '', GetPriceUOMIDFromCode, '');
		var sReorderMethod = Validate(null, 'Reorder Method', tiRecNum, maIntItemsBinColNames[5], taCols[5], false, 0, '', 'Min/Max, EOQ/ROP', 'M,E', null, '');

		Validate(null, 'Min On Hand', tiRecNum, maIntItemsBinColNames[6], taCols[6], false, 0, 'NUM-NO-NEGATIVES', null, null, null);
		Validate(null, 'Max On Hand', tiRecNum, maIntItemsBinColNames[7], taCols[7], false, 0, 'NUM-NO-NEGATIVES', null, null, null);

		var sDefaultReceiptLoc = null;
		if (taCols[8] && tsWarehouseID) {
			sDefaultReceiptLoc = Validate(null, 'Default Receipt Location', tiRecNum, maIntItemsBinColNames[8], taCols[8], false, 0, '', '', '', GetWhseBinID, tsWarehouseID);
		}

		var sDefaultIssueLoc = null;
		if (taCols[9] && tsWarehouseID) {
			sDefaultIssueLoc = Validate(null, 'Default Issue Location', tiRecNum, maIntItemsBinColNames[9], taCols[9], false, 0, '', '', '', GetWhseBinID, tsWarehouseID);
		}

		var sProject = validateProject(taCols[10], mfsIntItems.itemtype_code);
		
		// sl-14916 - dont allow changes  to any qtys of there are transactions on this item
		if (tsWarehouseID && tsWarehouseLocId && scopes.avInv.areThereTransactionsOnThisItem(mfsIntItems.item_id, tsWarehouseID, tsWarehouseLocId, sProject)) {
			if (!isBlank(taCols[3])) {
				mbCurrRowErrored = true;
				LogError(scopes.avText.createParamMsg('avanti.lbl.cantChangeItemQtyIfThereAreTrans', [maIntItemsBinColNames[3]]), tiRecNum, '');
			}
		}

		if (mbCurrRowErrored) {
			miNumRecsFailed += 1;
		} else if (tsWarehouseID) {
			var nEnableBinLocations = scopes.avDB.getVal('in_warehouse', ['whse_id'], [tsWarehouseID], 'whse_enable_bin_locations');
			/**@type {Number} */
			var nOHQ = isBlank(taCols[3]) ? null : parseFloat(taCols[3]);

			if (tsWarehouseLocId) {
				if (nEnableBinLocations) {
					_sInsertOrUpdate = addWarehouseLocation(mfsIntItems.item_id, tsWarehouseID, tsWarehouseLocId, taCols[2], tsWareEstUomID, sReorderMethod, taCols[6], taCols[7], sDefaultReceiptLoc, sDefaultIssueLoc);
				} else {
					oErr.message = "The warehouse with code " + taCols[1] + " doesn't use a bin location.";
					throw oErr;
				}
			} else {
				if (nEnableBinLocations) {
					oErr.message = "The warehouse with code " + taCols[1] + " requires a bin location.";
					throw oErr;
				} else {
					addWarehouseForBinLocImport(mfsIntItems.item_id, tsWarehouseID, tsWareEstUomID, null, tiRecNum, sReorderMethod, taCols[6], taCols[7], sDefaultReceiptLoc, sDefaultIssueLoc);
				}
			}

			// we dont need to check here to see if there transactions, this is already done above
			// and the rec error is there are transactions and any qty changes, so it wouldnt get here
			if (nOHQ > 0 && tsWarehouseID && tsWarehouseLocId) {
				var sReference = scopes.avText.getLblMsg('InventoryImport') + ": " + _sFileName;
				
				scopes.avInv.createInventoryReceiptTransaction(tsWarehouseID, sReference, mfsIntItems.item_id, nOHQ, tsWarehouseLocId, nOHQ, null, null, null, sProject);
			}

			if (_sInsertOrUpdate == 'I') {
				miNumInserts = miNumInserts + 1;
			} else if (_sInsertOrUpdate == 'U') {
				miNumUpdates = miNumUpdates + 1;
			}
		}
	}
	// Validate() has its own error handling - this is here in case something gets by our validation and there is an error trying to write to a col
	catch (ex) {
		if (ex.name == "ERROR LIMIT REACHED") {
			throw ex
		} else {
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, _sInsertOrUpdate);
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 * @param {String} sItemCode
 * @param {Array<String>} aIntItemsColNames
 * @param {Number} [nUseItemRevision]
 * @param {String} [sRevision]
 *
 * @properties={typeid:24,uuid:"2AE65BAB-B9F1-4A1D-BF88-61DEC254C8AA"}
 * @AllowToRunInFind
 */
function WriteIntItemToDB(taCols, tsInsertOrUpdate, tiRecNum, sItemCode, aIntItemsColNames, nUseItemRevision, sRevision) {
	try {
		mbCurrRowErrored = false;
		_sInsertOrUpdate = tsInsertOrUpdate;

		if (tsInsertOrUpdate == 'I') {
			if (nUseItemRevision) {
				Validate(mfsIntItems, 'item_code_no_revision', tiRecNum, aIntItemsColNames[0], sItemCode, true, 36, '', '', '', null, ''); // sl-4806 - ucased item code
				
				if (sRevision) {
					if (scopes.avText.isAlphanumeric(sRevision)) {
						// revision uses maIntItemsColNames, not aIntItemsColNames, as revision has been spliced out of aIntItemsColNames
						Validate(mfsIntItems, 'item_revision', tiRecNum, maIntItemsColNames[1], sRevision, false, 10, '', '', '', null, ''); // sl-4806 - ucased item code
					}
					else {
						mbCurrRowErrored = true;
						LogError(i18n.getI18NMessage("i18n:avanti.lbl.RevisionMustBeaplhanumeric"), tiRecNum);
					}
				}
			}
			else {
				Validate(mfsIntItems, 'item_code', tiRecNum, aIntItemsColNames[0], sItemCode, true, 36, '', '', '', null, ''); // sl-4806 - ucased item code
			}
		}

		Validate(mfsIntItems, 'item_desc1', tiRecNum, aIntItemsColNames[1], taCols[1], true, 128, '', '', '', null, '');
		Validate(mfsIntItems, 'item_desc2', tiRecNum, aIntItemsColNames[2], taCols[2], false, 128, '', '', '', null, '');
		Validate(mfsIntItems, 'itemclass_id', tiRecNum, aIntItemsColNames[3], taCols[3], true, 0, '', '', '', GetIntClassIDFromCode, '');
		
		if (!isBlank(taCols[4])) {
			var sItemTypeBAK = mfsIntItems.itemtype_code;
			
			// call Validate() so it can do the transalation
			Validate(mfsIntItems, 'itemtype_code', tiRecNum, aIntItemsColNames[4], taCols[4], true, 0, '', 'Assembled Kit, Account Item, Build-to-Order Kit, Finished Goods, Non Stock, Product, Stock Item, Service Item', 'A,AC,B,F,N,P,S,SE', null, '');
			
			// sl-23640 - dont allow changing the item type if there are trans
			if (tsInsertOrUpdate == 'U' 
					&& scopes.avInv.areThereTransactionsOnThisItem(mfsIntItems.item_id)
					&& !scopes.avInv.canItemTypeBeChangedWhenTransactions(sItemTypeBAK, mfsIntItems.itemtype_code)) {
						
				mbCurrRowErrored = true;
				LogError(scopes.avText.getDlgMsg("ItemTypeCantBeChanged"), tiRecNum, aIntItemsColNames[4]);
				mfsIntItems.itemtype_code = sItemTypeBAK;
			}
		}
		
		Validate(mfsIntItems, 'item_status', tiRecNum, aIntItemsColNames[5], taCols[5], true, 0, '', 'Active, Inactive, Obsolete, To be Deleted', 'A,I,O,D', null, '')
		Validate(mfsIntItems, 'item_lot_item', tiRecNum, aIntItemsColNames[6], taCols[6], false, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsIntItems, 'item_color', tiRecNum, aIntItemsColNames[7], taCols[7], false, 36, '', '', '', null, '');
		Validate(mfsIntItems, 'item_decimal_places', tiRecNum, aIntItemsColNames[8], taCols[8], false, 0, 'INTEGER', '', '', null, '');
		Validate(mfsIntItems, 'item_avg_lead_time', tiRecNum, aIntItemsColNames[9], taCols[9], false, 0, 'INTEGER', '', '', null, '');
		Validate(mfsIntItems, 'item_salestax_option', tiRecNum, aIntItemsColNames[10], taCols[10], true, 0, '', 'Based on Customer, Taxable, Non-Taxable', 'C,T,N', null, '');
		Validate(mfsIntItems, 'taxgroup_id', tiRecNum, aIntItemsColNames[11], taCols[11], false, 0, '', '', '', GetTaxGroupIDFromCode, '');
		Validate(mfsIntItems, 'item_allow_commissions', tiRecNum, aIntItemsColNames[12], taCols[12], true, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsIntItems, 'item_allow_discounts', tiRecNum, aIntItemsColNames[13], taCols[13], true, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsIntItems, 'item_allow_backorders', tiRecNum, aIntItemsColNames[14], taCols[14], true, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsIntItems, 'item_dimension_heigth', tiRecNum, aIntItemsColNames[15], taCols[15], false, 0, 'NUMBER', '', '', null, '');
		Validate(mfsIntItems, 'item_dimension_length', tiRecNum, aIntItemsColNames[16], taCols[16], false, 0, 'NUMBER', '', '', null, '');
		Validate(mfsIntItems, 'item_dimension_width', tiRecNum, aIntItemsColNames[17], taCols[17], false, 0, 'NUMBER', '', '', null, '');
		Validate(mfsIntItems, 'item_max_weight', tiRecNum, aIntItemsColNames[18], taCols[18], false, 0, 'NUMBER', '', '', null, '');
		Validate(mfsIntItems, 'item_standard_uom_id', tiRecNum, aIntItemsColNames[19], taCols[19], true, 0, '', '', '', GetUOMIDFromCode, '');
		Validate(mfsIntItems, 'cust_id', tiRecNum, aIntItemsColNames[20], taCols[20], false, 0, '', '', '', GetIntCustIDFromCode, '');
		Validate(mfsIntItems, 'worktype_id', tiRecNum, aIntItemsColNames[21], taCols[21], false, 0, '', '', '', GetWorkTypeIDFromCode, '');
		Validate(mfsIntItems, 'item_cust_part_number', tiRecNum, aIntItemsColNames[22], taCols[22], false, 50, '', '', '', null, '');
		Validate(mfsIntItems, 'item_isbn_number', tiRecNum, aIntItemsColNames[23], taCols[23], false, 13, '', '', '', null, '');
		Validate(mfsIntItems, 'ingroup_id', tiRecNum, aIntItemsColNames[24], taCols[24], false, 0, '', '', '', GetIntGroupIDFromCode, '');

		var sProject = validateProject(taCols[77], mfsIntItems.itemtype_code, mfsIntItems.cust_id);
		
		if (!isBlank(taCols[29])) {
			// sl-14916 - dont allow changes to OHQ if there are transactions on this item
			if (tsInsertOrUpdate == 'U' && scopes.avInv.areThereTransactionsOnThisItem(mfsIntItems.item_id, null, null, sProject)) {
				mbCurrRowErrored = true;
				LogError(scopes.avText.createParamMsg('avanti.lbl.cantChangeItemQtyIfThereAreTrans', ['onhand_qty']), tiRecNum, '');
			} else {
				// sl-14528 - not updating all those qty cols anymore - left them in the template tho, so it doesnt mess up custs existing templates.
				// marked them in template as OBSOLETE. thats why there is a col gap here between 24 and 33. the only one that is still used is col
				// 29 for ohq - it isnt assigned directly to ite.ohq tho - that is done thru createInventoryReceiptTransaction() furthe down
				Validate(null, 'item_onhand_qty', tiRecNum, aIntItemsColNames[29], taCols[29], false, 0, 'NUMBER', '', '', null, '');
			}
		}

		Validate(mfsIntItems, 'item_spec', tiRecNum, aIntItemsColNames[33], taCols[33], false, 512, '', '', '', null, '');
		Validate(mfsIntItems, 'item_glacct_id_inventory_adj', tiRecNum, aIntItemsColNames[34], taCols[34], false, 50, '', '', '', GetGLAccountIDFromNumber_IntAdj, '');
		Validate(mfsIntItems, 'item_glacct_id_cost_of_sales', tiRecNum, aIntItemsColNames[35], taCols[35], false, 50, '', '', '', GetGLAccountSegmentIDFromNumber, '');
		Validate(mfsIntItems, 'item_glacct_id_inventory', tiRecNum, aIntItemsColNames[36], taCols[36], false, 50, '', '', '', GetGLAccountSegmentIDFromNumber, '');
		Validate(mfsIntItems, 'item_glacct_id_sales_returns', tiRecNum, aIntItemsColNames[37], taCols[37], false, 50, '', '', '', GetGLAccountSegmentIDFromNumber, '');
		Validate(mfsIntItems, 'item_glacct_id_sales', tiRecNum, aIntItemsColNames[38], taCols[38], false, 50, '', '', '', GetGLAccountSegmentIDFromNumber, '');

		/////////// IF CLASS IS SET TO INK, CYLINDER, PLATE OR PAPER THEN MORE INFO IS IN TXT FILE - VALIDATE
		if (mfsIntItems.itemclass_id) {
			var tsClassType = GetIDFromCode('', 'itemclass_id', globals.UUIDtoStringNew(mfsIntItems.itemclass_id), 'in_item_class', 'itemclass_type', false, '');

			// INK
			if (tsClassType == 'I') // THE 2ND PARAM BELOW (FIELDNAME) ISNT ACTUALLY USED IN Validate(), ITS JUST PROVIDED HERE FOR DOCUMENTATION
				var tsInkTypeID = Validate(null, 'ink_type_code', tiRecNum, aIntItemsColNames[39], taCols[39], tsInsertOrUpdate == "I", 0, '', '', '', GetInkTypeIDFromCode, '');

			// CYLINDER
			else if (tsClassType == 'CY') {
				Validate(null, 'cylinder_teeth', tiRecNum, aIntItemsColNames[40], taCols[40], tsInsertOrUpdate == "I", 0, 'INTEGER', '', '', null, '');
				Validate(null, 'cylinder_diameter', tiRecNum, aIntItemsColNames[41], taCols[41], tsInsertOrUpdate == "I", 0, 'NUMBER', '', '', null, '');
			}
			// PLATES
			else if (tsClassType == 'PL')
				Validate(null, 'plate_run_length', tiRecNum, aIntItemsColNames[42], taCols[42], tsInsertOrUpdate == "I", 0, 'INTEGER', '', '', null, '');

			// PAPER - SHEETS, ROLLS, TABS AND ENVELOPES - I'm not going to make a distinction between them
			else if (tsClassType == 'P' || tsClassType == 'R' || tsClassType == 'TA' || tsClassType == 'EN') {
				var tsGradeID = Validate(null, 'paper_grade_name', tiRecNum, aIntItemsColNames[43], taCols[43], tsInsertOrUpdate == "I", 50, '', '', '', GetPaperGradeIDFromName, '');
				Validate(null, 'paper_first_dim', tiRecNum, aIntItemsColNames[45], taCols[45], false, 0, 'NUMBER', '', '', null, '');
				Validate(null, 'paper_second_dim', tiRecNum, aIntItemsColNames[46], taCols[46], false, 0, 'NUMBER', '', '', null, '');
				Validate(null, 'paper_caliper', tiRecNum, aIntItemsColNames[47], taCols[47], false, 0, 'NUMBER', '', '', null, '');
				Validate(null, 'item_number_of_tabs', tiRecNum, aIntItemsColNames[48], taCols[48], false, 0, 'INTEGER', '', '', null, '');
				Validate(null, 'paper_roll_weight', tiRecNum, aIntItemsColNames[49], taCols[49], false, 0, 'NUMBER', '', '', null, '');
				Validate(null, 'paper_m_weight', tiRecNum, aIntItemsColNames[50], taCols[50], false, 0, 'NUMBER', '', '', null, '');
				Validate(null, 'paper_weight', tiRecNum, aIntItemsColNames[51], taCols[51], false, 0, 'NUMBER', '', '', null, '');
				if (!isBlank(tsGradeID) && !isBlank(taCols[44]) && !isBlank(taCols[51]));
				var tsBrandID = Validate(null, 'paper_brand_name', tiRecNum, aIntItemsColNames[44], taCols[44], tsInsertOrUpdate == "I", 50, '', '', '', GetPaperBrandIDFromNameAndWeightAndGrade, taCols[51], tsGradeID);
				Validate(null, 'substrate - brand name', tiRecNum, aIntItemsColNames[63], taCols[63], false, 64, '', '', '', null, '');
			}
		}
		///////////////

		// warehouse
		var tsWarehouseID = Validate(null, 'Warehouse Code', tiRecNum, aIntItemsColNames[52], taCols[52], tsInsertOrUpdate == "I", 0, '', '', '', GetWarehouseIDFromCode, '');

		//// its not really a PRIMARY SUPPLIER (theres no such thing) - its just a supplier
		var tsSupplierID = Validate(null, 'primary_supplier_code', tiRecNum, aIntItemsColNames[53], taCols[53], false, 0, '', '', '', GetPrimarySupplierIDFromCode, '');
		var tsUOMID_Supp_Purch = Validate(null, 'primary_supplier_uom_code', tiRecNum, aIntItemsColNames[54], taCols[54], !isBlank(tsSupplierID), 0, '', '', '', GetPrimarySupplierUOMIDFromCode, '');
		var tsUOMID_Supp_Cost = Validate(null, 'primary_supplier_purchase_cost_uom_code', tiRecNum, aIntItemsColNames[55], taCols[55], !isBlank(tsSupplierID), 0, '', '', '', GetPrimarySupplierCostUOMIDFromCode, '');
		Validate(null, 'primary_supplier_list_price', tiRecNum, aIntItemsColNames[56], taCols[56], false, 0, 'NUMBER', '', '', null, '');
		var tsLeadTimeFlag = Validate(null, 'primary_supplier_leadtime_flag', tiRecNum, aIntItemsColNames[59], taCols[59], !isBlank(tsSupplierID), 0, '', 'Automatic, Manual', 'A,M', null, '');
		Validate(null, 'primary_supplier_uom_conversion_factor', tiRecNum, aIntItemsColNames[62], taCols[62], false, 0, 'NUMBER', '', '', null, '');
		Validate(null, 'Landed Cost Costing Units Factor', tiRecNum, aIntItemsColNames[66], taCols[66], false, 0, 'NUMBER', '', '', null, '');

		// sl-2653 - added these 2
		Validate(null, 'Supplier Details Item Number', tiRecNum, aIntItemsColNames[67], taCols[67], false, 50, '', '', '', null, '');
		Validate(null, 'Supplier Details Item Description', tiRecNum, aIntItemsColNames[68], taCols[68], false, 50, '', '', '', null, '');
		////

		// sl-7630 - added these 2
		Validate(null, 'Supplier Details Reorder Multiple', tiRecNum, aIntItemsColNames[69], taCols[69], false, 0, 'NUMBER', '', '', null, '');
		Validate(null, 'Supplier Details Minimum Order Qty', tiRecNum, aIntItemsColNames[70], taCols[70], false, 0, 'NUMBER', '', '', null, '');
		//

		/**@type {Number} */
		var nWareAvgCost = Validate(null, 'Warehouse Average Cost', tiRecNum, aIntItemsColNames[71], taCols[71], false, 0, 'NUMBER', '', '', null, '');
		var sDefaultReceiptLoc = null;
		if (taCols[72] && tsWarehouseID) {
			sDefaultReceiptLoc = Validate(null, 'Default Receipt Location', tiRecNum, aIntItemsColNames[72], taCols[72], false, 0, '', '', '', GetWhseBinID, tsWarehouseID);
		}

		/// in_item_selling_uom
		Validate(null, 'selling_list_price', tiRecNum, aIntItemsColNames[57], taCols[57], false, 0, 'NUMBER', '', '', null, '');
		var tsUOMID_selling = Validate(null, 'selling_uom_code', tiRecNum, aIntItemsColNames[58], taCols[58], false, 0, '', '', '', GetSellingUOMIDFromCode, '');
		Validate(null, 'selling_uom_conversion_factor', tiRecNum, aIntItemsColNames[60], taCols[60], !isBlank(tsUOMID_selling), 0, 'NUMBER', '', '', null, '');
		var tsUOMID_price = Validate(null, 'pricing_uom_code', tiRecNum, aIntItemsColNames[61], taCols[61], !isBlank(tsUOMID_selling), 0, '', '', '', GetPriceUOMIDFromCode, '');
		Validate(null, 'Selling Units->Per Quantity Of', tiRecNum, aIntItemsColNames[64], taCols[64], false, 0, 'NUMBER', '', '', null, '');
		///
		// sl-2642 - add warehouse est uom
		var tsWareEstUomID = Validate(null, 'Warehouse Estimating Units (code)', tiRecNum, aIntItemsColNames[65], taCols[65], !isBlank(tsWarehouseID), 0, '', '', '', GetPriceUOMIDFromCode, '');

		if (!isBlank(taCols[73])) {
			Validate(mfsIntItems, 'item_no_bin_location', tiRecNum, aIntItemsColNames[73], taCols[73], false, 0, '', 'Y, N', '1,0', null, '');
		}

		Validate(mfsIntItems, 'item_creation_date', tiRecNum, aIntItemsColNames[74], taCols[74], false, 0, 'DATE', '', '', null, '');
		Validate(mfsIntItems, 'item_expiry_date', tiRecNum, aIntItemsColNames[75], taCols[75], false, 0, 'DATE', '', '', null, '');

		var sDefaultIssueLoc = null;
		if (taCols[76] && tsWarehouseID) {
			sDefaultIssueLoc = Validate(null, 'Default Issue Location', tiRecNum, aIntItemsColNames[76], taCols[76], false, 0, '', '', '', GetWhseBinID, tsWarehouseID);
		}

		var sHarmCode = Trim(taCols[78]); 
		var sManufactCountry = Trim(taCols[79]); 
		
		if (sHarmCode != NO_VALUE) {
			if (sHarmCode.length > 20) {
				mbCurrRowErrored = true;
				LogError("Harmonized Code cannot be greater than 20 characters", tiRecNum, '');
			}
			else {
				mfsIntItems.item_harmonized_code = sHarmCode;
			}
		}
		
		if (sManufactCountry != NO_VALUE) {
			if (sManufactCountry.length > 2) {
				mbCurrRowErrored = true;
				LogError("Manufacture Country cannot be greater than 2 characters", tiRecNum, '');
			}
			else {
				mfsIntItems.item_country_of_origin = sManufactCountry; 
			}
		}
		
		Validate(mfsIntItems, 'item_is_virtual', tiRecNum, aIntItemsColNames[80], taCols[80], false, 0, '', 'Y, N', '1,0', null, '');
		Validate(mfsIntItems, 'sys_org_language_uuid', tiRecNum, aIntItemsColNames[81], taCols[81], false, 0, '', '', '', GetLanguageID, '');
		
		if (mbCurrRowErrored) {
			miNumRecsFailed += 1;
			revertChanges(mfsIntItems, tsInsertOrUpdate);
		} 
		else {
			databaseManager.saveData(mfsIntItems.getSelectedRecord());
			CheckForSaveErrors(mfsIntItems, tsInsertOrUpdate);

			/////////// IF CLASS IS SET TO INK, CYLINDER, PLATE OR PAPER THEN SECONDARY TABLES ARE UPDATED
			if (tsClassType == 'I' && !isBlank(tsInkTypeID)) {
				addInk(mfsIntItems.item_id, tsInkTypeID);
			} else if (tsClassType == 'CY' && (!isBlank(taCols[40]) || !isBlank(taCols[41]))) {
				addCylinder(mfsIntItems.item_id, taCols[40], taCols[41]);
			} else if (tsClassType == 'PL' && !isBlank(taCols[42])) {
				addPlate(mfsIntItems.item_id, taCols[42]);
			} else if (tsClassType == 'P' || tsClassType == 'R' || tsClassType == 'TA' || tsClassType == 'EN') {
				addPaper(mfsIntItems.item_id, tsGradeID, tsBrandID, taCols);
			}
			///////////////

			/**@type {Number} */
			var nOHQ = isBlank(taCols[29]) ? null : parseFloat(taCols[29]);

			if (!isBlank(tsWarehouseID)) {
				var setPrimaryWhse = true;
				if (utils.hasRecords(mfsIntItems.in_item_to_in_item_warehouse$primary)) {
					setPrimaryWhse = false;
				}
				addWarehouse(globals.UUIDtoStringNew(mfsIntItems.item_id), tsWarehouseID, tsWareEstUomID, nWareAvgCost, sDefaultReceiptLoc, sDefaultIssueLoc, setPrimaryWhse);
			}

			if (!isBlank(tsSupplierID)) {
				addPrimarySupplier(tsSupplierID, tsUOMID_Supp_Purch, tsUOMID_Supp_Cost, taCols[56], tsLeadTimeFlag, taCols[62], taCols[66], taCols[67], taCols[68], taCols[69], taCols[70]);
			}

			if (!isBlank(tsUOMID_selling)) { // taCols[57] != '' || tsUOMID_price != '')
				addSellingUOM(mfsIntItems.item_id, taCols[57], tsUOMID_selling, taCols[60], tsUOMID_price, taCols[64]);
			}

			// we dont need to check here to see if there transactions, this is already done above
			// and the rec error is there are transactions and any qty changes, so it wouldnt get here
			if (nOHQ > 0 && tsWarehouseID) {
				var sReference = scopes.avText.getLblMsg('InventoryImport') + ": " + _sFileName;
				
				scopes.avInv.createInventoryReceiptTransaction(tsWarehouseID, sReference, mfsIntItems.item_id, nOHQ, null, null, null, null, null, sProject);
			}

			if (tsInsertOrUpdate == 'I') {
				miNumInserts = miNumInserts + 1;
			} else {
				miNumUpdates = miNumUpdates + 1;
			}
		}

	} catch (ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error
		// trying to write to a col
		if (ex.name == "ERROR LIMIT REACHED") {
			throw ex
		} else {
			miNumRecsFailed += 1
			revertChanges(mfsIntItems, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * @param {String} sProject
 * @param {String} sItemType
 * @param {UUID} [uCustID]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"BA701D0B-CFD9-4B10-B41E-B547ABFC215C"}
 */
function validateProject(sProject, sItemType, uCustID) {
	var sReturnProject = null;
	
	sProject = sProject ? Trim(sProject) : null; 
	sItemType = sItemType ? Trim(sItemType) : null; 
	
	if (sProject && sItemType == scopes.avUtils.ITEM_TYPE.FinishedGood && scopes.avInv.isProjectInventoryOn()) {
		if (scopes.avDB.SQLQuery("SELECT COUNT(*) FROM sa_customer_project WHERE org_id = ? AND custproj_desc = ?", null, [globals.org_id, sProject])) {
			sReturnProject = sProject;
		}
		else {
			/**@type {JSRecord<db:/avanti/sa_customer_project>} */
			var rProject = scopes.avDB.newRecord("sa_customer_project");

			if (rProject) {
				rProject.custproj_desc = sProject;
				rProject.custproj_active = 1;
				rProject.created_date = application.getTimeStamp();
				rProject.created_by_id = globals.avBase_employeeUUID; 
				
				if (uCustID) {
					rProject.cust_id = uCustID;
				}
				
				databaseManager.saveData(rProject);				
				sReturnProject = sProject;
			}
		}
	}
	
	return sReturnProject;
}

/**
 * @param {String} sBinLoc
 * @param {String} sWhseID
 *
 * @return
 * @properties={typeid:24,uuid:"6DBF90C8-3292-4516-A96B-360009C7C66B"}
 */
function GetWhseBinID(sBinLoc, sWhseID){
	return scopes.avDB.SQLQuery('select whseloc_id from in_warehouse_location where whse_id = ? and whseloc_bin_location = ?',
		true, [sWhseID, sBinLoc]);
}

/**
 * @param {JSFoundSet} fs
 * @param {String} tsInsertOrUpdate
 *
 * @properties={typeid:24,uuid:"2F6FE34B-30B2-492F-A045-A30FBEF981EC"}
 */
function revertChanges(fs, tsInsertOrUpdate){
	if(tsInsertOrUpdate=='I'){
		fs.deleteRecord()
	}
	else if(tsInsertOrUpdate=='U'){
		if(fs.getSelectedRecord())
			fs.getSelectedRecord().revertChanges()
	}	
}

/**
 * 
 * @param {UUID} psIntItemID
 * @param {String} psInkTypeID
 *
 * @properties={typeid:24,uuid:"3E81BBD2-F77B-47B5-B0B7-FBFF45882C74"}
 * @AllowToRunInFind
 */
function addInk(psIntItemID, psInkTypeID){
	/***@type {JSFoundSet<db:/avanti/in_item_ink>} */
	var tfsItemInk = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_ink')
	var tsInsertOrUpdate=''

	if(tfsItemInk.find() || tfsItemInk.find()){
		tsInsertOrUpdate = 'U'
		tfsItemInk.item_id = psIntItemID
		if(tfsItemInk.search() == 0){
			tfsItemInk.newRecord()
			tfsItemInk.item_id = psIntItemID
			tsInsertOrUpdate = 'I'
		}

		tfsItemInk.inktype_id = psInkTypeID

		databaseManager.saveData(tfsItemInk.getSelectedRecord())		
		CheckForSaveErrors(tfsItemInk, tsInsertOrUpdate)		
	}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} psSuppID
 * @param {String} psUOMID_Purch
 * @param {String} psUOMID_Cost
 * @param {Number|String} pnListPrice
 * @param {String} psLeadTimeFlag
 * @param {Number|String} pnConvFactor
 * @param {Number|String} costingUnitConvFactor
 * @param {String} itemNum
 * @param {String} itemDescr
 * @param {Number|String} nReorderMultiple
 * @param {Number|String} nMinOrderQty
 *
 * @properties={typeid:24,uuid:"4DF5E8A0-CF3D-4D0D-8DF4-4F17259A12CE"}
 */
function addPrimarySupplier(psSuppID, psUOMID_Purch, psUOMID_Cost, pnListPrice, psLeadTimeFlag, pnConvFactor, costingUnitConvFactor, itemNum, itemDescr, nReorderMultiple, nMinOrderQty){
	/***@type {JSFoundSet<db:/avanti/in_item_supplier>} */
	var tfsItemSupplier = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_supplier')
	var tsInsertOrUpdate='';
	var nOldSeqNum = null;

	if(tfsItemSupplier.find() || tfsItemSupplier.find()){
		tsInsertOrUpdate = 'U'
		tfsItemSupplier.item_id = mfsIntItems.item_id
		tfsItemSupplier.supplier_id = psSuppID
		if(tfsItemSupplier.search() == 0){
			tfsItemSupplier.newRecord()
			tfsItemSupplier.item_id = mfsIntItems.item_id
			tfsItemSupplier.supplier_id = psSuppID
			tsInsertOrUpdate = 'I'
		}
		else if(tfsItemSupplier.sequence_nr != 1){
			nOldSeqNum = tfsItemSupplier.sequence_nr;
		}
		
		tfsItemSupplier.sequence_nr = 1; 
		
		/// sl-5116 - although these are mandatory cols they may have used NO-VALUE if its an update
		if(psLeadTimeFlag){
			tfsItemSupplier.itemsupp_leadtime_flag=psLeadTimeFlag
		}
		if(psUOMID_Purch){
			tfsItemSupplier.uom_id = psUOMID_Purch
		}
		if(psUOMID_Cost){
			tfsItemSupplier.itemsupp_cost_uom_id = psUOMID_Cost
		}
		///

		if(!isBlank(pnListPrice)){
			tfsItemSupplier.itemsupp_list_price = pnListPrice
		}
		
        if (tfsItemSupplier.itemsupp_uom_conv_factor && (isBlank(pnConvFactor) || pnConvFactor == '0')) {
            tfsItemSupplier.itemsupp_uom_conv_factor = tfsItemSupplier.itemsupp_uom_conv_factor;
        } else {
            if(isBlank(pnConvFactor) || pnConvFactor=='0') {
                pnConvFactor = 1;
            }
            tfsItemSupplier.itemsupp_uom_conv_factor = pnConvFactor;
        }
		
		/// sl-4852
		if (tfsItemSupplier.itemsupp_cost_uom_factor && (isBlank(costingUnitConvFactor) || costingUnitConvFactor == '0')) {
			tfsItemSupplier.itemsupp_cost_uom_factor = tfsItemSupplier.itemsupp_cost_uom_factor;
		} else {
			if (isBlank(costingUnitConvFactor) || costingUnitConvFactor == '0') {
				costingUnitConvFactor = 1;
			}
			tfsItemSupplier.itemsupp_cost_uom_factor = costingUnitConvFactor;
		}
		///
		
		/// sl-2653
		if(!isBlank(itemNum)){
			tfsItemSupplier.itemsupp_part_number = itemNum 
		}
		
		if(!isBlank(itemDescr)){
			tfsItemSupplier.itemsupp_part_desc = itemDescr 
		}
		///
		
		/// sl-7630
		if(!isBlank(nReorderMultiple)){
			tfsItemSupplier.itemsupp_reorder_multiple = nReorderMultiple;
		}
		
		if(!isBlank(nMinOrderQty)){
			tfsItemSupplier.itemsupp_min_order_qty = nMinOrderQty;
		}
		///
		
		
		CalcLandedCost(tfsItemSupplier)
		
		databaseManager.saveData(tfsItemSupplier.getSelectedRecord())		
		CheckForSaveErrors(tfsItemSupplier, tsInsertOrUpdate)		
		
		if(tsInsertOrUpdate == 'I' || nOldSeqNum){
			pushItemSuppliersDown(tfsItemSupplier.item_id, tfsItemSupplier.supplier_id, nOldSeqNum);
		}
	}
}

/**
 * @param {UUID} sItemID
 * @param {UUID} sNewSuppID
 * @param {Number} nOnlyUpdateForSeqNumsLessThan
 *
 * @properties={typeid:24,uuid:"9392DF8F-C80B-4B27-A9BB-0C5340F7AD21"}
 */
function pushItemSuppliersDown(sItemID, sNewSuppID, nOnlyUpdateForSeqNumsLessThan){
	/**@type {JSFoundset<db:/avanti/in_item_supplier>} */
	var fsItemSupps = scopes.avDB.getFS('in_item_supplier', ['item_id'], [sItemID], "sequence_nr asc");
	
	if(fsItemSupps && fsItemSupps.getSize() > 1){
		for(var i=1; i<=fsItemSupps.getSize(); i++){
			var rItemSupp = fsItemSupps.getRecord(i);
			
			// dont incr supp just added
			if(rItemSupp.supplier_id != sNewSuppID){
				// 1. if this is an insert then just increment all other seqnums
				// 2. if changing an existing seqnum, then all seqnums less than the old seqnum of this rec have to be incremented
				if(!nOnlyUpdateForSeqNumsLessThan || rItemSupp.sequence_nr < nOnlyUpdateForSeqNumsLessThan){
					rItemSupp.sequence_nr++;
					databaseManager.saveData(rItemSupp);
				}
				else{
					break;
				}
			}
		}
	}
}

/** THIS CODE MODIFED FROM CODE TAKEN FROM processInventoryImport() IN utils_data_import.js
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} tfsItemSupplier
 *
 * @properties={typeid:24,uuid:"BD907164-C34B-462F-89FE-40089B9F6A23"}
 */
function CalcLandedCost(tfsItemSupplier){
	/*** @type 
	 {{
	 sItemID:String,
	 sPurchaseUomId:String,
	 sCostUomId:String,
     nCostUomConvFactor:Number,
     sCostUomType:String,
     nListPrice:Number,
 	 sDiscountType:String,
	 nDiscountRate:Number,
	 nDiscountAmt:Number,
	 sFreightInMethod:String,
	 nFobCost:Number,
 	 sFreightType:String,
	 nFreightRate:Number,
	 nFreightCost:Number,
	 nExchangeRateFactor:Number,
	 nExchangeCost:Number,
	 nDutyRate:Number,
	 nDutyCost:Number,
	 sUdf1Type:String,
	 nUdf1Rate:Number,
	 nUdf1Cost:Number,
	 sUdf2Type:String,
	 nUdf2Rate:Number,
	 nUdf2Cost:Number,
	 sUdf3Type:String,
	 nUdf3Rate:Number,
	 nUdf3Cost:Number,
	 sUdf4Type:String,
	 nUdf4Rate:Number,
	 nUdf4Cost:Number,
	 nLandedCost:Number,
	 nFobCostPerPurchaseUOM:Number
	 }} */
	var _oCost = new(Object);
	
	/** @type {{nFobCost:Number, nLandedCost:Number }} */
	var _oLandedCost = new Object;
	
	_oCost.sItemID = tfsItemSupplier.item_id
	_oCost.sPurchaseUomId = tfsItemSupplier.uom_id
	_oCost.sCostUomId = tfsItemSupplier.itemsupp_cost_uom_id
	
	// sl-4806
//	_oCost.nCostUomConvFactor = tfsItemSupplier.itemsupp_uom_conv_factor
	_oCost.nCostUomConvFactor = tfsItemSupplier.itemsupp_cost_uom_factor
	
	_oCost.nListPrice = tfsItemSupplier.itemsupp_list_price
	_oCost.sDiscountType = '%';
	_oCost.nDiscountRate = 0;
	_oCost.nDiscountAmt = 0;
	_oCost.sFreightInMethod = 'S';
	_oCost.nFobCost = tfsItemSupplier.itemsupp_list_price
	_oCost.sFreightType = '%';
	_oCost.nFreightRate = 0;
	_oCost.nFreightCost = 0;
	_oCost.nExchangeRateFactor = 1;
	_oCost.nDutyRate = 0;
	_oCost.nFobCostPerPurchaseUOM = 0;
	_oCost.sUdf1Type = null;
	_oCost.nUdf1Rate = null;
	_oCost.nUdf1Cost = 0;
	_oCost.sUdf2Type = null;
	_oCost.nUdf2Rate = null;
	_oCost.nUdf2Cost = 0;
	_oCost.sUdf3Type = null;
	_oCost.nUdf3Rate = null;
	_oCost.nUdf3Cost = 0;
	_oCost.sUdf4Type = null;
	_oCost.nUdf4Rate = null;
	_oCost.nUdf4Cost = 0;
		
	_oLandedCost = globals["avCalcs_landedCost"](_oCost)
	
      tfsItemSupplier.itemsupp_list_price=tfsItemSupplier.itemsupp_list_price
      tfsItemSupplier.itemsupp_fob_cost=_oLandedCost.nFobCost
      tfsItemSupplier.itemsupp_landed_cost=_oLandedCost.nLandedCost
      tfsItemSupplier.itemsupp_cost_uom_type=_oCost.sCostUomType
 
//      tfsItemSupplier.itemsupp_discount_type=_oCost.sDiscountType
//      tfsItemSupplier.itemsupp_discount_amount=_oLandedCost.nDiscountAmt
//      tfsItemSupplier.itemsupp_freight_in_method=_oCost.sFreightInMethod
//      tfsItemSupplier.itemsupp_freight_type=_oCost.sFreightType
//      tfsItemSupplier.itemsupp_freight_pct=_oCost.nFreightRate
//      tfsItemSupplier.itemsupp_freight_cost=_oLandedCost.nFreightCost
//      //tfsItemSupplier.curr_id=
//      tfsItemSupplier.itemsupp_exchange_rate_factor=_oCost.nExchangeRateFactor
//      tfsItemSupplier.itemsupp_exchange_cost=_oLandedCost.nExchangeCost
//      tfsItemSupplier.itemsupp_discount_pct=_oCost.nDiscountRate
//      tfsItemSupplier.itemsupp_uom_conv_factor=
}

/**
 * 
 * @param {UUID} psIntItemID
 * @param {Number|String} pnListPrice
 * @param {String} psUOMID
 * @param {Number|String} pnConversionFactor
 * @param {String} psPricingUOMID
 * @param {Number|String} pnPerQtyOf
 *
 * @properties={typeid:24,uuid:"F9079342-A7CD-43B4-B572-FE03BCF5CDCE"}
 * @AllowToRunInFind
 */
function addSellingUOM(psIntItemID, pnListPrice, psUOMID, pnConversionFactor, psPricingUOMID, pnPerQtyOf){
	/***@type {JSFoundSet<db:/avanti/in_item_selling_uom>} */
	var tfsSellingUOM = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_selling_uom')
	var tsInsertOrUpdate=''

	if(tfsSellingUOM.find() || tfsSellingUOM.find()){
		tsInsertOrUpdate='U'
		tfsSellingUOM.item_id = psIntItemID
		tfsSellingUOM.itemselluom_sell_uom_id = psUOMID
		if(tfsSellingUOM.search() == 0){ // didnt find it, do new record
			tfsSellingUOM.newRecord()
			tfsSellingUOM.item_id = psIntItemID
			tfsSellingUOM.itemselluom_sell_uom_id = psUOMID
			tfsSellingUOM.sequence_nr = GetNextItemSellingUOMSeqNum(globals.UUIDtoStringNew(psIntItemID))
		}
	}
	
	tfsSellingUOM.itemselluom_sell_conv_factor = pnConversionFactor
	if(psPricingUOMID){
		tfsSellingUOM.itemselluom_price_uom_id = psPricingUOMID
	}
	
	if(pnListPrice != null && pnListPrice != '')
		tfsSellingUOM.itemselluom_list_price = pnListPrice
		
	if(pnPerQtyOf != null && pnPerQtyOf != '')
		tfsSellingUOM.itemselluom_price_conv_factor = pnPerQtyOf

	databaseManager.saveData(tfsSellingUOM.getSelectedRecord())		
	CheckForSaveErrors(tfsSellingUOM, tsInsertOrUpdate)		
}

/**
 * SL-11712 - restored this old addWarehouse() prior to changes for 9747 as the new one was causing too many problems with the item import.
 * addWarehouse() had been changed for 9747 to work with the new multi loc import. i remaned the new version to addWarehouseForBinLocImport()
 * 
 * @AllowToRunInFind
 * 
 * @param {String} psIntItemID
 * @param {String} psWarehouseID
 * @param {String} tsWareEstUomID
 * @param {Number} nWareAvgCost
 * @param {String} sDefaultReceiptLoc
 * @param {String} sDefaultIssueLoc
 * @param {Boolean} setPrimaryWhse
 *
 * @properties={typeid:24,uuid:"55FF4FE5-976B-4C99-BD8D-A65A35EBD46E"}
 */
function addWarehouse(psIntItemID, psWarehouseID, tsWareEstUomID, nWareAvgCost, sDefaultReceiptLoc, sDefaultIssueLoc, setPrimaryWhse) {
	var tsInsertOrUpdate = '';

	if (tfsItemWarehouse.find() || tfsItemWarehouse.find()) {
		tsInsertOrUpdate = 'U';
		tfsItemWarehouse.item_id = psIntItemID;
		tfsItemWarehouse.whse_id = psWarehouseID;

		if (tfsItemWarehouse.search() == 0) {
			tfsItemWarehouse.newRecord();
			tfsItemWarehouse.item_id = psIntItemID;
			tfsItemWarehouse.whse_id = psWarehouseID;
			tsInsertOrUpdate = 'I';
		}
		
		if (!isBlank(psWarehouseID) && setPrimaryWhse) {
			tfsItemWarehouse.itemwhse_primary_whse = 1;
		}

		if (!isBlank(tsWareEstUomID)) {
			tfsItemWarehouse.itemwhse_estimate_cost_uom_id = tsWareEstUomID; // sl-2642 - add estimating uom
		}

		if (!isBlank(nWareAvgCost)) {
			tfsItemWarehouse.itemwhse_avg_cost = nWareAvgCost;
		}

		if (!isBlank(sDefaultReceiptLoc)) {
			tfsItemWarehouse.itemwhse_default_receipt_bin = sDefaultReceiptLoc;

			// sl-18510 - have to create a item ware location record for the default receipt location
			createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_receipt_bin);
		}

		if (!isBlank(sDefaultIssueLoc)) {
			tfsItemWarehouse.itemwhse_default_issue_bin = sDefaultIssueLoc;
			// Create a item warehouse location record for the default issue location
			createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_issue_bin);
		}

		databaseManager.saveData(tfsItemWarehouse.getSelectedRecord());
		CheckForSaveErrors(tfsItemWarehouse, tsInsertOrUpdate);
	}
}

/**
 * @param {UUID} uItemID
 * @param {UUID} uItemWarehouseID
 * @param {UUID} uBinID
 *
 * @properties={typeid:24,uuid:"C045E2DD-3709-4F6A-BC34-4DCE1ACCE4FD"}
 */
function createItemWarehouseLocationRecord(uItemID, uItemWarehouseID, uBinID) {
	var sSQL = "SELECT COUNT(*) \
				FROM in_item_warehouse_location \
				WHERE \
					org_id = ? \
					AND itemwhse_id = ? \
					AND whseloc_id = ?";
	var aArgs = [globals.org_id, uItemWarehouseID.toString(), uBinID.toString()];

	// no rec for this item/ware/bin - create one
	if (!scopes.avDB.SQLQuery(sSQL, null, aArgs)) {
		/**@type {JSRecord<db:/avanti/in_item_warehouse_location>} */
        var rItemWareBin = scopes.avDB.newRecord('in_item_warehouse_location');
        
        rItemWareBin.item_id = uItemID;
        rItemWareBin.itemwhse_id = uItemWarehouseID;
        rItemWareBin.whseloc_id = uBinID;
        rItemWareBin.itemwhseloc_onhand_qty = 0;
        rItemWareBin.itemwhseloc_unavailible_qty = 0;
        rItemWareBin.itemwhseloc_unusable_qty = 0;
        rItemWareBin.sequence_nr = scopes.avDB.GetNextSeqNumForAvantiDataTable('in_item_warehouse_location', 'itemwhse_id', rItemWareBin.itemwhse_id);
        
		databaseManager.saveData(rItemWareBin);		
	}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String|UUID} psIntItemID
 * @param {String} psWarehouseID
 * @param {String} tsWareEstUomID
 * @param {Number} nWareAvgCost
 * @param {String} tiRecNum
 * @param {String} [sReorderMethod]
 * @param {Number} [nMinOnHand]
 * @param {Number} [nMaxOnHand]
 * @param {String} sDefaultReceiptLoc
 * @param {String} sDefaultIssueLoc
 *
 * @properties={typeid:24,uuid:"97F4CE8A-833D-4CBE-849E-6001DA72A853"}
 */
function addWarehouseForBinLocImport(psIntItemID, psWarehouseID, tsWareEstUomID, nWareAvgCost, tiRecNum, sReorderMethod, nMinOnHand, nMaxOnHand, sDefaultReceiptLoc, sDefaultIssueLoc) {
	if (tfsItemWarehouse.find() || tfsItemWarehouse.find()) {
		_sInsertOrUpdate = 'U';
		tfsItemWarehouse.item_id = psIntItemID;
		tfsItemWarehouse.whse_id = psWarehouseID;

		if (tfsItemWarehouse.search() == 0) {
			tfsItemWarehouse.newRecord();

			tfsItemWarehouse.item_id = psIntItemID;
			tfsItemWarehouse.whse_id = psWarehouseID;

			_sInsertOrUpdate = 'I';
		}

		if (!isBlank(tsWareEstUomID)) {
			tfsItemWarehouse.itemwhse_estimate_cost_uom_id = tsWareEstUomID; // sl-2642 - add estimating uom
		}

		if (!isBlank(nWareAvgCost)) {
			tfsItemWarehouse.itemwhse_avg_cost = nWareAvgCost;
		}

		if (!isBlank(sReorderMethod)) {
			tfsItemWarehouse.itemwhse_reorder_method = sReorderMethod;
		}

		if (!isBlank(nMinOnHand)) {
			tfsItemWarehouse.itemwhse_min_qty = nMinOnHand;
		}

		if (!isBlank(nMaxOnHand)) {
			tfsItemWarehouse.itemwhse_max_qty = nMaxOnHand;
		}

		if (!isBlank(sDefaultReceiptLoc)) {
			tfsItemWarehouse.itemwhse_default_receipt_bin = sDefaultReceiptLoc;
			// Create a item ware location record for the default receipt location
			createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_receipt_bin);
		}
		
		if (!isBlank(sDefaultIssueLoc)) {
			tfsItemWarehouse.itemwhse_default_issue_bin = sDefaultIssueLoc;
			// Create a item warehouse location record for the default issue location
			createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_issue_bin);
		}

		databaseManager.saveData(tfsItemWarehouse);
		databaseManager.saveData(mfsIntItems);

		CheckForSaveErrors(mfsIntItems, _sInsertOrUpdate);
		CheckForSaveErrors(tfsItemWarehouse, _sInsertOrUpdate);
	}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String|UUID} psIntItemID
 * @param {String} psWarehouseID
 * @param {String} whseLocId
 * @param {String} binLoc
 * @param {String} [tsWareEstUomID]
 * @param {String} [sReorderMethod]
 * @param {Number} [nMinOnHand]
 * @param {Number} [nMaxOnHand]
 * @param {String} sDefaultReceiptLoc
 * @param {String} sDefaultIssueLoc
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C5D3C45F-5AE5-497E-9EB8-C0996B7B53D2"}
 */
function addWarehouseLocation(psIntItemID, psWarehouseID, whseLocId, binLoc, tsWareEstUomID, sReorderMethod, nMinOnHand, nMaxOnHand, sDefaultReceiptLoc, sDefaultIssueLoc) {
	var tsInsertOrUpdate = null;

	if (tfsItemWarehouse.find() || tfsItemWarehouse.find()) {
		tfsItemWarehouse.item_id = psIntItemID;
		tfsItemWarehouse.whse_id = psWarehouseID;

		if (tfsItemWarehouse.search() == 0) {
			tfsItemWarehouse.newRecord();

			tfsItemWarehouse.item_id = psIntItemID;
			tfsItemWarehouse.whse_id = psWarehouseID;
		}

		/**@type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
		var tfsItemWarehouseLoc = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_location');

		if (tfsItemWarehouseLoc.find() || tfsItemWarehouseLoc.find()) {
			tsInsertOrUpdate = 'U';
			tfsItemWarehouseLoc.item_id = psIntItemID;
			tfsItemWarehouseLoc.itemwhse_id = tfsItemWarehouse.itemwhse_id;
			tfsItemWarehouseLoc.whseloc_bin_location = binLoc;

			if (tfsItemWarehouseLoc.search() == 0) {
				tfsItemWarehouseLoc.newRecord();

				tfsItemWarehouseLoc.item_id = psIntItemID;
				tfsItemWarehouseLoc.itemwhse_id = tfsItemWarehouse.itemwhse_id;
				tfsItemWarehouseLoc.whseloc_bin_location = binLoc;
				tfsItemWarehouseLoc.whseloc_id = whseLocId;
				tfsItemWarehouseLoc.itemwhseloc_active = 1;

				tsInsertOrUpdate = 'I';
			}

			if (!isBlank(tsWareEstUomID)) {
				tfsItemWarehouse.itemwhse_estimate_cost_uom_id = tsWareEstUomID;
			}

			if (!isBlank(sReorderMethod)) {
				tfsItemWarehouse.itemwhse_reorder_method = sReorderMethod;
			}

			if (!isBlank(nMinOnHand)) {
				tfsItemWarehouse.itemwhse_min_qty = nMinOnHand;
			}

			if (!isBlank(nMaxOnHand)) {
				tfsItemWarehouse.itemwhse_max_qty = nMaxOnHand;
			}

			if (!isBlank(sDefaultReceiptLoc)) {
				tfsItemWarehouse.itemwhse_default_receipt_bin = sDefaultReceiptLoc;
				// Create a item ware location record for the default receipt location
				createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_receipt_bin);
			}

			if (!isBlank(sDefaultIssueLoc)) {
				tfsItemWarehouse.itemwhse_default_issue_bin = sDefaultIssueLoc;
				// Create a item warehouse location record for the default issue location
				createItemWarehouseLocationRecord(tfsItemWarehouse.item_id, tfsItemWarehouse.itemwhse_id, tfsItemWarehouse.itemwhse_default_issue_bin);
			}

			databaseManager.saveData(mfsIntItems);
			databaseManager.saveData(tfsItemWarehouse);
			databaseManager.saveData(tfsItemWarehouseLoc);

			CheckForSaveErrors(mfsIntItems, tsInsertOrUpdate);
			CheckForSaveErrors(tfsItemWarehouse, tsInsertOrUpdate);
			CheckForSaveErrors(tfsItemWarehouseLoc, tsInsertOrUpdate);
		}
	}

	return tsInsertOrUpdate;
}

/**
 * 
 * @param {UUID} psIntItemID
 * @param {Number|String} pnTeeth
 * @param {Number|String} pnDiameter
 *
 * @properties={typeid:24,uuid:"42BCB405-7312-4110-830C-5D9C79A3986F"}
 * @AllowToRunInFind
 */
function addCylinder(psIntItemID, pnTeeth, pnDiameter){
	/***@type {JSFoundSet<db:/avanti/in_item_cylinder>} */
	var tfsCylinder = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_cylinder')
	var tsInsertOrUpdate = ''

	if(tfsCylinder.find() || tfsCylinder.find()){
		tsInsertOrUpdate = 'U'
		tfsCylinder.item_id = psIntItemID
		if(tfsCylinder.search() == 0){ 
			tfsCylinder.newRecord()
			tfsCylinder.item_id = psIntItemID
			tsInsertOrUpdate = 'I'
		}

		if(!isBlank(pnTeeth)){
			tfsCylinder.itemcyl_teeth = pnTeeth
		}
		if(!isBlank(pnDiameter)){
			tfsCylinder.itemcyl_diameter = pnDiameter
		}
		
		databaseManager.saveData(tfsCylinder.getSelectedRecord())		
		CheckForSaveErrors(tfsCylinder, tsInsertOrUpdate)		
	}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {UUID} psIntItemID
 * @param {Number|String} pnRunLength
 *
 * @properties={typeid:24,uuid:"94C6590E-603E-407B-8331-63EF8FE51D37"}
 */
function addPlate(psIntItemID, pnRunLength){
	/***@type {JSFoundSet<db:/avanti/in_item_plate>} */
	var tfsPlate = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_plate')
	var tsInsertOrUpdate = ''

	if(tfsPlate.find() || tfsPlate.find()){
		tsInsertOrUpdate = 'U'
		tfsPlate.item_id = psIntItemID
		if(tfsPlate.search() == 0){ 
			tfsPlate.newRecord()
			tfsPlate.item_id = psIntItemID
			tsInsertOrUpdate = 'I'
		}

		tfsPlate.itemplate_run_length = pnRunLength
		
		databaseManager.saveData(tfsPlate.getSelectedRecord())		
		CheckForSaveErrors(tfsPlate, tsInsertOrUpdate)		
	}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {UUID} psIntItemID
 * @param {String} psGradeID
 * @param {String} psBrandID
 * @param {Array} paCols
 *
 * @properties={typeid:24,uuid:"B45682F5-F43C-477B-8F18-290646525F3C"}
 */
function addPaper(psIntItemID, psGradeID, psBrandID, paCols){
	/***@type {JSFoundSet<db:/avanti/in_item_paper>} */
	var tfsPaper = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_paper')
	var tsInsertOrUpdate = ''

	if(tfsPaper.find() || tfsPaper.find()){
		tsInsertOrUpdate = 'U'
		tfsPaper.item_id = psIntItemID
		if(tfsPaper.search() == 0){ 
			tfsPaper.newRecord()
			tfsPaper.item_id = psIntItemID
			tsInsertOrUpdate = 'I'
		}

		if(!isBlank(psGradeID)){
			tfsPaper.papergrade_id = psGradeID
		}
		if(!isBlank(psBrandID)){
			tfsPaper.paperbrand_id = psBrandID
		}
		
		if(!isBlank(paCols[45]))
			tfsPaper.paper_first_dim = paCols[45]
		if(!isBlank(paCols[46]))
			tfsPaper.paper_second_dim  = paCols[46]
		if(!isBlank(paCols[47]))
			// UI only shows 4 dec places, only save 4 to db
			tfsPaper.paper_caliper = scopes.avUtils.truncateNum(paCols[47], 4);			
		if(!isBlank(paCols[48]))
			tfsPaper.item_number_of_tabs = paCols[48]
		if(!isBlank(paCols[49]))
			tfsPaper.paper_roll_weight  = paCols[49]
		if(!isBlank(paCols[50]))
			tfsPaper.paper_m_weight  = paCols[50]
		if(!isBlank(paCols[51]))
			tfsPaper.paper_weight = paCols[51]
		if(!isBlank(paCols[63]))
			tfsPaper.paper_brand_name = paCols[63]
		
		databaseManager.saveData(tfsPaper.getSelectedRecord())		
		CheckForSaveErrors(tfsPaper, tsInsertOrUpdate)		
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"3761BD7D-103C-4AF1-9329-77A1752C6FAD"}
 */
function processPaperGradeRecords(){
	mfsPaperGrades = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_paper_grade')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processPaperGradeRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"DA5FD323-00B5-4906-826C-9D2C601AFB38"}
 */
function processPaperGradeRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column '" + maPaperGradeColNames[0] + "' is blank. This is a required field.", tiRecNum, '')
		else if(mfsPaperGrades.find() || mfsPaperGrades.find()){  // unique identifier for grade is grade name + size
			mfsPaperGrades.papergrade_name = taCols[0]			

			if(mfsPaperGrades.search() > 0){ // found it do an update
				WritePaperGradeToDB(taCols, 'U', tiRecNum)
			}
			else{ // do an insert
				mfsPaperGrades.newRecord()
				mfsPaperGrades.papergrade_name = taCols[0]
				WritePaperGradeToDB(taCols, 'I', tiRecNum)
			}
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"C57751D0-53DE-4EFA-A6ED-4A7465A39026"}
 */
function WritePaperGradeToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate=="I"){
			mfsPaperGrades.sequence_nr = GetNextSeqNum('in_paper_grade')
			Validate(mfsPaperGrades, 'papergrade_name', tiRecNum, maPaperGradeColNames[0], taCols[0], true, 50, '', '', '', null, '')
		}

		Validate(mfsPaperGrades, 'papergrade_active', tiRecNum, maPaperGradeColNames[1], taCols[1], true, 0, '', 'Y, N', '1,0', null, '')

		Validate(mfsPaperGrades, 'papergrade_weight_by', tiRecNum, maPaperGradeColNames[5], taCols[5], true, 0, '', 'Basis Weight, GSM', 'b,g', null, '')
		Validate(mfsPaperGrades, 'papergrade_is_roll', tiRecNum, maPaperGradeColNames[4], taCols[4], true, 0, '', 'Y, N', '1,0', null, '')
		
		// sl-2949 - width not mandatory for GSM
		Validate(mfsPaperGrades, 'papergrade_width', tiRecNum, maPaperGradeColNames[2], taCols[2], (mfsPaperGrades.papergrade_weight_by == 'b'), 0, 'NUMBER', '', '', null, '')
		// sl-2949 - length not mandatory for GSM or rolls
		Validate(mfsPaperGrades, 'papergrade_length', tiRecNum, maPaperGradeColNames[3], taCols[3], (mfsPaperGrades.papergrade_weight_by == 'b' && !mfsPaperGrades.papergrade_is_roll), 0, 'NUMBER', '', '', null, '')
		
		Validate(mfsPaperGrades, 'papergrade_charge_partial', tiRecNum, maPaperGradeColNames[6], taCols[6], true, 0, '', 'Y, N', '1,0', null, '')
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsPaperGrades, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsPaperGrades.getSelectedRecord())		
			CheckForSaveErrors(mfsPaperGrades, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsPaperGrades, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"F98999EB-C721-4953-A671-11E66E5DEC1B"}
 */
function processPaperBrandRecords(){
	mfsPaperBrands = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_paper_brand')
	mfsPaperGrades = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_paper_grade')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processPaperBrandRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * 
 * @param {Number} tiRecNum
 * @param {String} tsRecord
 *
 * @properties={typeid:24,uuid:"559D2433-2B7E-48E3-809C-F831AC1BB230"}
 */
function processPaperBrandRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column '" + maPaperBrandColNames[0] + "' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[1]))
			LogError("Column '" + maPaperBrandColNames[1] + "' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[4]))
			LogError("Column '" + maPaperBrandColNames[4] + "' is blank. This is a required field.", tiRecNum, '')
		else{
			var tsPapergrade_id = GetPaperGradeIDFromName(taCols[0], false, '')
			if(isBlank(tsPapergrade_id))
				LogError("Column '" + maPaperBrandColNames[0] + "' is invalid.", tiRecNum, '')
			else if(mfsPaperBrands.find() || mfsPaperBrands.find()){				
				mfsPaperBrands.papergrade_id = tsPapergrade_id 
				mfsPaperBrands.paperbrand_name=taCols[1]
				mfsPaperBrands.paperbrand_basis_weight=taCols[4]

				if(mfsPaperBrands.search() > 0) // found it - do an update
					WritePaperBrandToDB(taCols, 'U', tiRecNum)
				else{ // insert
					mfsPaperBrands.newRecord()
					mfsPaperBrands.papergrade_id = tsPapergrade_id 
					mfsPaperBrands.paperbrand_name=taCols[1]
					mfsPaperBrands.paperbrand_basis_weight=taCols[4]
					WritePaperBrandToDB(taCols, 'I', tiRecNum)
				}
			}				
		}
	}
}

/**
 * 
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"26639FA9-6FCC-46DB-BF87-C80D29A71152"}
 */
function WritePaperBrandToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate=="I"){
			mfsPaperBrands.sequence_nr = GetNextSeqNum('in_paper_brand')
			Validate(mfsPaperBrands, 'paperbrand_name', tiRecNum, maPaperBrandColNames[1], taCols[1], true, 50, '', '', '', null, '')
		}

//		Validate(mfsPaperBrands, 'paperbrand_active', tiRecNum, maPaperBrandColNames[2], taCols[2], true, 0, '', 'Y, N', '1,0', null, '')
//		Validate(mfsPaperBrands, 'paperbrand_paper_full_desc', tiRecNum, maPaperBrandColNames[3], taCols[3], false, 100, '', '', '', null, '')

		Validate(mfsPaperBrands, 'paperbrand_basis_weight', tiRecNum, maPaperBrandColNames[4], taCols[4], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsPaperBrands, 'paperbrand_finish_back', tiRecNum, maPaperBrandColNames[5], taCols[5], false, 0, '', 'None, Coated, Glossy, High Gloss, Matte, Satin, Semi Gloss', '', null, '')
		Validate(mfsPaperBrands, 'paperbrand_finish_front', tiRecNum, maPaperBrandColNames[6], taCols[6], false, 0, '', 'None, Coated, Glossy, High Gloss, Matte, Satin, Semi Gloss', '', null, '')
		
		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsPaperBrands, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsPaperBrands.getSelectedRecord())		
			CheckForSaveErrors(mfsPaperBrands, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsPaperBrands, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"CCE0E86F-1264-4B88-91EC-60FB5C44F7D3"}
 */
function processSalesRepRecords(){
	mfsSalesReps = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_sales_person')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processSalesRepRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"AA19FB6C-A929-4917-9FDE-F61A9D73B311"}
 */
function processSalesRepRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column '" + maSalesRepColNames[0] + "' is blank. This is a required field.", tiRecNum, '')
		else if(mfsSalesReps.find() || mfsSalesReps.find()){  
			mfsSalesReps.salesper_code = taCols[0]			

			if(mfsSalesReps.search() > 0) // found it do an update
				WriteSalesRepToDB(taCols, 'U', tiRecNum)
			else{ 
				mfsSalesReps.newRecord()
				mfsSalesReps.salesper_code = taCols[0]
				WriteSalesRepToDB(taCols, 'I', tiRecNum)
			}
		}
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"F2E7D3BF-ACCB-45BA-BC0B-50C8AA3E9AC6"}
 */
function WriteSalesRepToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsSalesReps, 'salesper_code', tiRecNum, maSalesRepColNames[0], taCols[0], true, 10, '', '', '', null, '')
		Validate(mfsSalesReps, 'terr_id', tiRecNum, maSalesRepColNames[1], taCols[1], true, 0, '', '', '', GetTerritoryIDFromCode, '')
		Validate(mfsSalesReps, 'salesper_name', tiRecNum, maSalesRepColNames[2], taCols[2], true, 50, '', '', '', null, '')
		Validate(mfsSalesReps, 'salesper_active', tiRecNum, maSalesRepColNames[3], taCols[3], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsSalesReps, 'salesper_comm', tiRecNum, maSalesRepColNames[4], taCols[4], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsSalesReps, 'salesper_comm_type', tiRecNum, maSalesRepColNames[5], taCols[5], true, 0, '', 'Added Value, Sales Price', '1,2', null, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsSalesReps, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsSalesReps.getSelectedRecord())		
			CheckForSaveErrors(mfsSalesReps, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsSalesReps, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"65DBFD4E-C935-41CC-97AB-A2B4409712FF"}
 */
function processEmpRecords(){
	mfsEmps = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processEmpRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"D875BBA9-3BF7-415B-A968-CC9480ECA750"}
 */
function processEmpRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column '" + maEmpColNames[0] + "' is blank. This is a required field.", tiRecNum, '')
		else if(mfsEmps.find() || mfsEmps.find()){  
			mfsEmps.empl_code  = taCols[0]			

			if(mfsEmps.search() > 0) // found it do an update
				WriteEmpToDB(taCols, 'U', tiRecNum)
			else{ 
				mfsEmps.newRecord()
				mfsEmps.empl_code  = taCols[0]			
				WriteEmpToDB(taCols, 'I', tiRecNum)
			}
		}
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"C453D962-7114-42FA-8644-521B461C5266"}
 */
function WriteEmpToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsEmps, 'empl_code', tiRecNum, maEmpColNames[0], taCols[0], true, 10, '', '', '', null, '')
		Validate(mfsEmps, 'empl_first_name', tiRecNum, maEmpColNames[1], taCols[1], true, 32, '', '', '', null, '')
		Validate(mfsEmps, 'empl_last_name', tiRecNum, maEmpColNames[2], taCols[2], false, 32, '', '', '', null, '')
		mfsEmps.empl_full_name = mfsEmps.empl_first_name + ' ' + mfsEmps.empl_last_name
		Validate(mfsEmps, 'emplclass_id', tiRecNum, maEmpColNames[3], taCols[3], false, 0, '', '', '', GetEmplClassIDFromCode, '')
		Validate(mfsEmps, 'empl_active', tiRecNum, maEmpColNames[4], taCols[4], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsEmps, 'empl_date_format', tiRecNum, maEmpColNames[5], taCols[5], false, 0, '', 'MMDDYYYY, DDMMYYYY', '1, 2', null, '')
		Validate(mfsEmps, 'salesper_id', tiRecNum, maEmpColNames[6], taCols[6], false, 0, '', '', '', GetSalesrepIDFromCode, '')
		Validate(mfsEmps, 'app_assignment_type_id', tiRecNum, maEmpColNames[7], taCols[7], false, 0, '', '', '', GetAppAssignTypeIDFromDescr, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsEmps, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsEmps.getSelectedRecord())		
			CheckForSaveErrors(mfsEmps, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsEmps, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @properties={typeid:24,uuid:"99089CFF-9B86-423D-9707-5204EBBEADD8"}
 */
function processBinRecords(){
	mfsBins = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_warehouse_location');

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processBinRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"445DFD43-0601-475E-8D6D-F34182781BFC"}
 */
function processBinRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0]))
			LogError("Column '" + maBinColNames[0] + "' is blank. This is a required field.", tiRecNum, '')
		else{
			var sWareID = GetWarehouseIDFromCode(taCols[0], false, null);
			
			if(!sWareID){
				LogError("Column '" + maBinColNames[0] + "' is invalid. Please check warehouse setup for valid values.", tiRecNum, '')
			}
			else{
				var aLevelCodeIDs = [];
				var sBinLoc = scopes.avDB.getVal('in_warehouse', ['whse_id'], [sWareID], 'whse_code');
				
				for(var i=1; i<=5; i++){
					if(isBlank(taCols[i])){
						aLevelCodeIDs.push(null);
					}
					else{
						var sSQL = "select whselevelcode_id, whselevelcode_code \
									from in_warehouse_level_code c \
									inner join in_warehouse_level l on l.whselevel_id = c.whselevel_id \
									where c.org_id = ? and c.whselevelcode_code = ? and l.whselevel_level = ? and l.whse_id = ? and l.whselevel_active = 1";
						
						var aArgs = [globals.org_id.toString() ,taCols[i], i, sWareID];
						var aLevelCodeIDAndCode = scopes.avDB.SQLQuery(sSQL, false, aArgs, null, null, 2);
						
						if(aLevelCodeIDAndCode && aLevelCodeIDAndCode.length == 2){
							var sLevelCodeID = aLevelCodeIDAndCode[0];
							var sLevelCode = aLevelCodeIDAndCode[1];
							
							aLevelCodeIDs.push(sLevelCodeID);
							sBinLoc += '-' + sLevelCode; 
						}
						else{
							LogError("Column '" + maBinColNames[i] + "' is invalid. Please check Warehouse Configuration for valid values.", tiRecNum, '')
							return;
						}
					}
				}
				
				mfsBins = getBinLocFS(sWareID, aLevelCodeIDs);
				
				if(mfsBins && mfsBins.getSize() > 0){  
					mfsBins.whseloc_bin_location = sBinLoc;
					WriteBinToDB(taCols, 'U', tiRecNum);
				}
				else{
					mfsBins.newRecord();
					
					mfsBins.whse_id = sWareID;
					mfsBins.whselevelcode1_id = aLevelCodeIDs[0];
					mfsBins.whselevelcode2_id = aLevelCodeIDs[1];
					mfsBins.whselevelcode3_id = aLevelCodeIDs[2];
					mfsBins.whselevelcode4_id = aLevelCodeIDs[3];
					mfsBins.whselevelcode5_id = aLevelCodeIDs[4];
					mfsBins.whseloc_bin_location = sBinLoc;
					
					WriteBinToDB(taCols, 'I', tiRecNum);
				}
			}
		}
	}
}

/**
 * @param {String} sWareID
 * @param {Array<String>} aLevelCodeIDs
 *
 * @return
 * @properties={typeid:24,uuid:"BD5BC16B-E590-4746-B486-AB709078DEE3"}
 */
function getBinLocFS(sWareID, aLevelCodeIDs){
	var sSQL = "select whseloc_id from in_warehouse_location where org_id = '" + globals.org_id.toString() 
		+ "' and whse_id = '" + sWareID + "'";
	
	for(var i=0; i<5; i++){
		var nLevelNum = i+1;
		
		if(isBlank(aLevelCodeIDs[i])){
			sSQL += " and whselevelcode" + nLevelNum + "_id is null";
		}
		else{
			sSQL += " and whselevelcode" + nLevelNum + "_id = '" + aLevelCodeIDs[i] + "'";
		}
	}
	
	return scopes.avDB.getFSFromSQL(sSQL, 'in_warehouse_location');
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"F696A8D8-6DAD-49F8-B8E4-64A98FBD4526"}
 */
function WriteBinToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsBins, 'whseloc_min_qty', tiRecNum, maBinColNames[6], taCols[6], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsBins, 'whseloc_max_qty', tiRecNum, maBinColNames[7], taCols[7], false, 0, 'NUMBER', '', '', null, '')
		Validate(mfsBins, 'whseloc_active', tiRecNum, maBinColNames[8], taCols[8], false, 0, '', 'Y, N', '1,0', null, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsBins, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsBins.getSelectedRecord())		
			CheckForSaveErrors(mfsBins, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsBins, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"66EA3517-8667-44AF-A14A-484EEB0F1E25"}
 */
function processActivityRecords(){
	mfsActivities = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'crm_activity')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processActivityRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"0AB2730E-8D20-4F2D-B837-2DEBE50F6C5B"}
 */
function processActivityRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		// I think an insert is all we need
		mfsActivities.newRecord()
		WriteActivityToDB(taCols, 'I', tiRecNum)
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"480968D7-55AE-44F8-A954-51D994E321F7"}
 */
function WriteActivityToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsActivities, 'activity_type', tiRecNum, maActivityColNames[0], taCols[0], true, 0, '', 
				 'Phone Call, Task, Appointment, Service Ticket, Collection, Other, Inquiry, Client Delivery, Client Lunch, Client Visit, Client Visit To Us, Conduct Research, Email, Meeting, Presentation, Samples', 
				 '', null, '')
		Validate(mfsActivities, 'activity_subject', tiRecNum, maActivityColNames[1], taCols[1], false, 500, '', '', '', null, '')
		Validate(mfsActivities, 'activity_regarding', tiRecNum, maActivityColNames[2], taCols[2], false, 0, '', 'Customer, Estimate, Sales Order, Job, Packing Slip, Lead', '', null, '')
		Validate(mfsActivities, 'activity_assignment_empl_id', tiRecNum, maActivityColNames[3], taCols[3], false, 0, '', '', '', GetEmpIDFromCode, '')
		Validate(mfsActivities, 'activity_customer_id', tiRecNum, maActivityColNames[4], taCols[4], false, 0, '', '', '', GetCustIDFromCode, '')
		Validate(mfsActivities, 'activity_contact_id', tiRecNum, maActivityColNames[5], taCols[5], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, mfsActivities.activity_customer_id )
		Validate(mfsActivities, 'activity_priority', tiRecNum, maActivityColNames[6], taCols[6], false, 0, '', 'Low, Medium, High', '', null, '')
		Validate(mfsActivities, 'activity_end_datetime', tiRecNum, maActivityColNames[7], taCols[7], false, 0, 'DATETIME', '', '', null, '')
		Validate(mfsActivities, 'activity_estimate_id', tiRecNum, maActivityColNames[8], taCols[8], false, 0, '', '', '', GetEstIDFromNumber, '')
		Validate(mfsActivities, 'activity_sales_order_id', tiRecNum, maActivityColNames[9], taCols[9], false, 0, '', '', '', GetSalesOrderIDFromNumber, '')
		Validate(mfsActivities, 'activity_job_id', tiRecNum, maActivityColNames[10], taCols[10], false, 0, '', '', '', GetJobIDFromNumber, '')
		Validate(mfsActivities, 'activity_packing_slip_id', tiRecNum, maActivityColNames[11], taCols[11], false, 0, '', '', '', GetPackingSlipIDFromNumber, '')
		Validate(mfsActivities, 'activity_status', tiRecNum, maActivityColNames[12], taCols[12], false, 0, '', 'Open, On Hold, Complete', '', null, '')
		Validate(mfsActivities, 'activity_due_datetime', tiRecNum, maActivityColNames[13], taCols[13], false, 0, 'DATETIME', '', '', null, '')
		Validate(mfsActivities, 'activity_objective_complete', tiRecNum, maActivityColNames[14], taCols[14], true, 0, '', 'Y, N', '1,0', null, '')
		
		if(taCols[15] != '')
			mfsActivities.activity_objective_id = CreateActivityObjective(taCols[15]) 

		if(taCols[16] != '' && mfsActivities.activity_customer_id != '' && mfsActivities.activity_customer_id != null)
			mfsActivities.note_id = CreateActivityNote(mfsActivities.activity_customer_id, taCols[16]) 

		Validate(mfsActivities, 'created_date', tiRecNum, maActivityColNames[17], taCols[17], false, 0, 'DATE', '', '', null, '')
		Validate(mfsActivities, 'created_by_id', tiRecNum, maActivityColNames[18], taCols[18], false, 0, '', '', '', GetEmpIDFromCode, '')
		Validate(mfsActivities, 'activity_lead_id', tiRecNum, maActivityColNames[19], taCols[19], false, 0, '', '', '', GetLeadIDFromNumber, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsActivities, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsActivities.getSelectedRecord())		
			CheckForSaveErrors(mfsActivities, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsActivities, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 *
 * @param {String} psObjective
 *
 * @return
 * @properties={typeid:24,uuid:"CDABB887-F22F-4717-8D21-E7C0B7292346"}
 */
function CreateActivityObjective(psObjective){
	/***@type {JSFoundSet<db:/avanti/crm_activity_objective>} */
	var tfsActivityObjective = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'crm_activity_objective')
	tfsActivityObjective.newRecord()
	tfsActivityObjective.objective = psObjective.substr(0,500)
	databaseManager.saveData(tfsActivityObjective.getSelectedRecord())
	CheckForSaveErrors(tfsActivityObjective, 'I')
	return tfsActivityObjective.crm_activity_objective_id 
}

/**
 *
 * @param {UUID} psCustID
 * @param {String} psNote
 *
 * @return
 * @properties={typeid:24,uuid:"0289C217-1037-42BE-A969-6A3804983D6D"}
 */
function CreateActivityNote(psCustID, psNote){
	/***@type {JSFoundSet<db:/avanti/sys_note>} */
	var tfsNote = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_note')
	tfsNote.newRecord()

	tfsNote.note_object_source_type = 'Customer'
	tfsNote.note_object_relation_type  = 'Customer'
	tfsNote.note_object_source_id = psCustID
	tfsNote.note_object_relation_id  = psCustID
	tfsNote.note_text = psNote
	
	databaseManager.saveData(tfsNote.getSelectedRecord())
	CheckForSaveErrors(tfsNote, 'I')
	return tfsNote.note_id  
}

/**
 * @param {String} psTitle
 *
 * @return
 * @properties={typeid:24,uuid:"355CE3BD-B58F-47CF-8CDA-F8499EA0BF59"}
 */
function CreateJobTitle(psTitle){
	/***@type {JSFoundSet<db:/avanti/sys_contact_job_title>} */
	var tfs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact_job_title')
	tfs.newRecord()

	tfs.jobtitle_desc = psTitle
	tfs.jobtitle_active = 1
	
	databaseManager.saveData(tfs.getSelectedRecord())
	CheckForSaveErrors(tfs, 'I')
	return tfs.jobtitle_id   
}

/**
 * @param {Object} psDept
 *
 * @return
 * @properties={typeid:24,uuid:"B655296A-3BDA-4370-AD18-AFB1442B82F3"}
 */
function CreateContactDept(psDept){
	/***@type {JSFoundSet<db:/avanti/sys_contact_department>} */
	var tfs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact_department')
	tfs.newRecord()

	tfs.contactdept_desc = psDept
	tfs.contactdept_active = 1
	
	databaseManager.saveData(tfs.getSelectedRecord())
	CheckForSaveErrors(tfs, 'I')
	return tfs.contactdept_id 
}

/**
 * @param {Object} psDiv
 *
 * @return
 * @properties={typeid:24,uuid:"D72BA70A-CB1E-4D73-A054-C6DE68D1DF03"}
 */
function CreateContactDiv(psDiv){
	/***@type {JSFoundSet<db:/avanti/sys_contact_division>} */
	var tfs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_contact_division')
	tfs.newRecord()

	tfs.contactdiv_desc = psDiv
	tfs.contactdiv_active = 1
	
	databaseManager.saveData(tfs.getSelectedRecord())
	CheckForSaveErrors(tfs, 'I')
	return tfs.contactdiv_id  
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"89776394-DD47-45D3-9414-FFD64752EEA1"}
 */
function processNoteRecords(){
	mfsNotes = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_note')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processNoteRecord(msLines[i], i)
		}

		databaseManager.refreshRecordFromDatabase(mfsNotes, -1)
		plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_note');
}

/**
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"6D095856-C40B-4B46-B1DE-8B48B1600B4D"}
 */
function processNoteRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		// I think an insert is all we need
		mfsNotes.newRecord()
		WriteNoteToDB(taCols, 'I', tiRecNum)
		mfsNotes.getSelectedRecord().revertChanges()
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"A79C36E6-358B-4653-92C7-8DEAC5B93E88"}
 */
function WriteNoteToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsNotes, 'note_creation_date', tiRecNum, maNoteColNames[0], taCols[0], false, 0, 'DATE', '', '', null, '')
		Validate(mfsNotes, 'note_creation_empl_id', tiRecNum, maNoteColNames[1], taCols[1], false, 0, '', '', '', GetEmpIDFromCode, '')
		Validate(mfsNotes, 'note_last_modified_date', tiRecNum, maNoteColNames[2], taCols[2], false, 0, 'DATE', '', '', null, '')
		Validate(mfsNotes, 'note_last_modified_empl_id', tiRecNum, maNoteColNames[3], taCols[3], false, 0, '', '', '', GetEmpIDFromCode, '')

		if(mfsNotes.note_last_modified_date == null && mfsNotes.note_creation_date != null){
			mfsNotes.note_last_modified_date = mfsNotes.note_creation_date;
		}
		if(mfsNotes.note_last_modified_empl_id == null && mfsNotes.note_creation_empl_id != null){
			mfsNotes.note_last_modified_empl_id = mfsNotes.note_creation_empl_id;
		}

		Validate(mfsNotes, 'note_text', tiRecNum, maNoteColNames[4], taCols[4], true, 0, '', '', '', null, '')
		Validate(mfsNotes, 'notetype_id', tiRecNum, maNoteColNames[5], taCols[5], false, 128, '', '', '', GetNoteTypeIDFromDescr, '')
		Validate(mfsNotes, 'note_title', tiRecNum, maNoteColNames[6], taCols[6], false, 128, '', '', '', null, '')
		Validate(mfsNotes, 'note_internal_only', tiRecNum, maNoteColNames[7], taCols[7], true, 0, '', 'Y, N', '1,0', null, '')

		if(msSubType == 'Activity' || msSubType == 'Customer'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_cust_code', taCols[8], true, 0, '', '', '', GetCustIDFromCode, '')
			mfsNotes.note_object_relation_id = mfsNotes.note_object_source_id
			mfsNotes.note_object_source_type = 'Customer'
			mfsNotes.note_object_relation_type = 'Customer'
		}
		else if(msSubType == 'Customer Contact'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_cust_code', taCols[8], true, 0, '', '', '', GetCustIDFromCode, '')
			Validate(mfsNotes, 'note_object_relation_id', tiRecNum, 'note_contact_first_and_last', taCols[9], true, 0, '', '', '', GetContactIDFromFirstAndLast, '')
			mfsNotes.note_object_source_type = 'Customer'
			mfsNotes.note_object_relation_type = 'Contact'
		}
		else if(msSubType == 'Estimate'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_cust_code', taCols[8], true, 0, '', '', '', GetCustIDFromCode, '')
			Validate(mfsNotes, 'note_object_relation_id', tiRecNum, 'note_estimate_number', taCols[9], true, 0, '', '', '', GetEstIDFromNumber, '')
			mfsNotes.note_object_source_type = 'Customer'
			mfsNotes.note_object_relation_type = 'Estimate'
		}
		else if(msSubType == 'Lead'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_cust_code', taCols[8], true, 0, '', '', '', GetCustIDFromCode, '')
			Validate(mfsNotes, 'note_object_relation_id', tiRecNum, 'note_lead_number', taCols[9], true, 0, '', '', '', GetLeadIDFromNumber, '')
			mfsNotes.note_object_source_type = 'Customer'
			mfsNotes.note_object_relation_type = 'Lead'
		}
		else if(msSubType == 'Sales Order'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_cust_code', taCols[8], true, 0, '', '', '', GetCustIDFromCode, '')
			Validate(mfsNotes, 'note_object_relation_id', tiRecNum, 'note_sales_order_number', taCols[9], true, 0, '', '', '', GetSalesOrderIDFromNumber, '')
			mfsNotes.note_object_source_type = 'Customer'
			mfsNotes.note_object_relation_type = 'Order'
		}
		else if(msSubType == 'Supplier'){
			Validate(mfsNotes, 'note_object_source_id', tiRecNum, 'note_supplier_code', taCols[8], true, 0, '', '', '', GetSupplierIDFromCode, '')
			mfsNotes.note_object_relation_id = mfsNotes.note_object_source_id
			mfsNotes.note_object_source_type = 'Supplier'
			mfsNotes.note_object_relation_type = 'Supplier'
		}

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
//			revertChanges(mfsNotes, tsInsertOrUpdate)
		}
		else{
//			databaseManager.saveData(mfsNotes.getSelectedRecord())		
//			CheckForSaveErrors(mfsNotes, tsInsertOrUpdate)

			writeWithSQL(mfsNotes, [], [], tsInsertOrUpdate, tiRecNum);
			
			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
//			revertChanges(mfsNotes, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"F4D0459C-5429-4DB2-9C8E-17BB51505522"}
 */
function processLeadRecords(){
	mfsLeads = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'crm_lead')
	var leadDocStream = null;
	/** @type {JSFoundSet<db:/avanti/sys_document_number> } */
	var rDocStream = scopes.avDB.getRec("sys_document_number", ["doctype_code", "docnum_active", "docnum_is_default"], ["LEAD", 1, 1]);
	if (rDocStream) {
		leadDocStream = rDocStream.docnum_stream;
	}

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processLeadRecord(msLines[i], i, leadDocStream)
		}
}

/**
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 * @param {String} leadDocStream
 *
 * @properties={typeid:24,uuid:"D2BCEA86-F25E-4849-82B8-D4B6175BBA47"}
 * @AllowToRunInFind
 */
function processLeadRecord(tsRecord, tiRecNum, leadDocStream){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			mfsLeads.newRecord()
			WriteLeadToDB(taCols, 'I', tiRecNum, leadDocStream)
		}
		else if(mfsLeads.find() || mfsLeads.find()){  
			mfsLeads.lead_number = taCols[0]			

			if(mfsLeads.search() > 0) // found it do an update
				WriteLeadToDB(taCols, 'U', tiRecNum, null)
			else 
				LogError("Column '" + maLeadColNames[0] + "' has an invalid value. Please check Lead seup for valid values.", tiRecNum, '')
		}
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 * @param {String} leadDocStream
 *
 * @properties={typeid:24,uuid:"56A2166F-46AE-49FA-A726-************"}
 */
function WriteLeadToDB(taCols, tsInsertOrUpdate, tiRecNum, leadDocStream){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate=='I')
			//HTP-lead nums arent setup in internal db - so i just incremented number
//			mfsLeads.lead_number = tiRecNum
			mfsLeads.lead_number = globals.GetNextDocumentNumber('LEAD',leadDocStream)

		Validate(mfsLeads, 'cust_id', tiRecNum, maLeadColNames[1], taCols[1], false, 0, '', '', '', GetCustIDFromCode, '')
		Validate(mfsLeads, 'lead_date', tiRecNum, maLeadColNames[2], taCols[2], false, 0, 'DATETIME', '', '', null, '')
		Validate(mfsLeads, 'contact_id', tiRecNum, maLeadColNames[3], taCols[3], false, 0, '', '', '', GetContactIDFromFirstAndLast, '')		
		Validate(mfsLeads, 'custcontact_id', tiRecNum, maLeadColNames[3], taCols[3], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, mfsLeads.cust_id)		
		Validate(mfsLeads, 'lead_date_to_rep', tiRecNum, maLeadColNames[4], taCols[4], false, 0, 'DATETIME', '', '', null, '')
		//Validate(mfsLeads, 'salesper_id', tiRecNum, maLeadColNames[5], taCols[5], false, 0, '', '', '', GetSalesrepIDFromCode, '')
		//Validate(mfsLeads, 'csr_id', tiRecNum, maLeadColNames[6], taCols[6], false, 0, '', '', '', GetCSRIDFromCode, '')
		Validate(mfsLeads, 'is_active', tiRecNum, maLeadColNames[7], taCols[7], true, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsLeads, 'ordh_id', tiRecNum, maLeadColNames[8], taCols[8], false, 0, '', '', '', GetEstIDFromNumber, '')
		Validate(mfsLeads, 'created_date_time', tiRecNum, maLeadColNames[10], taCols[10], false, 0, 'DATETIME', '', '', null, '')
		Validate(mfsLeads, 'created_by_id', tiRecNum, maLeadColNames[11], taCols[11], false, 0, '', '', '', GetEmpIDFromCode, '')
		Validate(mfsLeads, 'modified_date_time', tiRecNum, maLeadColNames[12], taCols[12], false, 0, 'DATETIME', '', '', null, '')
		Validate(mfsLeads, 'modified_by_id', tiRecNum, maLeadColNames[13], taCols[13], false, 0, '', '', '', GetEmpIDFromCode, '')
		Validate(mfsLeads, 'lead_owner_id', tiRecNum, maLeadColNames[16], taCols[16], false, 0, '', '', '', GetSalesrepIDFromCode, '')
		
		if (mfsLeads.lead_date == null) mfsLeads.lead_date = mfsLeads.created_date;
		
		if(!isBlank(taCols[17]))
			mfsLeads.crm_lead_source_id = GetLeadSourceIDFromDescr(taCols[17], false, '', true)
			
		if(!isBlank(taCols[9]))
			mfsLeads.crm_lead_stage_id = GetLeadStageIDFromDescr(taCols[9], false, '', true)
		
		if(!isBlank(taCols[14]))
			mfsLeads.crm_lead_rating_id = GetLeadRatingIDFromDescr(taCols[14], false, '', true)

		if(!isBlank(taCols[15]))
			mfsLeads.crm_lead_rating_after_id = GetLeadRatingIDFromDescr(taCols[15], false, '', true)

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsLeads, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsLeads.getSelectedRecord())		
			CheckForSaveErrors(mfsLeads, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsLeads, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @param {String} psTable
 * @param {String} psSetColName
 * @param {String} psSetColVal
 * @param {String} rsRetColName
 *
 * @return
 * @properties={typeid:24,uuid:"AA45436D-B319-4608-A7C8-0ED387A20D2B"}
 */
function AddNewRecord(psTable, psSetColName, psSetColVal, rsRetColName){
	var tfs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, psTable)
	tfs.newRecord()
	tfs.setDataProviderValue(psSetColName, psSetColVal)	
	databaseManager.saveData(tfs.getSelectedRecord())
	CheckForSaveErrors(tfs, 'I')
	return tfs.getDataProviderValue(rsRetColName)  
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"1411CD22-A309-487A-9E57-ECC971C24E32"}
 */
function processUDFAnswerRecords(){
	mfsUdfAnswers = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_values')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processUDFAnswerRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 *
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"050DC8C2-29FF-409D-BFAC-8A6969853EB5"}
 */
function processUDFAnswerRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])) //udf field descr
			LogError("Column '" & maUDFAnswerColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[2])) // cust code, etc
			LogError("Column '" & maUDFAnswerColNames[2] & "' is blank. This is a required field.", tiRecNum, '')
		else{
			if(mfsUdfAnswers.find() || mfsUdfAnswers.find()){
				var tsUdfCategory = TranslateUdfSubTypeToUdfCategory(msSubType)			
				var tsSys_udf_type_id = GetUdfTypeIDFromCategoryAndDescr(tsUdfCategory, taCols[0], false, '')
				mfsUdfAnswers.sys_udf_type_id = tsSys_udf_type_id
				
				if(mfsUdfAnswers.sys_udf_type_id == '' || mfsUdfAnswers.sys_udf_type_id == null)
					LogError(taCols[0] + " is not a valid UDF field.", tiRecNum, '')
				else{
					if(msSubType=='Customer')
						mfsUdfAnswers.unique_id = GetCustIDFromCode(taCols[2], false, '')				
					else if(msSubType=='Customer Contact'){
						var tsCustID = GetCustIDFromCode(taCols[2], false, '')			
						mfsUdfAnswers.unique_id = GetCustContactIDFromFirstAndLast(taCols[3], tsCustID, false, '')						
					}
					else if(msSubType=='Item')
						mfsUdfAnswers.unique_id = GetItemIDFromCode(taCols[2], false, '');
					else if(msSubType=='Suppliers')
						mfsUdfAnswers.unique_id = GetSupplierIDFromCode(taCols[2], false, '');

					if(mfsUdfAnswers.unique_id == '' || mfsUdfAnswers.unique_id == null){
						if(msSubType=='Customer Contact')
							LogError(taCols[3] + " (" + taCols[2] + ") is not a valid " + msSubType, tiRecNum, '')
						else
							LogError(taCols[2] + " is not a valid " + msSubType, tiRecNum, '')
							
					}
					else{
						var tsUnique_id = mfsUdfAnswers.unique_id
						if(mfsUdfAnswers.search() > 0) // found it do an update
							WriteUDFAnswerToDB(taCols, 'U', tiRecNum)
						else{
							mfsUdfAnswers.newRecord()
							mfsUdfAnswers.sys_udf_type_id = tsSys_udf_type_id
							mfsUdfAnswers.unique_id = tsUnique_id
							WriteUDFAnswerToDB(taCols, 'I', tiRecNum)
						}
					}
				}
			}
		}
	}
}

/**
 *
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"A969ED00-E3AE-4D9F-9065-EC47A6CE3B98"}
 */
function WriteUDFAnswerToDB(taCols, tsInsertOrUpdate, tiRecNum) {
    try {
        mbCurrRowErrored = false;
        _sInsertOrUpdate = tsInsertOrUpdate;

        if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'TEXT')
            Validate(mfsUdfAnswers, 'udf_answer_text', tiRecNum, maUDFAnswerColNames[1], taCols[1], true, 500, '', '', '', null, '');
        else if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'NUMBER')
            Validate(mfsUdfAnswers, 'udf_answer_number', tiRecNum, maUDFAnswerColNames[1], taCols[1], true, 0, 'NUMBER', '', '', null, '');
        else if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'INTEGER')
            Validate(mfsUdfAnswers, 'udf_answer_integer', tiRecNum, maUDFAnswerColNames[1], taCols[1], true, 0, 'INTEGER', '', '', null, '');
        else if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'DATETIME')
            Validate(mfsUdfAnswers, 'udf_answer_datetime', tiRecNum, maUDFAnswerColNames[1], taCols[1], true, 0, 'DATETIME', '', '', null, '');
        else if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'TABLE_VALUE')
            Validate(mfsUdfAnswers, 'udf_answer_table_value', tiRecNum, maUDFAnswerColNames[1], taCols[1], true, 0, '', '', '', GetUdfTableValueIDFromTypeIDAndAnswer, mfsUdfAnswers.sys_udf_type_id);
        else if (mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type == 'MULTI_SELECT') {
            var taTableAnswers = taCols[1].split(",");
            var tsTableValID;
            var tiTableValSeqNum;

            mfsUdfAnswers.udf_answer_multi_select = '';
            mfsUdfAnswers.udf_answer_multi_select_seq = '';

            for (var i = 0; i < taTableAnswers.length; i++) {
                tsTableValID = GetUdfTableValueIDFromTypeIDAndAnswer(taTableAnswers[i], globals.UUIDtoStringNew(mfsUdfAnswers.sys_udf_type_id), false, maUDFAnswerColNames[1]);
                if (isBlank(tsTableValID)) {
                    LogError("Table value #" + ( i + 1 ) + " in the multiple value list in udf_answer is invalid.", tiRecNum, '');
                    mbCurrRowErrored = true;
                }
                else {
                    tiTableValSeqNum = FSQuery('sys_udf_type_table_values', 'sys_udf_type_table_value_id', tsTableValID, 'sequence_nr');

                    if (i > 0) {
                        mfsUdfAnswers.udf_answer_multi_select += ', ';
                        mfsUdfAnswers.udf_answer_multi_select_seq += ', ';
                    }

                    mfsUdfAnswers.udf_answer_multi_select += tsTableValID;
                    mfsUdfAnswers.udf_answer_multi_select_seq += tiTableValSeqNum;
                }
            }
        }

        mfsUdfAnswers.udf_answer = taCols[1].substr(0, 500);

        if (mbCurrRowErrored) {
            miNumRecsFailed += 1;
            revertChanges(mfsUdfAnswers, tsInsertOrUpdate);
        }
        else {
            databaseManager.saveData(mfsUdfAnswers.getSelectedRecord());
            CheckForSaveErrors(mfsUdfAnswers, tsInsertOrUpdate);
            scopes.avUDF.handleChangedUDFValue(mfsUdfAnswers.sys_udf_type_id, mfsUdfAnswers.unique_id, mfsUdfAnswers.sys_udf_values_to_sys_udf_type.udf_field_type);

            if (tsInsertOrUpdate == 'I') {
                miNumInserts = miNumInserts + 1;
            }
            else {
                miNumUpdates = miNumUpdates + 1;
            }
        }

    }
    catch (ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error
        // trying to write to a col
        if (ex.name == "ERROR LIMIT REACHED") {
            throw ex;
        }
        else {
            miNumRecsFailed += 1;
            revertChanges(mfsUdfAnswers, tsInsertOrUpdate);
            LogError(ex.message, tiRecNum, tsInsertOrUpdate);
        }
    }
}

/**
 *
 * @param {String} vsUdfSubType
 *
 * @return
 * @properties={typeid:24,uuid:"3CC2D9F3-DADE-434B-837A-B0A9DB6D2EB5"}
 */
function TranslateUdfSubTypeToUdfCategory(vsUdfSubType){
	var tsRetVal = ''
		
	if(vsUdfSubType=="Customer")
		tsRetVal = "CUSTOMER"
	else if(vsUdfSubType=="Customer Contact")
		tsRetVal = "CONTACT"
	else if(vsUdfSubType=="Job")
		tsRetVal = "JOBS"
	else if(vsUdfSubType=="Sales Order")
		tsRetVal = "SALES"
	else if(vsUdfSubType=="Item")
		tsRetVal = "ITEM";
	else if(vsUdfSubType=="Suppliers")
		tsRetVal = "SUPPLIER";

	return tsRetVal
}

/**
 *
 * @param {String} vsTable
 * @param {String} vsSearchColName
 * @param {String} vsSearchColValue
 * @param {String} vsReturnColName
 *
 * @return
 * @properties={typeid:24,uuid:"ECB13F58-8437-43E6-9D25-E6DCBFCF906A"}
 * @AllowToRunInFind
 */
function FSQuery(vsTable, vsSearchColName, vsSearchColValue, vsReturnColName){
	/** @type {JSFoundSet} */
	var toFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, vsTable)
	var toRetVal = ''
		
	if(toFoundset.find() || toFoundset.find()){
		toFoundset.setDataProviderValue(vsSearchColName, vsSearchColValue)
		if(toFoundset.search() > 0)
			toRetVal = toFoundset.getDataProviderValue(vsReturnColName)
	}

	return toRetVal
	
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} vsTable
 * @param {String} vsSearchColName
 * @param {String} vsSearchColValue
 *
 * @return
 * @properties={typeid:24,uuid:"CEC1F20F-41E0-40F0-B326-6B38E42764D9"}
 */
function Exists(vsTable, vsSearchColName, vsSearchColValue){
	/** @type {JSFoundSet} */
	var toFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, vsTable)
	var tbRetVal = false
		
	if(toFoundset.find() || toFoundset.find()){
		toFoundset.setDataProviderValue(vsSearchColName, vsSearchColValue)
		if(toFoundset.search() > 0)
			tbRetVal = true
	}
	
	return tbRetVal
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"DF3F3A63-09B4-4B26-A3C6-EAF0336ADB80"}
 */
function processUDFQuestionRecords(){
	mfsUdfQuestions = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processUDFQuestionRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"83854B3C-DF28-4124-9936-3E136838C516"}
 */
function processUDFQuestionRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])) 
			LogError("Column '" & maUDFQuestionColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[1])) 
			LogError("Column '" & maUDFQuestionColNames[1] & "' is blank. This is a required field.", tiRecNum, '')
		else{
			if(mfsUdfQuestions.find() || mfsUdfQuestions.find()){
				mfsUdfQuestions.udf_code = taCols[0]
				mfsUdfQuestions.udf_field = taCols[1]
				
				if(mfsUdfQuestions.search() > 0) // found it do an update
					WriteUDFQuestionToDB(taCols, 'U', tiRecNum)
				else{
					mfsUdfQuestions.newRecord()
					mfsUdfQuestions.udf_code = taCols[0]
					mfsUdfQuestions.udf_field = taCols[1]
					WriteUDFQuestionToDB(taCols, 'I', tiRecNum)
				}
			}
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"2A7DB103-27C6-4451-9C6E-D8814ED87BDB"}
 */
function WriteUDFQuestionToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		if(tsInsertOrUpdate == "I"){
			Validate(mfsUdfQuestions, 'udf_code', tiRecNum, maUDFQuestionColNames[0], taCols[0], true, 0, 'JOBS, SALES, CUSTOMER, CONTACT', '', '', null, '')
			mfsUdfQuestions.sequence_nr = GetNextSeqNum('sys_udf_type')
		}
		
		Validate(mfsUdfQuestions, 'udf_field', tiRecNum, maUDFQuestionColNames[1], taCols[1], true, 500, '', '', '', null, '')
		Validate(mfsUdfQuestions, 'udf_field_type', tiRecNum, maUDFQuestionColNames[2], taCols[2], false, 0, 'TEXT, NUMBER, INTEGER, DATETIME, TABLE_VALUE, MULTI_SELECT', '', '', null, '')
		if(taCols[3] == '') // parentid
			mfsUdfQuestions.parent_sys_udf_type_id = null
		else
			Validate(mfsUdfQuestions, 'parent_sys_udf_type_id', tiRecNum, maUDFQuestionColNames[3], taCols[3], false, 0, '', '', '', GetUdfTypeIDFromFieldNameAndCategory, taCols[0])

			mfsUdfQuestions.depth = 1

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsUdfQuestions, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsUdfQuestions.getSelectedRecord())		
			CheckForSaveErrors(mfsUdfQuestions, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsUdfQuestions, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/*****************************************************************************************************************************
 * @properties={typeid:24,uuid:"50F9432D-7F61-4DC4-88D3-D890E253999B"}
 */
function processUDFQuestionTableOptionRecords(){
	mfsUdfQuestionTableOptions = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type_table_values')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processUDFQuestionTableOptionRecord(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"48A95D3F-4389-4068-A6BB-23585FE51DCD"}
 */
function processUDFQuestionTableOptionRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])) 
			LogError("Column '" & maUdfQuestionTableOptionColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
		else if(isBlank(taCols[1])) 
			LogError("Column '" & maUdfQuestionTableOptionColNames[1] & "' is blank. This is a required field.", tiRecNum, '')
		else{
			mfsUdfQuestionTableOptions.newRecord() // only inserts
			mfsUdfQuestionTableOptions.sys_udf_type_id = GetUdfTypeIDFromFieldNameAndCategory(taCols[1], taCols[0], false, '')
			
			if(mfsUdfQuestionTableOptions.sys_udf_type_id =='' || mfsUdfQuestionTableOptions.sys_udf_type_id==null) 
				LogError("Invalid " + maUdfQuestionTableOptionColNames[0] + " and " + maUdfQuestionTableOptionColNames[1] , tiRecNum, '')
			else
				WriteUDFQuestionTableOptionToDB(taCols, 'I', tiRecNum) 
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"DC6E9ADD-F8EA-415C-ADCB-7C3E965E3838"}
 */
function WriteUDFQuestionTableOptionToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {  
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate

		Validate(mfsUdfQuestionTableOptions, 'udf_table_value', tiRecNum, maUdfQuestionTableOptionColNames[2], taCols[2], true, 100, '', '', '', null, '')
		mfsUdfQuestionTableOptions.sequence_nr = GetNextSeqNum('sys_udf_type_table_values')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
			revertChanges(mfsUdfQuestionTableOptions, tsInsertOrUpdate)
		}
		else{
			databaseManager.saveData(mfsUdfQuestionTableOptions.getSelectedRecord())		
			CheckForSaveErrors(mfsUdfQuestionTableOptions, tsInsertOrUpdate)

			if(tsInsertOrUpdate=='I')
				miNumInserts=miNumInserts+1
			else
				miNumUpdates=miNumUpdates+1
		}
		
	} 
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(mfsUdfQuestionTableOptions, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/***************************************************************************************************************************** 
 * @param {Function} processRecFunction
 *
 * @properties={typeid:24,uuid:"95D3AA91-87C8-4895-BCFB-7C7BD5E465CE"}
 */
function processRecords(processRecFunction){
	// fst rec is col headings
	for(var i=1;i<msLines.length;i++)
		if(msLines[i]){
			processRecFunction(msLines[i], i)
		}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"C28F86A5-0A03-40C6-8500-218B9BF7141B"}
 */
function processSalesOrderRecord(tsRecord, tiRecNum){
	try {  
		mbCurrRowErrored=false
		var taCols = tsRecord.split('\t')
		var tsInsertOrUpdate
		
		if(taCols.length > 0){
			if(isBlank(taCols[0])) 
				LogError("Column '" & maColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
			else{
				/***@type {JSFoundSet<db:/avanti/sa_order>} */
				var tfsSalesOrder = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order')
	
				if(tfsSalesOrder.find() || tfsSalesOrder.find()){
					// SECONDARY TABLES
					/***@type {JSFoundSet<db:/avanti/sa_order_revision_header>} */
					var tfsSalesOrderRevision = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revision_header')
					/***@type {JSFoundSet<db:/avanti/sa_order_address>} */
					var tfsBillToOrderAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_address')
					/***@type {JSFoundSet<db:/avanti/sa_order_address>} */
					var tfsShipToOrderAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_address')
					/***@type {JSFoundSet<db:/avanti/sa_customer_address>} */
					var tfsCustAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_address')
					/***@type {JSFoundSet<db:/avanti/sys_address>} */
					var tfsBillToAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')
					/***@type {JSFoundSet<db:/avanti/sys_address>} */
					var tfsShipToAddress = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_address')
					/***@type {JSFoundSet<db:/avanti/sa_customer_contact>} */
					var tfsCustContact = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_contact')
					
					tfsSalesOrder.ordh_document_num = taCols[0]					
					if(tfsSalesOrder.search() > 0){
						tsInsertOrUpdate='U'
						
						// set foundsets used below to be relations of sa_order
						tfsBillToOrderAddress = tfsSalesOrder.sa_order_to_sa_order_address_billto
						tfsShipToOrderAddress = tfsSalesOrder.sa_order_to_sa_order_address_shipto
						tfsBillToAddress = tfsSalesOrder.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address 
						tfsShipToAddress = tfsSalesOrder.sa_order_to_sa_order_address_shipto.sa_order_address_to_sys_address
						
						if(tfsSalesOrderRevision.find() || tfsSalesOrderRevision.find()){
							tfsSalesOrderRevision.ordh_id = tfsSalesOrder.ordh_id
							tfsSalesOrderRevision.ordrevh_revision = 0
							tfsSalesOrderRevision.search()								
						}
					}
					else{ // didnt find rec - do an insert
						tsInsertOrUpdate='I'
						tfsSalesOrder.newRecord()
						tfsSalesOrder.ordh_document_num = taCols[0]
						
						tfsSalesOrderRevision.newRecord()
						tfsSalesOrderRevision.ordh_id = tfsSalesOrder.ordh_id 
						tfsSalesOrderRevision.ordrevh_revision = 0
						
						tfsBillToOrderAddress.newRecord()
						tfsBillToOrderAddress.ordh_id = tfsSalesOrder.ordh_id 
						tfsBillToOrderAddress.ordaddr_type = 'B'
						
						tfsShipToOrderAddress.newRecord()
						tfsShipToOrderAddress.ordh_id = tfsSalesOrder.ordh_id						
						tfsShipToOrderAddress.ordaddr_type = 'S'
					}
					
					///************************************* WRITE TO DB ****************************************///00

					//*** SA_ORDER TABLE
					if(tsInsertOrUpdate=='I'){
						tfsSalesOrder.ordh_document_type = 'ORD'
						tfsSalesOrder.ordh_current_revision = 0
						tfsSalesOrder.ordh_total_revisions = 0
						tfsSalesOrder.ordh_historical = 1;
						Validate(tfsSalesOrder, 'ordh_document_num', tiRecNum, maColNames[0], taCols[0], true, 16, '', '', '', null, '')
					}
					
					_sInsertOrUpdate = tsInsertOrUpdate
					Validate(tfsSalesOrder, 'cust_id', tiRecNum, maColNames[1], taCols[1], true, 0, '', '', '', GetCustIDFromCode, '')
					Validate(tfsSalesOrder, 'ordh_custaddr_id', tiRecNum, maColNames[83], taCols[83], false, 0, '', '', '', GetCustAddrIDFromCode, tfsSalesOrder.cust_id)
					Validate(tfsSalesOrder, 'ordh_shipto_custaddr_id', tiRecNum, maColNames[2], taCols[2], false, 0, '', '', '', GetCustAddrIDFromCode, tfsSalesOrder.cust_id)
					Validate(tfsSalesOrder, 'ordh_billto_custaddr_id', tiRecNum, maColNames[3], taCols[3], false, 0, '', '', '', GetCustAddrIDFromCode, tfsSalesOrder.cust_id)
					Validate(tfsSalesOrder, 'ordh_description', tiRecNum, maColNames[4], taCols[4], false, 128, '', '', '', null, '')
					Validate(tfsSalesOrder, 'curr_id', tiRecNum, maColNames[5], taCols[5], true, 16, '', '', '', GetCurrencyIDFromCode, '')
					Validate(tfsSalesOrder, 'salesterr_id', tiRecNum, maColNames[6], taCols[6], false, 0, '', '', '', GetTerritoryIDFromCode, '')
					Validate(tfsSalesOrder, 'ordh_order_date', tiRecNum, maColNames[7], taCols[7], false, 0, 'DATE', '', '', null, '')
					Validate(tfsSalesOrder, 'ordh_customer_po', tiRecNum, maColNames[8], taCols[8], false, 32, '', '', '', null, '')
					
					if(Trim(taCols[9]) != '')
						tfsSalesOrder.campaign_id = GetCampaignIDFromDescr(taCols[9], false, '', true)
					if(Trim(taCols[10]) != '')
						tfsSalesOrder.rush_id =  GetRushIDFromCode(taCols[10], false, '', true)
					if(Trim(taCols[11]) != '')
						tfsSalesOrder.custproj_id =  GetCustProjIDFromDescr(taCols[11], false, '', true)
					
					Validate(tfsSalesOrder, 'ordtype_id', tiRecNum, maColNames[12], taCols[12], false, 0, '', '', '', GetOrderTypeIDFromCode, '')
					Validate(tfsSalesOrder, 'ordh_salesper_id', tiRecNum, maColNames[13], taCols[13], false, 0, '', '', '', GetSalesrepIDFromCode, '')
					Validate(tfsSalesOrder, 'ordh_csr_empl_id', tiRecNum, maColNames[14], taCols[14], false, 0, '', '', '', GetCSRIDFromCode, '')
					Validate(tfsSalesOrder, 'plant_id', tiRecNum, maColNames[15], taCols[15], true, 0, '', '', '', GetPlantIDFromCode, '')
					
					//sl-2525 - infer div from plant
					if(tfsSalesOrder.plant_id){
						tfsSalesOrder.div_id = getDivFromPlant(tfsSalesOrder.plant_id)
					}
					
					Validate(tfsSalesOrder, 'ordh_document_stream', tiRecNum, maColNames[16], taCols[16], false, 0, '', '', '', ValidateOrderStream, '')
					Validate(tfsSalesOrder, 'shipmethod_id', tiRecNum, maColNames[17], taCols[17], false, 0, '', '', '', GetShipMethodIDFromCode, '')
					Validate(tfsSalesOrder, 'custcontact_id', tiRecNum, maColNames[18], taCols[18], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, tfsSalesOrder.cust_id)
					Validate(tfsSalesOrder, 'ordh_priority', tiRecNum, maColNames[19], taCols[19], false, 0, 'INTEGER', '', '', null, '')
					Validate(tfsSalesOrder, 'ordh_staging_location', tiRecNum, maColNames[20], taCols[20], false, 50, '', '', '', null, '')
					Validate(tfsSalesOrder, 'paymethod_id', tiRecNum, maColNames[21], taCols[21], false, 0, '', '', '', GetPayMethodIDFromCode, '')
					Validate(tfsSalesOrder, 'ordh_chargeback_code', tiRecNum, maColNames[22], taCols[22], false, 64, '', '', '', null, '')
						
					//*** SA_ORDER_REVISION TABLE
					Validate(tfsSalesOrderRevision, 'ordrevh_order_status', tiRecNum, maColNames[23], taCols[23], true, 0, '', '', '', ValidateOrderStatus, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_promise_date', tiRecNum, maColNames[24], taCols[24], false, 0, 'DATE', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_expected_date', tiRecNum, maColNames[25], taCols[25], false, 0, 'DATE', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_exchange_rate', tiRecNum, maColNames[26], taCols[26], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_order_qty', tiRecNum, maColNames[27], taCols[27], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_ship_qty', tiRecNum, maColNames[28], taCols[28], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_backorder_qty', tiRecNum, maColNames[29], taCols[29], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_subtotal_amount', tiRecNum, maColNames[30], taCols[30], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_markup_pct', tiRecNum, maColNames[31], taCols[31], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_markup_amount', tiRecNum, maColNames[32], taCols[32], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_shipping_amount', tiRecNum, maColNames[33], taCols[33], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_warehouse_amount', tiRecNum, maColNames[34], taCols[34], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_rush_amount', tiRecNum, maColNames[35], taCols[35], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_amount', tiRecNum, maColNames[36], taCols[36], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_onhold', tiRecNum, maColNames[37], taCols[37], true, 0, '', 'Y, N', '1, 0', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_taxes', tiRecNum, maColNames[38], taCols[38], false, 0, 'NUMBER', '', '', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_is_active', tiRecNum, maColNames[39], taCols[39], true, 0, '', 'Y, N', '1, 0', null, '')
					Validate(tfsSalesOrderRevision, 'ordrevh_total_commission', tiRecNum, maColNames[40], taCols[40], false, 0, 'NUMBER', '', '', null, '')
					
					//*** START - SA_ORDER_ADDRESS - BILLTO

					//* WAS ADDRESS CODE SUPPLIED?
					var tbFoundBillToAddrCode = false
					if(Trim(taCols[3]) != ''){
						if(tfsCustAddress.find() || tfsCustAddress.find()){
							tfsCustAddress.custaddr_code = taCols[3]
							if(tfsCustAddress.search() > 0){
								tbFoundBillToAddrCode=true

								// get the sa_order_address fields that are popped from cust_address
								tfsBillToOrderAddress.custaddr_id = tfsCustAddress.custaddr_id 
								tfsBillToOrderAddress.addr_id = tfsCustAddress.addr_id  
								tfsBillToOrderAddress.ordaddr_name = tfsCustAddress.custaddr_address_name 
								tfsBillToOrderAddress.ordaddr_phone1 = tfsCustAddress.custaddr_phone1  
								tfsBillToOrderAddress.ordaddr_phone1_ext = tfsCustAddress.custaddr_phone1_ext 
								tfsBillToOrderAddress.ordaddr_phone2 = tfsCustAddress.custaddr_phone2 
								tfsBillToOrderAddress.ordaddr_phone2_ext = tfsCustAddress.custaddr_phone2_ext 
								tfsBillToOrderAddress.ordaddr_phone3 = tfsCustAddress.custaddr_phone3 
								tfsBillToOrderAddress.ordaddr_phone3_ext = tfsCustAddress.custaddr_phone3_ext 
								tfsBillToOrderAddress.ordaddr_fax = tfsCustAddress.custaddr_fax 
								tfsBillToOrderAddress.ordaddr_fax_ext = tfsCustAddress.custaddr_fax_ext 
							}							
						}						
					}

					if(tbFoundBillToAddrCode==false){
						// fields that otherwise would be from sa_customer_address
						Validate(tfsBillToOrderAddress, 'ordaddr_name', tiRecNum, maColNames[41], taCols[41], false, 64, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone1', tiRecNum, maColNames[42], taCols[42], false, 32, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone1_ext', tiRecNum, maColNames[43], taCols[43], false, 10, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone2', tiRecNum, maColNames[44], taCols[44], false, 32, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone2_ext', tiRecNum, maColNames[45], taCols[45], false, 10, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone3', tiRecNum, maColNames[46], taCols[46], false, 32, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_phone3_ext', tiRecNum, maColNames[47], taCols[47], false, 10, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_fax', tiRecNum, maColNames[48], taCols[48], false, 32, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_fax_ext', tiRecNum, maColNames[49], taCols[49], false, 10, '', '', '', null, '')

						//* create sys_address entry for onetime address - if insert
						if(tsInsertOrUpdate=='I')
							tfsBillToAddress.newRecord()
						
						Validate(tfsBillToAddress, 'addr_address1', tiRecNum, maColNames[55], taCols[55], false, 64, '', '', '', null, '')
						Validate(tfsBillToAddress, 'addr_address2', tiRecNum, maColNames[56], taCols[56], false, 64, '', '', '', null, '')
						Validate(tfsBillToAddress, 'addr_address3', tiRecNum, maColNames[57], taCols[57], false, 64, '', '', '', null, '')
						Validate(tfsBillToAddress, 'addr_city', tiRecNum, maColNames[58], taCols[58], false, 64, '', '', '', null, '')
						Validate(tfsBillToAddress, 'stateprov_id', tiRecNum, maColNames[59], taCols[59], false, 0, '', '', '', GetStateIDFromCode, '')
						Validate(tfsBillToAddress, 'addr_postal', tiRecNum, maColNames[60], taCols[60], false, 20, '', '', '', null, '')
						Validate(tfsBillToAddress, 'country_id', tiRecNum, maColNames[61], taCols[61], false, 0, '', '', '', GetCountryIDFromCode, '')
						
						tfsBillToOrderAddress.addr_id = tfsBillToAddress.addr_id 
					}
						
					//* WAS CONTACT SUPPLIED?
					var tbFoundContact = false
					if(Trim(taCols[54]) != ''){
						if(tfsCustContact.find() || tfsCustContact.find()){							
							tfsCustContact.custcontact_id = GetCustContactIDFromFirstAndLast(taCols[54], globals.UUIDtoStringNew(tfsSalesOrder.cust_id), false, '')
							if(tfsCustContact.search() > 0){
								tbFoundContact=true
								
								// get the sa_order_address fields that are popped from contact
								tfsBillToOrderAddress.ordaddr_fax_home = tfsCustContact.sa_customer_contact_to_sys_contact.contact_home_fax
								tfsBillToOrderAddress.ordaddr_email_home = tfsCustContact.sa_customer_contact_to_sys_contact.contact_home_email
								tfsBillToOrderAddress.ordaddr_email_other = tfsCustContact.sa_customer_contact_to_sys_contact.contact_other_email 
								tfsBillToOrderAddress.ordaddr_email_business = tfsCustContact.sa_customer_contact_to_sys_contact.contact_business_email 
							}							
						}						
					}
					if(tbFoundContact==false){
						// fields that otherwise would be from sys_contact
						Validate(tfsBillToOrderAddress, 'ordaddr_fax_home', tiRecNum, maColNames[50], taCols[50], false, 32, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_email_business', tiRecNum, maColNames[51], taCols[51], false, 128, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_email_home', tiRecNum, maColNames[52], taCols[52], false, 128, '', '', '', null, '')
						Validate(tfsBillToOrderAddress, 'ordaddr_email_other', tiRecNum, maColNames[53], taCols[53], false, 128, '', '', '', null, '')
					}
					//*** FINISH - SA_ORDER_ADDRESS - BILLTO
					
					
					//*** START - SA_ORDER_ADDRESS - SHIPTO

					//* WAS ADDRESS CODE SUPPLIED?
					var tbFoundShipToAddrCode = false
					if(Trim(taCols[2]) != ''){
						if(tfsCustAddress.find() || tfsCustAddress.find()){
							tfsCustAddress.custaddr_code = taCols[2]
							if(tfsCustAddress.search() > 0){
								tbFoundShipToAddrCode=true

								// get the sa_order_address fields that are popped from cust_address
								tfsShipToOrderAddress.custaddr_id = tfsCustAddress.custaddr_id 
								tfsShipToOrderAddress.addr_id = tfsCustAddress.addr_id  
								tfsShipToOrderAddress.ordaddr_name = tfsCustAddress.custaddr_address_name 
								tfsShipToOrderAddress.ordaddr_phone1 = tfsCustAddress.custaddr_phone1  
								tfsShipToOrderAddress.ordaddr_phone1_ext = tfsCustAddress.custaddr_phone1_ext 
								tfsShipToOrderAddress.ordaddr_phone2 = tfsCustAddress.custaddr_phone2 
								tfsShipToOrderAddress.ordaddr_phone2_ext = tfsCustAddress.custaddr_phone2_ext 
								tfsShipToOrderAddress.ordaddr_phone3 = tfsCustAddress.custaddr_phone3 
								tfsShipToOrderAddress.ordaddr_phone3_ext = tfsCustAddress.custaddr_phone3_ext 
								tfsShipToOrderAddress.ordaddr_fax = tfsCustAddress.custaddr_fax 
								tfsShipToOrderAddress.ordaddr_fax_ext = tfsCustAddress.custaddr_fax_ext 
							}							
						}						
					}

					if(tbFoundShipToAddrCode==false){
						// fields that otherwise would be from sa_customer_address
						Validate(tfsShipToOrderAddress, 'ordaddr_name', tiRecNum, maColNames[62], taCols[62], false, 64, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone1', tiRecNum, maColNames[63], taCols[63], false, 32, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone1_ext', tiRecNum, maColNames[64], taCols[64], false, 10, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone2', tiRecNum, maColNames[65], taCols[65], false, 32, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone2_ext', tiRecNum, maColNames[66], taCols[66], false, 10, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone3', tiRecNum, maColNames[67], taCols[67], false, 32, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_phone3_ext', tiRecNum, maColNames[68], taCols[68], false, 10, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_fax', tiRecNum, maColNames[69], taCols[69], false, 32, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_fax_ext', tiRecNum, maColNames[70], taCols[70], false, 10, '', '', '', null, '')

						//* create sys_address entry for onetime address - if insert
						if(tsInsertOrUpdate=='I')
							tfsShipToAddress.newRecord()
						
						Validate(tfsShipToAddress, 'addr_address1', tiRecNum, maColNames[76], taCols[76], false, 64, '', '', '', null, '')
						Validate(tfsShipToAddress, 'addr_address2', tiRecNum, maColNames[77], taCols[77], false, 64, '', '', '', null, '')
						Validate(tfsShipToAddress, 'addr_address3', tiRecNum, maColNames[78], taCols[78], false, 64, '', '', '', null, '')
						Validate(tfsShipToAddress, 'addr_city', tiRecNum, maColNames[79], taCols[79], false, 64, '', '', '', null, '')
						Validate(tfsShipToAddress, 'stateprov_id', tiRecNum, maColNames[80], taCols[80], false, 0, '', '', '', GetStateIDFromCode, '')
						Validate(tfsShipToAddress, 'addr_postal', tiRecNum, maColNames[81], taCols[81], false, 20, '', '', '', null, '')
						Validate(tfsShipToAddress, 'country_id', tiRecNum, maColNames[82], taCols[82], false, 0, '', '', '', GetCountryIDFromCode, '')
						
						tfsShipToOrderAddress.addr_id = tfsShipToAddress.addr_id 
					}
						
					//* WAS CONTACT SUPPLIED?
					tbFoundContact = false
					if(Trim(taCols[75]) != ''){
						if(tfsCustContact.find() || tfsCustContact.find()){							
							tfsCustContact.custcontact_id = GetCustContactIDFromFirstAndLast(taCols[75], globals.UUIDtoStringNew(tfsSalesOrder.cust_id), false, '')
							if(tfsCustContact.search() > 0){
								tbFoundContact=true
								
								// get the sa_order_address fields that are popped from contact
								tfsShipToOrderAddress.ordaddr_fax_home = tfsCustContact.sa_customer_contact_to_sys_contact.contact_home_fax
								tfsShipToOrderAddress.ordaddr_email_home = tfsCustContact.sa_customer_contact_to_sys_contact.contact_home_email
								tfsShipToOrderAddress.ordaddr_email_other = tfsCustContact.sa_customer_contact_to_sys_contact.contact_other_email 
								tfsShipToOrderAddress.ordaddr_email_business = tfsCustContact.sa_customer_contact_to_sys_contact.contact_business_email 
							}							
						}						
					}
					if(tbFoundContact==false){
						// fields that otherwise would be from sys_contact
						Validate(tfsShipToOrderAddress, 'ordaddr_fax_home', tiRecNum, maColNames[71], taCols[71], false, 32, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_email_business', tiRecNum, maColNames[72], taCols[72], false, 128, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_email_home', tiRecNum, maColNames[73], taCols[73], false, 128, '', '', '', null, '')
						Validate(tfsShipToOrderAddress, 'ordaddr_email_other', tiRecNum, maColNames[74], taCols[74], false, 128, '', '', '', null, '')
					}
					//*** FINISH - SA_ORDER_ADDRESS - SHIPTO

					if(mbCurrRowErrored){
						miNumRecsFailed += 1
						revertChanges(tfsSalesOrder, tsInsertOrUpdate)
						revertChanges(tfsSalesOrderRevision, tsInsertOrUpdate)
						revertChanges(tfsBillToOrderAddress, tsInsertOrUpdate)
						revertChanges(tfsBillToAddress, tsInsertOrUpdate)
						revertChanges(tfsShipToAddress, tsInsertOrUpdate)
					}
					else{
						databaseManager.saveData(tfsSalesOrder.getSelectedRecord())		
						CheckForSaveErrors(tfsSalesOrder, tsInsertOrUpdate)

						if(tsInsertOrUpdate=='I'){
							miNumInserts=miNumInserts+1

							databaseManager.saveData(tfsSalesOrderRevision.getSelectedRecord())		
							CheckForSaveErrors(tfsSalesOrderRevision, tsInsertOrUpdate)
							
							databaseManager.saveData(tfsBillToOrderAddress.getSelectedRecord())		
							CheckForSaveErrors(tfsBillToOrderAddress, tsInsertOrUpdate)
							
							databaseManager.saveData(tfsShipToOrderAddress.getSelectedRecord())		
							CheckForSaveErrors(tfsShipToOrderAddress, tsInsertOrUpdate)
							
							if(tbFoundBillToAddrCode==false){
								databaseManager.saveData(tfsBillToAddress.getSelectedRecord())		
								CheckForSaveErrors(tfsBillToAddress, tsInsertOrUpdate)
							}
							
							if(tbFoundShipToAddrCode==false){
								databaseManager.saveData(tfsShipToAddress.getSelectedRecord())		
								CheckForSaveErrors(tfsShipToAddress, tsInsertOrUpdate)
							}
						}
						else{
							miNumUpdates=miNumUpdates+1
							databaseManager.saveData()
						}
					}
					///
				}
			}
		} 
	}
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(tfsSalesOrder, tsInsertOrUpdate)
			revertChanges(tfsSalesOrderRevision, tsInsertOrUpdate)
			revertChanges(tfsBillToOrderAddress, tsInsertOrUpdate)
			revertChanges(tfsBillToAddress, tsInsertOrUpdate)
			revertChanges(tfsShipToAddress, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @param {UUID} plantID
 *
 * @return
 * @properties={typeid:24,uuid:"1B3375D0-C989-456B-B148-B7FAAD03C981"}
 * @AllowToRunInFind
 */
function getDivFromPlant(plantID){
	/***@type {JSFoundset<db:/avanti/sys_plant>} */
	var fs_sys_plant = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_plant')
	
	if(fs_sys_plant.find() || fs_sys_plant.find()){
		fs_sys_plant.plant_id = plantID
		if(fs_sys_plant.search()){
			return fs_sys_plant.div_id
		}
	}
	
	return null
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"3FFA2610-6A8B-472E-9018-6A6955BE8FF3"}
 */
function GetCampaignIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'camp_desc', psCode, 'crm_campaign', 'camp_id', pbThrowErrorIfNotFound, 'Campaign setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('crm_campaign', 'camp_desc', psCode, 'camp_id')

	return tsID
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"E1B75E32-54C9-4BDA-8FD5-4055B797900F"}
 */
function GetRushIDFromCode(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'rush_code', psCode, 'sa_order_rush', 'rush_id', pbThrowErrorIfNotFound, 'Rush setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('sa_order_rush', 'rush_code', psCode, 'rush_id')

	return tsID
}

/**
 * @param {String} psCode
 * @param {Boolean} pbThrowErrorIfNotFound
 * @param {String} niceColName
 * @param {Boolean} pbCreateRecIfDoesntExist
 *
 * @return
 * @properties={typeid:24,uuid:"0BF05362-7629-47F6-B2D5-025A3140B59B"}
 */
function GetCustProjIDFromDescr(psCode, pbThrowErrorIfNotFound, niceColName, pbCreateRecIfDoesntExist){
	var tsID = GetIDFromCode(niceColName, 'custproj_desc', psCode, 'sa_customer_project', 'custproj_id', pbThrowErrorIfNotFound, 'Customer Project setup')
	
	if((tsID=='' || tsID==null) && pbCreateRecIfDoesntExist)
		tsID = AddNewRecord('sa_customer_project', 'custproj_desc', psCode, 'custproj_id')

	return tsID
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"11ECE18C-D5C5-48D1-8282-8152F8C7150C"}
 */
function processSalesOrderDetailRecord(tsRecord, tiRecNum){
	try {  
		mbCurrRowErrored=false
		var taCols = tsRecord.split('\t')
		var tsInsertOrUpdate = ''
		
		if(taCols.length > 0){
			if(isBlank(taCols[0])) 
				LogError("Column '" & maColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
			else if(isBlank(taCols[1])) 
				LogError("Column '" & maColNames[1] & "' is blank. This is a required field.", tiRecNum, '')
			else{
				var tsOrdHID = FSQuery('sa_order', 'ordh_document_num', taCols[0], 'ordh_id')
				if(tsOrdHID=='')
					LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
				else{
					var tsOrdRevHID = GetIDFrom2Codes('', 'sa_order_revision_header', 'ordh_id', tsOrdHID, 'ordrevh_revision', '0', 'ordrevh_id', false, '', false)						
					if(tsOrdRevHID=='')
						LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
					else{
						/***@type {JSFoundSet<db:/avanti/sa_order_revision_detail>} */
						var tfsSalesOrderDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revision_detail')
						/***@type {JSFoundSet<db:/avanti/sa_order_revision_detail_qty>} */
						var tfsSalesOrderDetailQty = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revision_detail_qty')

						if(tfsSalesOrderDetail.find() || tfsSalesOrderDetail.find()){
							
							tfsSalesOrderDetail.ordrevh_id = tsOrdRevHID 

							// sl-2525 - ordrevd_line_num is a calculated col - should be assigning to sequence_nr instead
							//tfsSalesOrderDetail.ordrevd_line_num = taCols[1]				
							tfsSalesOrderDetail.sequence_nr = taCols[1]				
							
							if(tfsSalesOrderDetail.search() > 0){
								tsInsertOrUpdate='U'
								tfsSalesOrderDetailQty = tfsSalesOrderDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base
							}
							else{ // didnt find rec - do an insert
								tsInsertOrUpdate='I'
								tfsSalesOrderDetail.newRecord()
								tfsSalesOrderDetail.ordrevh_id = tsOrdRevHID 

								// sl-2525 - ordrevd_line_num is a calculated col - should be assigning to sequence_nr instead
								//tfsSalesOrderDetail.ordrevd_line_num = taCols[1]				
								tfsSalesOrderDetail.sequence_nr = taCols[1]				
								
								tfsSalesOrderDetail.ordrevd_display_line_num = tfsSalesOrderDetail.sequence_nr
								tfsSalesOrderDetailQty.newRecord()
								tfsSalesOrderDetailQty.ordrevd_id = tfsSalesOrderDetail.ordrevd_id 
								tfsSalesOrderDetailQty.sequence_nr = 1
								tfsSalesOrderDetailQty.ordrevdqty_is_base = 1
							}
							
							///************************************* START - WRITE TO DB ****************************************///
							
							_sInsertOrUpdate = tsInsertOrUpdate
							Validate(tfsSalesOrderDetail, 'ordrevd_prod_desc', tiRecNum, maColNames[2], taCols[2], false, 128, '', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'whse_id', tiRecNum, maColNames[3], taCols[3], true, 0, '', '', '', GetWarehouseIDFromCode, '')
							Validate(tfsSalesOrderDetail, 'uom_id', tiRecNum, maColNames[4], taCols[4], true, 0, '', '', '', GetUOMIDFromCode, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_qty_ordered', tiRecNum, maColNames[5], taCols[5], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_qty_shipped', tiRecNum, maColNames[6], taCols[6], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_qty_backord', tiRecNum, maColNames[7], taCols[7], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_sell_price', tiRecNum, maColNames[8], taCols[8], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_disc_amt', tiRecNum, maColNames[9], taCols[9], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_extended_price', tiRecNum, maColNames[10], taCols[10], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'item_id', tiRecNum, maColNames[11], taCols[11], false, 0, '', '', '', GetIntItemIdFromCode, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_unit_cost', tiRecNum, maColNames[12], taCols[12], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_extended_cost', tiRecNum, maColNames[13], taCols[13], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'sysworktype_id', tiRecNum, maColNames[14], taCols[14], false, 0, '', '', '', GetSysWorkTypeIdFromCode, '')
							Validate(tfsSalesOrderDetail, 'worktype_id', tiRecNum, maColNames[15], taCols[15], false, 0, '', '', '', GetSAWorkTypeIdFromDescr, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_unit_price', tiRecNum, maColNames[16], taCols[16], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_staging_location', tiRecNum, maColNames[17], taCols[17], false, 50, '', '', '', null, '')
							Validate(tfsSalesOrderDetail, 'ordrevd_ext_price_over', tiRecNum, maColNames[18], taCols[18], false, 0, 'NUMBER', '', '', null, '')
							
							Validate(tfsSalesOrderDetailQty, 'ordrevdqty_cost', tiRecNum, maColNames[13], taCols[13], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetailQty, 'ordrevdqty_price', tiRecNum, maColNames[10], taCols[10], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderDetailQty, 'ordrevdqty_qty', tiRecNum, maColNames[5], taCols[5], false, 0, 'NUMBER', '', '', null, '')

							///************************************* STOP - WRITE TO DB ****************************************///
							
							if(mbCurrRowErrored){
								miNumRecsFailed += 1
								revertChanges(tfsSalesOrderDetail, tsInsertOrUpdate)
								revertChanges(tfsSalesOrderDetailQty, tsInsertOrUpdate)
							}
							else{
								databaseManager.saveData(tfsSalesOrderDetail.getSelectedRecord())		
								CheckForSaveErrors(tfsSalesOrderDetail, tsInsertOrUpdate)

								if(tsInsertOrUpdate=='I'){
									miNumInserts=miNumInserts+1
									databaseManager.saveData(tfsSalesOrderDetailQty.getSelectedRecord())		
									CheckForSaveErrors(tfsSalesOrderDetailQty, tsInsertOrUpdate)									
								}
								else{
									miNumUpdates=miNumUpdates+1
									databaseManager.saveData(tfsSalesOrderDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base.getSelectedRecord())		
									CheckForSaveErrors(tfsSalesOrderDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base, tsInsertOrUpdate)									
								}
							}
						}
					}
				}
			}
		} 
	}
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(tfsSalesOrderDetail, tsInsertOrUpdate)
			revertChanges(tfsSalesOrderDetailQty, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"09C0EDAC-067E-442D-AC60-957B2AB70448"}
 */
function processSalesOrderMultishipRecord(tsRecord, tiRecNum){
	try {  
		mbCurrRowErrored=false
		var taCols = tsRecord.split('\t')
		var tsInsertOrUpdate
		
		if(taCols.length > 0){
			if(isBlank(taCols[0])) 
				LogError("Column '" & maColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
			else{
				var tsOrdHID = FSQuery('sa_order', 'ordh_document_num', taCols[0], 'ordh_id')
				if(isBlank(tsOrdHID))
					LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
				else{
					var tsOrdRevHID = GetIDFrom2Codes('', 'sa_order_revision_header', 'ordh_id', tsOrdHID, 'ordrevh_revision', '0', 'ordrevh_id', false, '', false)						
					if(isBlank(tsOrdRevHID))
						LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
					else{
						/***@type {JSFoundSet<db:/avanti/sa_order_revh_multi_ship>} */
						var tfsSalesOrderMS = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revh_multi_ship')

						if(tfsSalesOrderMS.find() || tfsSalesOrderMS.find()){
							if(isBlank(taCols[1])){// no sequence num specified - so were doing an insert
								tsInsertOrUpdate='I'
								tfsSalesOrderMS.newRecord()
								tfsSalesOrderMS.ordrevh_id = tsOrdRevHID 
								tfsSalesOrderMS.sequence_nr = GetNextSeqNumFor('sa_order_revh_multi_ship', 'ordrevh_id', tsOrdRevHID)				
							}
							else{ 
								tfsSalesOrderMS.ordrevh_id = tsOrdRevHID 
								tfsSalesOrderMS.sequence_nr = taCols[1]				
								
								if(tfsSalesOrderMS.search() > 0){
									tsInsertOrUpdate='U'
								}
								else{ 
									tsInsertOrUpdate='I'
									tfsSalesOrderMS.newRecord()
									tfsSalesOrderMS.ordrevh_id = tsOrdRevHID 
									tfsSalesOrderMS.sequence_nr = taCols[1]									
								}
							}
							
							_sInsertOrUpdate = tsInsertOrUpdate
							Validate(tfsSalesOrderMS, 'cust_id', tiRecNum, maColNames[2], taCols[2], true, 0, '', '', '', GetCustIDFromCode, '')
							Validate(tfsSalesOrderMS, 'custaddr_id', tiRecNum, maColNames[3], taCols[3], true, 0, '', '', '', GetCustAddrIDFromCode, tfsSalesOrderMS.cust_id)
							Validate(tfsSalesOrderMS, 'shipmethod_id', tiRecNum, maColNames[4], taCols[4], false, 0, '', '', '', GetShipMethodIDFromCode, '')
							Validate(tfsSalesOrderMS, 'ordrevhms_instructions', tiRecNum, maColNames[5], taCols[5], false, 1024, '', '', '', null, '')
							Validate(tfsSalesOrderMS, 'ordrevhms_event_date', tiRecNum, maColNames[6], taCols[6], false, 0, 'DATE', '', '', null, '')
							Validate(tfsSalesOrderMS, 'ordrevhms_must_arrive_date', tiRecNum, maColNames[7], taCols[7], false, 0, 'DATE', '', '', null, '')
							Validate(tfsSalesOrderMS, 'ordrevhms_ship_date', tiRecNum, maColNames[8], taCols[8], false, 0, 'DATE', '', '', null, '')
							Validate(tfsSalesOrderMS, 'ordrevhms_shipping_charges', tiRecNum, maColNames[9], taCols[9], false, 0, 'NUMBER', '', '', null, '')
							Validate(tfsSalesOrderMS, 'custcontact_first_and_last', tiRecNum, maColNames[10], taCols[10], false, 0, '', '', '', GetCustContactIDFromFirstAndLast, tfsSalesOrderMS.cust_id)

							if(mbCurrRowErrored){
								miNumRecsFailed += 1
								revertChanges(tfsSalesOrderMS, tsInsertOrUpdate)
							}
							else{
								databaseManager.saveData(tfsSalesOrderMS.getSelectedRecord())		
								CheckForSaveErrors(tfsSalesOrderMS, tsInsertOrUpdate)

								if(tsInsertOrUpdate=='I'){
									miNumInserts=miNumInserts+1
								}
								else{
									miNumUpdates=miNumUpdates+1
								}
							}
						}
					}
				}
			}
		} 
	}
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(tfsSalesOrderMS, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"06DB2FC2-59BD-4CC1-9EBD-B8F8F83B7688"}
 */
function processSalesOrderMultishipQtyRecord(tsRecord, tiRecNum){
	try {  
		mbCurrRowErrored=false
		var taCols = tsRecord.split('\t')
		var tsInsertOrUpdate
		
		if(taCols.length > 0){
			if(isBlank(taCols[0])) 
				LogError("Column '" & maColNames[0] & "' is blank. This is a required field.", tiRecNum, '')
			else if(isBlank(taCols[1])) 
				LogError("Column '" & maColNames[1] & "' is blank. This is a required field.", tiRecNum, '')
			else if(isBlank(taCols[2])) 
				LogError("Column '" & maColNames[2] & "' is blank. This is a required field.", tiRecNum, '')
			else{
				var tsOrdHID = FSQuery('sa_order', 'ordh_document_num', taCols[0], 'ordh_id')
				if(isBlank(tsOrdHID))
					LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
				else{
					var tsOrdRevHID = GetIDFrom2Codes('', 'sa_order_revision_header', 'ordh_id', tsOrdHID, 'ordrevh_revision', '0', 'ordrevh_id', false, '', false)						
					if(isBlank(tsOrdRevHID))
						LogError('Invalid ' + maColNames[0], tiRecNum, tsInsertOrUpdate) 
					else{
						// sl-2525 - ordrevd_line_num is a calculated col - should be using sequence_nr instead
						//var tnDetailLineID = GetIDFrom2Codes('', 'sa_order_revision_detail', 'ordrevh_id', tsOrdRevHID, 'ordrevd_line_num', taCols[1], 'ordrevd_id', false, '', false)
						var tnDetailLineID = GetIDFrom2Codes('', 'sa_order_revision_detail', 'ordrevh_id', tsOrdRevHID, 'sequence_nr', taCols[1], 'ordrevd_id', false, '', false)						
						var tnMSLineID = GetIDFrom2Codes('', 'sa_order_revh_multi_ship', 'ordrevh_id', tsOrdRevHID, 'sequence_nr', taCols[2], 'ordrevhms_id', false, '', false)						

						if(isBlank(tnDetailLineID))
							LogError('Invalid ' + maColNames[1], tiRecNum, tsInsertOrUpdate) 
						else if(isBlank(tnMSLineID))
							LogError('Invalid ' + maColNames[2], tiRecNum, tsInsertOrUpdate)
						else{
							/***@type {JSFoundSet<db:/avanti/sa_order_revd_multi_ship_qty>} */
							var tfsSalesOrderMSQty = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revd_multi_ship_qty')
							
							if(tfsSalesOrderMSQty.find() || tfsSalesOrderMSQty.find()){
								tfsSalesOrderMSQty.ordrevd_id = tnDetailLineID 
								tfsSalesOrderMSQty.ordrevhms_id = tnMSLineID				
								
								if(tfsSalesOrderMSQty.search() > 0){
									tsInsertOrUpdate='U'
								}
								else{ 
									tsInsertOrUpdate='I'
									tfsSalesOrderMSQty.newRecord()
									tfsSalesOrderMSQty.ordrevd_id = tnDetailLineID 
									tfsSalesOrderMSQty.ordrevhms_id = tnMSLineID				
								}
								
								_sInsertOrUpdate = tsInsertOrUpdate
								Validate(tfsSalesOrderMSQty, 'ordrevdms_qty', tiRecNum, maColNames[3], taCols[3], false, 0, 'NUMBER', '', '', null, '')
								Validate(tfsSalesOrderMSQty, 'ordrevdms_qty_backord', tiRecNum, maColNames[4], taCols[4], false, 0, 'NUMBER', '', '', null, '')
								Validate(tfsSalesOrderMSQty, 'ordrevdms_qty_shipped', tiRecNum, maColNames[5], taCols[5], false, 0, 'NUMBER', '', '', null, '')

								if(mbCurrRowErrored){
									miNumRecsFailed += 1
									revertChanges(tfsSalesOrderMSQty, tsInsertOrUpdate)
								}
								else{
									databaseManager.saveData(tfsSalesOrderMSQty.getSelectedRecord())		
									CheckForSaveErrors(tfsSalesOrderMSQty, tsInsertOrUpdate)

									if(tsInsertOrUpdate=='I'){
										miNumInserts=miNumInserts+1
									}
									else{
										miNumUpdates=miNumUpdates+1
									}
								}
							}
						}
					}
				}
			}
		} 
	}
	catch(ex) { // Validate() has its own error handling - this is here in case something gets by our validation and there is an error 
				// trying to write to a col
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			revertChanges(tfsSalesOrderMSQty, tsInsertOrUpdate)
			LogError(ex.message, tiRecNum, tsInsertOrUpdate) 
		}
	}
}

/**
 * @param {Object} vs
 * @return {Boolean} 
 *
 * @properties={typeid:24,uuid:"E5F6B9E9-7FE8-40CF-A1CB-5164B1E588A0"}
 */
function isBlank(vs){
	if(vs == null || Trim(vs) == '' || Trim(vs) == 'NO-VALUE')
		return true
	else
		return false
}

/**
 * @properties={typeid:24,uuid:"7201EB88-63C5-40D3-8732-9AA3BCE9BDFF"}
 */
function test(){
	/**@type {JSRecord<db:/avanti/sa_customer>} */
	var rCust = scopes.avDB.newRecord('sa_customer');

	rCust.cust_code = 'dadasdasda';
	
	scopes.avDB.saveData('utils_dataImport_dtl.test.1', null, rCust);
}

/**
 * @properties={typeid:24,uuid:"B5A03754-9E76-444F-B9AD-3FD36B67A76F"}
 */
function sqltest(){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	
	/// HP - as per randy/jennifer treat old post press trim as final trim
	oSQL.sql = "update sa_order_revision_detail_section " +
			   "set ordrevds_flg_postpresstrim = 0, " + 
				   "ordrevds_flg_finaltrim = 1 " +
			   "where ordrevds_flg_postpresstrim = 1";
	oSQL.args = [];
	globals["avUtilities_sqlRaw"](oSQL);

	oSQL.sql = "update sa_task_worktype_section " +
			   "set worktypesection_flg_pptrim = 0, " + 
				   "worktypesection_flg_finaltrim = 1 " +
			   "where worktypesection_flg_pptrim = 1";
	oSQL.args = [];
	globals["avUtilities_sqlRaw"](oSQL);
}

/**
 * @properties={typeid:24,uuid:"69DE31F7-5FE7-473B-8F97-7FD52AC18CEA"}
 */
function logTest(){
	var log = scopes.svyLogManager.getLogger('com.mycompany.mysolution')
	log.debug("Hello, I'm debuggin'")
	var name = 'you'
	log.trace("Hello {}", name) //outputs "Hello you":	
}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"538A4C90-4A29-48DA-9C67-791E01461C70"}
 */
function checkOrdersForMissingMilestones(){
	/***@type {JSFoundset<db:/avanti/sa_order_revision_header>} */
	var fs = databaseManager.getFoundSet('avanti', 'sa_order_revision_header')
	if(fs.find() || fs.find()){
		fs.ordrevh_order_status = '!Open'
		
		if(fs.search()){
			fs.sort('fs.sa_order_revision_header_to_sa_order.ordh_order_date asc')
			var numrecs = databaseManager.getFoundSetCount(fs)
			var numbad =0
			for(var i=1;i<=numrecs;i++){
				numbad += checkOrderForMissingMilestones(fs.getRecord(i).ordrevh_id, fs.getRecord(i).sa_order_revision_header_to_sa_order.ordh_document_num,fs.getRecord(i).sa_order_revision_header_to_sa_order.ordh_order_date)
			}
			
			application.output('numbad: '+ numbad)
		}
	}
}

/**
 * @param ordrevh_id
 * @param sOrdNum
 * @param ordDate
 *
 * @return
 * @properties={typeid:24,uuid:"4849D63D-AA63-42C2-AF7B-7143C2AEF4D6"}
 */
function checkOrderForMissingMilestones(ordrevh_id, sOrdNum, ordDate){
	var sql = "select COUNT(*) from sa_order_revision_detail_section " +
		"where ordrevd_id in " +
	    "(select ordrevd_id from sa_order_revision_detail det where ordrevh_id = '" + ordrevh_id + "')" 
//	            "(select ordrevh_id from sa_order_revision_header where ordh_id in " +
//	                "(select ordh_id from sa_order ord where ordh_document_num = '" + sOrdNum + "')))"
	var numsects = globals.Query(sql)
	
	sql = "select count(distinct(ordrevds_id)) from sch_milestone ms " +
		"where ms.ordrevds_id in " + 
	       "(select ordrevds_id from sa_order_revision_detail_section where ordrevd_id in " +
	              "(select ordrevd_id from sa_order_revision_detail where ordrevh_id = '" + ordrevh_id + "'))" 
//	                     "(select ordrevh_id from sa_order_revision_header where ordh_id in " + 
//	                           "(select ordh_id from sa_order where ordh_document_num= '" + sOrdNum + "'))))"
   	var numSectsInMs = globals.Query(sql)

	if(numsects > numSectsInMs && numSectsInMs > 0){
		application.output(ordDate + ' ' + sOrdNum + ' numsects=' + numsects + ', numSectsInMs=' + numSectsInMs)
		return 1
	}
   	
	return 0
}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"BD07894B-AC3B-4F7C-8F94-80C066FC1998"}
 */
function deleteJob(){
	/***@type {JSFoundset<db:/avanti/prod_job>} */
	var jobs = databaseManager.getFoundSet('avanti', 'prod_job')
	if(jobs.find() || jobs.find()){
		jobs.job_number = '13-00199-002'
		if(jobs.search()){
			jobs.deleteRecord(1)
			databaseManager.saveData()
		}
	}
}

/**
 * @param {String} sTime
 *
 * @return
 * @properties={typeid:24,uuid:"5C9D94BB-6AAD-42C5-A4FE-C3FFBF5CF79E"}
 */
function createCronStringFromTime(sTime){
	// '0 30 14 ? * *' is 2:30 pm 
	
	if(sTime.indexOf(':') > -1){
		var aNums = sTime.split(':')
		/***@type {Number} */
		var hours = parseInt(aNums[0])
		
		if(aNums[1].indexOf(' ') > -1){
			var aMinAndMeridian = aNums[1].split(' ')
			var mins = aMinAndMeridian[0]
			
			if(hours==12){
				if(aMinAndMeridian[1].toLowerCase() == 'am'){
					hours = 0
				}
			}
			else if(aMinAndMeridian[1].toLowerCase() == 'pm'){
				hours += 12
			}
			
			return '0 ' + mins  + ' ' + hours.toString() + ' ? * *'
		}
	}
	
	return null	
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sOrdNum
 *
 * @properties={typeid:24,uuid:"3D19622D-FE5C-4CF2-B228-D690E30DE2B2"}
 */
function test_clearOpenCommittments(sOrdNum){
	/***@type {JSFoundset<db:/avanti/sa_order>} */
	var fs_ord = databaseManager.getFoundSet('avanti', 'sa_order')
	if(fs_ord.find() || fs_ord.find()){
		fs_ord.ordh_document_num = sOrdNum
		if(fs_ord.search()){
			clearOpenCommittments(fs_ord.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail)
		}
	}
}

/**
 * @param {JSFoundSet<db:/avanti/sa_order_revision_detail>} fs_sa_order_revision_detail
 *
 * @properties={typeid:24,uuid:"85E97B4B-D642-4519-8BB4-7E76D09818D2"}
 */
function clearOpenCommittments(fs_sa_order_revision_detail){
	if(utils.hasRecords(fs_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item)){
		var tiNumRecs = fs_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item.getSize()
		/***@type {JSRecord<db:/avanti/sa_order_revd_item>} */
		var rec_sa_order_revd_item
		/***@type {JSRecord<db:/avanti/in_committment>} */
		var rec_in_committment
		
		for(var i=1;i<=tiNumRecs;i++){
			rec_sa_order_revd_item = fs_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item.getRecord(i)
			if(utils.hasRecords(rec_sa_order_revd_item.sa_order_revd_item_to_in_committment)){
				rec_in_committment = rec_sa_order_revd_item.sa_order_revd_item_to_in_committment.getRecord(1)
				if(rec_in_committment.cur_qty_committed > 0){
					// reduce comit qty on in_item
					if(rec_in_committment.in_committment_to_in_item.item_committed_qty > 0){
						rec_in_committment.in_committment_to_in_item.item_committed_qty -= rec_in_committment.cur_qty_committed
						databaseManager.saveData(rec_in_committment.in_committment_to_in_item)
					}
	
					// reduce comit qty on in_item_warehouse
					if(fs_sa_order_revision_detail.whse_id && fs_sa_order_revision_detail.sa_order_revision_detail_to_in_item_warehouse.itemwhse_committed_qty > 0){
						fs_sa_order_revision_detail.sa_order_revision_detail_to_in_item_warehouse.itemwhse_committed_qty -=  rec_in_committment.cur_qty_committed
						databaseManager.saveData(fs_sa_order_revision_detail.sa_order_revision_detail_to_in_item_warehouse)
					}
				}
				// dont need commit rec anymore - delete it 
				rec_sa_order_revd_item.sa_order_revd_item_to_in_committment.deleteRecord(1)
			}
		}
	}
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"DDDB44A5-DCFA-464E-98BA-D2460EF0CA23"}
 */
function btnPurgeData_onAction(event) {
	if(ValidEntry()){
		if(!msLines || msLines.length <= 1)
			scopes.avText.showWarning('nothingToProcess');
		else
		{
			var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),'Are you sure you want to purge the data in the list?', i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no')); 
			
			if(answer == i18n.getI18NMessage('avanti.dialog.yes')) {
				msAction = 'Purge';
				if(ValidImportFile()){
					clearResults();			
					_oImportCache = {}; //initialize cache object
					_oImportCache.getIDFromCode = {}; // iniitalize the code lookup object
					enableForm(false);

					processFile()
					
					_oImportCache = null;					
					showResults(true);				
					enableForm(true);
					lblNumRecs='';
					msLines=[''];
				}
			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"E7D66C59-6267-4B8D-8B89-E082E3BC452D"}
 */
function purgeCustomerRecords(){ 
	mfsCustomers = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer')
	
	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			purgeCustomerRecord(msLines[i], i)
		}
	}	
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"1F170733-36CF-4D93-9841-64F2F89AE0C3"}
 */
function purgeCustomerRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')

	if(taCols.length > 0){
		if(taCols[0]=='')
			LogError("Column 'Customer Code' is blank. This is a required field.", tiRecNum, '')
		else{
			if(mfsCustomers.find() || mfsCustomers.find()){
				mfsCustomers.cust_code = taCols[0].toUpperCase()
				if(mfsCustomers.search() > 0) // found it, then delete it
					try {
						mfsCustomers.deleteRecord()
						miNumDeletes++
					} catch (ex) {
						LogError('Customer ' + taCols[0].toUpperCase() + ' could not be deleted.', tiRecNum, '')
						miNumRecsFailed++
					}
			}
		}
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6A688092-E9E4-4FB9-8C86-F3DF584E0923"}
 */
function onAction_importNotes(event) {
    scopes.avText.showCustomInfo('ImportNotes', 'importNotes_contents', 700, 400);
}

/**
 * @param {String} sOrderNum
 *
 * @properties={typeid:24,uuid:"C463E563-63EF-4B8B-8B59-E659845C6080"}
 */
function getNumLineItems(sOrderNum){
	var procedure_declaration = '{call getNumLineItems(?, ?)}'
	var typesArray = [0, 0];
	var args = [globals.org_id.toString(), sOrderNum.toString()]
	var dataset = plugins.rawSQL.executeStoredProcedure(globals.avBase_dbase_avanti, procedure_declaration, 
		args, typesArray, 1);
	dataset.getValue(1, 1);
}

/**
 * @param {String} colName
 *
 * @return
 * @properties={typeid:24,uuid:"E81A23D4-180C-4FCA-8B51-804661D0DE6C"}
 */
function getReqColMsg(colName){
	return scopes.avText.createParamMsg('avanti.dialog.importReqField', [colName]);
}

/**
 * @properties={typeid:24,uuid:"B9CD9FF9-7001-4A67-ACC4-5F2045898A9B"}
 */
function processTaxItemRecords(){
	mfsTaxItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_item')
	mfsTaxGroups = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group')
	mfsTaxGroupItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item')
	mfsTaxItemRates = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_rate')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processTaxItemRecord(msLines[i], i)		
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsTaxItems, -1)
	databaseManager.refreshRecordFromDatabase(mfsTaxGroups, -1)
	databaseManager.refreshRecordFromDatabase(mfsTaxGroupItems, -1)
	databaseManager.refreshRecordFromDatabase(mfsTaxItemRates, -1)
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_item')
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_group');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_rate')
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"618B2135-A352-47D6-A208-46B3E172A4EA"}
 */
function processTaxItemRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError(getReqColMsg(maTaxItems[0]), tiRecNum, '')
		}
		else{
			mfsTaxItems.newRecord()
			
			var taxID = scopes.avDB.SQLQuery('select taxitem_id from sys_sales_tax_item where taxitem_code = ?', true, [taCols[0]])

			if(taxID){ // UPDATE
			// update based on code now - not id - assigning id here auto loaded the right rec in the fs - like it auto did a search - then got a 'cant delete' err when did revertChanges
//				mfsTaxItems.taxitem_id = taxID
				WriteTaxItemToDB(taCols, 'U', tiRecNum)
			}
			else{ // INSERT
				WriteTaxItemToDB(taCols, 'I', tiRecNum)
			}
			
			mfsTaxItems.getSelectedRecord().revertChanges()
			aTaxItemNoValueCols = [];
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"870C76C4-A587-46FF-BACC-80E328BF81D6"}
 */
function WriteTaxItemToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
		var sTaxTypeOptions = scopes.avText.getLblMsg('taxType_Sales') + ', ' + scopes.avText.getLblMsg('taxType_Purchases')
		var sBasedOnOptions = 'Percentage of Sales/Purchase, Percentage of Another Tax Item, Tax Included with Item Price'
																																			 
		Validate(mfsTaxItems, 'taxitem_code', tiRecNum, maTaxItems[0], taCols[0], true, 32, '', '', '', null, '')
		Validate(mfsTaxItems, 'taxitem_type', tiRecNum, maTaxItems[1], taCols[1], true, 0, '', sTaxTypeOptions, 'S,P', null, '')
		Validate(mfsTaxItems, 'taxitem_desc', tiRecNum, maTaxItems[2], taCols[2], false, 64, '', '', '', null, '')
		Validate(mfsTaxItems, 'taxitem_reg_number', tiRecNum, maTaxItems[3], taCols[3], false, 32, '', '', '', null, '')
		Validate(mfsTaxItems, 'glacct_id', tiRecNum, maTaxItems[4], taCols[4], false, 36, '', '', '', GetGLAccountSegmentIDFromNumber, '');
		Validate(mfsTaxItems, 'taxitem_based_on', tiRecNum, maTaxItems[5], taCols[5], true, 0, '', sBasedOnOptions, 'P,PI,TI', null, '')
		Validate(mfsTaxItems, 'taxitem_based_on_taxitem', tiRecNum, maTaxItems[6], taCols[6], mfsTaxItems.taxitem_based_on == 'PI', 0, '', '', '', GetTaxItemIDFromCode, '')
		
		if(tsInsertOrUpdate=='I' && !taCols[7]){
			taCols[7] = 'Up to the Next Currency Decimal Digit' // def
		}
		
		Validate(mfsTaxItems, 'taxitem_rounding_rule', tiRecNum, maTaxItems[7], taCols[7], false, 0, '', 'Up to the Next Currency Decimal Digit', 'UP', null, '')
		
		if(!taCols[8]) taCols[8] = 'N';
		if(!taCols[9]) taCols[9] = 'N';
		if(!taCols[10]) taCols[10] = 'N';
		if(!taCols[11]) taCols[11] = 'N';
		
		Validate(mfsTaxItems, 'taxitem_taxable_tax', tiRecNum, maTaxItems[8], taCols[8], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsTaxItems, 'taxitem_active', tiRecNum, maTaxItems[9], taCols[9], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsTaxItems, 'taxitem_included_tax', tiRecNum, maTaxItems[10], taCols[10], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsTaxItems, 'taxitem_refundable_tax', tiRecNum, maTaxItems[11], taCols[11], false, 0, '', 'Y, N', '1,0', null, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
		}
		else{
			if(tsInsertOrUpdate=="I"){
				writeWithSQL(mfsTaxItems, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				aTaxItemNoValueCols.push('taxitem_id')
				writeWithSQL(mfsTaxItems, ['taxitem_code'], aTaxItemNoValueCols, tsInsertOrUpdate, tiRecNum);
			}
			
			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * @properties={typeid:24,uuid:"1EDD4F1E-1F50-444D-958B-64C5923C70BB"}
 */
function processTaxGroupRecords(){
	mfsTaxGroups = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group')
	mfsTaxGroupItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processTaxGroupRecord(msLines[i], i)		
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsTaxGroups, -1)
	databaseManager.refreshRecordFromDatabase(mfsTaxGroupItems, -1)
	
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_group');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item');
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"BD5AAB01-775E-432B-9C76-1B5A44D3F919"}
 */
function processTaxGroupRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError(getReqColMsg(maTaxItems[0]), tiRecNum, '')
		}
		else{
			mfsTaxGroups.newRecord()
			
			var grpID = scopes.avDB.SQLQuery('select taxgroup_id from sys_sales_tax_group where taxgroup_code = ?', true, [taCols[0]]);
			if(grpID){ // UPDATE
				mfsTaxGroups.taxgroup_id = grpID;
				WriteTaxGroupToDB(taCols, 'U', tiRecNum)
			}
			else{ // INSERT
				WriteTaxGroupToDB(taCols, 'I', tiRecNum)
			}

			mfsTaxGroups.getSelectedRecord().revertChanges()
			aTaxGroupNoValueCols = [];
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"E71900D5-FB77-4CD6-BAF8-0EB9606B2D12"}
 */
function WriteTaxGroupToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
																																			 
		Validate(mfsTaxGroups, 'taxgroup_code', tiRecNum, maTaxGroups[0], taCols[0], true, 32, '', '', '', null, '')
		Validate(mfsTaxGroups, 'taxgroup_desc', tiRecNum, maTaxGroups[1], taCols[1], false, 50, '', '', '', null, '')
		
		if(!taCols[2]) taCols[2] = 'N';
		if(!taCols[3]) taCols[3] = 'N';
		
		Validate(mfsTaxGroups, 'taxgroup_active', tiRecNum, maTaxGroups[2], taCols[2], false, 0, '', 'Y, N', '1,0', null, '')
		Validate(mfsTaxGroups, 'taxgroup_shipping_taxable', tiRecNum, maTaxGroups[3], taCols[3], false, 0, '', 'Y, N', '1,0', null, '')

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
		}
		else{
			if(tsInsertOrUpdate=="I"){
				writeWithSQL(mfsTaxGroups, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				aTaxItemNoValueCols.push('taxgroup_id')
				writeWithSQL(mfsTaxGroups, ['taxgroup_code'], aTaxGroupNoValueCols, tsInsertOrUpdate, tiRecNum);
			}
			
			/// ITEM CODES
			if(taCols.length > miNumStaticTaxGroupCols){
				for(var i=miNumStaticTaxGroupCols;i<taCols.length;i++){
					if(!isBlank(taCols[i])){
						var taxID = scopes.avDB.SQLQuery('select taxitem_id from sys_sales_tax_item where taxitem_code = ?', true, [taCols[i]]);
						
						if(taxID){
							if(tsInsertOrUpdate=="I" || !scopes.avDB.SQLExists('select * from sys_sales_tax_group_item where taxgroup_id = ? and taxitem_id = ?', true, [mfsTaxGroups.taxgroup_id.toString(), taxID])){
								mfsTaxGroupItems.newRecord()
								mfsTaxGroupItems.taxgroup_id = mfsTaxGroups.taxgroup_id;
								mfsTaxGroupItems.taxitem_id = taxID;
								writeWithSQL(mfsTaxGroupItems, [], [], 'I', tiRecNum);
								mfsTaxGroupItems.getSelectedRecord().revertChanges()
							}
						}
						else{
							LogError('Invalid Tax Item Code: ' + taCols[i], tiRecNum, '')
						}
					}
				}
			}
			///

			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * @properties={typeid:24,uuid:"0E77B906-70FD-4DD7-B09B-053B169D88AC"}
 */
function processTaxRateRecords(){
	mfsTaxItemRates = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_rate')

	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processTaxRateRecord(msLines[i], i)		
		}
	}

	databaseManager.refreshRecordFromDatabase(mfsTaxItemRates, -1)
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_sales_tax_rate');
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"D310FE5F-4CAB-41E5-91BC-F878B44E8A9C"}
 */
function processTaxRateRecord(tsRecord, tiRecNum){
	var taCols = tsRecord.split('\t')
	
	if(taCols.length > 0){
		if(isBlank(taCols[0])){
			LogError(getReqColMsg(maTaxItemRates[0]), tiRecNum, '')
		}
		else{
			var taxID = scopes.avDB.SQLQuery('select taxitem_id from sys_sales_tax_item where taxitem_code = ?', true, [taCols[0]])
			if(!taxID){
				LogError("Column '" + maTaxItemRates[1] + "' is not a valid Tax Item.", tiRecNum, '')
			}
			
			if(IsValidDate(taCols[1])){
				var effDate = MakeDate(taCols[1])
			}
			else{
				LogError("Column '" + maTaxItemRates[1] + "' is '" + taCols[1] + "'. This not a valid date.", tiRecNum, '')
			}
			
			if(taxID && effDate){
				mfsTaxItemRates.newRecord()

				mfsTaxItemRates.taxitem_id = taxID;
				mfsTaxItemRates.taxrate_effective_date = effDate;
				
				if(scopes.avDB.SQLExists('select * from sys_sales_tax_rate where taxitem_id = ? and taxrate_effective_date = ?', true, [taxID, effDate] ) ){ // UPDATE
					WriteTaxRateToDB(taCols, 'U', tiRecNum)
				}
				else{ // INSERT
					WriteTaxRateToDB(taCols, 'I', tiRecNum)
				}
				
				mfsTaxItemRates.getSelectedRecord().revertChanges()
			}
		}
	}
}

/**
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"D7D9DC6B-4BD9-49D4-AAE7-9B9E97D52DD7"}
 */
function WriteTaxRateToDB(taCols, tsInsertOrUpdate, tiRecNum){
	try {
		mbCurrRowErrored=false
		_sInsertOrUpdate = tsInsertOrUpdate
																																			 
		Validate(mfsTaxItemRates, 'taxrate_percent', tiRecNum, maTaxItemRates[2], taCols[2], true, 0, 'NUMBER', '', '', null, '')
		mfsTaxItemRates.taxrate_percent = mfsTaxItemRates.taxrate_percent / 100;

		if(mbCurrRowErrored){
			miNumRecsFailed += 1
		}
		else{
			if(tsInsertOrUpdate=="I"){
				writeWithSQL(mfsTaxItemRates, [], [], tsInsertOrUpdate, tiRecNum);
			}
			else{
				writeWithSQL(mfsTaxItemRates, ['taxitem_id', 'taxrate_effective_date'], [], tsInsertOrUpdate, tiRecNum);
			}
			
			if(tsInsertOrUpdate=="I"){
				miNumInserts=miNumInserts+1
			}
			else{
				miNumUpdates=miNumUpdates+1
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1
			LogError(ex.message, tiRecNum, tsInsertOrUpdate)
		}
	}
}

/**
 * @properties={typeid:24,uuid:"6EBAF8E9-6E14-45E1-85B3-FD6CCB744004"}
 */
function processInvoiceRecords(){
    globals.avSales_orderType = "ORD";
    
	if (!utils.hasRecords(_to_sys_document_number$org_active_default)){
		scopes.avText.showWarning('noDefOrderStream');
	}
	else{
		if(msLines.length > 1){
			/**@type {JSRecord<db:/avanti/sa_invoice_register>} */
			var rInvoiceReg = scopes.avDB.newRecord('sa_invoice_register');
			rInvoiceReg.invreg_number = "IMPORT";
			rInvoiceReg.is_hidden = 1;
			rInvoiceReg.invreg_date_updated = application.getTimeStamp();
			databaseManager.saveData(rInvoiceReg);
		}
		
		// fst rec is col headings
		for(var i=1;i<msLines.length;i++){
			if(msLines[i]){
				processInvoiceRecord(msLines[i], i, rInvoiceReg.invreg_id)		
			}
		}
	}
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 * @param {UUID} sInvRegID
 *
 * @properties={typeid:24,uuid:"FC0E9878-AF17-4D38-BA10-EFEAEB28E283"}
 */
function processInvoiceRecord(tsRecord, tiRecNum, sInvRegID){
	var taCols = tsRecord.split('\t')

	try{
		if(taCols.length == 5){
			var sCustID = Validate(null, null, tiRecNum, maInvoiceColNames[0], taCols[0], true, 0, '', '', '', GetCustIDFromCode, '');
			var dInvoiceDate = Validate(null, null, tiRecNum, maInvoiceColNames[1], taCols[1], true, 0, 'DATE', '', '', null, '');
			var sInvNum = Validate(null, null, tiRecNum, maInvoiceColNames[2], taCols[2], true, 16, '', '', '', null, '');
			var nInvoiceTot = parseFloat(Validate(null, null, tiRecNum, maInvoiceColNames[4], taCols[4], true, 0, 'NUMBER', '', '', null, ''));
			var sItemID = Validate(null, null, tiRecNum, maInvoiceColNames[3], taCols[3], true, 0, '', '', '', GetItemIDFromCode, '');
			
			// ** HAVE TO GET WORK TYPE FROM ITEM FOUND
			
			if(sCustID && dInvoiceDate && sItemID && nInvoiceTot != null && sInvNum){
				createSkeletonOrder(sCustID, dInvoiceDate, sItemID, nInvoiceTot, sInvNum, sInvRegID);
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED")
			throw ex
		else{
			miNumRecsFailed += 1;
			LogError(ex.message, tiRecNum, 'I');
		}
	}
}

/**
 * @param {String} sCustID
 * @param {Date} dOrdDate
 * @param {String} sItemID
 * @param {Number} nOrdTot
 * @param {String} sInvNum
 * @param {UUID} sInvRegID
 * 
 * @properties={typeid:24,uuid:"165CCB9B-4A0B-4CAB-9E9C-A480C86916D2"}
 */
function createSkeletonOrder(sCustID, dOrdDate, sItemID, nOrdTot, sInvNum, sInvRegID){
	dOrdDate = new Date(dOrdDate);
	
	/// ORDER
	/**@type {JSRecord<db:/avanti/sa_order>} */
	var rNewOrder = scopes.avDB.newRecord('sa_order');
	
	rNewOrder.ordh_historical = 1;
	rNewOrder.ordh_document_type = 'ORD';
	rNewOrder.ordh_document_stream = _to_sys_document_number$org_active_default.getRecord(1).docnum_stream;
	rNewOrder.ordh_document_num = globals.GetNextDocumentNumber(rNewOrder.ordh_document_type, rNewOrder.ordh_document_stream);
	rNewOrder.cust_id = sCustID;
	rNewOrder.ordh_order_date = dOrdDate;
	rNewOrder.ordh_csr_empl_id = rNewOrder.sa_order_to_sa_customer.cust_csr_empl_id;
	rNewOrder.curr_id = rNewOrder.sa_order_to_sa_customer.curr_id;
	rNewOrder.ordtype_id = _to_sa_order_type$defaultordertype.ordtype_id;
	
	if(utils.hasRecords(rNewOrder.sa_order_to_sa_customer.sa_customer_to_sa_customer_address_primary)){
		var rCustAddr = rNewOrder.sa_order_to_sa_customer.sa_customer_to_sa_customer_address_primary.getRecord(1);
		
		rNewOrder.custcontact_id = rCustAddr.custcontact_id;
		rNewOrder.ordh_custaddr_id = rCustAddr.custaddr_id;
		rNewOrder.ordh_billto_custaddr_id = rCustAddr.custaddr_id;
		rNewOrder.ordh_shipto_custaddr_id = rCustAddr.custaddr_id;
		rNewOrder.ordh_custaddr_id = rCustAddr.custaddr_id;
		rNewOrder.salesterr_id = rCustAddr.terr_id;
		rNewOrder.ordh_salesper_id = rCustAddr.salesper_id;

		// ORDER ADDRESS
		var rOrderAddress = rNewOrder.sa_order_to_sa_order_address.getRecord(rNewOrder.sa_order_to_sa_order_address.newRecord());
		rOrderAddress.custaddr_id = rCustAddr.custaddr_id;
		rOrderAddress.custcontact_id = rCustAddr.custcontact_id;
		rOrderAddress.ordaddr_code = rCustAddr.custaddr_code;
		rOrderAddress.ordaddr_email_business = rCustAddr.custaddr_email_business;
		rOrderAddress.ordaddr_fax = rCustAddr.custaddr_fax;
		rOrderAddress.ordaddr_fax_ext = rCustAddr.custaddr_fax_ext;
		rOrderAddress.ordaddr_name = rCustAddr.custaddr_address_name;
		rOrderAddress.ordaddr_phone1 = rCustAddr.custaddr_phone1;
		rOrderAddress.ordaddr_phone1_ext = rCustAddr.custaddr_phone1_ext;
		rOrderAddress.ordaddr_phone2 = rCustAddr.custaddr_phone2;
		rOrderAddress.ordaddr_phone2_ext = rCustAddr.custaddr_phone2_ext;
		rOrderAddress.ordaddr_phone3 = rCustAddr.custaddr_phone3;
		rOrderAddress.ordaddr_phone3_ext = rCustAddr.custaddr_phone3_ext;
		rOrderAddress.ordaddr_type = 'C';
		
		// ORDER BILL ADDRESS
		var rBillAddress = rNewOrder.sa_order_to_sa_order_address.getRecord(rNewOrder.sa_order_to_sa_order_address.newRecord());
		rBillAddress.custaddr_id = rCustAddr.custaddr_id;
		rBillAddress.custcontact_id = rCustAddr.custcontact_id;
		rBillAddress.ordaddr_code = rCustAddr.custaddr_code;
		rBillAddress.ordaddr_email_business = rCustAddr.custaddr_email_business;
		rBillAddress.ordaddr_fax = rCustAddr.custaddr_fax;
		rBillAddress.ordaddr_fax_ext = rCustAddr.custaddr_fax_ext;
		rBillAddress.ordaddr_name = rCustAddr.custaddr_address_name;
		rBillAddress.ordaddr_phone1 = rCustAddr.custaddr_phone1;
		rBillAddress.ordaddr_phone1_ext = rCustAddr.custaddr_phone1_ext;
		rBillAddress.ordaddr_phone2 = rCustAddr.custaddr_phone2;
		rBillAddress.ordaddr_phone2_ext = rCustAddr.custaddr_phone2_ext;
		rBillAddress.ordaddr_phone3 = rCustAddr.custaddr_phone3;
		rBillAddress.ordaddr_phone3_ext = rCustAddr.custaddr_phone3_ext;
		rBillAddress.ordaddr_type = 'B';

		// ORDER SHIP ADDRESS
		var rShipAddress = rNewOrder.sa_order_to_sa_order_address.getRecord(rNewOrder.sa_order_to_sa_order_address.newRecord());
		rShipAddress.custaddr_id = rCustAddr.custaddr_id;
		rShipAddress.custcontact_id = rCustAddr.custcontact_id;
		rShipAddress.ordaddr_code = rCustAddr.custaddr_code;
		rShipAddress.ordaddr_email_business = rCustAddr.custaddr_email_business;
		rShipAddress.ordaddr_fax = rCustAddr.custaddr_fax;
		rShipAddress.ordaddr_fax_ext = rCustAddr.custaddr_fax_ext;
		rShipAddress.ordaddr_name = rCustAddr.custaddr_address_name;
		rShipAddress.ordaddr_phone1 = rCustAddr.custaddr_phone1;
		rShipAddress.ordaddr_phone1_ext = rCustAddr.custaddr_phone1_ext;
		rShipAddress.ordaddr_phone2 = rCustAddr.custaddr_phone2;
		rShipAddress.ordaddr_phone2_ext = rCustAddr.custaddr_phone2_ext;
		rShipAddress.ordaddr_phone3 = rCustAddr.custaddr_phone3;
		rShipAddress.ordaddr_phone3_ext = rCustAddr.custaddr_phone3_ext;
		rShipAddress.ordaddr_type = 'S';

		if(utils.hasRecords(rCustAddr.sa_customer_address_to_sys_address)){
			/**@type {JSRecord<db:/avanti/sys_address>} */
			var rAddress = scopes.avDB.newRecord('sys_address');
			var rCustAddress = rCustAddr.sa_customer_address_to_sys_address.getRecord(1);
			
			rAddress.addr_address1 = rCustAddress.addr_address1;
			rAddress.addr_address2 = rCustAddress.addr_address2;
			rAddress.addr_address3 = rCustAddress.addr_address3;
			rAddress.addr_city = rCustAddress.addr_city;
			rAddress.addr_postal = rCustAddress.addr_postal;
			rAddress.addr_street_name = rCustAddress.addr_street_name;
			rAddress.addr_street_number = rCustAddress.addr_street_number;
			rAddress.country_id = rCustAddress.country_id;
			rAddress.stateprov_id = rCustAddress.stateprov_id;
			rAddress.created_by_import = 1;
			
			// use same addr rec for billto and shipto as well
			rOrderAddress.addr_id = rAddress.addr_id;
			rBillAddress.addr_id = rAddress.addr_id;
			rShipAddress.addr_id = rAddress.addr_id;
		}
	}
	
	// ORDER REVISION
	var rNewOrderRevH = rNewOrder.sa_order_to_sa_order_revision_header.getRecord(rNewOrder.sa_order_to_sa_order_revision_header.newRecord());
	
	rNewOrderRevH.ordrevh_revision = 0;
	rNewOrderRevH.ordrevh_order_status = 'Released';
	rNewOrderRevH.ordrevh_total_amount = nOrdTot;
	rNewOrderRevH.ordrevh_subtotal_amount = nOrdTot;
	rNewOrderRevH.ordrevh_total_order_qty = 1;
	rNewOrderRevH.ordrevh_total_ship_qty = 1;
		
	/// ORDER LINE ITEM
	var rLineItem = rNewOrderRevH.sa_order_revision_header_to_sa_order_revision_detail.getRecord(rNewOrderRevH.sa_order_revision_header_to_sa_order_revision_detail.newRecord());
	
	rLineItem.sequence_nr = 1;
	rLineItem.ordrevd_line_num = 1;
	rLineItem.ordrevd_display_line_num = 1;
	rLineItem.ordrevd_qty_ordered = 1;
	rLineItem.ordrevd_qty_shipped = 1;
	rLineItem.whse_id = _to_sys_organization.sys_organization_to_sys_plant$default_plant.plant_default_whse_id;
	rLineItem.ordrevd_extended_price = nOrdTot;
	rLineItem.ordrevd_unit_price = nOrdTot;
	rLineItem.uom_id = scopes.avInv.getSysUomIDFromAppCode('EA');
	rLineItem.ordrevd_unit_price_per = 1;
	rLineItem.ordrevd_unit_price_uom_id = rLineItem.uom_id;
	rLineItem.item_id = sItemID;
	rLineItem.display_code_id = sItemID;	
	rLineItem.ordrevd_prod_desc = rLineItem.sa_order_revision_detail_to_in_item.item_desc1;

	if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_in_item.in_item_to_sa_task_worktype) ) {
		rLineItem.worktype_id = rLineItem.sa_order_revision_detail_to_in_item.in_item_to_sa_task_worktype.worktype_id;
		rLineItem.sysworktype_id = rLineItem.sa_order_revision_detail_to_in_item.in_item_to_sa_task_worktype.sysworktype_id;
	}

	// sa_order_revision_detail_qty?

	// INVOICE
	/**@type {JSRecord<db:/avanti/sa_invoice>} */
	var rInvoice = scopes.avDB.newRecord('sa_invoice');
	
	rInvoice.inv_historical = 1;
	rInvoice.inv_cust_id = sCustID;
	rInvoice.inv_billto_custaddr_id = rNewOrder.ordh_billto_custaddr_id;
	rInvoice.inv_status = 'U'
	rInvoice.inv_date = dOrdDate;
	rInvoice.inv_number = sInvNum;
	rInvoice.curr_id = rNewOrder.curr_id;
	rInvoice.created_date = dOrdDate;
	rInvoice.inv_record_type = 'I';
	rInvoice.inv_type = scopes.avUtils.INVOICE_TYPE.Standard;
	rInvoice.inv_total_amt = nOrdTot;
	rInvoice.inv_shipto_custaddr_id = rNewOrder.ordh_shipto_custaddr_id;
	if(rShipAddress){
		rInvoice.inv_shipto_ordaddr_id = rShipAddress.ordaddr_id;
	}
	rInvoice.terms_id = rNewOrder.sa_order_to_sa_customer.terms_id;
	
	rInvoice.inv_balance_amount = 0;
	rInvoice.inv_balance_owing = 0;
	rInvoice.inv_balance_all = 0;
	rInvoice.inv_date_paid = dOrdDate;
	rInvoice.inv_paid_flag = 1;	
	rInvoice.invreg_id = sInvRegID;
	rInvoice.custcontact_id = rNewOrder.custcontact_id;

	// INVOICE ADDRESS
	if(rBillAddress){
		rInvoice.inv_addr_id = globals.getNewAddressID();
		rInvoice.inv_address_name = rBillAddress.ordaddr_name;
		
		if(utils.hasRecords(rBillAddress.sa_order_address_to_sys_address)){
			rInvoice.sa_invoice_to_sys_address.addr_address1 = rBillAddress.sa_order_address_to_sys_address.addr_address1;
			rInvoice.sa_invoice_to_sys_address.addr_address2 = rBillAddress.sa_order_address_to_sys_address.addr_address2;
			rInvoice.sa_invoice_to_sys_address.addr_address3 = rBillAddress.sa_order_address_to_sys_address.addr_address3;
			rInvoice.sa_invoice_to_sys_address.addr_city = rBillAddress.sa_order_address_to_sys_address.addr_city;
			rInvoice.sa_invoice_to_sys_address.stateprov_id = rBillAddress.sa_order_address_to_sys_address.stateprov_id
			rInvoice.sa_invoice_to_sys_address.addr_postal = rBillAddress.sa_order_address_to_sys_address.addr_postal;
			rInvoice.sa_invoice_to_sys_address.country_id = rBillAddress.sa_order_address_to_sys_address.country_id;
		}
	}
	
	// INVOICE DETAIL
	var rInvoiceDet = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(rInvoice.sa_invoice_to_sa_invoice_det.newRecord());
	rInvoiceDet.sequence_nr = 1;
	rInvoiceDet.invd_invoice_qty = 1;
	rInvoiceDet.invd_unit_price = rLineItem.ordrevd_unit_price;
	rInvoiceDet.invd_description = rLineItem.ordrevd_prod_desc;
	rInvoiceDet.created_date = dOrdDate;
	rInvoiceDet.ordrevd_id = rLineItem.ordrevd_id;
	rInvoiceDet.invd_extended_total = rLineItem.ordrevd_extended_price;
	rInvoiceDet.uom_id = rLineItem.uom_id;
	rInvoiceDet.item_id = rLineItem.item_id;
	rInvoiceDet.ordh_id = rNewOrder.ordh_id;
	rInvoiceDet.invd_unit_price_per = 1;
	rInvoiceDet.invd_unit_price_uom_id = rLineItem.ordrevd_unit_price_uom_id;
	
	databaseManager.saveData();
	
	application.output('ORD: ' + rNewOrder.ordh_document_num + ' -> INV:' + rInvoice.inv_number);
}

/**
 * @return {Number}
 * 
 * @properties={typeid:24,uuid:"6F2A814B-523A-4F2B-8D68-9ABA7BA10346"}
 */
function processCashReceiptDetailRecords(){
	var iStart;
	var bLongFormat = false;
	var nPayTot = 0;
	
	// fst rec is col headings - only if doing Cash Receipt Details import 	
	if(msImportType == 'Cash Receipts'){
		iStart = 0;
	}
	else{
		iStart = 1;
		var aHeaderCols = msLines[0].split('\t');
		
		if(aHeaderCols.length >= 8 && aHeaderCols[6] == 'Invoice Number' && aHeaderCols[7] == 'Payment Amount'){
			bLongFormat = true;
		}
	}
	
	for(var i=iStart;i<msLines.length;i++){
		if(msLines[i]){
			nPayTot += processCashReceiptDetailRecord(msLines[i], i, bLongFormat);
		}
	}

	// update cash receipt tot - have to include existing details recs, not just the ones added here
	if(msImportType == 'Cash Receipt Details'){
		var nCRTot = scopes.avDB.safeSQLQuery('select sum(invoice_payment_amount) from sa_cash_receipt_detail',
			'sa_cash_receipt_id = ?', 'sa_cash_receipt_detail', [sCashReceiptID]);
		
		scopes.avDB.safeSQLRun('update sa_cash_receipt set sa_cash_receipt_payment_amt = ?', 'sa_cash_receipt_id = ?',
			'sa_cash_receipt', [nCRTot, sCashReceiptID]);
	}
	
	return nPayTot;
}

/**
 * @param {String} sTable
 *
 * @properties={typeid:24,uuid:"3BAAE2A5-4063-4041-BF00-4A43AE3B87E4"}
 */
function refreshTableFromDB(sTable){
	databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable), -1);
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, sTable);
}

/**
 * @param {String} tsRecord
 * @param {Number} tiRecNum
 * @param {Boolean} bLongFormat
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"05B4B302-0E94-4497-A342-AF68ECCE1329"}
 */
function processCashReceiptDetailRecord(tsRecord, tiRecNum, bLongFormat){
	var taCols = tsRecord.split('\t')
	var bInserted = false;
	var nPayAmt = 0;

	if(taCols.length >= 2){
		var sInvNum = bLongFormat ? taCols[6] : taCols[0];
		nPayAmt = bLongFormat ? parseFloat(taCols[7]) : parseFloat(taCols[1]);
		
		if(msImportType == 'Cash Receipts'){
			tiRecNum = parseInt(taCols[2]);
		}
		
		if(isBlank(sInvNum)){
			LogError(getReqColMsg("Invoice Number"), tiRecNum, '');
		}
		else if(isBlank(nPayAmt) || !scopes.avUtils.isNumber(nPayAmt) || nPayAmt <= 0)
			LogError(scopes.avText.getLblMsg('PaymentAmountMustBe>0'), tiRecNum, '');
		else{
			var sSQL = scopes.avDB.safeSQL('select inv_id, inv_balance_amount, inv_cust_id, inv_status from sa_invoice', 'inv_number = ?', 'sa_invoice');
			var dsInv = scopes.avDB.SQLQueryDataset(sSQL, false, [removeCommaQuotes(sInvNum)]);
			
			if (dsInv && dsInv.getMaxRowIndex() == 1) {
				var sInvID = dsInv.getValue(1,1).toString();
				var sInvBal = dsInv.getValue(1,2);
				var sInvCustID = dsInv.getValue(1,3);
				var sInvStatus = dsInv.getValue(1,4);
				
				if (sInvStatus != 'U' && sInvStatus != 'P') {
					/** @type {String} */
					var lblMsg = scopes.avText.getLblMsg('CashReceiptUnavailableInvoice');
					LogError(lblMsg.replace("<invoice_num>", removeCommaQuotes(sInvNum)), tiRecNum, '');
				}
				else if (nPayAmt > sInvBal){
					LogError(scopes.avText.getLblMsg('PaymentAmountCantBe>InvoiceBalance'), tiRecNum, '');
				}
				// dont have to do cut test if Cash Receipts import, we have already grouped lines by cust
				else if(msImportType != 'Cash Receipts' && sInvCustID != sCashReceiptCustID && !scopes.avCust.isCustAChildOfParent(sInvCustID, sCashReceiptCustID)){
					LogError(scopes.avText.getLblMsg('invoiceCustDoesntMatchCashReceiptCust'), tiRecNum, '');
				}
				else{
					var sCRDetID = scopes.avDB.getVal('sa_cash_receipt_detail', ['sa_cash_receipt_id', 'inv_id'],
						[sCashReceiptID, sInvID], 'sa_cash_receipt_detail_id');
					
					// detail rec exists for this cr and inv - update
					if(sCRDetID){
						scopes.avDB.safeSQLRun('update sa_cash_receipt_detail set invoice_payment_amount = ?, invoice_total_amount = ?',	
							'sa_cash_receipt_id = ? and inv_id = ?', 'sa_cash_receipt_detail', [nPayAmt, nPayAmt, sCashReceiptID, sInvID]);
					}
					// insert
					else{
						sCRDetID = scopes.avDB.insert('sa_cash_receipt_detail', ['sa_cash_receipt_id', 'inv_id', 'invoice_payment_amount', 'invoice_total_amount'], 
							[sCashReceiptID, sInvID, nPayAmt, nPayAmt], 'sa_cash_receipt_detail_id');
					}
					
					if(sCRDetID){
						scopes.avDB.safeSQLRun('update sa_invoice set inv_committed_amount = ISNULL(inv_committed_amount, 0) + ?',	
							'inv_id = ?', 'sa_invoice', [nPayAmt, sInvID]);
						bInserted = true;
					}
				}
			}
			else{
				LogError("'Invoice Number' does not exist.", tiRecNum, '')
			}
		}
	}
	
	if(bInserted){
		miNumInserts++; 
		return nPayAmt;
	}
	else{
		miNumRecsFailed++; 
		return 0;
	}
}

/**
 * @public 
 * 
 * @param {String} sImportType
 *
 * @properties={typeid:24,uuid:"0645926E-59BD-402D-B447-BD4C20B5D1E1"}
 */
function selectFile(sImportType){
	msImportType = sImportType;
	cboImportType_onAction(null);
	btnSelectFile_onAction(null);
}

/**
 * @public 
 * 
 * @properties={typeid:24,uuid:"F22B5EDE-F85C-4F46-9C4B-5D1F7C1894DC"}
 */
function doImport(){
	mbTurnOffHaltingAfterMaxErrors = true;
	btnImportData_onAction(null, true);
	
	var sMsg = scopes.avText.getLblMsg('NumRecordsImported') + miNumInserts;
	if(miNumErrors > 0){
		sMsg += '\n' + scopes.avText.getLblMsg('NumRecordsInError') + miNumRecsFailed;
		sMsg += '\n\n' + scopes.avText.getLblMsg('Errors:')
		sMsg += '\n\n' + msErrorMsgs;
	}
	forms.utils_msg_box_big.showMsg(sMsg, 'Info');
}

/**
 * @properties={typeid:24,uuid:"A98D054D-13BD-41DC-A39A-3AC088BC4C44"}
 */
function processCashReceiptRecords(){
	/**@type {Array<String>} */
	var aCustPayNumKeys = [];
	/**@type {Array<String>} */
	var aCashReceiptHeaders = [];
	/**@type {Array<Array<String>>} */
	var aCashReceiptDetailLines = [];
	
	// fst rec is col headings	
	for(var i=1;i<msLines.length;i++){
		if(!isBlank(msLines[i])){
			var taCols = msLines[i].split('\t');
			
			if(taCols.length >= 8){
				var sCustPayNumKey = taCols[0] + '-' + taCols[1];
				var nIdx = aCustPayNumKeys.indexOf(sCustPayNumKey);
				
				if(nIdx > -1){
					aCashReceiptDetailLines[nIdx].push(taCols[6] + '\t' + taCols[7] + '\t' + i.toString());
				}
				else{
					aCustPayNumKeys.push(sCustPayNumKey);
					aCashReceiptHeaders.push(taCols[0] + '\t' + taCols[1] + '\t' + taCols[2] + '\t' + taCols[3] + '\t' + taCols[4] + '\t' + taCols[5] + '\t' + i.toString());
					aCashReceiptDetailLines.push([taCols[6] + '\t' + taCols[7] + '\t' + i.toString()]);
				}
			}
		}
	}

	for(i=0;i<aCustPayNumKeys.length;i++){
		var sHeader = aCashReceiptHeaders[i];
		var aDetailLines = aCashReceiptDetailLines[i];
		
		processCashReceiptRecord(sHeader, aDetailLines);
	}
}

/**
 * @param {String} sHeader
 * @param {Array<String>} aDetailLines
 *
 * @properties={typeid:24,uuid:"A9B453FA-8113-4903-BB81-0DE72C190975"}
 */
function processCashReceiptRecord(sHeader, aDetailLines){
	var aHeaderCols = sHeader.split('\t');
	
	if(aHeaderCols.length == 7){
		var sCustCode = aHeaderCols[0];
		var sPayNum = aHeaderCols[1];
		var sPayDate = aHeaderCols[2];
		var sCurrency = aHeaderCols[3];
		var sPayMethod = aHeaderCols[4];
		var sAccount = aHeaderCols[5];
		var tiRecNum = parseInt(aHeaderCols[6]);
		var bErrored = false;
		var sIsInvalidText = scopes.avText.getLblMsg('isInvalid');
		var aInsertColNames = [];
		var aInsertColVals = [];
		var nPayTot = 0;
		var sPaymentType;
		var sDefaultGLAccount;
		
		if(isBlank(sCustCode)){
			LogError(getReqColMsg("Customer Code"), tiRecNum, '');
			bErrored = true;
		}
		else{
			var sCustID = scopes.avDB.safeSQLQuery('select cust_id from sa_customer', 'cust_code = ?', 'sa_customer', [sCustCode]);
			if(sCustID){
				aInsertColNames.push('cust_id');
				aInsertColVals.push(sCustID);
			}
			else{
				LogError('Customer Code ' + sIsInvalidText, tiRecNum, '')
				bErrored = true;
			}
		}
		
		if(isBlank(sPayNum)){
			LogError(getReqColMsg("Payment Number"), tiRecNum, '');
			bErrored = true;
		}
		else{
			aInsertColNames.push('sa_cash_receipt_num');
			aInsertColVals.push(sPayNum);
		}

		if(isBlank(sPayDate)){
			LogError(getReqColMsg("Payment Date"), tiRecNum, '');	
			bErrored = true;
		}
		else if(!IsValidDate(sPayDate)){
			LogError("'Payment Date' " + scopes.avText.getLblMsg('isNotAValidDate'), tiRecNum, '');	
			bErrored = true;
		}
		else{
			aInsertColNames.push('sa_cash_receipt_date');
			aInsertColVals.push("CONVERT ( datetime , '" + sPayDate + "' , 112)");
			aInsertColNames.push('sa_cash_receipt_trans_date');
			aInsertColVals.push("CONVERT ( datetime , '" + sPayDate + "' , 112)");
		}
		
		if(isBlank(sPayMethod)){
			LogError(getReqColMsg("Payment Method"), tiRecNum, '');	
			bErrored = true;
		}
		else{
			var sPayMethodRec = scopes.avDB.safeSQLQuery('select paymethod_id, glacct_id, paymethod_type from sa_payment_method', 'paymethod_code = ?', 'sa_payment_method', [sPayMethod], 3);
			if (sPayMethodRec) {
				aInsertColNames.push('paymethod_id');
				aInsertColVals.push(sPayMethodRec[0]);
				sDefaultGLAccount = sPayMethodRec[1];
				sPaymentType = sPayMethodRec[2];
			}
			else{
				LogError('Payment Method ' + sIsInvalidText, tiRecNum, '')
				bErrored = true;
			}
		}
		
		if (!isBlank(sCurrency)) {
			var sCurrencyID = scopes.avDB.safeSQLQuery('select curr_id from sys_currency', 'curr_iso_code = ?', 'sys_currency', [sCurrency]);
			if (sCurrencyID) {
				aInsertColNames.push('curr_id');
				aInsertColVals.push(sCurrencyID);
			} 
			else {
				LogError('Currency ' + sIsInvalidText, tiRecNum, '')
				bErrored = true;
			}
		}
		else {
			//Set the default organization currency if no currency is set in the import file
			if (utils.hasRecords(_to_sys_organization.sys_organization_to_sys_currency)) {
				aInsertColNames.push('curr_id');
				aInsertColVals.push(_to_sys_organization.sys_organization_to_sys_currency.curr_id);
			}
		}
		
		if(!isBlank(sAccount)){
			var sAccountID = scopes.avDB.safeSQLQuery('select glacct_id from gl_account', 'glacct_number = ?', 'gl_account', [sAccount]);
			if(sAccountID){

				if (sPaymentType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount) {
					aInsertColNames.push('apply_to_account_glacct_id');
					aInsertColVals.push(sAccountID);
					aInsertColNames.push('deposit_account_type');
					aInsertColVals.push(1);
				}
				else if (sPaymentType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount) {
					aInsertColNames.push('apply_to_account_glacct_id');
					aInsertColVals.push(sAccountID);
					aInsertColNames.push('deposit_account_type');
					aInsertColVals.push(2);
				}
				else {
					aInsertColNames.push('bank_account_glacct_id');
					aInsertColVals.push(sAccountID);
					aInsertColNames.push('deposit_account_type');
					aInsertColVals.push(0);
				}
			}
			else{
				LogError('Account ' + sIsInvalidText, tiRecNum, '')
				bErrored = true;
			}
		}
		else {
			aInsertColNames.push('deposit_account_type');
			aInsertColVals.push(0);
			aInsertColNames.push('bank_account_glacct_id');
			aInsertColVals.push(sDefaultGLAccount);
		}
		
		if(!bErrored){
			sCashReceiptID = application.getUUID();
			aInsertColNames.push('sa_cash_receipt_id');
			aInsertColVals.push(sCashReceiptID);
			
			if(aDetailLines.length > 0){
				msLines = aDetailLines;
				nPayTot = processCashReceiptDetailRecords();
			}

			aInsertColNames.push('sa_cash_receipt_amount');
			aInsertColVals.push(nPayTot);
			aInsertColNames.push('sa_cash_receipt_payment_amt');
			aInsertColVals.push(nPayTot);
			aInsertColNames.push('sa_cash_receipt_status');
			aInsertColVals.push('M');
			
			if(nPayTot > 0){
				/** @type {JSFoundSet<db:/avanti/sys_document_number> } */
				var rDocStream = scopes.avDB.getRec("sys_document_number", ["doctype_code", "docnum_active", "docnum_is_default"], [scopes.avUtils.DOCUMENT_TYPE_CODE.CashReceipt, 1, 1]);
				if (rDocStream) {
				    aInsertColNames.push('sa_cash_receipt_document_num');
					aInsertColVals.push(globals.GetNextDocumentNumber(scopes.avUtils.DOCUMENT_TYPE_CODE.CashReceipt, rDocStream.docnum_stream));
				}
				scopes.avDB.insert('sa_cash_receipt', aInsertColNames, aInsertColVals);
			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"0576CF39-5D65-49D1-BDA0-F6B08A661479"}
 */
function processProjectRecords() {
	// fst rec is col headings
	for (var i = 1; i < msLines.length; i++) {
		if (msLines[i]) {
			processProjectRecord(msLines[i], i);
		}
	}

	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_customer_project');
}

/**
 * @param {String} sLine
 * @param {Number} nRecNum
 *
 * @properties={typeid:24,uuid:"BAD524B8-08BA-4ABD-A260-2348B5B8103F"}
 */
function processProjectRecord(sLine, nRecNum) {
	try {
		var taCols = sLine.split('\t');
		var bBadInputs = false;

		if (taCols.length >= 1) {
			// inputs - values from file
			var sInput_Project = Trim(taCols[0]);
			var sInput_Customer = Trim(taCols[1]);
			var sInput_ProjectControlsTaxes = Trim(taCols[2]);
			var sInput_SalesTaxOption = Trim(taCols[3]);
			var sInput_TaxGroup = Trim(taCols[4]);
			var sInput_TaxExemptReason = Trim(taCols[5]);			
			
			// outputs - values to be written to db
			var sOutput_Project = null;
			var sOutput_Customer = null;		
			var nOutput_ProjectControlsTaxes = null;
			var sOutput_SalesTaxOption = null;
			var sOutput_TaxGroup = null;
			var sOutput_TaxExemptReason = null;
			
			// valid inputs/outputs
			var aProjectControlsTaxes_Inputs = ['Y', 'y', 'N', 'n'];
			var aProjectControlsTaxes_Outputs = [1, 1, 0, 0];
			var aSalesTaxOption_Inputs = ['Based on Customer/Ship-To', 'Based on Tax-Type', 'Taxable', 'Non-Taxable'];
			var aSalesTaxOption_Outputs = ['C', 'Y', 'T', 'N'];
			
			var sIsInvalidText = " " + scopes.avText.getLblMsg("isInvalid");
			var asColNames = [];
			var aColValues = [];
			var nSeqNum = null;
			
			// Customer
			if (!isBlank(sInput_Customer)) {
				sOutput_Customer = scopes.avDB.getVal('sa_customer', ['cust_code'], [sInput_Customer], 'cust_id');

				if (!sOutput_Customer) {
					LogError(maProjects[1] + " " + scopes.avText.getLblMsg('isInvalid'), nRecNum, '');
					bBadInputs = true;
				}
			}

			// Project - dont process if we have a bad cust
			if (!bBadInputs && sInput_Project) {
				var sProjectSQL = "SELECT custproj_id FROM sa_customer_project WHERE org_id = ? AND custproj_desc = ?";
				var aProjectArgs = [globals.org_id, sInput_Project];
				
				if (sOutput_Customer) {
					sProjectSQL += " AND cust_id = ?";
					aProjectArgs.push(sOutput_Customer.toString());
				}
				else {
					sProjectSQL += " AND cust_id IS NULL";
				}
				
				sOutput_Project = scopes.avDB.SQLQuery(sProjectSQL, null, aProjectArgs);
			}
			else if (!sInput_Project) {
				LogError(getReqColMsg(maProjects[0]), nRecNum, '');
				bBadInputs = true;
			}

			// Allow Project to control the tax
			if (sInput_ProjectControlsTaxes == NO_VALUE) {
				nOutput_ProjectControlsTaxes = NO_VALUE;
			}
			else if (sInput_ProjectControlsTaxes) {
				nOutput_ProjectControlsTaxes = scopes.avUtils.translate(sInput_ProjectControlsTaxes, aProjectControlsTaxes_Inputs, aProjectControlsTaxes_Outputs);

				if (nOutput_ProjectControlsTaxes == null) {
					LogError(null, nRecNum, '', maProjects[2], aProjectControlsTaxes_Inputs);
					bBadInputs = true;
				}
			}
			
			// Sales Tax Option
			if (sInput_SalesTaxOption == NO_VALUE) {
				sOutput_SalesTaxOption = NO_VALUE;
			}
			else if (sInput_SalesTaxOption) {
				sOutput_SalesTaxOption = scopes.avUtils.translate(sInput_SalesTaxOption, aSalesTaxOption_Inputs, aSalesTaxOption_Outputs);

				if (sOutput_SalesTaxOption == null) {
					LogError(null, nRecNum, '', maProjects[3], aSalesTaxOption_Inputs);
					bBadInputs = true;
				}
			}

			// Tax Group
			if (sInput_TaxGroup == NO_VALUE) {
				sOutput_TaxGroup = NO_VALUE;
			}
			else if (sInput_TaxGroup) {
				sOutput_TaxGroup = scopes.avDB.SQLQuery("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", null, [globals.org_id, sInput_TaxGroup]);

				if (sOutput_TaxGroup == null) {
					LogError(maProjects[4] + sIsInvalidText, nRecNum);
					bBadInputs = true;
				}
			}
			
			// Tax Exempt Reason
			if (sInput_TaxExemptReason == NO_VALUE) {
				sOutput_TaxExemptReason = NO_VALUE;
			}
			else if (sInput_TaxExemptReason) {
				sOutput_TaxExemptReason = scopes.avDB.SQLQuery("SELECT tax_exemption_id FROM sys_tax_exemption_reason WHERE org_id = ? AND tax_exemption_code = ?", null, [globals.org_id, sInput_TaxExemptReason]);

				if (sOutput_TaxExemptReason == null) {
					LogError(maProjects[5] + sIsInvalidText, nRecNum);
					bBadInputs = true;
				}
			}
			
			if (bBadInputs) {
				miNumRecsFailed++;
			}
			else {
				// project already exists - do an update
				if (sOutput_Project) {
					asColNames = ['cust_id'];
					aColValues = [sOutput_Customer];
					
					if (nOutput_ProjectControlsTaxes != NO_VALUE) {
						asColNames.push("project_controls_tax");
						aColValues.push(nOutput_ProjectControlsTaxes);
					}
					if (sOutput_SalesTaxOption != NO_VALUE) {
						asColNames.push("custproj_salestax_option");
						aColValues.push(sOutput_SalesTaxOption);
					}
					if (sOutput_TaxGroup != NO_VALUE) {
						asColNames.push("taxgroup_id");
						aColValues.push(sOutput_TaxGroup);
					}
					if (sOutput_TaxExemptReason != NO_VALUE) {
						var bProceed = false;
						
						if (sOutput_TaxExemptReason) {
							if (sOutput_SalesTaxOption == scopes.avAccounting.TaxCode.NotTaxable) {
								bProceed = true;
							}
							// if no tax option in file then check value we have in record
							else if (!sOutput_SalesTaxOption) {
								var sCurrentSalesTaxOption = scopes.avDB.SQLQuery("SELECT custproj_salestax_option FROM sa_customer_project WHERE org_id = ? AND custproj_id = ?",
									null, [globals.org_id, sOutput_Project]);
								
								if (sCurrentSalesTaxOption == scopes.avAccounting.TaxCode.NotTaxable) {
									bProceed = true;
								}
							}
						}
						else {
							bProceed = true;
						}
						
						if (bProceed) {
							asColNames.push("tax_exemption_id");
							aColValues.push(sOutput_TaxExemptReason);
						}
					}
					
					scopes.avDB.safeSqlUpdate('sa_customer_project', asColNames, aColValues, ['custproj_id'], [sOutput_Project]);
					miNumUpdates++;
				}
				else {
					nSeqNum = scopes.avDB.SQLQuery('select max(sequence_nr) from sa_customer_project', true) + 1;
					asColNames = ['custproj_desc', 'custproj_active', 'sequence_nr', 'cust_id', 'sequence_icon'];
					aColValues = [sInput_Project, 1, nSeqNum, sOutput_Customer, '<html> <head></head> <body> <img src="media:///UpdownGrey.png"></body></html>'];
					
					if (nOutput_ProjectControlsTaxes != null && nOutput_ProjectControlsTaxes != NO_VALUE) {
						asColNames.push("project_controls_tax");
						aColValues.push(nOutput_ProjectControlsTaxes);
					}
					if (sOutput_SalesTaxOption != null && sOutput_SalesTaxOption != NO_VALUE) {
						asColNames.push("custproj_salestax_option");
						aColValues.push(sOutput_SalesTaxOption);
					}
					if (sOutput_TaxGroup != null && sOutput_TaxGroup != NO_VALUE) {
						asColNames.push("taxgroup_id");
						aColValues.push(sOutput_TaxGroup);
					}
					if (sOutput_TaxExemptReason != null && sOutput_TaxExemptReason != NO_VALUE) {
						asColNames.push("tax_exemption_id");
						aColValues.push(sOutput_TaxExemptReason);
					}
					
					scopes.avDB.insert('sa_customer_project', asColNames, aColValues, 'custproj_id');
					
					miNumInserts++;
				}
			}
		}
	}
	catch (ex) {
		if (ex.name == "ERROR LIMIT REACHED")
			throw ex
		else {
			miNumRecsFailed++;
			LogError(ex.message, nRecNum, 'I');
		}
	}
}

/**
 * Process chart of account records
 * 
 * @properties={typeid:24,uuid:"9DA5E819-A032-42D6-A516-C37B2BBE6A18"}
 */
function processChartOfAccountRecords() {
	mfsGlAccount = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'gl_account');
	mfsGlAccountSegment = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'gl_account_segment');
	// first rec is col headings
	for (var i = 1; i < msLines.length; i++) {
		if (msLines[i]) {
			processChartOfAccountRecord(msLines[i], i);
		}
	}

	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'gl_account');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'gl_account_segment');
}

/**
 * Process chart of account record
 * @param {String} tsRecord 
 * @param {Number} tiRecNum 
 *
 * @properties={typeid:24,uuid:"44E915E5-E995-4061-B2AF-79E7D184DDF6"}
 * @AllowToRunInFind
 */
function processChartOfAccountRecord(tsRecord, tiRecNum) {
	var taCols = tsRecord.split('\t')
	var bErrored = false;

	if (taCols.length > 0) {
		if (isBlank(taCols[0])) {
			LogError("Column 'Account' is blank. This is a required field.", tiRecNum, '');
			bErrored = true;
		}

		if (!isBlank(taCols[0]) && !scopes.avAccounting.isGLAccountInValidFormat(taCols[0].toString())) {
			LogError("Column 'Account' is in incorrect format.", tiRecNum, '');
			bErrored = true;
		}

		if (isBlank(taCols[1])) {
			LogError("Column 'Description' is blank. This is a required field.", tiRecNum, '');
			bErrored = true;
		}

		if (!bErrored && (mfsGlAccount.find() || mfsGlAccount.find())) {
			mfsGlAccount.glacct_number = removeCommaQuotes(taCols[0].toUpperCase());
			if (mfsGlAccount.search() > 0) { // found it do an update
				if (_bOverwriteChartOfAccounts == true) {
					WriteChartOfAccountToDB(taCols, 'U', tiRecNum);
				}
			} 
			else { // do an insert
				mfsGlAccount.newRecord();
				mfsGlAccount.glacct_number = taCols[0].toUpperCase();
				WriteChartOfAccountToDB(taCols, 'I', tiRecNum);
			}
		}
	}
}

/**
 * Write chart of accounts records to DB
 * @param {Array<String>} taCols
 * @param {String} tsInsertOrUpdate
 * @param {Number} tiRecNum
 *
 * @properties={typeid:24,uuid:"AEB0B9C0-89A8-43CD-8384-B118C845796B"}
 * @AllowToRunInFind
 */
function WriteChartOfAccountToDB(taCols, tsInsertOrUpdate, tiRecNum) {
	try {
		mbCurrRowErrored = false
		_sInsertOrUpdate = tsInsertOrUpdate;

		var glAcctNum = taCols[0];
		var glAcctDesc = taCols[1];
		var glAcctType = taCols[2];
		var oAccountType = {
			A: 'asset',
			L: 'liability',
			R: 'revenue',
			E: 'expense',
			Q: 'equity',
			C: 'cost of goods sold'
		};

		mfsGlAccount.glacct_number = glAcctNum;
		mfsGlAccount.glacct_desc = glAcctDesc;
		mfsGlAccount.glacct_active = 1;
		mfsGlAccount.created_date = application.getServerTimeStamp();
		mfsGlAccount.created_by_id = globals.avBase_employeeUUID;
		mfsGlAccount.org_id = scopes.globals.org_id;

		var segmentCodes = scopes.avAccounting.getSegmentCodeArray(glAcctNum);
		var orderArray = _to_gl_account_settings.order.split(',');

		for (var counter = 1; counter <= segmentCodes.length; counter++) {
			assignCode(orderArray[counter - 1], segmentCodes[counter - 1]);
		}

		if (mbCurrRowErrored) {
			miNumRecsFailed += 1;
			revertChanges(mfsGlAccount, tsInsertOrUpdate);
		} 
		else {
			databaseManager.saveData(mfsGlAccount.getSelectedRecord());
			CheckForSaveErrors(mfsGlAccount, tsInsertOrUpdate);

			mfsGlAccountSegment = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'gl_account_segment');
			if ((mfsGlAccountSegment.find() || mfsGlAccountSegment.find())) {
				mfsGlAccountSegment.glacctseg_number = mfsGlAccount.account_code;

				if (mfsGlAccountSegment.search() > 0) {
					if (!isBlank(glAcctType)) {
						mfsGlAccountSegment.glacctseg_type = getKeyByValue(oAccountType, glAcctType.toLowerCase());
					}
					databaseManager.saveData(mfsGlAccount.getSelectedRecord());
					CheckForSaveErrors(mfsGlAccount, tsInsertOrUpdate);
				}
			}

			if (tsInsertOrUpdate == 'I') {
				miNumInserts = miNumInserts + 1;
			} 
			else {
				miNumUpdates = miNumUpdates + 1;
			}
		}
	} 
	catch (ex) { // Validate() has its own error handling - this is here in case something gets by our validationand there is an error
		// trying to write to a col
		if (ex.name == "ERROR LIMIT REACHED") {
			throw ex;
		} 
		else {
			miNumRecsFailed += 1;
			revertChanges(mfsGlAccount, tsInsertOrUpdate);
			revertChanges(mfsGlAccountSegment, tsInsertOrUpdate);
			LogError(ex.message, tiRecNum, tsInsertOrUpdate);
		}
	}

	// Returns object key by value
	function getKeyByValue(obj, value) {
		return Object.keys(obj).filter(function(key) {
			return obj[key] === value
		})[0];
	}

	function assignCode(segment_code, value) {
		switch (segment_code) {
		case scopes.avAccounting.GLSEGMENT_CODE.Account:
			mfsGlAccount.account_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.Division:
			mfsGlAccount.division_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.Plant:
			mfsGlAccount.plant_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.Warehouse:
			mfsGlAccount.warehouse_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.OperationCategory:
			mfsGlAccount.opcat_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.Department:
			mfsGlAccount.department_code = value;
			break;
		case scopes.avAccounting.GLSEGMENT_CODE.WorkType:
			mfsGlAccount.worktype_code = value;
			break;
		default:
			break;
		}

	}
}

/**
 * @properties={typeid:24,uuid:"CD9CF808-2934-45E0-8D93-38598F7F0194"}
 */
function processItemDocumentRecords(){
	if(sAppendReplace == 'R'){
		_aItemsProcessed = [];
	}
	
	// fst rec is col headings
	for(var i=1;i<msLines.length;i++){
		if(msLines[i]){
			processItemDocumentRecord(msLines[i], i);
		}
	}

	// have to flush cache as we are inserting using sql
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_item_doc');
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sys_document');
}

/**
 * @properties={typeid:24,uuid:"0988E070-E637-48E2-A33D-C3E769C9AEAF"}
 */
function processItemBOMRecords(){
	if (sAppendReplace == 'R') {
		_aItemsProcessed = [];
	}

	// fst rec is col headings
	for (var i = 1; i < msLines.length; i++) {
		if (msLines[i]) {
			processItemBOMRecord(msLines[i], i);
		}
	}

	// have to flush cache as we are inserting using sql
	plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_item_bill_of_material');
}

/**
 * @param {String} sLine
 * @param {Number} nRecNum
 *
 * @properties={typeid:24,uuid:"DDE88683-576F-4B66-AB19-E1F78B630DDF"}
 */
function processItemDocumentRecord(sLine, nRecNum){
	try{
		var taCols = sLine.split('\t');
		var sCurDateSQL = scopes.avDate.getDateSQl(application.getTimeStamp());
		
		mbCurrRowErrored = false;
		
		if(taCols.length == maItemDocs.length){
			var sItemID = Validate(null, null, nRecNum, maItemDocs[0], taCols[0], true, 0, '', '', '', GetItemIDFromCode);

			// if replace then delete all item doc recs (and their corresponding sys docs), if we havent already processed
			// this item
			if(sAppendReplace == 'R' && sItemID && _aItemsProcessed.indexOf(sItemID) == -1){
				var sItemDocSQL = " from in_item_doc where item_id = ?";
				scopes.avDB.RunSQL("delete from sys_document where doc_id in (select doc_id" + sItemDocSQL + ")", true, [sItemID]);
				scopes.avDB.RunSQL("delete" + sItemDocSQL, true, [sItemID]);
				_aItemsProcessed.push(sItemID);
			}

			// retrieve/convert/validate import values
			var sTitle = Validate(null, null, nRecNum, maItemDocs[1], taCols[1], false, 100, '', '', '', null);
			var sFilePath = Validate(null, null, nRecNum, maItemDocs[2], taCols[2], true, 1000, '', '', '', null);
			var sBOMItem = Validate(null, null, nRecNum, maItemDocs[3], taCols[3], false, null, '', '', '', getBOMItemIDFromCode, sItemID);
			var sSection = Validate(null, null, nRecNum, maItemDocs[4], taCols[4], false, null, '', '', '', getWTSectionIDFromDesc, sItemID);
			var sCategory = Validate(null, null, nRecNum, maItemDocs[5], taCols[5], false, null, '', '', '', getDocumentManagementCategoryIDFromName);
			var sProof = Validate(null, null, nRecNum, maItemDocs[6], taCols[6], false, null, '', 'Y, N', '1,0', null);
			var sForJob = Validate(null, null, nRecNum, maItemDocs[7], taCols[7], false, null, '', 'Y, N', '1,0', null);
			var sOKD = Validate(null, null, nRecNum, maItemDocs[8], taCols[8], false, null, '', 'Y, N', '1,0', null);
			var sOKDBy = Validate(null, null, nRecNum, maItemDocs[9], taCols[9], false, null, '', '', '', GetEmpIDFromCode);
			var sOKDDate = IsValidDate(taCols[10]) ? taCols[10] : null;
			var sShowOnJobTicket = Validate(null, null, nRecNum, maItemDocs[11], taCols[11], false, null, '', 'Y, N', '1,0', null);
			var sShowOnQuote = Validate(null, null, nRecNum, maItemDocs[12], taCols[12], false, null, '', 'Y, N', '1,0', null);

			// if no validation error occurred above then do inserts
			if(!mbCurrRowErrored){
				mbCurrRowErrored = true;
				var sSysDocID = insertSysDoc();
				
				if(sSysDocID){
					var sItemDocID = insertItemDoc();
					
					if(sItemDocID){
						miNumInserts++;
						mbCurrRowErrored = false;
					}
					else{
						// item doc didnt insert for some reason, delete sys doc
						scopes.avDB.RunSQL("delete from sys_document where doc_id = ?", true, [sSysDocID]);
					}
				}
			}
			
			if(mbCurrRowErrored){
				miNumRecsFailed++;
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED"){
			throw ex
		}
		else{
			miNumRecsFailed++;
			LogError(ex.message, nRecNum, 'I');
		}
	}
	
	function insertSysDoc(){
		var asColNames = [];
		var aColValues = [];
		var nSeqNum = scopes.avDB.SQLQuery('select max(sequence_nr) from sys_document', true) + 1;
		var sSeparator = sFilePath.indexOf('/') > -1 ? '/' : '\\';
		var sDocName = scopes.avText.getLastSegment(sFilePath, sSeparator);
		var sExt = scopes.avText.getLastSegment(sDocName, '.');
		var sDocDescription = sDocName.substr(0, sDocName.length - (sExt.length+1));
		// plugins.http.getMediaData('media:///download16.png') returned an array which i couldnt use with sql insert - so 
		// using this string which is the varbinary value for download16.png 
		var sIcon = "0x89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF61000000097048597300000B1300000B1301009A9C180000001974455874536F6674776172650041646F626520496D616765526561647971C9653C000000644944415478DA62FCFFFF3F0325800544141717936C4A6F6F2F23DC00A800D19A8116C2D94CF814662E9D48D030266234E233888990ED845C81D580429760BC7C8206A889CBC0358168101F6F34E232647A743E7981381A0B748E05166C19841400106000CDA328CE22530BCF0000000049454E44AE426082";
		var sDocPath = "";
		var sDocTypeID = null;
		var sDocType = null;
		
		if(globals.avDocs_oDocs.docTypes[sExt.toLowerCase()]){
			sDocTypeID = globals.avDocs_oDocs.docTypes[sExt.toLowerCase()].id;
			
			if(sDocTypeID){
				sDocType = globals.avDocs_oDocs.docTypes[sDocTypeID].type;
			}
		}
		
		// as per jp if no title specified, then used file name wo ext
		if(!sTitle){
			sTitle = sDocDescription;
		}
		
		// sDocPath logic taken from attach file code
        if(utils.hasRecords(_to_docs_management)) {
            if(_to_docs_management.docs_use_sub_for_type) {
            	sDocPath += "Item/";
            }
            
            if(_to_docs_management.docs_use_sub_for_record) {
            	sDocPath += forms['_docs_base'].getPathItem(sItemID);
            }
            else {
            	sDocPath += "/";
            }
        }
		
		asColNames.push('sequence_nr');
		aColValues.push(nSeqNum);
		
		asColNames.push('sequence_icon');
		aColValues.push('<html> <head></head> <body> <img src="media:///UpdownGrey.png"></body></html>');
		
		asColNames.push('doc_title');
		aColValues.push(sTitle);
		
		asColNames.push('doc_name');
		aColValues.push(sDocName);
		
		asColNames.push('doc_description');
		aColValues.push(sDocDescription);
		
		asColNames.push('doc_ext');
		aColValues.push(sExt);
		
		asColNames.push('doc_path');
		aColValues.push(sDocPath);
		
		asColNames.push('doc_link_path');
		aColValues.push(sFilePath);
		
		asColNames.push('doc_path_storage');
		aColValues.push(sFilePath);
		
		asColNames.push('doc_is_link');
		aColValues.push(1);
		
		asColNames.push('docs_mgmt_category_id');
		aColValues.push(sCategory);
		
		asColNames.push('item_id');
		aColValues.push(sItemID);
		
		asColNames.push('doc_type_icon_scaled');
		aColValues.push(sIcon);
		
		asColNames.push('doc_created_by_id');
		aColValues.push(globals.avBase_employeeUUID);
		
		asColNames.push('doc_modified_by_id');
		aColValues.push(globals.avBase_employeeUUID);
		
		asColNames.push('doc_create_date');
		aColValues.push(sCurDateSQL);
		
		asColNames.push('doc_modify_date');
		aColValues.push(sCurDateSQL);
		
		asColNames.push('doctype_id');
		aColValues.push(sDocTypeID);
		
		asColNames.push('doc_type');
		aColValues.push(sDocType);

		// sl-27466 - according to Arron this needs to be set to 0 to solve a problem with DFE
		asColNames.push('doc_is_purged');
		aColValues.push(0);

		return scopes.avDB.insert('sys_document', asColNames, aColValues, 'doc_id');
	}

	function insertItemDoc(){
		var asColNames = [];
		var aColValues = [];
		var nSeqNum = scopes.avDB.SQLQuery('select max(sequence_nr) from in_item_doc', true) + 1;

		asColNames.push('sequence_nr');
		aColValues.push(nSeqNum);
		
		asColNames.push('sequence_icon');
		aColValues.push('<html> <head></head> <body> <img src="media:///UpdownGrey.png"></body></html>');
		
		asColNames.push('doc_id');
		aColValues.push(sSysDocID);
		
		asColNames.push('item_id');
		aColValues.push(sItemID);
		
		asColNames.push('worktypesection_id');
		aColValues.push(sSection);
		
		asColNames.push('itembom_id');
		aColValues.push(sBOMItem);
		
		asColNames.push('item_doc_is_proof');
		aColValues.push(sProof);
		
		asColNames.push('item_doc_is_for_order');
		aColValues.push(sForJob);
		
		asColNames.push('item_doc_is_approved');
		aColValues.push(sOKD);
		
		asColNames.push('item_doc_approved_by');
		aColValues.push(sOKDBy);
		
		asColNames.push('item_doc_approved_date');
		aColValues.push("CONVERT ( datetime , '" + sOKDDate + "' , 112)");
		
		asColNames.push('item_doc_show_jobticket');
		aColValues.push(sShowOnJobTicket);
		
		asColNames.push('item_doc_show_quote');
		aColValues.push(sShowOnQuote);
		
		asColNames.push('created_by_id');
		aColValues.push(globals.avBase_employeeUUID);
		
		asColNames.push('modified_by_id');
		aColValues.push(globals.avBase_employeeUUID);
		
		asColNames.push('created_date');
		aColValues.push(sCurDateSQL);
		
		asColNames.push('modified_date');
		aColValues.push(sCurDateSQL);

		return scopes.avDB.insert('in_item_doc', asColNames, aColValues, 'item_doc_id');
	}
}

/**
 * @private 
 * 
 * @param {String} sLine
 * @param {Number} nRecNum
 *
 * @properties={typeid:24,uuid:"22C65AED-960D-4845-AA2E-C4FAC2FBB8AF"}
 */
function processItemBOMRecord(sLine, nRecNum){
	try{
		var taCols = sLine.split('\t');
		
		mbCurrRowErrored = false;
		
		if (taCols.length == maBOM.length) {
			var sItemID = Validate(null, null, nRecNum, maBOM[0], taCols[0], true, 0, '', '', '', GetItemIDFromCode);
			var sBOMItemID = Validate(null, null, nRecNum, maBOM[1], taCols[1], true, 0, '', '', '', GetItemIDFromCode);
			var nQty = Validate(null, 'item_onhand_qty', nRecNum, maBOM[2], taCols[2], true, 0, 'NUMBER', '', '', null, '');
			
			if (mbCurrRowErrored) {
				miNumRecsFailed++;
			}
			else if (sItemID && sBOMItemID && nQty) {
				// if replace then delete all item bom
				if (sAppendReplace == 'R' && sItemID && _aItemsProcessed.indexOf(sItemID) == -1) {
					scopes.avDB.RunSQL("DELETE FROM in_item_bill_of_material WHERE org_ID = ? AND item_id = ?", null, [globals.org_id, sItemID]);
					_aItemsProcessed.push(sItemID);
				}
				
				var sPKID = application.getUUID().toString();
				var sSequenceIcon = '<html> <head></head> <body><img src="media:///UpdownGrey.png"></body></html>';
				var nSequenceNum = scopes.avDB.SQLQuery("SELECT ISNULL(MAX(sequence_nr), 0) + 1 FROM in_item_bill_of_material WHERE org_id = ? AND item_id = ?", 
					null, [globals.org_id, sItemID]);	
				var sSQL = "INSERT INTO in_item_bill_of_material \
								(itembom_id, item_id, org_id, itembom_rawmat_item_id, itembom_qty_per_unit, sequence_nr, sequence_icon) \
							VALUES \
								(?, ?, ?, ?, ?, ?, ?)";
				var aArgs = [sPKID, sItemID, globals.org_id, sBOMItemID, nQty, nSequenceNum, sSequenceIcon];
				
				if (scopes.avDB.RunSQL(sSQL, null, aArgs)) {
					miNumInserts++;
				}
				else {
					throw {message: "Error Inserting in_item_bill_of_material record"};
				}
			}
		}
	} 
	catch(ex) {
		if(ex.name == "ERROR LIMIT REACHED"){
			throw ex
		}
		else{
			miNumRecsFailed++;
			LogError(ex.message, nRecNum, 'I');
		}
	}
}

/**
 * @private
 *  
 * @param {String} sItemCode
 * @param {UUID} sItemID
 *
 * @return
 * @properties={typeid:24,uuid:"BE313D4A-2DE0-4972-93E2-21E99E4F6EA8"}
 */
function getBOMItemIDFromCode(sItemCode, sItemID){
	var sSQL = "select bom.itembom_id \
				from in_item i \
				inner join in_item_bill_of_material bom on bom.item_id = i.item_id \
				inner join in_item bomItem on bomItem.item_id = bom.itembom_rawmat_item_id \
				where i.item_id = ? and i.itemtype_code = 'B' and bomItem.item_code = ? and bomItem.org_id = ?";

	return scopes.avDB.SQLQuery(sSQL, null, [sItemID.toString(), sItemCode, globals.org_id.toString()]);
}

/**
 * @private 
 * 
 * @param {String} sSectDesc
 * @param {UUID} sItemID
 * 
 * @return
 * @properties={typeid:24,uuid:"94D8AAA5-F869-4AEC-88A3-3D4F181A0BA5"}
 */
function getWTSectionIDFromDesc(sSectDesc, sItemID){
	var sSQL = "select wts.worktypesection_id \
				from in_item i \
				inner join sa_task_worktype_section wts on wts.worktype_id = i.worktype_id \
				where i.item_id = ? and wts.worktypesection_desc = ? and wts.org_id = ?";
	
	return scopes.avDB.SQLQuery(sSQL, null, [sItemID.toString(), sSectDesc, globals.org_id.toString()]);
}

/**
 * @private 
 * 
 * @param {String} sCatName
 *
 * @return
 * @properties={typeid:24,uuid:"E7839198-A94A-465C-B48A-AAEC00C463D1"}
 */
function getDocumentManagementCategoryIDFromName(sCatName){
	var sSQL = "select docs_mgmt_category_id \
				from docs_management_category \
				where category_name = ? and org_id = ?";

	return scopes.avDB.SQLQuery(sSQL, true, [sCatName, globals.org_id.toString()]);
}

/**
 * @param {String} sImportType
 * @param {String} sImportSubType
 * @param {Array<String>} asImportLines
 * @param {String} sFileName
 * @param {Number} [nReplaceExistingRecords]
 *
 * @properties={typeid:24,uuid:"7C762AA5-4BC0-4465-90F4-A9D63F6D1168"}
 */
function doAutoImport(sImportType, sImportSubType, asImportLines, sFileName, nReplaceExistingRecords) {
    bAutoImport = true;
    mbTurnOffHaltingAfterMaxErrors = true;
    msLines = asImportLines;
    msImportType = sImportType;
    msSubType = sImportSubType;
    _sFileName = sFileName;
    sAppendReplace = nReplaceExistingRecords == 1 ? "R" : "A"; 
    
    btnImportData_onAction(null, true);
    
    bAutoImport = false;
}

/**
 * @param {String} sMsg
 * @param {String} sMsgType
 * @param {String} [sTitle]
 *
 * @properties={typeid:24,uuid:"5697E3DB-073A-44D8-B960-E1F5F29D69BF"}
 */
function showMsg(sMsg, sMsgType, sTitle) {
    if (bAutoImport) {
        autoDataImportLog(sMsg, sMsgType);
    }
    else if (sMsgType.toLowerCase() == 'info') {
        plugins.dialogs.showInfoDialog(sTitle, sMsg);
    }
    else if (sMsgType.toLowerCase() == 'warning') {
        plugins.dialogs.showWarningDialog(sTitle, sMsg);
    }
    else if (sMsgType.toLowerCase() == 'error') {
        plugins.dialogs.showErrorDialog(sTitle, sMsg);
    }
}

/**
 * Perform the element default action.
 * 
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D1725863-6DF4-46DF-88F8-EA84B070680D"}
 */
function onAction_btnExportData(event) {
	switch (msImportType) {
		case scopes.avDataExport.ENUM_IMPORT_TYPES.PriceRules:
			doPriceRulesExport();
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Customers:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers);
					break;
				case scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses);
					break;
				case scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts);
					break;
				case scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers);
					break;
				case scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses);
					break;
				case scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments);
					break;
				case scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories);
					break;
				case scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments);
					break;
				case scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Employees:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps);
					break;
				case scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Notes:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.Activity:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.Activity);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.Customer:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.Customer);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder);
					break;
				case scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.CRM:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads);
					break;
				case scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions);
					break;
				case scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer);
					break;
				case scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact);
					break;
				case scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item);
					break;
				case scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems);
					break;
				case scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups);
					break;
				case scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Invoices:
			scopes.avDataExport.exportData(msImportType);
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Projects:
			scopes.avDataExport.exportData(msImportType);
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders);
					break;
				case scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl);
					break;
				case scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip);
					break;
				case scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty);
					break;
			}
			break;
		case scopes.avDataExport.ENUM_IMPORT_TYPES.Accounting:
			switch (msSubType) {
				case scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts:
					scopes.avDataExport.exportData(msImportType, scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts);
					break;
			}
			break;
		default:
			break;
	}
}

/**
 * @properties={typeid:24,uuid:"E1A0406F-BBE3-4F6D-B44B-7EF7BCE7BF39"}
 */
function doPriceRulesExport() {
	/**@type {JSFoundSet<db:/avanti/sa_price_rule>} */
	var fsPriceRules = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_price_rule');
	var sOutput = null;
	var nMaxPriceBreaks = 0;
	
	fsPriceRules.loadAllRecords();
	
	if (utils.hasRecords(fsPriceRules)) {
		fsPriceRules.sort('sa_price_rule_to_sa_price_type.pricetype_type desc, sequence_nr asc');
		
		// PRICE RULES
		for (var r = 1; r <= fsPriceRules.getSize(); r++) {
			var rPriceRule = fsPriceRules.getRecord(r);
			
			if (utils.hasRecords(rPriceRule.sa_price_rule_to_sa_price_rule_detail) && utils.hasRecords(rPriceRule.sa_price_rule_to_sa_price_type)) {
				var rPriceType = rPriceRule.sa_price_rule_to_sa_price_type.getRecord(1);
				var fsPriceRuleDetails = rPriceRule.sa_price_rule_to_sa_price_rule_detail;
				
				if (utils.hasRecords(rPriceType.sa_price_type_to_sa_price_type_segment)) {
					rPriceType.sa_price_type_to_sa_price_type_segment.sort('sequence_nr asc');
				}

				// ui sorts details by segment values
				fsPriceRuleDetails.sort('priceruledtl_repsonse_seg1val asc, priceruledtl_repsonse_seg2val asc, priceruledtl_repsonse_seg3val asc, priceruledtl_repsonse_seg4val asc, priceruledtl_repsonse_seg5val asc, priceruledtl_repsonse_seg6val asc');
				
				// PRICE RULE DETAILS
				for (var d = 1; d <= fsPriceRuleDetails.getSize(); d++) {
					var rPriceRuleDetail = fsPriceRuleDetails.getRecord(d);
					var sLine = null; 
						
					buildDetailLine();				
					
					if (sOutput) {
						sOutput += '\n';
					}
					
					sOutput += sLine;
				}
			}
		}
	}
	
	if (sOutput) {
		// have to do slice - if i just assign maPriceRuleDetails to aColHeaders, when i modify aColHeaders it changes maPriceRuleDetails too
		var aColHeaders = maPriceRuleDetails.slice(0);
		
		for (var b = 1; b <= nMaxPriceBreaks; b++) {
			aColHeaders.push('break quantity');
			aColHeaders.push('break % or $');
		}
		
		var sColHeaders = aColHeaders.join('\t');
		
		sOutput = sColHeaders + '\n' + sOutput
		
        var sFileName = 'Export_PriceRules_' + scopes.avUtils.getTimeStamp("-") + '.txt';
        
        // prompt for folder to save to if smart client
		if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
			sFileName = plugins.file.showFileSaveDialog(sFileName);
		}

		if (sFileName && plugins.file.writeTXTFile(sFileName, sOutput)) {
			scopes.avText.showInfo('PriceRulesExportSuccessful');
        }
	}
	else {
		// nothing to export
	}
	
	function buildDetailLine() {
		// price type		
		sLine += translatePriceType() + '\t';
		// rule description		
		sLine += rPriceRule.pricerule_desc + '\t';
		// price category		
		sLine += rPriceType.pricetype_desc + '\t';
		// date from
		sLine += scopes.avDate.formatDate(rPriceRule.pricerule_start_date, 'YYYYMMDD') + '\t';
		// date to	
		sLine += scopes.avDate.formatDate(rPriceRule.pricerule_end_date, 'YYYYMMDD') + '\t';
		// active	
		sLine += rPriceRule.pricerule_active ? "Y\t" : "N\t";

		// price segment #1	
		sLine += getPriceTypeSegmentValue(1) + '\t';
		// price segment #2	
		sLine += getPriceTypeSegmentValue(2) + '\t';
		// price segment #3	
		sLine += getPriceTypeSegmentValue(3) + '\t';
		// price segment #4	
		sLine += getPriceTypeSegmentValue(4) + '\t';
		// price segment #5	
		sLine += getPriceTypeSegmentValue(5) + '\t';
		// price segment #6
		sLine += getPriceTypeSegmentValue(6) + '\t';
			
		// price method	
		sLine += translatePriceMethod() + '\t';
		// break method	
		sLine += translateBreakMethod() + '\t';
		
		// price method = cost + markup - set appropriate cols only 
		if (rPriceRuleDetail.priceruledtl_price_method == "C") {
			// base markup % (price method = cost plus markup %)
			sLine += rPriceRuleDetail.priceruledtl_base_pct * 100 + '\t';
			sLine += '\t';
			sLine += '\t';
		}
		// price method = actual price - set appropriate cols only
		else if (rPriceRuleDetail.priceruledtl_price_method == "A") {
			sLine += '\t';
			// price (price method = actual price)
			sLine += rPriceRuleDetail.priceruledtl_base_amount + '\t';
			// selling units (price method = actual price)
			sLine += translateSellingUnits(rPriceRuleDetail.priceruledtl_task_price_uom) + '\t';
			
			// step pricing selling units (price method = actual price)
			if (rPriceRuleDetail.priceruledtl_break_method == scopes.avUtils.PRICERULE_BREAK_METHOD.StepPricing 
					&& scopes.avPriceRules.isStepPricingUomSupported(rPriceRuleDetail.priceruledtl_task_price_uom)
					&& rPriceRuleDetail.priceruledtl_task_sell_uom) {

				sLine += translateSellingUnits(rPriceRuleDetail.priceruledtl_task_sell_uom);
			}
		}
		
		// PRICE BREAKS
		if (utils.hasRecords(rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks)) {
			// ui sorts breaks by qty
			rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.sort('pricerulebreak_qty asc');
			
			for (var rb = 1; rb <= rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getSize(); rb++) {
				var rPriceBreak = rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getRecord(rb);

				// break quantity
				sLine += '\t' + rPriceBreak.pricerulebreak_qty;
				
				// break %
				if (rPriceRuleDetail.priceruledtl_price_method == "C") {
					sLine += '\t' + rPriceBreak.pricerulebreak_pct * 100;
				}
				// break $
				else if (rPriceRuleDetail.priceruledtl_price_method == "A") {
					sLine += '\t' + rPriceBreak.pricerulebreak_amount;
				}
			}
			
			if (rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getSize() > nMaxPriceBreaks) {
				nMaxPriceBreaks = rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getSize();
			}
		}
	}
	
	function translatePriceType() {
		if (rPriceType.pricetype_type == 'INK') {
			return 'Ink Pricing';
		}
		else if (rPriceType.pricetype_type == 'ITEM') {
			return 'Item Pricing';
		}
		else if (rPriceType.pricetype_type == 'TASK') {
			return 'Task Pricing';
		}
		else {
			return '';
		}
	}
	
	function translatePriceMethod() {
		if(rPriceRuleDetail.priceruledtl_price_method == "C"){
			return i18n.getI18NMessage('avanti.lbl.costPlusMarkup%');
		}
		else if(rPriceRuleDetail.priceruledtl_price_method == "A"){
			return i18n.getI18NMessage('avanti.lbl.priceMethod_actualPrice');
		}
		else{
			return '';
		}
	}
	
	function translateBreakMethod() {
		if(rPriceRuleDetail.priceruledtl_break_method == "QB"){
			return i18n.getI18NMessage('avanti.lbl.breakMethod_QuantityBreaks');
		}
		else if(rPriceRuleDetail.priceruledtl_break_method == "SP"){
			return i18n.getI18NMessage('avanti.lbl.breakMethod_StepPricing');
		}
		else if(rPriceRuleDetail.priceruledtl_break_method == "CB"){
			return i18n.getI18NMessage('avanti.lbl.breakMethod_CostBreaks');
		}
		else if(rPriceRuleDetail.priceruledtl_break_method == "CP"){
			return i18n.getI18NMessage('avanti.lbl.breakMethod_StepPricingCost');
		}
		else{
			return '';
		}
	}
	
	function translateSellingUnits(sUomCode) {
		switch (sUomCode) {
			case "EACH": return i18n.getI18NMessage('avanti.lbl.taskPriceType_Each');
			case "PIECE": return i18n.getI18NMessage('avanti.lbl.taskPriceType_Piece');
			case "THOUSAND": return i18n.getI18NMessage('avanti.lbl.taskPriceType_Thousand');
			case "PAGE": return i18n.getI18NMessage('avanti.lbl.taskPriceType_Pages');
			case "SHEET": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Sheets');
			case "IMPRESSIONS": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Impressions');
			case "CLICKS": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Clicks');
			case "SQFT": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqFeet');
			case "SQIN": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqInch');
			case "SQCM": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqCentimetres');
			case "FEET": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearFeet');
			case "FEETTASK": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearFeetTask');
			case "INCH": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearInches');
			case "POUND": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Pounds');
			case "KILO": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Kilograms');
			case "HOURS": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Hours');
			case "TASKQTY": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_TaskQuantity');
			case "IMAGEAREA": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_ImageArea');
			case "IMAGEAREAxPAGES": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_ImageAreaXPages');
			case "FINISHSIZEAREA": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_FinishSizeArea');
			case "FINISHSIZEAREAxPAGES": return i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_FinishSizeAreaXPages');
			case "PRINTEDIMAGES": return i18n.getI18NMessage('avanti.lbl.taskPriceType_PrintedImages');
			default: return '';
		}
	}
	
	function getPriceTypeSegmentValue(nSegmentNum) {
		var sSegmentValue = '';
		
		if (rPriceType.sa_price_type_to_sa_price_type_segment && rPriceType.sa_price_type_to_sa_price_type_segment.getSize() >= nSegmentNum) {
			var rPriceTypeSegment = rPriceType.sa_price_type_to_sa_price_type_segment.getRecord(nSegmentNum); 
			
			// i was using rPriceRuleDetail.priceruledtl_repsonse_segXval for below, but found they werent always populated. look it up using id instead
			if (rPriceTypeSegment.pricetypeseg_code) {
				if (nSegmentNum == 1 && rPriceRuleDetail.priceruledtl_repsonse_seg1) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg1);
				}
				else if (nSegmentNum == 2 && rPriceRuleDetail.priceruledtl_repsonse_seg2) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg2);
				}
				else if (nSegmentNum == 3 && rPriceRuleDetail.priceruledtl_repsonse_seg3) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg3);
				}
				else if (nSegmentNum == 4 && rPriceRuleDetail.priceruledtl_repsonse_seg4) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg4);
				}
				else if (nSegmentNum == 5 && rPriceRuleDetail.priceruledtl_repsonse_seg5) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg5);
				}
				else if (nSegmentNum == 6 && rPriceRuleDetail.priceruledtl_repsonse_seg6) {
					sSegmentValue = getPriceRuleSegmentValueFromID(rPriceTypeSegment.pricetypeseg_code, rPriceRuleDetail.priceruledtl_repsonse_seg6);
				}
			}
		}
		
		return sSegmentValue;
	}
}

/**
 * @private 
 * 
 * @properties={typeid:24,uuid:"78E8046A-7B81-45B4-BA25-48E9E44CD508"}
 */
function importPriceRuleDetails(){
	// fst rec is col headings
	for (var i = 1; i < msLines.length; i++) {
		if (msLines[i]) {
			importPriceRuleDetail(msLines[i], i);
		}
	}
}

/**
 * @param {Array<String>} taCols
 * 
 * return {Array<String>}
 *
 * @return
 * @properties={typeid:24,uuid:"F5EFC4A7-7DF3-4E13-8CE8-1177AE2292DF"}
 */
function getPriceBreakCols(taCols) {
	var aBreakCols = taCols.slice(maPriceRuleDetails.length);
	var aRealBreakCols = [];
	
	for (var i = 0; i < aBreakCols.length; i++) {
		if (Trim(aBreakCols[i]) == '') {
			break;
		}
		else {
			aRealBreakCols.push(aBreakCols[i]);
		}
	}
	
	return aRealBreakCols;
}

/**
 * @private 
 * 
 * @param {String} sLine
 * @param {Number} nRecNum
 *
 * @properties={typeid:24,uuid:"*************-4D99-9CF6-5FC5DEB33F3B"}
 */
function importPriceRuleDetail(sLine, nRecNum){
	try {
		var taCols = sLine.split('\t');
		var bBadInputs = false;
		var aValidPriceTypes = ['Task Pricing', 'Item Pricing', 'Ink Pricing'];
		/**@type {JSRecord<db:/avanti/sa_price_type>} */
		var rPriceCategory = null;
		/**@type {{bValid:Boolean, aSegmentIDs:Array<UUID>}}*/
		var oSegments = null;
		/**@type {Array<String>} */
		var aBreakCols = getPriceBreakCols(taCols);
		/**@type {Array<JSRecord>} */
		var aInsertRecords = [];
		/**@type {Array<JSRecord>} */
		var aUpdateRecords = [];
		
		mbCurrRowErrored = false;
		
		if (taCols.length >= 1) {
			// PRICE RULE KEY COLS
			var sPriceType = Trim(taCols[0]);
			var sRuleDescription = cleanImportText(taCols[1]);
			var sPriceCategory = cleanImportText(taCols[2]);
			
			// PRICE RULE DETAIL KEY COLS
			var sPriceSegment1 = cleanImportText(taCols[6]);
			var sPriceSegment2 = cleanImportText(taCols[7]);
			var sPriceSegment3 = cleanImportText(taCols[8]);
			var sPriceSegment4 = cleanImportText(taCols[9]);
			var sPriceSegment5 = cleanImportText(taCols[10]);
			var sPriceSegment6 = cleanImportText(taCols[11]);
			var aSegmentValues = [sPriceSegment1, sPriceSegment2, sPriceSegment3, sPriceSegment4, sPriceSegment5, sPriceSegment6];
			
			if (isBlank(sPriceType)) {
				LogError(getReqColMsg(maPriceRuleDetails[0]), nRecNum);
				bBadInputs = true;
			}
			else if (aValidPriceTypes.indexOf(sPriceType) == -1) {
				LogError(sPriceType + " is not a valid Price Type.", nRecNum);
				bBadInputs = true;
			}
			
			if (isBlank(sRuleDescription)) {
				LogError(getReqColMsg(maPriceRuleDetails[1]), nRecNum);
				bBadInputs = true;
			}
			
			if (isBlank(sPriceCategory)) {
				LogError(getReqColMsg(maPriceRuleDetails[2]), nRecNum);
				bBadInputs = true;
			}
			else {
				rPriceCategory = scopes.avDB.getRec('sa_price_type', ['pricetype_desc'], [sPriceCategory]);
			}
			
			if (!rPriceCategory) {
				LogError(maPriceRuleDetails[2] + " " + scopes.avText.getLblMsg('isInvalid'), nRecNum);
				bBadInputs = true;
			}
			else {
				oSegments = validatePriceRuleSegments(rPriceCategory, aSegmentValues, nRecNum);
				
				if (!oSegments.bValid) {
					bBadInputs = true;
				}
			}

			if (!validatePriceBreaks(aBreakCols, nRecNum)) {
				bBadInputs = true;
			}

			if (bBadInputs) {
				miNumRecsFailed++;
			}
			else {
				// PRICE RULE IMPORT
				var rPriceRule = getPriceRule(sPriceType, sRuleDescription, rPriceCategory.pricetype_id);
				var bPriceRuleFound = false;
				var bPriceRuleDetailFound = false;
				
				// price rule already exists - do an update
				if (rPriceRule) {
					bPriceRuleFound = true;
					aUpdateRecords.push(rPriceRule);
				}
				// do insert
				else {
					rPriceRule = scopes.avDB.newRecord('sa_price_rule');
					
					rPriceRule.pricerule_type = getPriceTypeCode(sPriceType);
					rPriceRule.pricerule_desc = sRuleDescription;
					rPriceRule.pricetype_id = rPriceCategory.pricetype_id;
					rPriceRule.sequence_nr = getNextPriceRuleSeqNum(rPriceRule.pricerule_type);
					
					aInsertRecords.push(rPriceRule);
				}
				
				// set rPriceRule cols
				Validate(rPriceRule.foundset, 'pricerule_start_date', nRecNum, maPriceRuleDetails[3], taCols[3], false, 0, 'DATE', '', '', null, '');
				Validate(rPriceRule.foundset, 'pricerule_end_date', nRecNum, maPriceRuleDetails[4], taCols[4], false, 0, 'DATE', '', '', null, '');
				Validate(rPriceRule.foundset, 'pricerule_active', nRecNum, maPriceRuleDetails[5], taCols[5], true, 0, '', 'Y, N', '1,0', null);
				
				// PRICE RULE DETAIL IMPORT
				if (!mbCurrRowErrored) {
					var rPriceRuleDetail = null;
					
					// We'll base the _sInsertOrUpdate flag on insert or update detail, since there is 1 import line per detail
					_sInsertOrUpdate = "I";
					
					if (bPriceRuleFound) {
						rPriceRuleDetail = getPriceRuleDetail(rPriceRule.pricerule_id, oSegments.aSegmentIDs, aSegmentValues);
						
						if (rPriceRuleDetail) {
							bPriceRuleDetailFound = true
							_sInsertOrUpdate = "U";
							aUpdateRecords.push(rPriceRuleDetail);
						}
					}

					if (_sInsertOrUpdate == 'I') {
						rPriceRuleDetail = rPriceRule.sa_price_rule_to_sa_price_rule_detail.getRecord(rPriceRule.sa_price_rule_to_sa_price_rule_detail.newRecord());
						
						// populate segment cols
						for (var s = 0; s < oSegments.aSegmentIDs.length; s++) {
							if (oSegments.aSegmentIDs[s]) {
								rPriceRuleDetail.foundset.setDataProviderValue('priceruledtl_repsonse_seg' + (s + 1), oSegments.aSegmentIDs[s]);
								rPriceRuleDetail.foundset.setDataProviderValue('priceruledtl_repsonse_seg' + (s + 1) + 'val', aSegmentValues[s]);
							}
						}

						aInsertRecords.push(rPriceRuleDetail);
					}
					
					// validate/set rPriceRuleDetail cols here
					var sPriceMethodOptions = scopes.avText.getLblMsg('costPlusMarkup%') + ', ' + scopes.avText.getLblMsg('priceMethod_actualPrice');
					var sBreakMethodOptions = '';
					
					// sBreakMethodOptions vary depending on the PriceMethod used on this line
					if (taCols[12].equalsIgnoreCase(scopes.avText.getLblMsg('priceMethod_actualPrice'))) {
						sBreakMethodOptions = scopes.avText.getLblMsg('breakMethod_QuantityBreaks') + ', ' + 
						 					  scopes.avText.getLblMsg('breakMethod_StepPricing');
					}
					else if (taCols[12].equalsIgnoreCase(scopes.avText.getLblMsg('costPlusMarkup%'))) {
						sBreakMethodOptions = scopes.avText.getLblMsg('breakMethod_QuantityBreaks') + ', ' + 
											  scopes.avText.getLblMsg('breakMethod_StepPricing') + ', ' +
											  scopes.avText.getLblMsg('breakMethod_CostBreaks') + ', ' + 
											  scopes.avText.getLblMsg('breakMethod_StepPricingCost');
					}
					
					var sSUCodes = "EACH,PIECE,THOUSAND,PAGE,SHEET,IMPRESSIONS,CLICKS,SQFT,SQIN,SQCM,FEET,FEETTASK,INCH,POUND,KILO,HOURS,TASKQTY,IMAGEAREA,IMAGEAREAxPAGES,FINISHSIZEAREA,FINISHSIZEAREAxPAGES,PRINTEDIMAGES";
					var aSUDescriptions = [i18n.getI18NMessage('avanti.lbl.taskPriceType_Each'), i18n.getI18NMessage('avanti.lbl.taskPriceType_Piece'), i18n.getI18NMessage('avanti.lbl.taskPriceType_Thousand'),
						i18n.getI18NMessage('avanti.lbl.taskPriceType_Pages'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Sheets'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Impressions'),
						i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Clicks'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqFeet'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqInch'), 
						i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_SqCentimetres'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearFeet'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearFeetTask'),
						i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_LinearInches'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Pounds'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Kilograms'),
						i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_Hours'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_TaskQuantity'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_ImageArea'),
						i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_ImageAreaXPages'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_FinishSizeArea'), i18n.getI18NMessage('i18n:avanti.lbl.taskPriceType_FinishSizeAreaXPages'),
						i18n.getI18NMessage('avanti.lbl.taskPriceType_PrintedImages')];
					var sSUDescriptions = aSUDescriptions.join(', ');					
					
					Validate(rPriceRuleDetail.foundset, 'priceruledtl_price_method', nRecNum, maPriceRuleDetails[12], taCols[12], true, 0, '', sPriceMethodOptions, 'C,A', null);
					Validate(rPriceRuleDetail.foundset, 'priceruledtl_break_method', nRecNum, maPriceRuleDetails[13], taCols[13], true, 0, '', sBreakMethodOptions, 'QB,SP,CB,CP', null);

					if (taCols[12].equalsIgnoreCase(scopes.avText.getLblMsg('priceMethod_actualPrice'))) {
						Validate(rPriceRuleDetail.foundset, 'priceruledtl_base_amount', nRecNum, maPriceRuleDetails[15], taCols[15], false, 0, 'NUMBER', '', '', null, '');
					}
					else if (taCols[12].equalsIgnoreCase(scopes.avText.getLblMsg('costPlusMarkup%'))) {
						Validate(rPriceRuleDetail.foundset, 'priceruledtl_base_pct', nRecNum, maPriceRuleDetails[14], taCols[14] / 100, false, 0, 'NUMBER', '', '', null, '');
					}
					
					Validate(rPriceRuleDetail.foundset, 'priceruledtl_task_price_uom', nRecNum, maPriceRuleDetails[16], taCols[16], false, 0, '', sSUDescriptions, sSUCodes, null);
					Validate(rPriceRuleDetail.foundset, 'priceruledtl_task_sell_uom', nRecNum, maPriceRuleDetails[17], taCols[17], false, 0, '', sSUDescriptions, sSUCodes, null);

					// PRICE RULE BREAK IMPORT
					if (!mbCurrRowErrored) {
						// delete all price breaks if detail found - then the ones from the template will be added below
						if (bPriceRuleDetailFound && utils.hasRecords(rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks)) {
							rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.deleteAllRecords();
						}

						// add in template breaks
						for (var b = 0; b < aBreakCols.length; b += 2) {
							var nBreakQty = parseFloat(aBreakCols[b]);
							var nBreakPctOr$ = parseFloat(aBreakCols[b + 1]);
							var rPriceRuleBreak = rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getRecord(rPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.newRecord());
							
							rPriceRuleBreak.pricerulebreak_qty = nBreakQty;
								
							// break %
							if (rPriceRuleDetail.priceruledtl_price_method == "C") {
								rPriceRuleBreak.pricerulebreak_pct = nBreakPctOr$ / 100;
							}
							// break $
							else if (rPriceRuleDetail.priceruledtl_price_method == "A") {
								rPriceRuleBreak.pricerulebreak_amount = nBreakPctOr$;
							}
						}
					}
				}
				
				// if error occurred revert changes to all affected recs
				if (mbCurrRowErrored) {
					miNumRecsFailed++;
					revertRecs(aInsertRecords, aUpdateRecords);
				}
				// else save everything
				else {
					if (_sInsertOrUpdate == 'I') {
						miNumInserts++;
					}
					else if (_sInsertOrUpdate == 'U') {
						miNumUpdates++;
					}

					databaseManager.saveData();
				}
			}
		}
	} 
	catch (ex) {
		if (ex.name == "ERROR LIMIT REACHED") {
			throw ex
		}
		else {
			miNumRecsFailed++;
			LogError(ex.message, nRecNum, _sInsertOrUpdate);
			revertRecs(aInsertRecords, aUpdateRecords);
		}
	}
}

/**
 * @private 
 * 
 * @param {Array<String>} aBreakCols
 * @param {Number} nRecNum
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"D2EA6133-DCB4-457D-9E3D-5931B5523CD1"}
 */
function validatePriceBreaks(aBreakCols, nRecNum) {
	// variable break cols must come in pairs (qty/pctOrPrice), if aBreakCols.length is odd we have an incomplete pair - dont do import
	if (scopes.avMath.isOdd(aBreakCols.length)) {
		LogError("Incomplete Break pair. There must be a 'break quantity' and 'break % or $' for each break.", nRecNum);
		return false;
	}
	
	for (var b = 0; b < aBreakCols.length; b += 2) {
		var nBreakQty = aBreakCols[b];
		var nBreakPctOr$ = aBreakCols[b + 1];
		
		if (!scopes.avMath.isNumber(nBreakQty) || !scopes.avMath.isNumber(nBreakPctOr$)) {
			LogError("'break quantity' and 'break % or $' must both be numbers", nRecNum, _sInsertOrUpdate);
			return false;
		}
	}
	
	return true;
}

/**
 * @private 
 * 
 * @param {String} sPriceTypeCode
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"A0E40FE9-2C2B-4BF9-AE1A-AF12F7A76D8F"}
 */
function getNextPriceRuleSeqNum(sPriceTypeCode) {
	var sSQL = "SELECT MAX(sequence_nr) \
				FROM sa_price_rule \
				WHERE \
					org_id = ? \
					AND pricerule_type = ?";
	var aArgs = [globals.org_id, sPriceTypeCode];
	var nMaxSeqNum = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	
	if (!nMaxSeqNum) {
		nMaxSeqNum = 0;
	}
	
	return nMaxSeqNum + 1;
}

/**
 * @private 
 * 
 * @param {Array<JSRecord>} aInsertRecords
 * @param {Array<JSRecord>} aUpdateRecords
 *
 * @properties={typeid:24,uuid:"479159B8-C8E9-4369-957C-4BD1182253F1"}
 */
function revertRecs(aInsertRecords, aUpdateRecords) {
	/**@type {JSRecord} */
	var rec;
	var i;
	
	for (i = 0; i < aInsertRecords.length; i++) {
		rec = aInsertRecords[i];
		rec.foundset.deleteRecord(rec);
	}
	
	for (i = 0; i < aUpdateRecords.length; i++) {
		rec = aUpdateRecords[i];
		rec.revertChanges();
	}
}

/**
 * @private 
 * 
 * @param {JSRecord} rec
 * @param {String} sInsertOrUpdate
 *
 * @properties={typeid:24,uuid:"3DB15967-5BAE-4C84-AA24-FA1503205EFB"}
 */
function revertRecordChanges(rec, sInsertOrUpdate){
	if (rec) {
		if (sInsertOrUpdate == 'I') {
			rec.foundset.deleteRecord();
		}
		else if (sInsertOrUpdate == 'U') {
			rec.revertChanges();
		}
	}
}

/**
 * @private 
 * 
 * @param {JSRecord<db:/avanti/sa_price_type>} rPriceCategory
 * @param {Array<String>} aSegmentValues
 * @param {Number} nRecNum
 * 
 * @return {{bValid:Boolean, aSegmentIDs:Array<UUID>}}
 *
 * @properties={typeid:24,uuid:"C8FA3B94-2F23-43C3-80A0-E202159C226B"}
 */
function validatePriceRuleSegments(rPriceCategory, aSegmentValues, nRecNum) {
	/***@type {{bValid:Boolean, aSegmentIDs:Array<UUID>}}*/
	var oReturn = {
		bValid: true,
		aSegmentIDs: [null, null, null, null, null, null]
	};
	var nNumSegments = 0;
	var i;
	var uTaskID = null;
	
	if (utils.hasRecords(rPriceCategory.sa_price_type_to_sa_price_type_segment)) {
		databaseManager.saveData(rPriceCategory.sa_price_type_to_sa_price_type_segment);
		rPriceCategory.sa_price_type_to_sa_price_type_segment.sort('sequence_nr asc');
		
		// there should only ever be 6 segments, but i put 'i < 7' just in case - we only import 6
		for (i = 1; i <= rPriceCategory.sa_price_type_to_sa_price_type_segment.getSize() && i < 7; i++) {
			var rSegment = rPriceCategory.sa_price_type_to_sa_price_type_segment.getRecord(i)

			// this segment used
			if (rSegment.pricetypeseg_code) {
				nNumSegments++;
				var sSegmentValue = aSegmentValues[i-1];

				// validate Segment Value against whats expected for this Segment Codes
				if (sSegmentValue) {
					var uID = getPriceRuleSegmentValueID(rSegment.pricetypeseg_code, sSegmentValue, uTaskID);
					
					if (uID) {
						oReturn.aSegmentIDs[i-1] = uID;
						
						// if this a Task segment then get task id to be used later if there is a Variable segment. 
						if (rSegment.pricetypeseg_code == 'H') {
							uTaskID = uID;
						}
					}
					else {
						LogError("Segment #" + i + " has an invalid value", nRecNum);
						oReturn.bValid = false;
						break;
					}
				}
				else if (rSegment.pricetypeseq_flag) {
					LogError("Segment #" + i + " is mandatory and isn't filled in", nRecNum);
					oReturn.bValid = false;
					break;
				}
			}
		}
	}
	
	if (nNumSegments == 0) {
		LogError("No segments defined for price type " + rPriceCategory.pricetype_segment_desc, nRecNum);
		oReturn.bValid = false;
	}
		
	return oReturn;
}

/**
 * @private 
 * 
 * @param {String} sSegmentCode
 * @param {String} sSegmentValue
 * @param {UUID} [uTaskID]
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"541685D4-DF62-4659-ABB6-3AA6EB047564"}
 */
function getPriceRuleSegmentValueID(sSegmentCode, sSegmentValue, uTaskID) {
	var uID = null;
	var sSQL;
	
	switch (sSegmentCode) {
		// Customer
		case 'A':
			uID = scopes.avDB.SQLQuery("SELECT cust_id FROM sa_customer WHERE org_id = ? AND cust_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Customer Category
		case 'B':
			uID = scopes.avDB.SQLQuery("SELECT custcat_id FROM sa_customer_category WHERE org_id = ? AND custcat_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Customer Class
		case 'C':
			uID = scopes.avDB.SQLQuery("SELECT custclass_id FROM sa_customer_class WHERE org_id = ? AND custclass_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Item
		case 'D':
			uID = scopes.avDB.SQLQuery("SELECT item_id FROM in_item WHERE org_id = ? AND item_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Item Class
		case 'E':
			uID = scopes.avDB.SQLQuery("SELECT itemclass_id FROM in_item_class WHERE org_id = ? AND itemclass_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Item Group
		case 'F':
			uID = scopes.avDB.SQLQuery("SELECT ingroup_id FROM in_group WHERE org_id = ? AND ingroup_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Task Type
		case 'G':
			uID = scopes.avDB.SQLQuery("SELECT tasktype_uuid FROM app_task_type WHERE tasktype_desc = ?", null, [sSegmentValue]);
			break;
		// Task
		case 'H':
			uID = scopes.avDB.SQLQuery("SELECT task_id FROM sa_task WHERE org_id = ? AND task_description = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Department
		case 'I':
			uID = scopes.avDB.SQLQuery("SELECT dept_id FROM sys_department WHERE org_id = ? AND dept_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Task Operation
		case 'J':
			// sSegmentValue is in tasktype - taskop format. eg: 'Digital Sheet - RUN'
			var aSegmentValueParts = sSegmentValue.split(' - ');
			
			if (aSegmentValueParts.length == 2) {
				var sTaskType = aSegmentValueParts[0];
				var sTaskOperation = aSegmentValueParts[1];
				
				if (sTaskType && sTaskOperation) {
					sSQL = "SELECT ato.taskoper_id \
								FROM app_task_operation ato \
								INNER JOIN app_task_type att ON ato.tasktype_id = att.tasktype_id \
								WHERE \
									att.tasktype_desc = ? \
									AND ato.taskoper_desc = ?";	
		
					uID = scopes.avDB.SQLQuery(sSQL, null, [sTaskType, sTaskOperation]);
				}
			}
			
			break;
		// Cost Center
		case 'K':
			sSQL = "SELECT c.cc_id \
					FROM sys_cost_centre c \
					INNER JOIN sys_operation_category o on c.opcat_id = o.opcat_id \
					INNER JOIN sys_department d on o.dept_id = d.dept_id \
					WHERE \
						c.org_id = ? \
						AND d.dept_code + '-' + o.opcat_code + '-' + c.cc_op_code = ?";	

			uID = scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, sSegmentValue]);
			break;
		// Ink
		case 'L':
			uID = scopes.avDB.SQLQuery("SELECT inktype_id FROM in_ink_type WHERE org_id = ? AND inktype_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Variable
		case 'M':
			if (uTaskID) {
				uID = scopes.avDB.SQLQuery("SELECT taskmachine_id FROM sa_task_machine WHERE org_id = ? AND task_id = ? AND taskmachine_description = ?", null, 
					[globals.org_id, uTaskID.toString(), sSegmentValue]);
			}
			break;
		// Div
		case 'N':
			uID = scopes.avDB.SQLQuery("SELECT div_id FROM sys_division WHERE org_id = ? AND div_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
		// Plant
		case 'O':
			uID = scopes.avDB.SQLQuery("SELECT plant_id FROM sys_plant WHERE org_id = ? AND plant_code = ?", null, [globals.org_id, sSegmentValue]);
			break;
	}
	
	return uID;
}

/**
 * @private 
 * 
 * @param {String} sSegmentCode
 * @param {UUID} uSegmentID
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"AD3C6882-8F25-4957-96C4-E9A6ED9DD301"}
 */
function getPriceRuleSegmentValueFromID(sSegmentCode, uSegmentID) {
	var sSegmentValue = null;
	var sSQL;
	
	switch (sSegmentCode) {
		// Customer
		case 'A':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT cust_code FROM sa_customer WHERE org_id = ? AND cust_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Customer Category
		case 'B':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT custcat_code FROM sa_customer_category WHERE org_id = ? AND custcat_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Customer Class
		case 'C':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT custclass_code FROM sa_customer_class WHERE org_id = ? AND custclass_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Item
		case 'D':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT item_code FROM in_item WHERE org_id = ? AND item_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Item Class
		case 'E':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT itemclass_code FROM in_item_class WHERE org_id = ? AND itemclass_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Item Group
		case 'F':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT ingroup_code FROM in_group WHERE org_id = ? AND ingroup_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Task Type
		case 'G':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT tasktype_desc FROM app_task_type WHERE tasktype_uuid = ?", null, [uSegmentID.toString()]);
			break;
		// Task
		case 'H':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT task_description FROM sa_task WHERE org_id = ? AND task_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Department
		case 'I':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT dept_code FROM sys_department WHERE org_id = ? AND dept_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Task Operation
		case 'J':
			sSQL = "SELECT att.tasktype_desc + ' - ' + ato.taskoper_desc \
					FROM app_task_operation ato \
					INNER JOIN app_task_type att ON ato.tasktype_id = att.tasktype_id \
					WHERE ato.taskoper_id = ?";	

			sSegmentValue = scopes.avDB.SQLQuery(sSQL, null, [uSegmentID.toString()]);
			break;
		// Cost Center
		case 'K':
			sSQL = "SELECT d.dept_code + '-' + o.opcat_code + '-' + c.cc_op_code \
					FROM sys_cost_centre c \
					INNER JOIN sys_operation_category o on c.opcat_id = o.opcat_id \
					INNER JOIN sys_department d on o.dept_id = d.dept_id \
					WHERE \
						c.org_id = ? \
						AND c.cc_id = ?";	
		
			sSegmentValue = scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Ink
		case 'L':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT inktype_code FROM in_ink_type WHERE org_id = ? AND inktype_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
		// Variable
		case 'M':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT taskmachine_description FROM sa_task_machine WHERE org_id = ? AND taskmachine_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
			// Div
		case 'N':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT div_code FROM sys_division WHERE org_id = ? AND div_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
			// Plant
		case 'O':
			sSegmentValue = scopes.avDB.SQLQuery("SELECT plant_code FROM sys_plant WHERE org_id = ? AND plant_id = ?", null, [globals.org_id, uSegmentID.toString()]);
			break;
	}
	
	return sSegmentValue;
}

/**
 * @private 
 * 
 * @param {String} sPriceType
 * @param {String} sRuleDescription
 * @param {UUID} uPriceCategoryID
 * 
 * @return {JSRecord<db:/avanti/sa_price_rule>}
 *
 * @properties={typeid:24,uuid:"176FD412-E972-43F8-851E-4433A0C44433"}
 */
function getPriceRule(sPriceType, sRuleDescription, uPriceCategoryID) {
	var sPriceTypeCode = getPriceTypeCode(sPriceType);
	
	/**@type {JSRecord<db:/avanti/sa_price_rule>} */
	var rPriceRule = scopes.avDB.getRec('sa_price_rule', ['pricerule_type', 'pricerule_desc', 'pricetype_id'], [sPriceTypeCode, sRuleDescription, uPriceCategoryID]);
	
	return rPriceRule;
}

/**
 * @param {String} sPriceTypeLongFormat
 *
 * @return
 * @properties={typeid:24,uuid:"559B2D87-C256-44B4-B4EC-DC0AB5DBC9B5"}
 */
function getPriceTypeCode(sPriceTypeLongFormat) {
	if (sPriceTypeLongFormat == 'Ink Pricing') {
		return 'INK';
	}
	else if (sPriceTypeLongFormat == 'Item Pricing') {
		return 'ITEM';
	}
	else if (sPriceTypeLongFormat == 'Task Pricing') {
		return 'TASK';
	}
	else {
		return '';
	}
}

/**
 * @private 
 * 
 * @param {UUID} uPriceRuleID
 * @param {Array<UUID>} aSegmentValueIDs
 * @param {Array<String>} aSegmentValues
 * 
 * @return {JSRecord<db:/avanti/sa_price_rule_detail>}
 *
 * @properties={typeid:24,uuid:"24D7EBD8-0675-4761-91AF-940E624D72C2"}
 */
function getPriceRuleDetail(uPriceRuleID, aSegmentValueIDs, aSegmentValues) {
	var aSearchCols = ['pricerule_id'];
	var aSearchVals = [uPriceRuleID.toString()];
	var i;
	
	for (i = 0; i < aSegmentValueIDs.length; i++) {
		if (aSegmentValueIDs[i]) {
			aSearchCols.push('priceruledtl_repsonse_seg' + (i + 1));
			aSearchVals.push(aSegmentValueIDs[i].toString());
		}
	}
	
	/**@type {JSRecord<db:/avanti/sa_price_rule_detail>} */
	var rPriceRuleDetail = scopes.avDB.getRec('sa_price_rule_detail', aSearchCols, aSearchVals);
	
	// if couldnt find it using ids, try using segment values
	if (!rPriceRuleDetail) {
		aSearchCols = ['pricerule_id'];
		aSearchVals = [uPriceRuleID.toString()];

		for (i = 0; i < aSegmentValues.length; i++) {
			if (aSegmentValues[i]) {
				aSearchCols.push('priceruledtl_repsonse_seg' + (i + 1) + 'val');
				aSearchVals.push(aSegmentValues[i]);
			}
		}
		
		rPriceRuleDetail = scopes.avDB.getRec('sa_price_rule_detail', aSearchCols, aSearchVals);
	}
	
	return rPriceRuleDetail;
}

/**
 * @param {String} s
 *
 * @return
 * @properties={typeid:24,uuid:"1B02D98D-4A52-476E-B518-A3EADBA9C1A4"}
 */
function cleanImportText(s) {
	s = Trim(s); // remove leading and trailing spaces
	s = removeCommaQuotesItem(s); // remove double quotes that excel places around column text that has certain chars in it (eg. comma, bracket) 

	return s;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F77F4FC8-FE9B-498E-A4A9-FB0759E15F0F"}
 */
function onOptAppendReplaceDataChange(oldValue, newValue, event) {
	// save sAppendReplace value to sys_auto_data_import rec if there is one 
	if (utils.hasRecords(forms.utils_dataImport_dtl_automatic.foundset)) {
		forms.utils_dataImport_dtl_automatic.foundset.adi_replace_existing_records = newValue == "R" ? 1 : 0;
		databaseManager.saveData(forms.utils_dataImport_dtl_automatic.foundset);
	}
	
	return true;
}
