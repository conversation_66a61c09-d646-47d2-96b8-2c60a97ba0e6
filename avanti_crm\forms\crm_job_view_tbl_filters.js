/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"08524BAE-AB6B-437E-88D5-F636D4BC93F6"}
 */
var invoiceStatus = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A56B306B-9D2C-4867-90B6-5CA03F0DBC81"}
 */
var shippingStatus = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F6A0F1D1-802A-469F-AD7B-B3916C537AF2"}
 */
var currentView = globals.avSales_CRM_MyCustomers;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C1CF3297-1DBA-4231-8743-DC7C3A6CE959"}
 */
var selectedRep = null;

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"0780E925-C18C-4AA0-BDDE-7E81690608CD"}
 */
var selectedServiceRep = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"03945027-DFE5-4BDA-98C1-D090D3B377AF"}
 */
var sCSRTeamMembers = '';

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"3CB03BEF-DBBA-492C-94EB-F837AFA3BF87"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	var result =  _super.onShowForm(firstShow, event);
	loadCSRTeamMembers();
	applyFilters(firstShow);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * @param readonly
 *
 * @properties={typeid:24,uuid:"E9ECDFA8-E5A0-4A6F-ABE7-3B29488CDC30"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * @properties={typeid:24,uuid:"B1661A12-E4A9-41A4-800E-E6CDE55395AA"}
 */
function setCSRValueList() {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	
	var aDisplay = new Array("");
	var aReturn = new Array("");

	//Load the service rep value list with the reps that are in the same team
	oSQL.sql = "SELECT DISTINCT b.empl_id, b.csr_name \
				  FROM sys_employee_csr_team a \
			INNER JOIN sa_customer_service_rep b ON (a.empl_id = b.empl_id OR a.empl_id_leader = b.empl_id) \
				 WHERE a.org_id = ? \
				   AND b.csr_active = 1 \
				   AND a.empl_id_leader IN ( \
				   							SELECT DISTINCT c.empl_id_leader from sys_employee_csr_team c \
				   							 WHERE c.org_id = ? \
				   							   AND (c.empl_id = ? OR c.empl_id_leader = ?) \
				   							)";

	oSQL.args = [globals.org_id, globals.org_id, globals.avBase_employeeUUID, globals.avBase_employeeUUID];
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	for (var i = 1; i <= dsData.getMaxRowIndex(); i++) {
		aReturn.push(dsData.getValue(i, 1));
		aDisplay.push(dsData.getValue(i, 2));
	}
	
	application.setValueListItems("vl_ServiceRep_Name_in_CRM_team$all_withEmptyValue", aDisplay, aReturn);
}

/**
 * @properties={typeid:24,uuid:"6CDA218C-0840-4B6D-951C-5300B57FA173"}
 */
function loadCSRTeamMembers() {
	
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = {};
	
	if (utils.hasRecords(_to_sys_employee$avbase_employeeuuid) 
			&& utils.hasRecords(_to_sys_employee$avbase_employeeuuid.sys_employee_to_app_assignment_type) 
			&& _to_sys_employee$avbase_employeeuuid.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_CustomerServiceRep) {

		if (selectedServiceRep) {
			sCSRTeamMembers = "'" + selectedServiceRep + "'";
		}
		else {
			oSQL.sql = "SELECT DISTINCT a.sys_employee_csr_team_id \
				  	  	  FROM sys_employee_csr_team a \
				  	  	 WHERE a.org_id = ? \
				  	  	   AND (a.empl_id_leader = ? \
				  	  	    OR a.empl_id_leader IN (SELECT DISTINCT b.empl_id_leader FROM sys_employee_csr_team b WHERE b.org_id = ? AND b.empl_id = ?))";
			oSQL.args = [globals.org_id, globals.avBase_employeeUUID, globals.org_id, globals.avBase_employeeUUID];
			oSQL.table = 'sys_employee_csr_team';

			/***@type {JSFoundSet<db:/avanti/sys_employee_csr_team>}*/
			var fsCSR = globals["avUtilities_sqlFoundset"](oSQL);

			var aCSR = new Array(globals.avBase_employeeUUID);
			for (var i = 1; i <= fsCSR.getSize(); i++) {
				var rCSR = fsCSR.getRecord(i);
				if (rCSR.empl_id) aCSR.push(rCSR.empl_id);
			}

			if (aCSR.length > 0) {
				sCSRTeamMembers = scopes.avText.arrayToString(aCSR, ",", "'");
			}
			else {
				sCSRTeamMembers = '';
			}
		}
	}
	else {
		sCSRTeamMembers = '';
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @properties={typeid:24,uuid:"12039406-C293-4D64-8855-82927927832D"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	
	// Set globals and default view to not show managed sales reps.
	elements.managed_sales_reps.visible = false;
	elements.managed_sales_reps_lbl.visible = false;
	
	elements.service_rep_lbl.visible = false;
	elements.service_rep.visible = false;

	//var job_fs = forms.crm_job_view_tbl.foundset.duplicateFoundSet()
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard") {

	}
	else {
		if (selectedServiceRep || selectedServiceRep == "") {
			loadCSRTeamMembers();
		}
		applyFilters(true);
	}
}

/**
 * @AllowToRunInFind
 * Apply fitlers to foundset 
 * @param {Boolean} bReApplyFilters
 *
 * @properties={typeid:24,uuid:"47A92570-0A15-4EC2-A259-CEBA0D71D344"}
 */
function applyFilters(bReApplyFilters) {
	elements.service_rep_lbl.visible = false;
	elements.service_rep.visible = false;
	if(bReApplyFilters) {
		// Remove any filters.
		forms.crm_job_view_tbl.foundset.removeFoundSetFilterParam('JobViewStatus');
		forms.crm_job_view_tbl.foundset.removeFoundSetFilterParam('JobShipStatus');
		forms.crm_job_view_tbl.foundset.removeFoundSetFilterParam('JobInvoiceStatus');
		forms.crm_job_view_tbl.foundset.removeFoundSetFilterParam('employeeFilter');
		
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				var sQuery = "select prod_job.job_id from prod_job \
					inner join sa_order on prod_job.ordh_id = sa_order.ordh_id \
					inner join sa_customer on sa_order.cust_id = sa_customer.cust_id "
				
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
						// If a sales rep, filter by salesper_id
						case globals.avSales_CRM_SalesRep:
							if(utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								sQuery += "where sa_customer.salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "' or sa_order.ordh_salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "'"
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','sql:in',sQuery,'employeeFilter')
							} else {
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						// If a sales manager, filter by salesper_id and add the 'Sales Manager' dropdown and make the managed sales rep drop down visible
						case globals.avSales_CRM_SalesManager:
							if(currentView == globals.avSales_CRM_AllCustomers){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if(currentView == globals.avSales_CRM_AllCustomers && selectedRep != null && selectedRep != '') {
								sQuery += "where sa_customer.salesper_id = '" + selectedRep + "' or sa_order.ordh_salesper_id = '" + selectedRep + "'"
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','sql:in', sQuery,'employeeFilter')
							} else if (currentView == globals.avSales_CRM_AllCustomers && selectedRep == null) {
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id in (select salesper_id from sys_employee_managed_reps where empl_id = '" + globals.avBase_employeeUUID + "')",'employeeFilter')
							} else if(currentView == globals.avSales_CRM_MyCustomers && utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								sQuery += "where sa_customer.salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "' or sa_order.ordh_salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "'"
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','sql:in',sQuery,'employeeFilter')
							} else {
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						case globals.avSales_CRM_CustomerServiceRep:
							if (currentView == globals.avSales_CRM_MyTeamCustomers && sCSRTeamMembers != '') {
								elements.service_rep_lbl.visible = true;
								elements.service_rep.visible = true;
								setCSRValueList();
								sQuery += "where sa_customer.cust_csr_empl_id IN (" + sCSRTeamMembers + ") or sa_order.ordh_csr_empl_id IN (" + sCSRTeamMembers + ")"
							}
							else {
								elements.service_rep_lbl.visible = false;
								elements.service_rep.visible = false;
								sQuery += "where sa_customer.cust_csr_empl_id = '" + globals.avBase_employeeUUID + "' or sa_order.ordh_csr_empl_id = '" + globals.avBase_employeeUUID + "'"
							}
							forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','sql:in',sQuery,'employeeFilter')
							break;
						// If an admin
						case globals.avSales_CRM_Administrator:
							if(currentView == globals.avSales_CRM_AllCustomers){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if(selectedRep != null && selectedRep != '') {
								sQuery += "where sa_customer.salesper_id = '" + selectedRep + "' or sa_order.ordh_salesper_id = '" + selectedRep + "'"
								forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','sql:in',sQuery,'employeeFilter')
							}
							break;
						default:
							// Filter by invalid uuid.
							forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							break;
					}
				} else {
					// Filter by invalid uuid.
					forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('job_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
				}
			}
		}
		
		if (_jobStatusFilter != 'All')
		{
			forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('jobstat_id','=',_jobStatusFilter,'JobViewStatus')
		}

		if (shippingStatus != 'All')
		{
			forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('shipping_status','=',shippingStatus,'JobShipStatus')
		}
		
		if (invoiceStatus != 'All')
		{
			forms.crm_job_view_tbl.foundset.addFoundSetFilterParam('invoice_status','=',invoiceStatus,'JobInvoiceStatus')
		}
		
		foundset.addFoundSetFilterParam('job_is_reservation','^||=','0','reservations');
		
		forms.crm_job_view_tbl.foundset.loadAllRecords();
		forms.crm_job_view_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.crm_job_view_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		// Reload the table and run initial sort on it.
		//forms.crm_job_view_tbl.foundset.loadRecords(job_fs)
	}
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"419B02C4-9F0F-46A2-8D15-A3DE9D916A6F"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView != "Dashboard")
	{
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					if(employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_SalesManager || employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_Administrator) {
						currentView = globals.avSales_CRM_AllCustomers
					}
				}
			}
		}
		_jobStatusFilter = 'All'
		shippingStatus = 'All'
		invoiceStatus = 'All'
	}
	
	return _super.onLoad(event)
}
