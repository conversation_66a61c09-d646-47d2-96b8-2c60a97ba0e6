/**
 * @properties={typeid:35,uuid:"24C4B63B-902E-4526-9378-B44894E787B8",variableType:-4}
 */
var _bCanadianOrg = false;

/**
 * @type {JSRecord<db:/avanti/in_item>}
 *
 * @properties={typeid:35,uuid:"8FF350BE-51CE-4041-9B3C-88C2017EA47A",variableType:-4}
 */
var _curItem;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"6AB43FAC-DCF0-4D9B-BE02-2F36E39046F5"}
 */
var _selectedImage = null;

/**
 * <PERSON><PERSON> changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"992E4AD8-997B-4C9D-A290-7EF8C5839BC2"}
 */
function onDataChange_ItemClass(oldValue, newValue, event) {
	var _rRec;
	if(item_is_virtual == null){
		item_is_virtual = 0;
	}
	var item_is_virtual_bak = item_is_virtual;
	
	if (in_item_to_in_item_class.itemclass_is_virtual) {
		item_is_virtual = 1; 
		item_no_bin_location = 1;
	}
	else {
		item_is_virtual = 0; 
	}

	if (item_is_virtual != item_is_virtual_bak && utils.hasRecords(in_item_to_in_item_trans_detail)) {
		scopes.avText.showWarning("VirtualItemHasTrans");
		itemclass_id = oldValue;
		item_is_virtual = item_is_virtual_bak;
		return true;
	}
	
	// Set first tab as default
	scopes.globals.avUtilities_tabSetEnabled(controller.getName(), "tab1", 1, true)
	 
	//Set Item Default Values based on Class
	if (in_item_to_in_item_class)
	{
		
		if (!scopes.globals.avUtilities_tabEnabled(controller.getName(),'tab1',scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tab1"))){
			scopes.globals.avUtilities_tabSetEnabled(controller.getName(),'tab1',null,true);
		}

		var _currentUOM = item_standard_uom_id;
		item_standard_uom_id = in_item_to_in_item_class.itemclass_uom_id; 
		forms.in_item_details_dtl.onDataChange_StandardUOM(_currentUOM,item_standard_uom_id,event);
		
		item_decimal_places = in_item_to_in_item_class.itemclass_decimal_places;
		item_salestax_option = in_item_to_in_item_class.itemclass_salestax_option;
		taxgroup_id = in_item_to_in_item_class.taxgroup_id;
		item_purchtax_option = in_item_to_in_item_class.itemclass_purchtax_option;
		item_purch_taxgroup_id = in_item_to_in_item_class.itemclass_purchtax_taxgroup_id;
		item_lot_item = in_item_to_in_item_class.itemclass_lot_item;
		item_allow_backorders = in_item_to_in_item_class.itemclass_allow_backorders;
		item_allow_discounts = in_item_to_in_item_class.itemclass_allow_discounts;
		item_allow_commissions = in_item_to_in_item_class.itemclass_allow_commissions;
		item_track_rolls = in_item_to_in_item_class.itemclass_track_rolls;
		item_generate_roll_number = in_item_to_in_item_class.itemclass_generate_roll_number;
		item_no_packsize_adj = in_item_to_in_item_class.itemclass_no_packsize_adj;
		
		if (in_item_to_in_item_class.itemclass_salestax_option == "Y"){
			item_salestaxtype_code = in_item_to_in_item_class.itemclass_salestaxtype_code;
		}
		
		if (in_item_to_in_item_class.itemclass_default_itemtype != null)
		{
			itemtype_code = in_item_to_in_item_class.itemclass_default_itemtype;
		}	
		else
		{
			itemtype_code = 'S';
		}
		
		createRelatedRecords();
		setTabs();
		forms.in_item_details_dtl.setFieldVisibility();
		setDescriptionState();
		forms.in_item_details_dtl.setTaxGroupVisibility();
				
		// Populate the paper brand value list
		if (in_item_to_in_item_class.itemclass_type == 'P' || in_item_to_in_item_class.itemclass_type == 'R' || in_item_to_in_item_class.itemclass_type == 'EN' || in_item_to_in_item_class.itemclass_type == 'TA' )
		{
			setPaperBrandValueList(globals.UUIDtoString(in_item_to_in_item_paper.papergrade_id), in_item_to_in_item_paper.paper_weight);
		}
		
		//Set Valid Stocking Units of Measurement
		setValueList_avItem_StockingUOM(in_item_to_in_item_class.itemclass_type, item_track_rolls, item_carbonless_set);
		// GD - Apr 23, 2014: Added _super call and changed getSelectedRecord to getRecord(1) to avoid server errors
		_rRec = foundset.getSelectedRecord();
		_super.setValueList_avItem_PaperGrades(_rRec);
		_super.setValueList_avItem_SellingUOM(_rRec);
		_super.setValueList_avItem_EstimateCostingUOM(_rRec);
		_super.setValueList_avItem_PurchaseCostingUOM(_rRec);
		_super.setValueList_avItem_PricingUOM(_rRec);
				
		forms.in_item_selling_uom_tbl.elements.grid.getColumn(forms.in_item_selling_uom_tbl.elements.grid.getColumnIndex("itemselluom_sell_conv_factor")).format = item_qty_format;
	
		setPostageFieldsVisibility();

		databaseManager.saveData();
	}

	return true;
}

/**
 *
 * @properties={typeid:24,uuid:"517A9FDF-9EAA-4357-9224-6FC5F61776AF"}
 */
function setPostageFieldsVisibility(){
    var rRec = foundset.getSelectedRecord();
    var sClassType = null;
    
    if(rRec && utils.hasRecords(rRec.in_item_to_in_item_class)){
        sClassType = rRec.in_item_to_in_item_class.itemclass_type;
    }
    
    // if envelop or postage then show new postage fields: indicia, etc - only canadian tho
    if(_bCanadianOrg && (sClassType == 'EN' || sClassType == 'PO')){
        forms.in_item_details_dtl.elements.postacct_id.visible = true;
        forms.in_item_details_dtl.elements.postacctcontract_id.visible = true;
    }
    else{
        forms.in_item_details_dtl.elements.postacct_id.visible = false;
        forms.in_item_details_dtl.elements.postacctcontract_id.visible = false;
    }
}

/** 
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @properties={typeid:24,uuid:"A2E98144-331F-4A05-B1C4-34FD42BB7DAC"}
 */
function dc_cancel(_event, _triggerForm) {
	 _super.dc_cancel(_event, _triggerForm, i18n.getI18NMessage('avanti.dialog.ok'));
	 scopes.globals.avUtilities_tabSetEnabled(controller.getName(),'tab1',null,true);
}

/**
 * @properties={typeid:24,uuid:"C97DEEBF-D235-4ADE-81E9-E50FBD2A05D8"}
 */
function dc_save_pre() {
	 _super.dc_save_pre.apply(this, arguments); // try to pass the arguments as a normal method call: _super.dc_save_pre(arg1,arg2)

}

/**
 * @param {JSRecord<db:/avanti/in_item>} [rItem] For passing in an item record (default is current on layout)
 * 
 * @properties={typeid:24,uuid:"6B757D32-8E64-4D1E-B4A4-B71010CDDF8B"}
 */
function createRelatedRecords(rItem) 
{
	if (!rItem) rItem = foundset.getSelectedRecord();
	
	if (_inDuplicateMode == 1) {
		return;
	}
	
	if (utils.hasRecords(rItem.in_item_to_in_item_class))
	{
		
		switch (rItem.in_item_to_in_item_class.itemclass_type)
		{
		// Paper
		case 'P': case 'R': case 'TA': case 'EN':
			if (!utils.hasRecords(rItem.in_item_to_in_item_paper))
				{
					rItem.in_item_to_in_item_paper.newRecord();
				}
		break;
		
		//Cylinder
		case 'CY':
				if (!utils.hasRecords(rItem.in_item_to_in_item_cylinder))
				{
					rItem.in_item_to_in_item_cylinder.newRecord();
				}
		break;
		
		//Cylinder
		case 'D':
				if (!utils.hasRecords(rItem.in_item_to_in_item_die))
				{
					rItem.in_item_to_in_item_die.newRecord();
				}
		break;
		
		//Plate
		case 'PL': case 'FP':
				if (!utils.hasRecords(rItem.in_item_to_in_item_plate))
				{
					rItem.in_item_to_in_item_plate.newRecord();
				}
		break;
		
		//Ink
		case 'I':
				if (!utils.hasRecords(rItem.in_item_to_in_item_ink))
				{
					rItem.in_item_to_in_item_ink.newRecord();
					rItem.in_item_to_in_item_ink.inktype_id = globals.avBase_getSystemPreference_String(16);
				}
		break;
			
		}
		
		//Create Default Warehouse Record based on the current user's default warehouse in for the default user plant
		if (!globals.avBase_employeeDefaultWarehouse)
		{
			/*** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
			var fsWhse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_warehouse');
			fsWhse.loadAllRecords();
			fsWhse.sort("whse_active desc");
			
			globals.avBase_employeeDefaultWarehouse = fsWhse.getRecord(1).whse_id;
			
		}
		if (!utils.hasRecords(rItem.in_item_to_in_item_warehouse))
		{
			//Get reorder default
			rItem.in_item_to_in_item_warehouse.newRecord(false);
			rItem.in_item_to_in_item_warehouse.item_id = rItem.item_id;
			rItem.in_item_to_in_item_warehouse.whse_id = globals.avBase_employeeDefaultWarehouse;
			rItem.in_item_to_in_item_warehouse.itemwhse_reorder_method = globals.avBase_getSystemPreference_String(66)
			rItem.in_item_to_in_item_warehouse.itemwhse_roc_method = globals.avBase_getSystemPreference_String(67);
			rItem.in_item_to_in_item_warehouse.itemwhse_primary_whse = 1;
			
			if (rItem.in_item_to_in_item_class.itemclass_default_est_uom_id != null)
			{
				rItem.in_item_to_in_item_warehouse.itemwhse_estimate_cost_uom_id = rItem.in_item_to_in_item_class.itemclass_default_est_uom_id; 
			}
			else
			{
				rItem.in_item_to_in_item_warehouse.itemwhse_estimate_cost_uom_id = rItem.item_standard_uom_id; 
			}
		}
		
		// Have to save otherwise relations won't work in warehouse dialog using the lookup window
		//moved to the end of onDataChange_ItemClass as it caused errors creating a new item from the lookup window
		//databaseManager.saveData();
		
	}
	

}

/**
 * Callback method form when editing is started.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"11DA4019-5E0B-45BF-BD2B-AB5159DF6A1E"}
 */
function onRecordEditStart(event) {
	
	createRelatedRecords();
	
	return true;
	
}

/**
 * @author: Tim Dol, Jun 7, 2011
 * @description: Set Tabs
 * 
 *
 * @properties={typeid:24,uuid:"B203CED4-65B8-466A-AEE9-BAFB56817AFE"}
 */
function setTabs() {
	
	// remove all dynamic tabs
	var _startingTab = 5;
	
	var _maxTabs = scopes.globals.avUtilities_tabGetMaxTabIndex(controller.getName(), "tab1");
	
	var _sMetricTxt = '';
	
    // RG 2014-11-05 SL-3162 set metric labels if metric
    if (utils.hasRecords(in_item_to_in_item_paper)) {
        if (utils.hasRecords(in_item_to_in_item_paper.in_item_paper_to_in_paper_grade)) {
            if (in_item_to_in_item_paper.in_item_paper_to_in_paper_grade.papergrade_dimension_units && 
                ( in_item_to_in_item_paper.in_item_paper_to_in_paper_grade.papergrade_dimension_units == 'M' )) {
                _sMetricTxt = 'Metric';
            }
        }
    }

    for (var i = _maxTabs; i >= _startingTab; i--) {
        scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "tab1", i);
    }

    //If we have a single warehouse, swap out the table view for the detail view.
    if (globals.avBase_orgIsSingleWarehouse != true) {
        // GD - 2014-02-20: SL-1079 - had to switch this around, so that the detail view is the default, and the table is swapped in when needed. This is because we need the warehouse in the edit item dialog from section dialog
        scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "tab1", 4);
        //in_item_warehousetab_tbl form is used in other places, so we need to force set the foundset here.
        forms.in_item_warehousetab_tbl.controller.loadRecords(forms.in_item_dtl.foundset.in_item_to_in_item_warehouse);
        scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_warehousetab_tbl, i18n.getI18NMessage("i18n:avanti.tab.item_warehouses"), null, null, null, 4, null);
    }

	forms.in_item_details_dtl.elements.worktype_id_label.visible = false;
	forms.in_item_details_dtl.elements.worktype_id.visible = false;
	forms.in_item_details_dtl.elements.btnWorkTypeLookup.visible = false;
	forms.in_item_details_dtl.elements.tabPaperStandards.visible = false;
	forms.in_item_details_dtl.elements.item_separate_cost_bom.visible = false;
	
    if (itemtype_code == scopes.avInv.ITEM_TYPE_CODE.ServiceItem) {       
       forms.in_item_details_dtl.elements.item_exclude_from_avatax.visible = true;
    }
    else {       
        forms.in_item_details_dtl.elements.item_exclude_from_avatax.visible = false;
    }
	
    if (itemtype_code == scopes.avUtils.ITEM_TYPE.AssembledKit || itemtype_code == scopes.avUtils.ITEM_TYPE.BuildToOrderKit || itemtype_code == scopes.avUtils.ITEM_TYPE.Product || itemtype_code == scopes.avUtils.ITEM_TYPE.FinishedGood) {
        if (itemtype_code == scopes.avUtils.ITEM_TYPE.AssembledKit || itemtype_code == scopes.avUtils.ITEM_TYPE.BuildToOrderKit || itemtype_code == scopes.avUtils.ITEM_TYPE.Product) {
            scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_bill_of_material_tbl, i18n.getI18NMessage("avanti.tab.ItemBillOfMaterials"), "in_item_to_in_item_bill_of_material", null, null, 5, null);
            forms.in_item_details_dtl.elements.lbl_group_customer_stock.visible = true;
            forms.in_item_details_dtl.elements.item_separate_cost_bom.visible = (itemtype_code == scopes.avUtils.ITEM_TYPE.AssembledKit || itemtype_code == scopes.avUtils.ITEM_TYPE.BuildToOrderKit);
            _startingTab += 1;
        }

        if (itemtype_code == scopes.avUtils.ITEM_TYPE.BuildToOrderKit || itemtype_code == scopes.avUtils.ITEM_TYPE.Product || itemtype_code == scopes.avUtils.ITEM_TYPE.FinishedGood) {
            forms.in_item_details_dtl.elements.worktype_id_label.visible = true;
            forms.in_item_details_dtl.elements.worktype_id.visible = true;
            forms.in_item_details_dtl.elements.btnWorkTypeLookup.visible = true;
        }
    }
    else if (itemtype_code == scopes.avUtils.ITEM_TYPE.Account) {
        forms.in_item_details_dtl.elements.lbl_group_customer_stock.visible = true;
        forms.in_item_details_dtl.elements.cust_id_label.visible = true;
        forms.in_item_details_dtl.elements.cust_id.visible = true;
    }

    scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_docs_tbl, i18n.getI18NMessage("avanti.lbl.documents"), null, null, null, 5, null);
    _startingTab += 1;

	
	forms.in_item_details_dtl.elements.item_track_rolls.visible=false;
	forms.in_item_details_dtl.elements.item_generate_roll_number.visible=false;

    if (utils.hasRecords(in_item_to_in_item_class)) {

        switch (in_item_to_in_item_class.itemclass_type) {
            case 'P':
            case 'R':
            case 'TA':
            case 'EN':
                forms.in_item_details_dtl.elements.tabPaperStandards.visible = true;
				scopes.globals.avUtilities_setContainedForm("in_item_details_dtl", "tabPaperStandards", "in_item_paper_dtl", "in_item_to_in_item_paper");
                forms.in_item_paper_dtl.elements.lblGroupTabDetails.visible = false;
                forms.in_item_paper_dtl.elements.item_number_of_tabs_label.visible = false;
                forms.in_item_paper_dtl.elements.item_number_of_tabs.visible = false;
                forms.in_item_paper_dtl.elements.paper_roll_weight_label.visible = false;
                forms.in_item_paper_dtl.elements.paper_roll_weight.visible = false;
                forms.in_item_paper_dtl.elements.paper_roll_avg_length.visible = false;
                forms.in_item_paper_dtl.elements.paper_m_weight.visible = true;

                if (in_item_to_in_item_class.itemclass_type == 'R') {
                    forms.in_item_details_dtl.elements.item_track_rolls.visible = true;
                    forms.in_item_details_dtl.elements.item_generate_roll_number.visible = true;
                    forms.in_item_paper_dtl.elements.lblPaperSecondDimension.visible = true;
                    forms.in_item_paper_dtl.elements.paper_second_dim.visible = true;
                    forms.in_item_paper_dtl.elements.lblPaperFirstDimension.text = i18n.getI18NMessage('avanti.lbl.rollWidth' + _sMetricTxt);
                    forms.in_item_paper_dtl.elements.lblPaperSecondDimension.text = i18n.getI18NMessage('avanti.lbl.rollLength' + _sMetricTxt);
                    forms.in_item_paper_dtl.elements.paper_roll_weight_label.text = i18n.getI18NMessage('avanti.lbl.rollWeight' + _sMetricTxt);

                    forms.in_item_paper_dtl.elements.lblPaperRollAvgLength.text = i18n.getI18NMessage('avanti.lbl.averageLengthPerRoll' + _sMetricTxt);
                    forms.in_item_paper_dtl.elements.paper_roll_avg_length.visible = true;

                    forms.in_item_paper_dtl.elements.paper_roll_weight_label.visible = true;
                    forms.in_item_paper_dtl.elements.paper_roll_weight.visible = true;
                    forms.in_item_paper_dtl.elements.paper_m_weight.visible = false
                    forms.in_item_paper_dtl.elements.paper_roll_weight.visible = foundset.item_track_rolls != 1;
                }
                else if (in_item_to_in_item_class.itemclass_type == 'TA') {
                    forms.in_item_paper_dtl.elements.lblGroupTabDetails.visible = true;
                    forms.in_item_paper_dtl.elements.item_number_of_tabs_label.visible = true;
                    forms.in_item_paper_dtl.elements.item_number_of_tabs.visible = true;
                    forms.in_item_paper_dtl.elements.paper_roll_weight_label.visible = false;
                    forms.in_item_paper_dtl.elements.paper_roll_weight.visible = false;
                    forms.in_item_paper_dtl.elements.paper_roll_avg_length.visible = false;

                }
                else {
                    forms.in_item_paper_dtl.elements.lblPaperSecondDimension.visible = true;
                    forms.in_item_paper_dtl.elements.paper_second_dim.visible = true;
                    forms.in_item_paper_dtl.elements.lblPaperFirstDimension.text = i18n.getI18NMessage('i18n:avanti.lbl.paperSizeFirstDim' + _sMetricTxt);
                    forms.in_item_paper_dtl.elements.lblPaperSecondDimension.text = i18n.getI18NMessage('i18n:avanti.lbl.length' + _sMetricTxt);
                    forms.in_item_paper_dtl.elements.paper_roll_weight_label.visible = false;
                    forms.in_item_paper_dtl.elements.paper_roll_weight.visible = false;
                    forms.in_item_paper_dtl.elements.paper_roll_avg_length.visible = false;

                }
                break;

            case 'CY':
                scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_cylinder_dtl, i18n.getI18NMessage("avanti.tab.item_cylinderInfo"), "in_item_to_in_item_cylinder", null, null, _startingTab, null);
                forms.in_item_details_dtl.elements.tabPaperStandards.visible = true;
				scopes.globals.avUtilities_setContainedForm("in_item_details_dtl", "tabPaperStandards", "in_item_cylinder_impressions_dtl", "in_item_to_in_item_cylinder");
                break;

            case 'D':
                scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_die_dtl, i18n.getI18NMessage("avanti.tab.item_dieInfo"), "in_item_to_in_item_die", null, null, _startingTab, null);
                forms.in_item_details_dtl.elements.tabPaperStandards.visible = true;
                scopes.globals.avUtilities_setContainedForm("in_item_details_dtl", "tabPaperStandards", "in_item_die_impressions_dtl", "in_item_to_in_item_die");
                break;

            case 'PL':
            case 'FP':
                scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_plate_dtl, i18n.getI18NMessage("avanti.tab.item_plateInfo"), "in_item_to_in_item_plate", null, null, _startingTab, null);
                break;

            case 'I':
            case 'CO': // sl-4926 - show ink info for coatings as well
                scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_ink_dtl, i18n.getI18NMessage("avanti.tab.item_inkInfo"), "in_item_to_in_item_ink", null, null, _startingTab, null);
                break;

        }
    }

    // Add the transaction tab
    //TODO: add accounting tab based on module license
    if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration) != null) {
        scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_accounting_links, i18n.getI18NMessage("avanti.tab.accountingLinks"), "_to_in_item$foundset", null, null, _startingTab + 1, null);
    }

    scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_trans_detail_tbl, i18n.getI18NMessage("avanti.tab.transactions"), "in_item_to_in_item_trans_detail", null, null, _startingTab + 2, null);
    forms.in_item_fifo_dialog_detail._rItem = foundset.getSelectedRecord();
    forms.in_item_fifo_dialog_detail.whseId = null;
    scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_item_fifo_dialog_detail, i18n.getI18NMessage("avanti.lbl.fifoCostingItem"), "in_item_to_in_item_fifo", null, null, _startingTab + 3, null);

    scopes.globals.avUtilities_tabAdd("in_item_dtl", "tab1", forms.in_items_udf_dtl, i18n.getI18NMessage("avanti.lbl.UDF"), null, null, null, _startingTab + 4, null);
    if (globals["avSecurity_checkForUserRight"]('Items', 'inventory_history_tab', globals.avBase_employeeUserID)) {
        globals.avUtilities_tabAdd("in_item_dtl", "tab1", "sa_pack_history_tbl", "avanti.lbl.History", null, null);
    }
    else {
        globals["avUtilities_tabRemove"]("in_item_dtl", "tab1", "avanti.lbl.History");
    } 
}

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"CEBC199C-BE50-4926-94E3-1841B68C3877"}
 */
function onRecordSelection(_event, _form)
{
	var _rRec = foundset.getSelectedRecord();
	
	// init item_code_no_revision if it hasnt been done yet
	if (item_code && !item_code_no_revision && !item_revision) {
		item_code_no_revision = item_code.substr(0, 37);
	}
	
	setTabs();
	
	if (utils.hasRecords(in_item_to_in_item_paper))
	{
		setPaperBrandValueList(globals.UUIDtoString(in_item_to_in_item_paper.papergrade_id), in_item_to_in_item_paper.paper_weight);
		forms.in_item_paper_dtl.setPaperWeightValueList();
		// RG 2014-11-05 SL-3162 set visibility based on weight by
		if ( utils.hasRecords ( in_item_to_in_item_paper.in_item_paper_to_in_paper_grade )) {
			if ( in_item_to_in_item_paper.in_item_paper_to_in_paper_grade.papergrade_weight_by && ( in_item_to_in_item_paper.in_item_paper_to_in_paper_grade.papergrade_weight_by == 'g' ) ) {
				forms.in_item_paper_dtl.elements.paper_m_weight.visible = false ;
				forms.in_item_paper_dtl.elements.paper_m_weight_label.visible = false ;
			}
		}
	}
	
	
	//Set Valid Stocking Units of Measurement
	if (in_item_to_in_item_class)
	{
		setValueList_avItem_StockingUOM(in_item_to_in_item_class.itemclass_type, item_track_rolls, item_carbonless_set);
	}
	
	// GD - Apr 23, 2014: Added _super call and changed getSelectedRecord to getRecord(1) to avoid server errors
	_super.setValueList_avItem_SellingUOM(_rRec);
	_super.setValueList_avItem_EstimateCostingUOM(_rRec);
	_super.setValueList_avItem_PurchaseCostingUOM(_rRec);
	_super.setValueList_avItem_PricingUOM(_rRec);
	
	forms.in_item_selling_uom_tbl.elements.grid.getColumn(forms.in_item_selling_uom_tbl.elements.grid.getColumnIndex("itemselluom_sell_conv_factor")).format = item_qty_format;
	
	forms.in_item_details_dtl.setQuantityFormat();

	setPostageFieldsVisibility();
	
	loadAccountingLinks();
	
	// sl-23260 - we have had a lot of problems over the years with OHQ discrepancies (EG trans/bin/ware totals not adding up). we have not been able to identify all the causes of these
	// problems. a new dev util (#91) was created to fix this, but i also added a call to the util here to detect/fix these problems for this item. 
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DetectAndFixInventoryProblems)) {
		scopes.avInv.fixItemWarehouseBinOnHandQtysUsingTransactions(item_id);
		scopes.avInv.fixMissingFifoRecs(item_id, "item");
		scopes.avInv.recalcItemStatistics(_rRec);
	}
	
	// sl-26943 - force the calculated cols on this rec to be recalced as most of the item detail qty totals are now calculated cols.
	databaseManager.recalculate(_rRec);
	
	_super.onRecordSelection(_event, _form)
}

/**
 * @properties={typeid:24,uuid:"EB76B74B-E391-4EB7-8D92-793C8921010A"}
 */
function loadAccountingLinks() {
	if (item_id) {
		var fs = scopes.avAccounting.getChargebackAccountingLinks(scopes.avAccounting.CHARGEBACK_ACCOUNT_OBJECT_TYPE.Item, item_id);
		forms.sys_chargeback_accounting_links_tbl.foundset.loadRecords(fs);
		if (forms.sys_chargeback_accounting_links_tbl.foundset.getSize() > 1) {
			forms.sys_chargeback_accounting_links_tbl.foundset.sort('sys_chargeback_accounting_link_to_sa_customer_class.custclass_desc asc');
		}
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"98C308D7-124C-4723-BA0F-7934D400C9D0"}
 */
function btnImageAdd(event)
{
	var oParams = new Object();
	oParams.mode = "newCommit"; // options are newCommit, show, lookup   
	globals.svy_nav_showLookupWindow(event, "sys_image_id", "Images", null, null, oParams); 
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"3F7F80FA-B0D5-4D1A-B93A-CD3471D6F304"}
 */
function btnImageDelete(event)
{
	if (globals.nav.mode != 'edit')
	{
		return false;
	}
	
	var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"), i18n.getI18NMessage("avanti.dialog.deleteMsg"), i18n.getI18NMessage("avanti.dialog.ok"), i18n.getI18NMessage("avanti.dialog.cancel"));
	
	if (sAns != i18n.getI18NMessage("avanti.dialog.ok"))
	{
		return false;
	}
	
	sys_image_id = null;
	
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B9D79FE8-71F3-4F64-A140-77C5AC060B71"}
 */
function btnImageLookup(event)
{
	globals.svy_nav_showLookupWindow(event, "_selectedImage", "Images", "lookupReturn", null);
}

/**
 * Handles the return from the lookup window
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-27
 *
 *
 *
 * @properties={typeid:24,uuid:"D549FC2E-90F5-4FAB-AE7C-182ECC32CB02"}
 */
function lookupReturn()
{
	sys_image_id = _selectedImage;
	application.updateUI(100);
}

/**
 * SetPaperBrandValueList
 * @param {UUID} sPaperTypeId - The selected Paper Type
 * @param {Number} nWeight - Selected Weight
 *  *  *
 * @properties={typeid:24,uuid:"DC780C96-36D7-4402-B486-E750FFBDE77F"}
 */
function setPaperBrandValueList(sPaperTypeId,nWeight) 
{
		
	var vlRealValues = null
 	var vlDisplayValues = null
	
//	var _args = new Array()
//	var _where = ' where in_paper_grade.papergrade_is_roll ';
	
	if (utils.hasRecords(in_item_to_in_item_paper) 
	        && utils.hasRecords(in_item_to_in_item_class) 
	        && utils.hasRecords(in_item_to_in_item_paper.in_item_paper_to_in_paper_grade))
	{	
		
		var oSQL = new Object();
		oSQL.sql = " \
			SELECT paperbrand_id, paperbrand_name \
			FROM in_paper_brand pb\
			INNER JOIN in_paper_grade pg ON pb.papergrade_id = pg.papergrade_id \
			WHERE pb.org_id = ?";
		
		oSQL.sql += " AND pb.papergrade_id = '" + sPaperTypeId + "'";
		
		if (nWeight != null)
		{
			oSQL.sql += " AND pb.paperbrand_basis_weight = " + nWeight;
		}
		else
		{
			oSQL.sql += " AND pb.paperbrand_basis_weight is null ";
		}
		
		
		oSQL.args = [globals.org_id];
		
		if (in_item_to_in_item_class.itemclass_type == 'R')
		{
			oSQL.sql += " AND pg.papergrade_is_roll = ?";
			oSQL.args.push('1');
		}
		
		oSQL.sql += " ORDER BY paperbrand_name";
		
		var dsData = globals.avUtilities_sqlDataset(oSQL);
		
		if (dsData && dsData.getMaxRowIndex() > 0)
		{
			vlRealValues = dsData.getColumnAsArray(1);
		 	vlDisplayValues = dsData.getColumnAsArray(2);
		 	
		 	application.setValueListItems('avPaperBrands', vlDisplayValues, vlRealValues);
		}
		else
		{
			application.setValueListItems('avPaperBrands',[],[]);
		}
	}
	else
	{
		application.setValueListItems('avPaperBrands',[],[]);
	}
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E57CB97D-7D6C-4574-9A7A-A5F5C20B669C"}
 */
function onDataChange_ItemType(oldValue, newValue, event) {
	if (utils.hasRecords(in_item_to_in_item_trans_detail) && !scopes.avInv.canItemTypeBeChangedWhenTransactions(oldValue, newValue)) {
		scopes.avText.showWarning("ItemTypeCantBeChanged");
		itemtype_code = oldValue;
		return true;
	}
	
	setTabs();
	
	if (!scopes.avUtils.isNavModeReadOnly())
	{	
		/** @type {{required_fields:Object,noreadonly:Number,program_name:String,tab:Array}} */
		var _progObj = globals.nav.program['Items']
	
		if (newValue == "P")
		{
			_progObj.required_fields["worktype_id"] = new Object();
			_progObj.required_fields["worktype_id"].db_status = 'R'
			_progObj.required_fields["worktype_id"].req_by_prog = false
			_progObj.required_fields["worktype_id"].form_status = 'R'
			_progObj.required_fields["worktype_id"].on_form = false
	
			
			globals.svy_nav_setRequiredFields(_progObj,'in_item_dtl', "browse")
			globals.svy_nav_setRequiredFields(_progObj,'in_item_dtl', 'edit')
		}
		else if (oldValue == "P")
		{
			globals.svy_nav_setRequiredFields(_progObj,'in_item_dtl', "browse")
			_progObj.required_fields = null;
			_progObj.required_fields = globals.svy_nav_getRequiredFields(_progObj)
			globals.svy_nav_setRequiredFields(_progObj,'in_item_dtl', 'edit')
		}
	}
	
	return true
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"4885FA84-DF3A-4459-8F05-37A57878D1B3"}
 */
function dc_duplicate(_event, _triggerForm) {
	
	// Show dialog to verify duplicate method
	var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.duplicateItem_title"), i18n.getI18NMessage("avanti.dialog.duplicateItem_msg") + ' ' + foundset.item_code, i18n.getI18NMessage("avanti.dialog.ok"), i18n.getI18NMessage("avanti.dialog.cancel"));
	if (sAns == i18n.getI18NMessage("avanti.dialog.ok"))
	{
		scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tab1", 1);
		_inDuplicateMode = 1;
		duplicateItem();
		_inDuplicateMode = 0;
	}
	
	dc_edit(null, globals.nav.browser_buttonbar)
	
}

/**
 * duplicateItem
 * 
 * @param {String} [sItemCodeOver]
 * @param {UUID} [uReplacesItemID]
 * 
 * @return {JSRecord<db:/avanti/in_item>} - the new item
 *
 * @properties={typeid:24,uuid:"C71F5460-99D2-4488-A4D3-345E088502CE"}
 */
function duplicateItem(sItemCodeOver, uReplacesItemID)
{
	
	var rCurItem = foundset.getSelectedRecord();
//	var sCurItemPK = rCurItem.item_id;
	
	// Create a new record and get its UUID
	foundset.duplicateRecord(foundset.getSelectedIndex(), false, true);
	var rNewItem = foundset.getSelectedRecord();
	var sNewItemPK = rNewItem.item_id;
	
	if (!uReplacesItemID) {
		// RG 2014-04-11 SL-2375 if item class is auto build, do not add 'duplicated' to description, do build
		if (utils.hasRecords(rCurItem.in_item_to_in_item_class)) {
			if ( (rCurItem.in_item_to_in_item_class.itemclass_auto_build_item_desc == null) || (rCurItem.in_item_to_in_item_class.itemclass_auto_build_item_desc == 0)) {
				rNewItem.item_desc1 += i18n.getI18NMessage("avanti.lbl.duplicated");
			}
		}
		else {
			rNewItem.item_desc1 += i18n.getI18NMessage("avanti.lbl.duplicated");
		}
	}
	
	// Prevent Duplicate Codes
	if (sItemCodeOver) {
		rNewItem.item_code = sItemCodeOver;
		rNewItem.item_code_no_revision = sItemCodeOver;
	}
	else if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseItemRevisionField)) {
		// in case item_code_no_revision isnt set yet
		if (rCurItem.item_code && !rCurItem.item_code_no_revision && !rCurItem.item_revision) {
			rCurItem.item_code_no_revision = rCurItem.item_code.substr(0, 37);
			rNewItem.item_code_no_revision = rCurItem.item_code_no_revision;
		}
		
		// the current revision may not be the last one - get the last
		var sSQL = "SELECT MAX(item_revision) \
					FROM in_item \
					WHERE \
						org_id = ? \
						AND item_code_no_revision = ?";
		var sMaxRevision = scopes.avDB.SQLQuery(sSQL, false, [globals.org_id, rCurItem.item_code_no_revision]);
		
		if (!sMaxRevision) {
			sMaxRevision = rCurItem.item_revision;
		}
		
		rNewItem.item_revision = getNextRevision(sMaxRevision);
		rNewItem.item_code = rNewItem.item_code_no_revision + "-" + rNewItem.item_revision;
	}
	else {
		rNewItem.item_code = generateNewItemCode(rCurItem.item_code);
		rNewItem.item_code_no_revision = rNewItem.item_code;
	}
	
	if (uReplacesItemID) {
		rNewItem.replaces_item_id = uReplacesItemID;
	}
	
	// duplicate standard relations where there is no special data manipulation
	var i;
	var j;
	var iMax;
	var aClone = [];
	var aCloneItem = ['in_item_to_in_item_selling_uom',
	'in_item_to_in_item_bill_of_material',
	'in_item_to_in_item_cylinder',
	'in_item_to_in_item_ink',
	'in_item_to_in_item_paper',
	'in_item_to_in_item_plate',
	'in_item_to_in_item_supplier',
	'in_item_to_in_item_warehouse',
	'in_item_to_sys_udf_values'];
	
	var bApplyItemToPR = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ApplyDuplicatedItemsToPriceRules);
	
	if (bApplyItemToPR) {
        var aClonePriceRules = [
        'in_item_to_sa_price_rule_detail$segment1',
        'in_item_to_sa_price_rule_detail$segment2',
        'in_item_to_sa_price_rule_detail$segment3',
        'in_item_to_sa_price_rule_detail$segment4',
        'in_item_to_sa_price_rule_detail$segment5',
        'in_item_to_sa_price_rule_detail$segment6'];	
        
        aClone = aCloneItem.concat(aClonePriceRules);	    
	}
	else {
	    aClone = aCloneItem.slice(0);
	}
	
	for (i = 0; i <= aClone.length - 1; i++)
	{
		if (utils.hasRecords(rCurItem[aClone[i]]))
		{
			iMax = rCurItem[aClone[i]].getSize();
			for ( j = 1; j <= iMax; j++)
			{
				// sl-25487 - dont copy temp in_item_warehouse recs (created in Transactions)
				if (aClone[i] == "in_item_to_in_item_warehouse" && rCurItem[aClone[i]].getRecord(j)["intraneh_id_temp_insert"]) {
					continue;
				}
				
				rCurItem[aClone[i]].duplicateRecord(j, false, true);
				rCurItem[aClone[i]].item_id = sNewItemPK;
				
				switch( aClone[i] )
				{
				case 'in_item_to_in_item_warehouse':
				
					// Clear out statistical fields.
					rCurItem[aClone[i]].itemwhse_opening_balance = 0;
					rCurItem[aClone[i]].itemwhse_onhand_qty = 0;
					rCurItem[aClone[i]].itemwhse_unavailable_qty = 0;
					rCurItem[aClone[i]].itemwhse_unusable_qty = 0;
					rCurItem[aClone[i]].itemwhse_scheduled_qty = 0;
					rCurItem[aClone[i]].itemwhse_backord_qty = 0;
					rCurItem[aClone[i]].itemwhse_reserved_qty = 0;
					rCurItem[aClone[i]].itemwhse_onpo_qty = 0;
					rCurItem[aClone[i]].itemwhse_onporeq_qty = 0;
					rCurItem[aClone[i]].itemwhse_intransit_qty = 0;
					rCurItem[aClone[i]].itemwhse_avg_cost = 0;
					rCurItem[aClone[i]].itemwhse_last_cost = 0;
					rCurItem[aClone[i]].itemwhse_last_physical_date = null;
					rCurItem[aClone[i]].itemwhse_last_cyclecount_date = null;
					rCurItem[aClone[i]].itemwhse_avg_monthy_usage = 0;
					rCurItem[aClone[i]].itemwhse_hits = 0;
					rCurItem[aClone[i]].itemwhse_misses = 0;
					rCurItem[aClone[i]].itemwhse_fill_rate_pct = 0;
					rCurItem[aClone[i]].itemwhse_turns_pct = 0;
					rCurItem[aClone[i]].itemwhse_committed_qty = 0;
					rCurItem[aClone[i]].itemwhse_last_receipt_qty = 0;
					rCurItem[aClone[i]].itemwhse_inproduction_qty = 0;
					rCurItem[aClone[i]].itemwhse_opening_balance = 0;
					rCurItem[aClone[i]].itemwhse_openingbal_qty = 0;
					rCurItem[aClone[i]].itemwhse_default_receipt_bin = null;
					rCurItem[aClone[i]].itemwhse_default_issue_bin = null;
					
				
				break;
				
				case 'in_item_to_sa_price_rule_detail$segment1':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg1 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg1val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
				case 'in_item_to_sa_price_rule_detail$segment2':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg2 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg2val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
				case 'in_item_to_sa_price_rule_detail$segment3':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg3 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg3val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
				case 'in_item_to_sa_price_rule_detail$segment4':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg4 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg4val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
				case 'in_item_to_sa_price_rule_detail$segment5':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg5 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg5val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
				case 'in_item_to_sa_price_rule_detail$segment6':
					rCurItem[aClone[i]].priceruledtl_repsonse_seg6 = sNewItemPK;
					rCurItem[aClone[i]].priceruledtl_repsonse_seg6val = rNewItem.item_desc1 + " - " + rNewItem.item_code;
					break;
					
				case 'in_item_to_sys_udf_values':
				    rCurItem[aClone[i]].unique_id = sNewItemPK;
				    break;
				
				default:
				}
			}
		}
	}
	
	//Clear the item quantities
	item_backord_qty = 0;
	item_committed_qty = 0;
	item_inproduction_qty = 0;
	item_intransit_qty = 0;
	item_onhand_qty = 0;
	item_unavailable_qty = 0;
	item_unusable_qty = 0;
	item_onpo_qty = 0;
	item_onporeq_qty = 0;
	item_reserved_qty = 0;
	
	databaseManager.saveData();
	
	refreshQuantities();
	
	return rNewItem;
}

/**
 * generateNewItemCode
 * @param {String} _oldItemCode
 * 
 * @return {String} _newItemCode
 *
 * @properties={typeid:24,uuid:"E34F3220-98DA-43FE-93E2-A781FF4B7B5B"}
 * @AllowToRunInFind
 */
function generateNewItemCode(_oldItemCode)
{
	
	var _version = 0;
	var _exitDo = 0;
	var _testValue = '';
	
	/** @type {JSFoundSet<db:/avanti/in_item>} */
	var _fsItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
	
	while (_exitDo == 0)
	  {
		  _version +=1;
		  _testValue = _oldItemCode + '_' + _version.toString();
		 
		  if (_fsItem.find() || _fsItem.find())
		  {
			  _fsItem.item_code = _testValue
			  _fsItem.search();
				
			  // Found Duplicate
			  // SL-10218 - if _testValue.length >36, the string item_code is trimmed 
				if (_fsItem.getSize() == 0 || _fsItem.getRecord(1).item_code != _testValue)
				{
					_exitDo = 1;
				}
		  }

	  }
	
	
	
	return _testValue
	
}

/**
 * @private 
 * 
 * @param {String} sCurrentRevision
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"96F99C55-B02C-4230-ABD8-F1A1F1702DA0"}
 */
function getNextRevision(sCurrentRevision) {
	var sNextRevision = "001";
	
	if (sCurrentRevision != null) {
		sCurrentRevision = sCurrentRevision.trim();
		
		if (sCurrentRevision != "") {
			sNextRevision = scopes.avText.incrementAlphanumeric(sCurrentRevision);
		}
	}
	
	return sNextRevision;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"7FBD6C48-D0F2-473F-B665-93987D03C07C"}
 */
function onShowForm(_firstShow, _event) 
{
    var _rRec;

    _bCanadianOrg = scopes.avSystem.isCanadianOrg();
    
	scopes.avVL.load_vl_ItemCodes_BTO_and_AK();

    elements.item_expiry_date.format = globals.avBase_dateFormat;
    elements.item_creation_date.format = globals.avBase_dateFormat;

    //Set Valid Units of Measurement
    if (in_item_to_in_item_class) {
        setValueList_avItem_StockingUOM(in_item_to_in_item_class.itemclass_type, item_track_rolls, item_carbonless_set);
        // GD - Apr 23, 2014: Added _super call and changed getSelectedRecord to getRecord(1) to avoid server errors
        _rRec = foundset.getSelectedRecord();
        _super.setValueList_avItem_SellingUOM(_rRec);
        _super.setValueList_avItem_EstimateCostingUOM(_rRec);
        _super.setValueList_avItem_PurchaseCostingUOM(_rRec);
        _super.setValueList_avItem_PricingUOM(_rRec);
    }

    refreshUI();
    
	if (utils.hasRecords(foundset.in_item_to_in_item_supplier)) {
		for (var i = 1; i <= foundset.in_item_to_in_item_supplier.getSize(); i++) {
			var rItmSupplier = foundset.in_item_to_in_item_supplier.getRecord(i);
			if (utils.hasRecords(rItmSupplier.in_item_supplier_to_ap_supplier$inactive) && rItmSupplier.in_item_supplier_to_ap_supplier$inactive.getSize() > 0) {
				scopes.avText.showWarning('ItemWIthInactiveSupplier', false, [foundset.item_code], null, true);
				break;
			}
		}
	}
	
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseItemRevisionField)) {
		elements.item_code.visible = false;
		elements.item_code_no_revision.visible = true;
		elements.item_revision.visible = true;
	}
	else {
		elements.item_code.visible = true;
		elements.item_code_no_revision.visible = false;
		elements.item_revision.visible = false;
	}
	
    _super.onShowForm(_firstShow, _event);

    onRecordSelection(_event, null);
}

/**
 * Refreshes UI
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 15, 2014
 *
 * @properties={typeid:24,uuid:"9BFD1EC3-C260-443B-B57D-2C024EC1CDE3"}
 */
function refreshUI () {
	forms.in_item_details_dtl.elements.lblOutsideDiameter.visible = false;
	forms.in_item_details_dtl.elements.fldOutsideDiameter.visible = false;
	forms.in_item_details_dtl.elements.lblCoreDiameter.visible = false;
	forms.in_item_details_dtl.elements.fldCoreDiameter.visible = false;
	
	// GD - Apr 15, 2014: Will be turning these off for roll core
	forms.in_item_details_dtl.elements.lbl_item_max_weight.visible = true;
	forms.in_item_details_dtl.elements.item_max_weight.visible = true;
	forms.in_item_details_dtl.elements.lbl_item_dimension_length.visible = true;
	forms.in_item_details_dtl.elements.item_dimension_heigth.visible = true;
	forms.in_item_details_dtl.elements.item_dimension_length.visible = true;
	if (scopes.globals.avPref_dimension_unit == scopes.avUtils.ENUM_DIMENSION_UNITS.Metric) {
	    forms.in_item_details_dtl.elements.item_dimension_heigth.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions_mm');
	    forms.in_item_details_dtl.elements.item_dimension_width.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions_mm');
	    forms.in_item_details_dtl.elements.item_dimension_length.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions_mm');
    }
    else {
        forms.in_item_details_dtl.elements.item_dimension_heigth.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions');
        forms.in_item_details_dtl.elements.item_dimension_width.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions');               
        forms.in_item_details_dtl.elements.item_dimension_length.toolTipText = i18n.getI18NMessage('avanti.lbl.tooltip.itemDimensions');               
    }
	forms.in_item_details_dtl.elements.item_dimension_width.visible = true;
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"0041290B-216C-4A7C-B0CB-79A83822CD06"}
 */
function onAction_btnSpecifications(event) {
	forms.in_item_specifications_dlg.foundset.loadRecords(foundset)
	globals.DIALOGS.showFormInModalDialog(forms.in_item_specifications_dlg, -1, -1, 735, 335, "i18n:avanti.lbl.specifications", true, false, "dlgItemSpecifications", true);
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"261D81B3-2C93-482B-BABF-E1A16DB9CB71"}
 */
function dc_save(_event, _triggerForm)
{
	if (item_code == null || item_code.trim() == "") {
		scopes.avText.pleaseEnterAValueFor("i18n:avanti.lbl.itemCode");
		return null;
	}
	
	// sl-22321 - have to use sql to check for duplicate codes, otherwise we dont find duplicates from another div/plant if filter is used 
	if (scopes.avDB.SQLQuery("SELECT COUNT(*) FROM in_item WHERE org_id = ? AND item_code = ? and item_id != ?", null, [globals.org_id, item_code, item_id.toString()]) > 0) {
		scopes.avText.showWarning("itemCodeHasAlreadyBeenUsed");
		return null;
	}
	
	// GD - 2013-05-10: Trapping for missing related information 
	if (utils.hasRecords(in_item_to_in_item_class) 
			&& in_item_to_in_item_class.itemclass_type == "I"
			&& (
					!utils.hasRecords(in_item_to_in_item_ink)
					|| !in_item_to_in_item_ink.inktype_id
			))
	{
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.itemInkError_title"), i18n.getI18NMessage("avanti.dialog.itemInkError_msg"), i18n.getI18NMessage("avanti.dialog.ok"));

		return null;
	}
	else if(utils.hasRecords(in_item_to_in_item_class) && 
			(in_item_to_in_item_class.itemclass_type == "I" || in_item_to_in_item_class.itemclass_type == "CO"))
	{
		var fsItemInk = foundset.getSelectedRecord().in_item_to_in_item_ink;
		var rInk;
		if(utils.hasRecords(fsItemInk)){
			rInk = fsItemInk.getRecord(1);
			//Edit the ink type and coverage for the item
			if(forms.in_item_ink_dtl.inkType != '' && rInk.inktype_id != forms.in_item_ink_dtl.inkType){
				rInk.inktype_id = forms.in_item_ink_dtl.inkType;
			}
			if(forms.in_item_ink_dtl.nInkCoverage != null){
				rInk.itemink_coverage = forms.in_item_ink_dtl.nInkCoverage;
			}
		}
		else{
			//Assign ink to the item
			if(forms.in_item_ink_dtl.inkType != ''){
				rInk = fsItemInk.getRecord(fsItemInk.newRecord(false, true));
				rInk.item_id = foundset.getSelectedRecord().item_id;
				rInk.inktype_id = forms.in_item_ink_dtl.inkType;
				rInk.itemink_coverage = forms.in_item_ink_dtl.nInkCoverage;
			}
			else{
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.itemInkError_title"), i18n.getI18NMessage("avanti.dialog.itemInkError_msg"), i18n.getI18NMessage("avanti.dialog.ok"));
				return null;
			}
			
		}
	}
	// HP - SL-1027 - validate selling unit entries 
	if(ValidateSellingUnits()==false)
		return null;
	
	if(item_standard_uom_id == null){
		scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.item_standard_uom_id');
		return null;
	}
	
	// SL-17627 - comcorp had a few items that had several item supplier recs that all used sequence_nr # 1. not sure how it happened. possibly 2 users adding 
	// item suppliers to the same item at the same time. check  seq nums on save and correct if necessary 
	validateItemSupplierSequenceNums();
	
	if (utils.hasRecords(in_item_to_in_item_paper) && !scopes.avInv.isPaper(foundset.getSelectedRecord())) {
		in_item_to_in_item_paper.deleteAllRecords();
	}	
	
	return _super.dc_save(_event, _triggerForm);
}

/**
 * @private 
 * 
 * @properties={typeid:24,uuid:"AA628F77-9821-4D67-A511-03ADDEA718C8"}
 */
function validateItemSupplierSequenceNums() {
	if (utils.hasRecords(in_item_to_in_item_supplier)) {
		databaseManager.saveData(in_item_to_in_item_supplier); // fs needs to be saved or sort will throw error
		in_item_to_in_item_supplier.sort('sequence_nr asc');
		
		for (var i = 1; i <= in_item_to_in_item_supplier.getSize(); i++) {
			var rItemSupp = in_item_to_in_item_supplier.getRecord(i);
			
			if (rItemSupp.sequence_nr != i) {
				rItemSupp.sequence_nr = i;
				databaseManager.saveData(rItemSupp);
			}
		}
	}
}

/**
 * return {Boolean}
 * @return
 * @properties={typeid:24,uuid:"5248741D-D833-4F04-BF45-91D45706FCE8"}
 */
function ValidateSellingUnits(){
	var tsInvalidSellingUOMRecs = ''
	var tsInvalidPricingUOMRecs = ''

	if (utils.hasRecords(in_item_to_in_item_selling_uom)){
		var tiNumRecs = in_item_to_in_item_selling_uom.getSize()
		
		for(var i=1;i<=tiNumRecs;i++){
			in_item_to_in_item_selling_uom.setSelectedIndex(i)
			if(!in_item_to_in_item_selling_uom.itemselluom_sell_uom_id){
				if(tsInvalidSellingUOMRecs!='')
					tsInvalidSellingUOMRecs += ','
				tsInvalidSellingUOMRecs += i 
			}
				
			if(!in_item_to_in_item_selling_uom.itemselluom_price_uom_id){
				if(tsInvalidPricingUOMRecs!='')
					tsInvalidPricingUOMRecs += ','
				tsInvalidPricingUOMRecs += i 
			}
		}
	}
	
	if(tsInvalidSellingUOMRecs + tsInvalidPricingUOMRecs != ''){
		var tsMsg = i18n.getI18NMessage('avanti.dialog.InvalidSellingUnits') + ' \n\n'
		if(tsInvalidSellingUOMRecs!='')
			tsMsg+= i18n.getI18NMessage('avanti.dialog.MissingSellingUnit') + ' ' + tsInvalidSellingUOMRecs + '\n'
		if(tsInvalidPricingUOMRecs!='')
			tsMsg+= i18n.getI18NMessage('avanti.dialog.MissingPricingUnit') + ' ' + tsInvalidPricingUOMRecs + '\n'
			
		tsMsg+= '\n' + i18n.getI18NMessage('avanti.dialog.CorrectAndTryAgain')
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.InvalidData'), tsMsg, i18n.getI18NMessage("avanti.dialog.ok"));
		return false
	}
	else
		return true
}

/**
 * @public 
 * @return {String}
 * @properties={typeid:24,uuid:"5C3898A8-1856-4B8C-9BD6-B58E4EBE1B3F"}
 */
function getUOM(){
	if(utils.hasRecords(in_item_to_sys_unit_of_measure_stocking_uom)
		&& utils.hasRecords(in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom)){
		return in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_desc
	}
	return null
}

/**
 * @public 
 * @return
 * @properties={typeid:24,uuid:"D0512875-6DB7-4112-9839-536C0E2B58DF"}
 */
function getItem(){
	return foundset.item_id
}

/**
 * Callback method when the user changes tab in a tab panel or divider position in split pane.
 *
 * @param {Number} previousIndex index of tab shown before the change
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"F050A0F0-2FE7-41B4-BEDD-7942DADC68AE"}
 */
function onTabChange_tab1 (previousIndex, event) {
	var sCurrentForm = scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tab1", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tab1"));
    if (sCurrentForm == 'in_item_trans_detail_tbl') {
        forms.in_item_trans_detail_tbl.rItem = foundset.getSelectedRecord();
    }
    else if (sCurrentForm == 'in_item_fifo_dialog_detail') {
    	 forms.in_item_fifo_dialog._rItem = foundset.getSelectedRecord();
    }
}

/**
 *
 * @param {JSFoundSet} _foundset
 *
 * @return
 * @properties={typeid:24,uuid:"91C0F603-2C44-4777-ACED-AA515550FBB0"}
 */
function dc_save_post (_foundset) {
	 writeAuditHistoryRecordForItems(foundset.getSelectedRecord());// try to pass the arguments as a normal method call: _super.dc_save_pre(arg1,arg2)
	return _super.dc_save_post(_foundset)
	
}

/**
 * TODO generated, please specify type and doc for the params
 * @param {JSRecord<db:/avanti/in_item>} rItem
 *
 * @properties={typeid:24,uuid:"F11DC14A-A90E-47D2-A363-A9D72E907427"}
 */
function writeAuditHistoryRecordForItems(rItem) {
	/***@type {JSRecord<db:/avanti/audit_history>} ***/
	var rAuditHistory;
	var dsData;
	var dsData1;
	var oSQL1;
	var j;
	var date;
	if (!utils.hasRecords(rItem.in_item_to_audit_history)) {
		rItem.in_item_to_audit_history.newRecord();
		rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
		rAuditHistory.audit_module_name = "Item";
		rAuditHistory.original_record_number = rItem.item_code;
		date = rAuditHistory.created_date;
		rAuditHistory.audit_field_name = "Item Code";
		if (rItem.item_code != null) {
			rAuditHistory.audit_field_value = rItem.item_code;
		} else {
			rAuditHistory.audit_field_value = '';
		}
		databaseManager.saveData(rAuditHistory);

		rItem.in_item_to_audit_history.duplicateRecord();
		rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
		rAuditHistory.created_date = date;
		rAuditHistory.audit_field_name = "Item Class";
		if (utils.hasRecords(rItem.in_item_to_in_item_class)) {
			rAuditHistory.audit_field_value = rItem.in_item_to_in_item_class.itemclass_desc;
		} else {
			rAuditHistory.audit_field_value = '';
		}
		databaseManager.saveData(rAuditHistory);

		rItem.in_item_to_audit_history.duplicateRecord();
		rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
		rAuditHistory.created_date = date;
		rAuditHistory.audit_field_name = "Item Group";
		if (utils.hasRecords(rItem.in_item_to_in_group)) {
			rAuditHistory.audit_field_value = rItem.in_item_to_in_group.ingroup_desc;
		} else {
			rAuditHistory.audit_field_value = '';
		}
		databaseManager.saveData(rAuditHistory);

		if (utils.hasRecords(rItem.in_item_to_in_item_supplier)) {
			for (var i = 1; i <= rItem.in_item_to_in_item_supplier.getSize(); i++) {
				if (utils.hasRecords(rItem.in_item_to_in_item_supplier.getRecord(i).in_item_supplier_to_po_purchase_detail)) {
					rItem.in_item_to_audit_history.duplicateRecord();
					rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
					rAuditHistory.created_date = date;
					rAuditHistory.audit_field_name = "Landed Cost";
					rItem.in_item_to_in_item_supplier.getRecord(i).in_item_supplier_to_po_purchase_detail.sort('created_date desc')
					rAuditHistory.auxilary_record_id = rItem.in_item_to_in_item_supplier.getRecord(i).itemsupp_id;
					if (rItem.in_item_to_in_item_supplier.getRecord(i).in_item_supplier_to_po_purchase_detail.getRecord(1).podet_landed_cost != null) {
						rAuditHistory.audit_field_value = rItem.in_item_to_in_item_supplier.getRecord(i).in_item_supplier_to_po_purchase_detail.getRecord(1).podet_landed_cost;
					} else {
						rAuditHistory.audit_field_value = '';
					}
					databaseManager.saveData(rAuditHistory);
				}

			}
		}
		if (utils.hasRecords(rItem.in_item_to_in_item_selling_uom)) {
			for (j = 1; j <= rItem.in_item_to_in_item_selling_uom.getSize(); j++) {
				rItem.in_item_to_audit_history.duplicateRecord();
				rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
				rAuditHistory.created_date = date;
				rAuditHistory.audit_field_name = "List Price";
				rAuditHistory.auxilary_record_id = rItem.in_item_to_in_item_selling_uom.getRecord(j).itemselluom_id;
				if (rItem.in_item_to_in_item_selling_uom.getRecord(j).itemselluom_list_price != null)
					rAuditHistory.audit_field_value = "$" + rItem.in_item_to_in_item_selling_uom.getRecord(j).itemselluom_list_price.toFixed(rItem.in_item_to_in_item_selling_uom.getRecord(j).in_item_selling_uom_to_sys_unit_of_measure.uom_decimals_price).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
				else
					rAuditHistory.audit_field_value = '';
				databaseManager.saveData(rAuditHistory);
			}

		}
		rItem.in_item_to_in_item_supplier.in_item_supplier_to_po_purchase_detail
	} else {
		verifyAuxilaryRecordForItems(rItem, date);
		var oSQL = { };
		oSQL.sql = "SELECT audit_field_value \
                   FROM audit_history \
                   WHERE audit_field_name like ? and link_to_record_id = ? \
                  ORDER BY created_date DESC "

		oSQL.args = ["Item Code", rItem.item_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rItem.item_code && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {
				rItem.in_item_to_audit_history.newRecord();
				rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Item";
				rAuditHistory.original_record_number = rItem.item_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Item Code";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);

			}

		} else if (dsData.getValue(1, 1) != rItem.item_code) {
			rItem.in_item_to_audit_history.newRecord();
			rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
			rAuditHistory.audit_module_name = "Item";
			rAuditHistory.original_record_number = rItem.item_code;
			if (date != null) {
				rAuditHistory.created_date = date;
			} else {
				date = rAuditHistory.created_date;
			}

			rAuditHistory.audit_field_name = "Item Code";
			rAuditHistory.audit_field_value = rItem.item_code;
			databaseManager.saveData(rAuditHistory);
		}

		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC "

		oSQL.args = ["Item Class", rItem.item_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rItem.itemclass_id && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {

				rItem.in_item_to_audit_history.newRecord();
				rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Item";
				rAuditHistory.original_record_number = rItem.item_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Item Class";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);
			}
		} else if (dsData.getValue(1, 1) != rItem.in_item_to_in_item_class.itemclass_desc) {
			rItem.in_item_to_audit_history.newRecord();
			rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
			rAuditHistory.audit_module_name = "Item";
			rAuditHistory.original_record_number = rItem.item_code;
			if (date != null) {
				rAuditHistory.created_date = date;
			} else {
				date = rAuditHistory.created_date;
			}

			rAuditHistory.audit_field_name = "Item Class";
			rAuditHistory.audit_field_value = rItem.in_item_to_in_item_class.itemclass_desc;
			databaseManager.saveData(rAuditHistory);
		}

		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC "

		oSQL.args = ["Item Group", rItem.item_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rItem.ingroup_id && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {
				rItem.in_item_to_audit_history.newRecord();
				rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Item";
				rAuditHistory.original_record_number = rItem.item_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Item Group";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);

			}

		} else if (rItem.ingroup_id != null) {
			if (dsData.getValue(1, 1) != rItem.in_item_to_in_group.ingroup_desc) {
				rItem.in_item_to_audit_history.newRecord();
				rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Item";
				rAuditHistory.original_record_number = rItem.item_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Item Group";
				rAuditHistory.audit_field_value = rItem.in_item_to_in_group.ingroup_desc;
				databaseManager.saveData(rAuditHistory);
			}
		}

		if (utils.hasRecords(rItem.in_item_to_in_item_selling_uom)) {
			for (var t = 1; t <= rItem.in_item_to_in_item_selling_uom.getSize(); t++) {
				oSQL = { };
				oSQL.sql = "SELECT  auxilary_record_id \
	                   FROM audit_history \
	                   WHERE  link_to_record_id = ?  and   auxilary_record_id = ?\
	                  ORDER BY created_date DESC "
				oSQL.args = [rItem.item_id.toString(), rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_id.toString()];
				dsData = globals["avUtilities_sqlDataset"](oSQL);
				if (dsData.getMaxRowIndex() >= 1) {
					oSQL1 = { };
					oSQL1.sql = "SELECT audit_field_value \
			                   FROM audit_history \
			                   WHERE audit_field_name like ? and link_to_record_id = ?   and   auxilary_record_id = ?\
			                  ORDER BY created_date DESC "
					oSQL1.args = ["List Price", rItem.item_id.toString(), rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_id.toString()];
					/**@type {JSDataSet} **/
					dsData1 = globals["avUtilities_sqlDataset"](oSQL1);
					if (dsData1.getMaxRowIndex() >= 1) {
						var value;
						if (rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_list_price != null) {
							value = "$" + rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_list_price.toFixed(rItem.in_item_to_in_item_selling_uom.getRecord(t).in_item_selling_uom_to_sys_unit_of_measure.uom_decimals_price).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
						} else {
							value = '';
						}
						if (value != '') {
							if (value != dsData1.getValue(1, 1)) {
								rItem.in_item_to_audit_history.newRecord();
								rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
								rAuditHistory.audit_module_name = "Item";
								rAuditHistory.original_record_number = rItem.item_code;
								if (date != null) {
									rAuditHistory.created_date = date;
								} else {
									date = rAuditHistory.created_date;
								}
								rAuditHistory.audit_field_name = "List Price";
								rAuditHistory.auxilary_record_id = rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_id;
								rAuditHistory.audit_field_value = value;
								databaseManager.saveData(rAuditHistory);

							}

						} else if (!value && dsData1.getValue(1, 1)) {
							if (dsData1.getValue(1, 1) != "Data deleted by user") {
								rItem.in_item_to_audit_history.newRecord();
								rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
								rAuditHistory.audit_module_name = "Item";
								rAuditHistory.original_record_number = rItem.item_code;
								if (date != null) {
									rAuditHistory.created_date = date;
								} else {
									date = rAuditHistory.created_date;
								}
								rAuditHistory.audit_field_name = "List Price";
								rAuditHistory.auxilary_record_id = rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_id;
								rAuditHistory.audit_field_value = "Data deleted by user";
								databaseManager.saveData(rAuditHistory);
							}
						}
					}
				} else {
					rItem.in_item_to_audit_history.newRecord();
					rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
					rAuditHistory.audit_module_name = "Item";
					rAuditHistory.original_record_number = rItem.item_code;
					if (date != null) {
						rAuditHistory.created_date = date;
					} else {
						date = rAuditHistory.created_date;
					}
					rAuditHistory.audit_field_name = "List Price";
					rAuditHistory.auxilary_record_id = rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_id;
					if (rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_list_price != null)
						rAuditHistory.audit_field_value = "$" + rItem.in_item_to_in_item_selling_uom.getRecord(t).itemselluom_list_price.toFixed(rItem.in_item_to_in_item_selling_uom.getRecord(t).in_item_selling_uom_to_sys_unit_of_measure.uom_decimals_price).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
					else
						rAuditHistory.audit_field_value = '';
					databaseManager.saveData(rAuditHistory);

				}
			}
		}
	}
}

/** *
 * @param {JSRecord<db:/avanti/in_item>} rItem
 * @param {Date} date
 *
 * @properties={typeid:24,uuid:"DD242A89-760C-4861-B44D-685D738C3A19"}
 */
function verifyAuxilaryRecordForItems(rItem, date) {

	var oSQL = { };
	oSQL.sql = 'SELECT DISTINCT(auxilary_record_id) \
	          FROM audit_history\
	          WHERE audit_field_name LIKE  ? and  link_to_record_id = ?'
	oSQL.args = ['Landed Cost', rItem.item_id.toString()];
	/***@type {JSRecord<db:/avanti/audit_history>} ***/
	var rAuditHistory;
	var i;
	/**@type {JSDataSet} **/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData.getMaxRowIndex() >= 1) {
		for (i = 1; i <= dsData.getMaxRowIndex(); i++) {
			oSQL.sql = 'SELECT itemsupp_id \
	          FROM  in_item_supplier\
	          WHERE itemsupp_id  = ?'
			oSQL.args = [dsData.getValue(i, 1).toString()];
			/**@type {JSDataSet} **/
			var dsData1 = globals["avUtilities_sqlDataset"](oSQL);
			if (dsData1.getMaxRowIndex() < 1) {
				oSQL.sql = "SELECT audit_field_value \
		            FROM audit_history \
		            WHERE audit_field_name like ? and link_to_record_id = ? and  auxilary_record_id = ? \
		           ORDER BY created_date DESC"

				oSQL.args = ["Landed Cost", rItem.item_id.toString(), dsData.getValue(i, 1)];
				var dsData2 = globals["avUtilities_sqlDataset"](oSQL);
				if (dsData2.getMaxRowIndex() >= 1) {
					if (dsData2.getValue(1, 1) != '' && dsData2.getValue(1, 1) != 'Data deleted by user') {
						rItem.in_item_to_audit_history.newRecord();
						rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
						rAuditHistory.audit_module_name = "Item";
						rAuditHistory.original_record_number = rItem.item_code;

						if (date != null) {
							rAuditHistory.created_date = date;
						} else {
							date = rAuditHistory.created_date;
						}

						rAuditHistory.audit_field_name = "Landed Cost";
						rAuditHistory.audit_field_value = "Data deleted by user";
						rAuditHistory.auxilary_record_id = dsData.getValue(i, 1);
						databaseManager.saveData(rAuditHistory);
					}
				}

			}

		}
	}
	oSQL.sql = 'SELECT DISTINCT(auxilary_record_id) \
        FROM audit_history\
        WHERE audit_field_name LIKE  ? and  link_to_record_id = ?'
	oSQL.args = ['List Price', rItem.item_id.toString()];
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData.getMaxRowIndex() >= 1) {
		for (i = 1; i <= dsData.getMaxRowIndex(); i++) {
			oSQL.sql = 'SELECT itemselluom_id \
		          FROM  in_item_selling_uom\
		          WHERE itemselluom_id = ?'
			oSQL.args = [dsData.getValue(i, 1)];
			/**@type {JSDataSet} **/
			dsData1 = globals["avUtilities_sqlDataset"](oSQL);
			if (dsData1.getMaxRowIndex() < 1) {
				oSQL.sql = "SELECT audit_field_value \
	            FROM audit_history \
	            WHERE audit_field_name like ? and link_to_record_id = ? and  auxilary_record_id = ? \
	           ORDER BY created_date DESC"

				oSQL.args = ["List Price", rItem.item_id.toString(), dsData.getValue(i, 1)];
				dsData2 = globals["avUtilities_sqlDataset"](oSQL);
				if (dsData2.getMaxRowIndex() >= 1) {
					if (dsData2.getValue(1, 1) != '' && dsData2.getValue(1, 1) != 'Data deleted by user') {
						rItem.in_item_to_audit_history.newRecord();
						rAuditHistory = rItem.in_item_to_audit_history.getSelectedRecord();
						rAuditHistory.audit_module_name = "Item";
						rAuditHistory.original_record_number = rItem.item_code;

						if (date != null) {
							rAuditHistory.created_date = date;
						} else {
							date = rAuditHistory.created_date;
						}

						rAuditHistory.audit_field_name = "List Price";
						rAuditHistory.audit_field_value = "Data deleted by user";
						rAuditHistory.auxilary_record_id = dsData.getValue(i, 1);
						databaseManager.saveData(rAuditHistory);
					}
				}

			}

		}
	}

}

/**
 * Refresh Quantities
 *
 * <AUTHOR> Dol
 * @since Feb 27, 2018
 * @public
 *
 * @properties={typeid:24,uuid:"B3C2E02D-0950-4AD3-9016-494C653A6748"}
 */
function refreshQuantities() {
    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter) && item_id) {
        databaseManager.recalculate(foundset.getSelectedRecord());
    }
}

/**
 * @public 
 * 
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"C35BA606-DCD1-44BD-963C-0EA65B908254"}
 */
function isProjectEnabledItem() {
	if (utils.hasRecords(foundset) && itemtype_code == scopes.avUtils.ITEM_TYPE.FinishedGood && scopes.avInv.isProjectInventoryOn()) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8E50072D-356D-4B5B-ABF6-596BB7A1B686"}
 */
function onDataChange_status(oldValue, newValue, event) {
	if (newValue == scopes.avUtils.ITEM_STATUS.ObsoleteAndReplace) {
		if (utils.hasRecords(in_item_to_in_item$replacement)) {
			var sMSG = i18n.getI18NMessage("avanti.dialog.ItemAlreadyReplaced") + in_item_to_in_item$replacement.item_code;
			scopes.avText.showWarning(sMSG, true);
			item_status = oldValue;
		}
		else {
			var rObsoleteItem = foundset.getSelectedRecord();
			var rReplacementItem = null;
			
			scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tab1", 1);
			rObsoleteItem.item_status = oldValue; // set status back to old value so duplicate will have that value
			
			_inDuplicateMode = 1;
			
			// if UseItemRevisionField then dont pass in new item code - we will come up with it in duplicateItem()
			if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseItemRevisionField)) {
				rReplacementItem = duplicateItem(null, item_id);
			}
			else {
				rReplacementItem = duplicateItem(getReplacementItemCode(), item_id);
			}
			
			_inDuplicateMode = 0;		
			
			rObsoleteItem.item_status = scopes.avUtils.ITEM_STATUS.Obsolete;
			databaseManager.saveData(rObsoleteItem);
			
			if (forms.in_btos_dlg.loadData(rObsoleteItem.item_id, rReplacementItem.item_id)) {
				globals.DIALOGS.showFormInModalDialog(forms.in_btos_dlg, -1, -1, 825, 550, i18n.getI18NMessage("avanti.lbl.iItemInKits"), true, false, "dlgItemInKits", true);
			}
			
			dc_edit(null, globals.nav.browser_buttonbar);
		}
	}
	
	return true;
}

/**
 * @private 
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C7FBD62B-C30F-442A-89F5-0EE2C48DABE1"}
 */
function getReplacementItemCode() {
	// it will use these if this is the first replacement, or if we couldnt find the next replacement number (which shouldnt happen unless the user has done something silly with the numbers)
	var sDefaultItemCode = item_code;
	var sDefaultSuffix = "001";
	
	// if Current Item is a replacement
	if (replaces_item_id) {
		var sItemCode = null;
		var sSuffix = null;
		var a = item_code.split("-");
		
		if (a.length == 2) {
			sItemCode = a[0];
			sSuffix = a[1];
		}
		// there could be more than 1 '-' in item code, use last as suffix
		else if (a.length >= 2) {
			sSuffix = a[a.length - 1];
			sItemCode = item_code.substr(0, item_code.length - sSuffix.length - 1);
		}
		
		if (sItemCode && sSuffix) {
			var nSuffixNum = parseInt(sSuffix);
			
			if (scopes.avMath.isNumber(nSuffixNum)) {
				nSuffixNum++;
				sSuffix = scopes.avText.padBefore(nSuffixNum.toString(), "0", sSuffix.length);
			}
		}
	}
	
	if (sItemCode && sSuffix && nSuffixNum) {
		return sItemCode + "-" + sSuffix;
	}
	else {
		return sDefaultItemCode + "-" + sDefaultSuffix;
	}
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"409A057C-1C8A-495A-B579-5F84314EFF8B"}
 */
function onDataChange_item_code_no_revision(oldValue, newValue, event) {
	makeItemCodeWithRevision();
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E0EC9D29-BF7A-4942-AE37-095C79A7B4E6"}
 */
function onDataChange_item_revision(oldValue, newValue, event) {
	makeItemCodeWithRevision();
	return true;
}

/**
 * @properties={typeid:24,uuid:"F4ED6EF2-BD12-4808-831E-0488EE5A5023"}
 */
function makeItemCodeWithRevision() {
	if (item_code_no_revision != null) {
		item_code_no_revision = item_code_no_revision.trim();
		
		if (item_code_no_revision != "") {
			item_code = item_code_no_revision;
			
			if (item_revision != null) {
				item_revision = item_revision.trim();

				if (item_revision != "") {
					item_code += "-" + item_revision;
				}
			}
		}
	}
}
/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"03BA7F41-360D-4B79-9C2D-502CF977C770"}
 */
function onDataChange_item_code(oldValue, newValue, event) {
	// even if SYS_PREF.UseItemRevisionField use item_code_no_revision dp for item_code field. its 36 char len will limit user input. the field maxlen property wasnt working 
	item_code = item_code_no_revision;
	return true;
}
