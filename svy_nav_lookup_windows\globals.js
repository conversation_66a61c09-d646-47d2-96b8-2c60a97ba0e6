/**
 * @properties={typeid:35,uuid:"C8EE9766-9155-49FF-948C-DB2F33CE05F6",variableType:-4}
 */
var vSavedLookupWindowFoundset = null;

/**
 * @type {{pk:Number, fields:Array, data:Array, mode:String}}
 * @properties={typeid:35,uuid:"4195E908-335A-455F-A222-DD3D45AB673D",variableType:-4}
 */
var svy_nav_fr_solutionModelObject = null;

/**
 * @param {JSEvent} _event
 * @param {String} _returnField
 * @param {String} _program
 * @param {String} [_afterInsertMethodName]
 * @param {String} [_methodToAddFoundsetFilters]
 * @param {Object} [_params]  Mode can be (case insensitive):
 *							"newCommit" - will commit the transaction in the lookup window
 *							"newNoCommit" - will not commit the transaction in the lookup window
 *							"show" - shows the requested record (which has the edit button if needed) - pass the pk to use the correct record
 *							"editCommit" - will commit the transaction in the lookup window - pass the pk to use the correct record
 *							"editNoCommit" - will not commit the transaction in the lookup window - pass the pk to use the correct record
 * @param {String} [_useEditForm] - Optional to force a specific form to be used for the edit lookup
 * @param {String} [sSort] - Optional sort string to use for the records
 *
 * @properties={typeid:24,uuid:"E106D87F-1106-4FB1-A67D-ED2DBE853664"}
 * @AllowToRunInFind
 */
function svy_nav_showLookupWindow(_event, _returnField, _program, _afterInsertMethodName, _methodToAddFoundsetFilters, _params, _useEditForm, sSort) {
	var _element = _event.getElementName();
	var _returnForm = _event.getFormName();
	_program = _program.replace(/ /g, "_");

	//This was not being added at login because it does not have a security key, but we don't want users
	//to have access to this, it's only added as a program for the framework record selector.
	if (_program && !globals.nav.program[_program]) {
		if (_program == "_appItemType") {
			//initialize required fields for the item type lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "app_item_type";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = i18n.getI18NMessage('avanti.lbl.itemType');
			globals.nav.program[_program].sort_value = "itemtype_code asc";
		}
		// _ContactsAll not loaded at login, unless superuser, need it for this lookup th o
		else if (_program == "_ContactsAll") {
			//initialize required fields for the contacts lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "sa_customer_contact";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Contacts";
			globals.nav.program[_program].sort_value = null;
			globals.nav.program[_program].display_field_header = "contact_full_name";
		}
		else if (_program == "Order_Types") {
			//initialize required fields for the item type lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "sa_order_type";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Order Type";
			globals.nav.program[_program].sort_value = "ordtype_desc asc";
		}
		else if (_program == "_appOrderStatus") {
			//initialize required fields for the order status lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "app_order_status";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Order Status";
			globals.nav.program[_program].sort_value = "ordstat_id asc";
		}
		else if (_program == "Document_Number") {
			//initialize required fields for the document number lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "sys_document_number";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Document Number";
			globals.nav.program[_program].sort_value = "docnum_stream asc";
		}
		else if (_program && _program == "Projects") {
			//initialize required fields for the item type lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "sa_customer_project";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Order Project";
			globals.nav.program[_program].sort_value = "custproj_desc asc";
		}
		//SL-11546
		else if (_program == "_ReceiptForCancellation") {
			//initialize required fields for the item type lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "po_receipt";
			globals.nav.program[_program].btn_lookup_new = true;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = true;
			globals.nav.program[_program].description = "Receipts For Cancellation";
			globals.nav.program[_program].sort_value = "porec_po_document_num DESC, porec_receipt_version DESC";
		}
		else if (_program == "System_Notes") {
			//initialize required fields for the System Notes lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "sys_note";
			globals.nav.program[_program].btn_lookup_new = true;
			globals.nav.program[_program].add_mode = true;
			globals.nav.program[_program].btn_lookup_show = true;
			globals.nav.program[_program].description = "Notes";
			globals.nav.program[_program].sort_value = null;
			globals.nav.program[_program].form = [[1, 'detail', 'sys_note_dtl', 1],
			[2, 'table', 'sys_note_tbl', 1]];
			globals.nav.program[_program].program_name = "System_Notes";
			globals.nav.program[_program].base_form_name = "sys_note";
			globals.nav.program[_program].btn_export = true;
			globals.nav.program[_program].btn_help = true;
			globals.nav.program[_program].btn_all_records = false;
			globals.nav.program[_program].btn_lookup_new = true;
			globals.nav.program[_program].btn_method = true;
			globals.nav.program[_program].btn_print = true;
			globals.nav.program[_program].btn_rec_nav = true;
			globals.nav.program[_program].btn_record_information = true;
			globals.nav.program[_program].btn_required_fields = true;
			globals.nav.program[_program].btn_resettblheader = false;
			globals.nav.program[_program].btn_search = false;
			globals.nav.program[_program].btn_search_prop = false;
			globals.nav.program[_program].btn_sort = true;
			globals.nav.program[_program].delete_mode = true;
			globals.nav.program[_program].update_mode = true;
		}
		else if (_program == "Supplier_Contacts") {
			//initialize required fields for the contacts lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "ap_supplier_contact";
			globals.nav.program[_program].btn_lookup_new = true;
			globals.nav.program[_program].add_mode = true;
			globals.nav.program[_program].update_mode = true;
			globals.nav.program[_program].btn_lookup_show = true;
			globals.nav.program[_program].description = "Supplier Contacts";
			globals.nav.program[_program].sort_value = null;
			globals.nav.program[_program].display_field_header = "contact_full_name";
			globals.nav.program[_program].form = [[1, 'detail', 'ap_supplier_contact_dtl', 1]];
		}
		//SL-15707
		else if (_program == "Item_Warehouses") {
			//initialize required fields for the Item Warehouses lookup
			globals.nav.program[_program] = { };
			globals.nav.program[_program].server_name = "avanti";
			globals.nav.program[_program].table_name = "in_item_warehouse";
			globals.nav.program[_program].btn_lookup_new = false;
			globals.nav.program[_program].add_mode = false;
			globals.nav.program[_program].btn_lookup_show = false;
			globals.nav.program[_program].description = "Item Warehouses";
			globals.nav.program[_program].sort_value = "whse_id asc";
			globals.nav.program[_program].display_field_header = "whse_id";
			globals.nav.program[_program].form = [[1, 'detail', 'in_item_warehouse_dtl', 1],
			[2, 'table', 'in_item_warehouse_tbl', 1]];
			globals.nav.program[_program].program_name = "Item_Warehouses";
			globals.nav.program[_program].base_form_name = "in_item_warehouse";
			globals.nav.program[_program].btn_export = true;
			globals.nav.program[_program].btn_help = true;
			globals.nav.program[_program].btn_all_records = false;
			globals.nav.program[_program].btn_lookup_new = true;
			globals.nav.program[_program].btn_method = true;
			globals.nav.program[_program].btn_print = true;
			globals.nav.program[_program].btn_rec_nav = true;
			globals.nav.program[_program].btn_record_information = true;
			globals.nav.program[_program].btn_required_fields = true;
			globals.nav.program[_program].btn_resettblheader = false;
			globals.nav.program[_program].btn_search = false;
			globals.nav.program[_program].btn_search_prop = false;
			globals.nav.program[_program].btn_sort = true;
			globals.nav.program[_program].delete_mode = true;
			globals.nav.program[_program].update_mode = true;
		}
	}

    //This was not being added at login because it does not have a security key, but we don't want users
    //to have access to this, it's only added as a program for the framework record selector.
    if (_program && !globals.nav.program[_program]) {
        if (_program == "_appItemType") {
            //initialize required fields for the item type lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "app_item_type";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = i18n.getI18NMessage('avanti.lbl.itemType');
            globals.nav.program[_program].sort_value = "itemtype_code asc";
        }
        // _ContactsAll not loaded at login, unless superuser, need it for this lookup th o
        else if (_program == "_ContactsAll") {
            //initialize required fields for the contacts lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "sa_customer_contact";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Contacts";
            globals.nav.program[_program].sort_value = null;
            globals.nav.program[_program].display_field_header = "contact_full_name";
        }
        else if (_program == "Order_Types") {
            //initialize required fields for the item type lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "sa_order_type";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Order Type";
            globals.nav.program[_program].sort_value = "ordtype_desc asc";
        }
        else if (_program == "_appOrderStatus") {
            //initialize required fields for the order status lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "app_order_status";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Order Status";
            globals.nav.program[_program].sort_value = "ordstat_id asc";
        }
        else if (_program == "Document_Number") {
            //initialize required fields for the document number lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "sys_document_number";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Document Number";
            globals.nav.program[_program].sort_value = "docnum_stream asc";
        }
        else if (_program && _program == "Projects") {
            //initialize required fields for the item type lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "sa_customer_project";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Order Project";
            globals.nav.program[_program].sort_value = "custproj_desc asc";
        }
        //SL-11546
        else if (_program == "_ReceiptForCancellation") {
            //initialize required fields for the item type lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "po_receipt";
            globals.nav.program[_program].btn_lookup_new = true;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = true;
            globals.nav.program[_program].description = "Receipts For Cancellation";
            globals.nav.program[_program].sort_value = "porec_po_document_num DESC, porec_receipt_version DESC";
        }
        else if (_program == "System_Notes") {
            //initialize required fields for the System Notes lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "sys_note";
            globals.nav.program[_program].btn_lookup_new = true;
            globals.nav.program[_program].add_mode = true;
            globals.nav.program[_program].btn_lookup_show = true;
            globals.nav.program[_program].description = "Notes";
            globals.nav.program[_program].sort_value = null;
            globals.nav.program[_program].form = [
                [1, 'detail', 'sys_note_dtl', 1],
                [2, 'table', 'sys_note_tbl', 1]
            ];
            globals.nav.program[_program].program_name = "System_Notes";
            globals.nav.program[_program].base_form_name = "sys_note";
            globals.nav.program[_program].btn_export = true;
            globals.nav.program[_program].btn_help = true;
            globals.nav.program[_program].btn_all_records = false;
            globals.nav.program[_program].btn_lookup_new = true;
            globals.nav.program[_program].btn_method = true;
            globals.nav.program[_program].btn_print = true;
            globals.nav.program[_program].btn_rec_nav = true;
            globals.nav.program[_program].btn_record_information = true;
            globals.nav.program[_program].btn_required_fields = true;
            globals.nav.program[_program].btn_resettblheader = false;
            globals.nav.program[_program].btn_search = false;
            globals.nav.program[_program].btn_search_prop = false;
            globals.nav.program[_program].btn_sort = true;
            globals.nav.program[_program].delete_mode = true;
            globals.nav.program[_program].update_mode = true;
        }
        else if (_program == "Supplier_Contacts") {
            //initialize required fields for the contacts lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "ap_supplier_contact";
            globals.nav.program[_program].btn_lookup_new = true;
            globals.nav.program[_program].add_mode = true;
            globals.nav.program[_program].update_mode = true;
            globals.nav.program[_program].btn_lookup_show = true;
            globals.nav.program[_program].description = "Supplier Contacts";
            globals.nav.program[_program].sort_value = null;
            globals.nav.program[_program].display_field_header = "contact_full_name";
            globals.nav.program[_program].form = [
            [1, 'detail', 'ap_supplier_contact_dtl', 1]
            ];
        }
        //SL-15707
        else if (_program == "Item_Warehouses") {
            //initialize required fields for the Item Warehouses lookup
            globals.nav.program[_program] = { };
            globals.nav.program[_program].server_name = "avanti";
            globals.nav.program[_program].table_name = "in_item_warehouse";
            globals.nav.program[_program].btn_lookup_new = false;
            globals.nav.program[_program].add_mode = false;
            globals.nav.program[_program].btn_lookup_show = false;
            globals.nav.program[_program].description = "Item Warehouses";
            globals.nav.program[_program].sort_value = "whse_id asc";
            globals.nav.program[_program].display_field_header = "whse_id";
            globals.nav.program[_program].form = [
            [1, 'detail', 'in_item_warehouse_dtl', 1],
            [2, 'table', 'in_item_warehouse_tbl', 1]
            ];
            globals.nav.program[_program].program_name = "Item_Warehouses";
            globals.nav.program[_program].base_form_name = "in_item_warehouse";
            globals.nav.program[_program].btn_export = true;
            globals.nav.program[_program].btn_help = true;
            globals.nav.program[_program].btn_all_records = false;
            globals.nav.program[_program].btn_lookup_new = true;
            globals.nav.program[_program].btn_method = true;
            globals.nav.program[_program].btn_print = true;
            globals.nav.program[_program].btn_rec_nav = true;
            globals.nav.program[_program].btn_record_information = true;
            globals.nav.program[_program].btn_required_fields = true;
            globals.nav.program[_program].btn_resettblheader = false;
            globals.nav.program[_program].btn_search = false;
            globals.nav.program[_program].btn_search_prop = false;
            globals.nav.program[_program].btn_sort = true;
            globals.nav.program[_program].delete_mode = true;
            globals.nav.program[_program].update_mode = true;
        }

    }
	
	// Store the parameter into a global object
	if (_params) {
		globals.svy_nav_fr_solutionModelObject = _params;
		globals.svy_nav_fr_solutionModelObject.mode = globals.svy_nav_fr_solutionModelObject.mode.toLowerCase();
	}
	else {
		globals.svy_nav_fr_solutionModelObject = null;
	}

	//get the foundset for the lookup windows
	var _fs = globals.svy_nav_lkp_getFoundset(_program, _returnForm, _methodToAddFoundsetFilters);

	if (globals.svy_nav_fr_solutionModelObject && globals.nav.mode == "browse" && (globals.svy_nav_fr_solutionModelObject.mode == "show" || globals.svy_nav_fr_solutionModelObject.mode == "lookup")) {
		// Allowing "show" and "lookup" mode to run in browse
		globals.svy_nav_lkp_showRecord(_fs, _program, null, null, null, _useEditForm, _afterInsertMethodName, _returnForm);
		return;

	}
	else if (globals.nav.mode == "browse" && !globals.nav.program[globals.nav_program_name].noreadonly && (globals.svy_nav_fr_solutionModelObject && globals.svy_nav_fr_solutionModelObject.mode && globals.svy_nav_fr_solutionModelObject.mode != "rptlookup")) {

		if (globals.nav_program_name != "Planned_Purchasing") //[AVANTI] need a way to call lookup without changing noreadonly.
		{
			return; // not allowed in browse mode
		}
	}

	if (globals.svy_nav_fr_solutionModelObject && (globals.svy_nav_fr_solutionModelObject.mode == 'newcommit' || globals.svy_nav_fr_solutionModelObject.mode == 'newnocommit')) {
		globals.svy_nav_lkp_showRecord(_fs, _program, true, null, null, _useEditForm, _afterInsertMethodName, _returnForm)//also new record
		return;
	}
	else if (globals.svy_nav_fr_solutionModelObject && globals.svy_nav_fr_solutionModelObject.mode != 'lookup' && globals.svy_nav_fr_solutionModelObject.mode != "rptlookup") {
		globals.svy_nav_lkp_showRecord(_fs, _program);
		return;
	}

	if (!globals.nav.program[_program]) {
		//program is not in object, maybe user has no rights, or program does not exist
		return;
	}

	//create new instance of the popup window
	// dashes in form name no longer supported therefore replace it with underscore
	var _UUID = application.getUUID().toString().replace(/-/g, '_');
	var _lookupWindow = 'svy_nav_fr_lookup_window';
	if (forms['svy_nav_fr_lookup_window_custom']) _lookupWindow = 'svy_nav_fr_lookup_window_custom';
	var _formname = _lookupWindow + _UUID;
	application.createNewFormInstance(_lookupWindow, _formname);

	var sDataSource = 'db:/' + globals.nav.program[_program].server_name + '/' + globals.nav.program[_program].table_name;

	/** @type {RuntimeWebComponent<aggrid-groupingtable_abs>} */
	var gridLookup = forms[_formname].elements['datagrid_lookup'];
	gridLookup.myFoundset = databaseManager.getFoundSet(globals.nav.program[_program].server_name, globals.nav.program[_program].table_name);
	gridLookup.myFoundset.foundset.loadRecords(_fs);
	gridLookup.setReadOnly(true);

	// GD - 2014-02-23: optional control the sort order
	if (sSort) {
		gridLookup.myFoundset.foundset.sort(sSort);
	}

	/** @type {JSFoundSet<db:/svy_framework/nav_program_fields>} */
	var _fs_fields = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_program_fields');
	_fs_fields.addFoundSetFilterParam('program_name', '=', _program);
	_fs_fields.addFoundSetFilterParam('flag_lookup_field', '=', 1);
	_fs_fields.loadRecords();

	forms[_formname]['vFields'] = new Array();
	forms[_formname]['vElements'] = new Array();

	var _w_field = 150;

	var _total_field_w = 0;

	if (_fs_fields.getSize() == 0) {
		//there are no lookup fields specified
		return;
	}
	for (var i = 1; i <= _fs_fields.getSize(); i++) {

		/** @type {JSRecord<db:/svy_framework/nav_program_fields>} */
		var _rec_pf = _fs_fields.getRecord(i);
		var _field_width = _w_field;
		if (_rec_pf.field_width) {
			_field_width = _rec_pf.field_width;
		}
		var _name = _rec_pf.elementname;
		/**@type {String} */
		var sLabel = _rec_pf.label;
		var sI18nLabel = (sLabel) ? i18n.getI18NMessage(sLabel.split(":")[1]) : "";

		var column = gridLookup.newColumn(_rec_pf.dataprovider);
		column.headerTitle = sI18nLabel;
		column.editType = "NONE";
		column.enableSort = true;
		column.enableRowGroup = false;
		column.enableToolPanel = false;
		column.enableResize = true;
		column.width = _field_width;
		column.minWidth = _field_width;
		column.showAs = "text";
		column.autoResize = true;
		column.columnDef = {
			suppressMenu: true
		}
		if (_name && utils.stringPatternCount(_name, '_date') > 0) {
			column.format = globals['avBase_dateFormat'];
		}
		if (_rec_pf.valuelistname) {
			column.valuelist = _rec_pf.valuelistname;
			//			solutionModel.getValueList(_rec_pf.valuelistname)
		}
		forms[_formname].vFields.push(_rec_pf.dataprovider);
		forms[_formname].vElements.push(_name);

		_total_field_w += _field_width;
	}

	//set the properties
	forms[_formname]['vProgram'] = _program;
	forms[_formname]['vReturnField'] = _returnField;
	forms[_formname]['vReturnForm'] = _returnForm;
	forms[_formname]['vAfterInsertMethodName'] = _afterInsertMethodName;
	forms[_formname]['vMethodToAddFoundsetFilters'] = _methodToAddFoundsetFilters;

	//set the buttons
	if (globals.nav.program[_program].btn_lookup_new) {
		forms[_formname].elements['btn_new_record'].enabled = (globals.nav.program[_program].add_mode == 1) && security.canInsert(sDataSource);
	}
	else {
		forms[_formname].elements['btn_new_record'].enabled = false;
	}

	if (globals.nav.program[_program].btn_lookup_show) {
		forms[_formname].elements['btn_show_record'].enabled = true;
	}
	else {
		forms[_formname].elements['btn_show_record'].enabled = false;
	}

	if (_element) {
		forms[_formname]['vSelectedElement'] = _element;
	}

	//make width bigger if there are more fields
	var _width = 640;
	var _basis_width = 225;
	if ( (_basis_width + _total_field_w) > _width) {
		_width = _basis_width + _total_field_w;
		if (application.getScreenWidth() < _width) {
			_width = application.getScreenWidth();
		}
	}
	//show the popup
	globals.DIALOGS.showFormInModalDialog(forms[_formname], -1, -1, _width, 545, i18n.getI18NMessage('svy.fr.lbl.lookup_window:') + ' ' + globals.nav.program[_program].description, true, false, globals.nav.program[_program].description, true);
}

/**
 * @param {String} _program
 * @param {String} _returnForm
 * @param {String} _methodToAddFoundsetFilters
 * @return
 * @properties={typeid:24,uuid:"78AA52C9-48A2-43B5-A6C6-F8C95527A824"}
 * @AllowToRunInFind
 */
function svy_nav_lkp_getFoundset(_program, _returnForm, _methodToAddFoundsetFilters) {

	var sServer;
	// GD - Apr 15, 2014: Still getting errors at line 232
	if (!globals.nav.program || !globals.nav.program[_program]) return null;

	// GD - 2014-02-23: SL-2071 - Trap for missing server name because of bad program name
	if (globals.nav.program[_program] && globals.nav.program[_program].server_name) {
		sServer = globals.nav.program[_program].server_name;
	}
	else {
		sServer = "avanti"; // the default for us
	}
	var _fs = databaseManager.getFoundSet(sServer, globals.nav.program[_program].table_name);
	
	// sl-11489 - if contact lookup have to filter out one time contacts
	if (_program == "_ContactsAll") {
		_fs.addFoundSetFilterParam('custcontact_one_time', '!=', 1);
	}
	else if (_program == "Warehouses") {
		_fs.addFoundSetFilterParam('whse_active', '=', 1);
	}
	else if (_program == "Suppliers") {
		_fs.addFoundSetFilterParam('supplier_active', '=', 1);
	}
	

	//if there is a foundsetfilter add it
	if (_methodToAddFoundsetFilters && forms[_returnForm][_methodToAddFoundsetFilters]) {
		_fs = forms[_returnForm][_methodToAddFoundsetFilters](_fs);
	}
		//load the records
	_fs.loadAllRecords();

	

	//Added support to do a custom search after the foundset is loaded.
	if (_methodToAddFoundsetFilters && forms[_returnForm][_methodToAddFoundsetFilters + 'AfterLoad']) {
		_fs = forms[_returnForm][_methodToAddFoundsetFilters + 'AfterLoad'](_fs);
	}

	//sort the records
	_fs.sort(globals.nav.program[_program].sort_value, false);

	return _fs;
}

/**
 * To show a record, or also create a record
 * @param {JSFoundSet} _foundset
 * @param {String} _program
 * @param {Boolean} [_newRecord] if you want a new record
 * @param {String} [_lookUpWindowForm] Name of the lookup window that called this dialog
 * @param {Boolean} [_editRecord] if you want to show the record in the edit mode
 * @param {String} [_useEditForm] if you want to force a different form to be used for editing
 * @param {String} [_afterInsertMethodName] if you want to run a method after insert
 * @param {String} [_returnForm] form used to run the afterInsertMethod
 * @param {JSWindow} [jsLookupWindow] Lookup window to hide/show
 *
 * @return
 * @properties={typeid:24,uuid:"29004725-ED66-47B5-8DC6-4F5624E54CE8"}
 */
function svy_nav_lkp_showRecord(_foundset, _program, _newRecord, _lookUpWindowForm, _editRecord, _useEditForm, _afterInsertMethodName, _returnForm, jsLookupWindow) {
	var _orgformname = 'svy_nav_fr_buttonbar_lookup_window';

	//developer wants to use own form
	if (forms['svy_nav_fr_buttonbar_lookup_window_custom']) {
		_orgformname = 'svy_nav_fr_buttonbar_lookup_window_custom';
	}

	// dashes in form name no longer supported therefore replace it with underscore
	var _formname = _orgformname + application.getUUID().toString().replace(/-/g, '_');
	
	scopes.svyUI.deepCopyJSForm(_formname,solutionModel.getForm(_orgformname));
	
	var _orgEditForm;
	if (_useEditForm) {
		_orgEditForm = _useEditForm;
	}
	else {
		// GD - Apr 23, 2014: Trapping for error; no program
		if (!globals.nav.program[_program] || globals.nav.program[_program] === undefined) return null;
		_orgEditForm = globals.nav.program[_program].form[0][2];
	}

	// dashes in form name no longer supported therefore replace it with underscore
	var _editForm = _orgEditForm + application.getUUID().toString().replace(/-/g, '_');

	var _jsForm = solutionModel.cloneForm(_editForm, solutionModel.getForm(_orgEditForm));

	if (globals.svy_nav_fr_solutionModelObject) {
		/** @type {{onDeleteAllRecordsCmd:String, onDeleteRecordCmd:String,
		 * 			onDrag:String, onDragOver:String, onDrop:String,
		 * 			onDuplicateRecordCmd:String, onElementFocusGained:String, onElementFocusLost:String,
		 * 			onHide:String,onLoad:String,onNewRecordCmd:String,onNextRecordCmd:String,onPreviousRecordCmd:String,onRecordEditStart:String,
		 * 			onOmitRecordCmd:String,onRecordEditStop:String,onRecordSelection:String,onResize:String,onSearchCmd:String,
		 * 			onShow:String,onShowAllRecordsCmd:String,onShowOmittedRecordsCmd:String,onSortCmd:String,onUnLoad:String, allowEdit:Number }} */
		var _params = globals.svy_nav_fr_solutionModelObject;
		if (_params.onDeleteAllRecordsCmd) _jsForm.onDeleteAllRecordsCmd = _jsForm.newMethod(_params.onDeleteAllRecordsCmd);
		if (_params.onDeleteRecordCmd) _jsForm.onDeleteRecordCmd = _jsForm.newMethod(_params.onDeleteRecordCmd);
		if (_params.onDrag) _jsForm.onDrag = _jsForm.newMethod(_params.onDrag);
		if (_params.onDragOver) _jsForm.onDragOver = _jsForm.newMethod(_params.onDragOver);
		if (_params.onDrop) _jsForm.onDrop = _jsForm.newMethod(_params.onDrop);
		if (_params.onDuplicateRecordCmd) _jsForm.onDuplicateRecordCmd = _jsForm.newMethod(_params.onDuplicateRecordCmd);
		if (_params.onElementFocusGained) _jsForm.onElementFocusGained = _jsForm.newMethod(_params.onElementFocusGained);
		if (_params.onElementFocusLost) _jsForm.onElementFocusLost = _jsForm.newMethod(_params.onElementFocusLost);
		if (_params.onHide) _jsForm.onHide = _jsForm.newMethod(_params.onHide);
		if (_params.onLoad) _jsForm.onLoad = _jsForm.newMethod(_params.onLoad);
		if (_params.onNewRecordCmd) _jsForm.onNewRecordCmd = _jsForm.newMethod(_params.onNewRecordCmd);
		if (_params.onNextRecordCmd) _jsForm.onNextRecordCmd = _jsForm.newMethod(_params.onNextRecordCmd);
		if (_params.onOmitRecordCmd) _jsForm.onOmitRecordCmd = _jsForm.newMethod(_params.onOmitRecordCmd);
		if (_params.onPreviousRecordCmd) _jsForm.onPreviousRecordCmd = _jsForm.newMethod(_params.onPreviousRecordCmd);
		if (_params.onRecordEditStart) _jsForm.onRecordEditStart = _jsForm.newMethod(_params.onRecordEditStart);
		if (_params.onRecordEditStop) _jsForm.onRecordEditStop = _jsForm.newMethod(_params.onRecordEditStop);
		if (_params.onRecordSelection) _jsForm.onRecordSelection = _jsForm.newMethod(_params.onRecordSelection);
		if (_params.onResize) _jsForm.onResize = _jsForm.newMethod(_params.onResize);
		if (_params.onSearchCmd) _jsForm.onSearchCmd = _jsForm.newMethod(_params.onSearchCmd);
		if (_params.onShow) _jsForm.onShow = _jsForm.newMethod(_params.onShow);
		if (_params.onShowAllRecordsCmd) _jsForm.onShowAllRecordsCmd = _jsForm.newMethod(_params.onShowAllRecordsCmd);
		if (_params.onShowOmittedRecordsCmd) _jsForm.onShowOmittedRecordsCmd = _jsForm.newMethod(_params.onShowOmittedRecordsCmd);
		if (_params.onSortCmd) _jsForm.onSortCmd = _jsForm.newMethod(_params.onSortCmd);
		if (_params.onUnLoad) _jsForm.onUnLoad = _jsForm.newMethod(_params.onUnLoad);
	}

	forms[_formname].elements['form_view_01'].addTab(forms[_editForm]);
	forms[_formname]['vLookupWindow'] = _lookUpWindowForm;
	forms[_formname]['_jsLookupWindow'] = jsLookupWindow;

	forms[_formname]['vMode'] = "browse";
	forms[_formname]['vProgram'] = _program;
	forms[_formname]['vReturnForm'] = _returnForm;
	forms[_formname]['vAfterInsertMethodName'] = _afterInsertMethodName;
	forms[_formname].updateUI()

	if (globals.svy_nav_fr_solutionModelObject && globals.svy_nav_fr_solutionModelObject.pk) {
		// Select the correct record
		if (!forms[_editForm].foundset.selectRecord(globals.svy_nav_fr_solutionModelObject.pk)) {
			//SL-7069 - Unable to load saved notes in shop floor. The pk value below is a UUID in string format which causes an exception
			//due to which saved notes aren't able to load
			try {
				forms[_editForm].foundset.loadRecords(globals.svy_nav_fr_solutionModelObject.pk);
			}
			catch (ex) {
				if (globals.svy_nav_fr_solutionModelObject.pk.toString().length == 36) {
					forms[_editForm].foundset.loadRecords(application.getUUID(globals.svy_nav_fr_solutionModelObject.pk.toString()));
				}
				else {
					globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.contactSupport'), i18n.getI18NMessage('avanti.dialog.loadRecordsError'),
						i18n.getI18NMessage('avanti.dialog.ok'));
				}
			}
		}
	}

	// GD - Apr 17, 2015: Throwing an error on the server because .update_mode from null is undefined
	// GD - 2013-06-20: We check for chart editor security elsewhere
	if (_program != "Chart_Editor" && globals.nav.program[_program]) forms[_formname].elements['btn_edit'].enabled = (globals.nav.program[_program].update_mode == 1) && security.canUpdate(forms[_editForm].controller.getDataSource());

	//TODO: TD Need some way to disallow edit on 'lookup' - this is the quick and dirty
	if (_params && _params.allowEdit == false) forms[_formname].elements['btn_edit'].enabled = false;

	forms[_editForm].controller.readOnly = true;

	if (_newRecord) {
		forms[_formname].dc_edit();
		forms[_formname]['vNew'] = 1;
		forms[_editForm].foundset.newRecord();

		if (globals.svy_nav_fr_solutionModelObject && globals.svy_nav_fr_solutionModelObject.fields && globals.svy_nav_fr_solutionModelObject.data && globals.svy_nav_fr_solutionModelObject.fields.length == globals.svy_nav_fr_solutionModelObject.data.length) {
			for (var i = 0; i < globals.svy_nav_fr_solutionModelObject.fields.length; i++) {
				forms[_editForm][globals.svy_nav_fr_solutionModelObject.fields[i]] = globals.svy_nav_fr_solutionModelObject.data[i];
			}
		}

		//run onPostNew-method of table when available
		forms[_editForm].dc_new_post(forms[_editForm].foundset);
	}
	else if (_editRecord) {
		forms[_formname].dc_edit();
	}

	if (globals.svy_nav_fr_solutionModelObject != null && globals.svy_nav_fr_solutionModelObject.fields != null) {
		//For the extra field added on Shop Floor. The edition mode of the lookup window doesn't load the extra information
		var nIndexAdditionalRelation1 = globals.svy_nav_fr_solutionModelObject.fields.indexOf('_additionalRelation1');
		if( nIndexAdditionalRelation1 !== -1 ) {
			forms[_editForm]['_additionalRelation1'] = globals.svy_nav_fr_solutionModelObject.data[nIndexAdditionalRelation1]; 
		}	
	}
		
	globals.DIALOGS.showFormInModalDialog(forms[_formname], -1, -1, solutionModel.getForm(_orgEditForm).width+40, scopes.svyUI.getJSFormHeight(_orgEditForm) + 80, globals.nav.program[_program].description,true,false,'show_'+globals.nav.program[_program].description);
	
	return _formname;
}
