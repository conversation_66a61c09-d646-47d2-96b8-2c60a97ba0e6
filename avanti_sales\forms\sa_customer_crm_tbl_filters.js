/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C9DD0DC9-11C7-416C-95B5-A54115AC91D1"}
 */
var currentView = globals.avSales_CRM_MyCustomers;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AA6BAF14-7633-4B95-A8E5-81D0A7DD176E"}
 */
var selectedRep = null;

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"6C20D019-28DB-48E9-B376-36E64DAB81BE"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	var result =  _super.onShowForm(firstShow, event);
	refreshUI(event);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * TODO generated, please specify type and doc for the params
 * @param readonly
 *
 * @properties={typeid:24,uuid:"946002E9-34FC-4AB6-B6C3-3CE5448B7D27"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"DEDA24C0-57E6-4A86-8D70-294A45F936CE"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	// Set globals and default view to not show managed sales reps.
	elements.managed_sales_reps.visible = false;
	elements.managed_sales_reps_lbl.visible = false;
	
	//var customer_fs = forms.sa_customer_crm_tbl.foundset.duplicateFoundSet()
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard") {
		
	}
	else {
		applyFilters(true);
	}
}

/**
 * @AllowToRunInFind
 * 
 * Apply foundset filters.
 * @param bReApplyFilters
 *
 * @properties={typeid:24,uuid:"5C715CA6-A7E1-469F-B85E-79A82AACB4B4"}
 */
function applyFilters(bReApplyFilters) {
	if (bReApplyFilters) {
		// Remove any filters.
		forms.sa_customer_crm_tbl.foundset.removeFoundSetFilterParam('employeeFilter');
		
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee');
		if (employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID;
			
			if (employee_fs.search() > 0) {
				if (utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
						// If a sales rep, filter by salesper_id
						case globals.avSales_CRM_SalesRep:
							if (utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)) {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '=', employee_fs.sys_employee_to_sa_sales_person.salesper_id, 'employeeFilter');
							}
							else {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '=', scopes.avUtils.ENUM_DEFAULT_GUID.Default, 'employeeFilter');
							}
							break;
						// If a sales manager, filter by salesper_id and add the 'Sales Manager' dropdown and make the managed sales rep drop down visible
						case globals.avSales_CRM_SalesManager:
							if (currentView == globals.avSales_CRM_AllCustomers) {
								elements.managed_sales_reps.visible = true;
								elements.managed_sales_reps_lbl.visible = true;
							}
							
							if (currentView == globals.avSales_CRM_AllCustomers && selectedRep != null && selectedRep != '') {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '=', selectedRep, 'employeeFilter');
							}
							else if (currentView == globals.avSales_CRM_AllCustomers && selectedRep == null) {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '^||IN', "select salesper_id from sys_employee_managed_reps where empl_id = '" + globals.avBase_employeeUUID + "'", 'employeeFilter');
							}
							else if (currentView == globals.avSales_CRM_MyCustomers && utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '=', employee_fs.sys_employee_to_sa_sales_person.salesper_id, 'employeeFilter');
							}
							else {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id', '=', scopes.avUtils.ENUM_DEFAULT_GUID.Default, 'employeeFilter');
							}
							break;
						case globals.avSales_CRM_CustomerServiceRep:
							forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('cust_csr_empl_id','=',globals.avBase_employeeUUID,'employeeFilter');
							break;
						// If an admin
						case globals.avSales_CRM_Administrator:
							if (currentView == globals.avSales_CRM_AllCustomers) {
								elements.managed_sales_reps.visible = true;
								elements.managed_sales_reps_lbl.visible = true;
							}
							if (selectedRep != null && selectedRep != '') {
								forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id','=',selectedRep,'employeeFilter');
							}
							break;
						default:
							// Filter by invalid uuid.
							forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter');
							break;
					}
				} else {
					// Filter by invalid uuid.
					forms.sa_customer_crm_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter');
				}
			}
		}
		forms.sa_customer_crm_tbl.foundset.loadAllRecords();
		forms.sa_customer_crm_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.sa_customer_crm_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		// Reload the table and run initial sort on it.
		//forms.sa_customer_crm_tbl.foundset.loadRecords(customer_fs)
	}
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"1DE7264C-2818-4C49-A6FA-E2F6207C7A31"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView != "Dashboard") {
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee');
		if (employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID;
			
			if (employee_fs.search() > 0) {
				if (utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					if (employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_SalesManager
				        || employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_Administrator) {
						currentView = globals.avSales_CRM_AllCustomers;
					}
				}
			}
		}
	}
	
	return _super.onLoad(event);
}
