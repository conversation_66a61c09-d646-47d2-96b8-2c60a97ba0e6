/**
 * Validates if bin allocations match shipped quantities for a pack detail record
 *
 * @return {Number} Returns 1 if bin allocations are invalid, 0 if valid
 * @properties={type:12,typeid:36,uuid:"A4CDD51B-C4C5-481E-81FF-25A45D02555B"}
 */
function clc_onRender_verifyBin() {
	var hasAllocationMismatch = false;
	if (!utils.hasRecords(sa_pack_detail_to_sa_pack) || !utils.hasRecords(sa_pack_detail_to_sa_order_revision_detail)) {
		return null;
	}

	// Only validate for open status unless in developer mode
	if (!application.isInDeveloper() && sa_pack_detail_to_sa_pack.pack_status != "O") {
		return null;
	}

	var rLineItem = sa_pack_detail_to_sa_order_revision_detail.getRecord(1);
	var nShippedStockQty = scopes.avShipping.getPackDetailStockQtyShippedFromSellQty(this);

	// BTO Kit validation
	if (scopes.avDetail.isABTOKit(rLineItem)) {
		var rKit = rLineItem.sa_order_revision_detail_to_in_item.getRecord(1);

		for (var j = 1; j <= rLineItem.sa_order_revision_detail_to_sa_order_revision_detail$children.getSize(); j++) {
			var rKitItem = rLineItem.sa_order_revision_detail_to_sa_order_revision_detail$children.getRecord(j);

			if (!utils.hasRecords(rKitItem.sa_order_revision_detail_to_in_warehouse) || !utils.hasRecords(rKitItem.sa_order_revision_detail_to_in_item)) {
				continue;
			}

			var rWare = rKitItem.sa_order_revision_detail_to_in_warehouse.getRecord(1);
			var rItem = rKitItem.sa_order_revision_detail_to_in_item.getRecord(1);

			// Only validate items that use bins
			if (rItem.itemtype_code != 'SE' && rItem.itemtype_code != 'P' && rWare.whse_enable_bin_locations == 1 && rItem.item_no_bin_location != 1) {

				var rBOM = scopes.avInv.getBillOfMaterialItem(rKit, rKitItem.item_id);
				var nQtyFactor = rBOM ? rBOM.itembom_qty_per_unit : 1;
				var nQtyAlloc = scopes.avShipping.getTotalBinAllocationInPackSlip(this, rKitItem.ordrevd_id, null, rItem.item_id) / nQtyFactor;

				if (packd_qty_shipped != nQtyAlloc) {
					hasAllocationMismatch = true; // Invalid allocation
					break;
				}
			}
		}
	}
	// Regular item line validation
	else if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_in_item) && utils.hasRecords(rLineItem.sa_order_revision_detail_to_in_warehouse)) {

		var itemType = rLineItem.sa_order_revision_detail_to_in_item.itemtype_code;
		var warehouse = rLineItem.sa_order_revision_detail_to_in_warehouse;

		// Check regular items that use bins
		if (itemType != "SE" && itemType != "P" && warehouse.whse_enable_bin_locations == 1 && rLineItem.sa_order_revision_detail_to_in_item.item_no_bin_location != 1) {

			if (nShippedStockQty != scopes.avShipping.getTotalBinAllocationInPackSlip(this, null, null, rLineItem.item_id)) {
				hasAllocationMismatch = true; // Invalid allocation
			}
		}
		// Check postage items
		else if (warehouse.whse_enable_bin_locations == 1) {
			var fsPostItems = scopes.avDetail.getPostageItems(rLineItem, true);

			if (fsPostItems) {
				for (var i = 1; i <= fsPostItems.getSize(); i++) {
					var rPostItem = fsPostItems.getRecord(i);
					var nExpectedAlloc = rPostItem.ordrevdstaskpost_qty * (packd_qty_shipped / rLineItem.ordrevd_qty_ordered);
					nQtyAlloc = scopes.avShipping.getTotalBinAllocationInPackSlip(this, null, rPostItem.ordrevdstaskpostitem_id, rPostItem.item_id);

					if (nQtyAlloc != nExpectedAlloc) {
						hasAllocationMismatch = true; // Invalid allocation
						break;
					}
				}
			}
		}
	}

	return hasAllocationMismatch ? 'bold' : null;
}

/**
 * @properties={type:12,typeid:36,uuid:"F7F63E71-8F89-4591-8503-897306E17424"}
 */
function clc_line_code() {
	var sCode = "";
	
	if (utils.hasRecords(sa_pack_detail_to_sa_order_revision_detail)) {
		var rLineItem = sa_pack_detail_to_sa_order_revision_detail.getRecord(1);
		
		if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_in_item)) {
			sCode = rLineItem.sa_order_revision_detail_to_in_item.item_code;
		}
		else if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_sa_task_worktype)) {
			sCode = rLineItem.sa_order_revision_detail_to_sa_task_worktype.worktype_code;
		}
	}
	
	return sCode;
}

/**
 * @properties={type:8,typeid:36,uuid:"CCF40423-EE29-4608-9DB0-9979EED4B626"}
 */
function clc_remaining_qty_allow_over()
{
	return sa_pack_detail_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_qty + (sa_pack_detail_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_qty * sa_pack_detail_to_sa_ship_detail$ordrevd_id.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold) - packd_qty_shipped_ttd; 
}

/**
 * @properties={type:8,typeid:36,uuid:"01B12255-9235-4D58-8B03-5E1E1D154B3C"}
 */
function clc_remaining_qty_allow_under()
{
	if(utils.hasRecords(sa_pack_detail_to_sa_ship_detail) 
		&& utils.hasRecords(sa_pack_detail_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty)
		&& utils.hasRecords(sa_pack_detail_to_sa_ship_detail$ordrevd_id)
		&& utils.hasRecords(sa_pack_detail_to_sa_ship_detail$ordrevd_id.sa_ship_detail_to_sa_order_revision_detail)){
			
		return sa_pack_detail_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_qty - 
			(sa_pack_detail_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_qty * 
			sa_pack_detail_to_sa_ship_detail$ordrevd_id.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold) - 
			packd_qty_shipped_ttd;
	}
	else{
		return 0;
	}
}

/**
 * @properties={type:8,typeid:36,uuid:"C537AB0A-E7B5-4EE7-AF3C-DFED9AF1A211"}
 */
function packd_qty_shipped_ttd()
{
	var ttd_shipped = 0;
	var current_release = 0;
	
	/** @type {UUID} */
	var current_ship_location = null;
	
    if (utils.hasRecords(sa_pack_detail_to_sa_ship_detail)) {
        current_release = sa_pack_detail_to_sa_ship_detail.ship_revision;
        current_ship_location = sa_pack_detail_to_sa_ship_detail.ordrevdms_id;
    }
	
	//Get total shipped from the previous shipments
	var fsShipDetail = sa_pack_detail_to_sa_ship_detail$ordrevd_id;
	
    if (fsShipDetail != null) {
        fsShipDetail.sort("ship_revision asc");

        for (var i = 1; i <= fsShipDetail.getSize(); i++) {
            var rShipDetail = fsShipDetail.getRecord(i);

            if (utils.hasRecords(rShipDetail.sa_ship_detail_to_sa_pack_detail)) {
                if (rShipDetail.ship_revision < current_release && rShipDetail.ordrevdms_id == current_ship_location 
                        && ( rShipDetail.sa_ship_detail_to_sa_pack_detail.packd_shipment_type == null 
                                || rShipDetail.sa_ship_detail_to_sa_pack_detail.packd_shipment_type == scopes.avUtils.ENUM_SHIPMENT_TYPE.Standard)) {
                    ttd_shipped += rShipDetail.shipd_qty_shipped;
                }
            }
        }
        
        //If this is an inter-branch order line then we have to also include any branch order shipments.
        if (utils.hasRecords(fsShipDetail.sa_ship_detail_to_sa_order_revision_detail) 
                && scopes.avDetail.isOrderDetailInterbranchProjectPlan(fsShipDetail.sa_ship_detail_to_sa_order_revision_detail.getRecord(1))) {
            var rOrderDetail = fsShipDetail.sa_ship_detail_to_sa_order_revision_detail.getRecord(1);

            for (var j = 1; j <= rOrderDetail.sa_order_revision_detail_to_sa_order_revd_plant_qty.getSize(); j++) {
                var rOrderDetailPlantQty = rOrderDetail.sa_order_revision_detail_to_sa_order_revd_plant_qty.getRecord(j);
                
                if (utils.hasRecords(rOrderDetailPlantQty.sa_order_revd_plant_qty_to_sa_order_revision_detail$plan_order_line) 
                        && utils.hasRecords(rOrderDetailPlantQty.sa_order_revd_plant_qty_to_sa_order_revision_detail$plan_order_line.sa_order_revision_detail_to_sa_ship_detail)) {
                    var fsShipDetail2 = rOrderDetailPlantQty.sa_order_revd_plant_qty_to_sa_order_revision_detail$plan_order_line.sa_order_revision_detail_to_sa_ship_detail;
                    fsShipDetail2.sort("ship_revision asc");
                    var rOrderDetail2 = rOrderDetailPlantQty.sa_order_revd_plant_qty_to_sa_order_revision_detail$plan_order_line.getRecord(1);
                    
                    for (i = 1; i <= fsShipDetail2.getSize(); i++) {
                        var rShipDetail2 = fsShipDetail2.getRecord(i);

                        if (utils.hasRecords(rShipDetail2.sa_ship_detail_to_sa_pack_detail) 
                                && utils.hasRecords(rShipDetail2.sa_ship_detail_to_sa_order_revd_multi_ship_qty) 
                                && utils.hasRecords(rShipDetail2.sa_ship_detail_to_sa_pack_detail.sa_pack_detail_to_sa_pack)) {
                            var rPack = rShipDetail2.sa_ship_detail_to_sa_pack_detail.sa_pack_detail_to_sa_pack.getRecord(1);
                            if (rShipDetail2.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_pp_ordrevdms_id == fsShipDetail.ordrevdms_id 
                                    && ((rShipDetail2.sa_ship_detail_to_sa_pack_detail.packd_shipment_type == null 
                                            || rShipDetail2.sa_ship_detail_to_sa_pack_detail.packd_shipment_type == scopes.avUtils.ENUM_SHIPMENT_TYPE.Standard)) 
                                            && !scopes.avDetail.isShippingBackToSellingPlant(rPack, rOrderDetail2)) {
                                ttd_shipped += rShipDetail2.shipd_qty_shipped;
                            }
                        }
                    }
                }
            }
        }
    }
	
	return ttd_shipped;
}

/**
 * @properties={type:8,typeid:36,uuid:"5BF51806-9314-4F10-B71B-D8DC756A31DB"}
 */
function packd_total_bin_qty()
{
var iCount = 0;
	
	for (var i = 1; i <= sa_pack_detail_to_sa_pack_detail_bin.getSize(); i++)
	{
		var rPackBin = sa_pack_detail_to_sa_pack_detail_bin.getRecord(i);
		iCount += rPackBin.packdb_qty;
	}
	
	return iCount;
}

/**
 * @properties={type:4,typeid:36,uuid:"4449EE8A-285B-49EB-B179-AA04F6D08B46"}
 */
function packd_total_packages()
{
	var iCount = 0;
	
	for (var i = 1; i <= sa_pack_detail_to_sa_pack_detail_pkg.getSize(); i++)
	{
		var rPackBox = sa_pack_detail_to_sa_pack_detail_pkg.getRecord(i);
		iCount += rPackBox.packdp_total_pkgs;
	}
	
	return iCount;
}
