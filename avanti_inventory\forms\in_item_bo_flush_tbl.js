/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"88DFE646-DD28-4DC7-8C00-A797E412BA1C",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"594BC101-58DF-4B90-AF8F-3A9B2B6C17BA"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"DB9418F1-FCA3-42D9-AFF2-101C6C5CFA9F"}
 */
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.DueAtCustomerFormatInSalesOrders) == "DATETIME") {
		elements.grid.getColumn(elements.grid.getColumnIndex("ordrevh_promise_date")).format = globals.avBase_dateTimeFormat;
	} 
	else {
		elements.grid.getColumn(elements.grid.getColumnIndex("ordrevh_promise_date")).format = globals.avBase_dateFormat;
	}
	
	elements.grid.getColumn(elements.grid.getColumnIndex("ordh_order_date")).format = globals.avBase_dateFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("podet_expected_date")).format  = globals.avBase_dateFormat;
	
    foundset.clear();
    controller.readOnly = false;
    controller.enabled = true;
    _totalSelected = 0;
    elements.grid.setReadOnly(false, ["ordrevditem_selected"]);
    elements.grid.setReadOnly(false, ["ordrevditem_selected_res"]);
    showBackorderSelectionsDialog(_event);
    
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"916BC4AB-C809-43FA-8A79-3CB1D504DE12"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "ordrevditem_selected" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChangeCheckOrder(oldValue, newValue, event);
	}
	if (col.id == "ordrevditem_selected_res" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChangeCheckOrder(oldValue, newValue, event);
	}
}

/**
 * @properties={typeid:35,uuid:"D385A6A0-9041-4EEC-9E10-01B82D44C3DA",variableType:-4}
 */
var _bReserved = false;

/**
 * @type {UUID}
 * @properties={typeid:35,uuid:"0797756A-2766-4871-BEB3-4C8A7FF76453",variableType:-4}
 */
var _sPrevId = null;

/**
 * @type {Array<{UUID,Number}>}
 * @properties={typeid:35,uuid:"C567EEA4-F9FB-4F6D-B852-9F2F5E1B6B13",variableType:-4}
 */
var oDocNum = [];

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"DB4F511E-42D6-4B3D-92F7-57FD452F8589",variableType:8}
 */
var _nTotal = 0.00;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"652AE44F-8E97-4CC6-B91A-E68E0861CB3F",variableType:8}
 */
var _nTotalFlushable = 0.00;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"8DBAED02-B8B4-4D94-BF28-10D36EAD051E",variableType:8}
 */
var _nTotalAllocated = 0.00;

/**
 * @type {JSFoundSet<db:/avanti/sa_order_revd_item>}
 * @properties={typeid:35,uuid:"BC6AC0C3-58CD-40A6-84BF-25243DBDDE0C",variableType:-4}
 */
var _foundset = null;

/**
 * @type {Array<{UUID,Number}>}
 *
 * @properties={typeid:35,uuid:"7EF6FBC3-47AB-4B5C-8EBF-9D98590D446C",variableType:-4}
 */
var mOldValMap = [];

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"887CC955-2CAD-4DB6-931C-644D9384692F",variableType:4}
 */
var _totalSelected = 0;

/** 
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"70D4F60C-FF5F-4C7C-B66B-3B63ABBC7103"}
 */
function onRecordSelection(_event, _form) {
    
    _super.onRecordSelection(_event, _form);
    
    forms.in_item_bo_flush_detail.controller.loadRecords(foundset);

    if (utils.hasRecords(foundset.sa_order_revd_item_to_sa_order_revision_detail$item_id)) {
        forms.in_item_bo_flush_detail._qty_used = foundset.sa_order_revd_item_to_sa_order_revision_detail$item_id.ordrevd_qty_shipped;
    }
    else {
        forms.in_item_bo_flush_detail._qty_used = ordrevditem_shippedqty_uomstk;
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"E04F4EB2-234A-4276-A2C0-AEB42C42358B"}
 */
function onAction_btnSuppliers(event) {
    if (!utils.hasRecords(foundset)) {
        return;
    }

    // Load the in_item_dtl form with the in_item record needed
    forms["in_item_supplier_dtl"].controller.loadRecords(foundset.sa_order_revd_item_to_in_item.in_item_to_in_item_supplier);

    globals.avBase_callback_afterItemSupplierLookupWindowLoad = "in_item_bo_flush_tbl";

    globals.DIALOGS.showFormInModalDialog(forms['in_item_supplier_dtl'], -1, -1, 1000, 600, "Supplier Details for Item: " + sa_order_revd_item_to_in_item.item_code + ' - ' + sa_order_revd_item_to_in_item.item_desc1, true, false, "dlgItemBOFlushItemSupplier", true);
    globals.avBase_callback_afterItemSupplierLookupWindowLoad = null;
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} _fs
 * @return {JSFoundSet<db:/avanti/in_item_supplier>}
 *
 *
 *
 * @properties={typeid:24,uuid:"06A58E07-5735-4B53-847D-C3E20063B16C"}
 */
function addLookupFilters(_fs)
{
	_fs.addFoundSetFilterParam('item_id','=',item_id,'LookupFilter')
	return _fs;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"4AF0C358-B6E6-4371-B871-95AC093A5300"}
 */
function onActionBtnWarehouse(event) 
{
	if (utils.hasRecords(foundset) && utils.hasRecords(foundset.sa_order_revd_item_to_in_item.in_item_to_in_item_warehouse))
	{
	    forms["in_item_warehousetab_tbl"].controller.loadRecords(foundset.sa_order_revd_item_to_in_item.in_item_to_in_item_warehouse);
	    forms['in_item_warehousetab_tbl'].elements["btnAdd"].visible = false;
		globals.DIALOGS.showFormInModalDialog(forms['in_item_warehousetab_tbl'], -1, -1, 1280, 343, "Warehouse Details for Item: " + sa_order_revd_item_to_in_item.item_code + ' - ' + sa_order_revd_item_to_in_item.item_desc1, true, false, "dlgItemBOFlushItemWarehouse", true);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"42744AF9-51DC-404C-8621-EA456DF23E65"}
 */
function onActionDisplayFilters(event) {
    _totalSelected = 0;
    showBackorderSelectionsDialog(event);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"562C7606-F98C-44E4-9CA9-39BFFF6EA573"}
 */
function onDataChangeCheckOrder(oldValue, newValue, event) {
    if (newValue == 0 && oldValue ==1) {
        _nTotalAllocated -= ordrevditem_flushqty_uomstk;
        mOldValMap[ordrevditem_id] = ordrevditem_flush_qty;
        ordrevditem_flush_qty = 0;
        _totalSelected--;
    }
    else if(newValue ==1){
        if(mOldValMap[ordrevditem_id] !=null){
            ordrevditem_flush_qty = mOldValMap[ordrevditem_id];
            _nTotalAllocated += ordrevditem_flushqty_uomstk;
            mOldValMap[ordrevditem_id] = null;
        }
        _totalSelected++;
    }
    
    databaseManager.saveData(foundset.getSelectedRecord());
    
    return true;
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"7143E91B-4491-4F19-9111-DFCFA264A216"}
 */
function onRender(event) {

    /** @type {JSRecord<db:/avanti/sa_order_revd_item>}*/
    var record = event.getRecord(),
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} */
        dsData;
        
    if (!record) {
        return;
    }
    
    if (_sPrevId == record.ordrevditem_id) {
        return;
    }
    _sPrevId = record.ordrevditem_id;

    var bAllowFlush = false;
    
    // _bReserved uses opposite logic of bo
	if (_bReserved) {
		bAllowFlush = record.ordrevditem_flushqty_uomstk < 0 || (_nTotalAllocated > _nTotalFlushable && mOldValMap[record.ordrevditem_id] != null); 
	}
	else {
		bAllowFlush = record.ordrevditem_flushqty_uomstk > 0 || (_nTotalAllocated < _nTotalFlushable && mOldValMap[record.ordrevditem_id] != null); 
	}
    
    if (bAllowFlush) {
        event.getRenderable().enabled = true;
    }
    else {
        event.getRenderable().enabled = false;
    }
    
    if (oDocNum[record.ordrevditem_id] != null) {
        
        record.ordrevditem_selected = 0;
        event.getRenderable().enabled = false;
    }
    else if (!oDocNum[record.ordrevditem_id]) {

        oSQL.sql = "SELECT po.po_document_number \
                    FROM \
                    po_purchase_detail_qty pdq \
                    INNER JOIN po_purchase_detail pd ON pdq.podet_id = pd.podet_id \
                    INNER JOIN po_purchase po ON pd.po_id = po.po_id \
                    WHERE \
                    pdq.item_id = ? AND pdq.ordrevd_id = ?";
        oSQL.args = [record.item_id.toString(), record.ordrevd_id.toString()];
        dsData = globals.avUtilities_sqlDataset(oSQL);
        
        if (dsData && dsData.getValue(1,1)) {
            
            oDocNum[record.ordrevditem_id] = dsData.getValue(1,1);
            record.ordrevditem_selected = 0;
            event.getRenderable().enabled = false;
        }
        else {
            oDocNum[record.ordrevditem_id] = null;
        }
    }
}

/**
 * @properties={typeid:24,uuid:"C409539D-1BAC-409D-A976-73F29ACA7E7D"}
 */
function allocateAvailableQty() {
    /***@type {Number}*/
    var nAvailableQty = 0.00;
    _nTotalFlushable = 0.00;
    _nTotalAllocated = 0.00;
    _totalSelected = 0;
    /***@type {Array<{String,Number}>}*/
    var mQtyWhseMap = [];
    /***@type {Array<{String,Number}>}*/
    var mQtyWhseProjectMap = [];
    
    for (var i = 1; i <= foundset.getSize(); i++) {
        var rRevDetailItem = foundset.getRecord(i);
        var bUseProjectInventory = scopes.avInv.doesItemUseProjectInventory(rRevDetailItem.item_id);
        var uOrderID = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordh_id;
        var sProject = scopes.avSales.getOrderProject(null, uOrderID);
        var sKey = null;

        if (!rRevDetailItem.item_id 
            || ( utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_po_purchase_detail_qty) 
                && utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_purchase_detail) 
                && utils.hasRecords(rRevDetailItem.sa_order_revd_item_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_purchase_detail.po_purchase_detail_to_po_purchase) 
                && rRevDetailItem.sa_order_revd_item_to_po_purchase_detail_qty.po_purchase_detail_qty_to_po_purchase_detail.po_purchase_detail_to_po_purchase.po_document_number )) {
            rRevDetailItem.ordrevditem_selected = 0;
            rRevDetailItem.ordrevditem_flush_qty = 0;
            continue;
        }

		if (sProject) {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id + sProject;
			
			if (mQtyWhseProjectMap[sKey] != null) {
				nAvailableQty = mQtyWhseProjectMap[sKey];
			}
			else {
				nAvailableQty = scopes.avInv.getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id, sProject);
				mQtyWhseProjectMap[sKey] = nAvailableQty;
				_nTotalFlushable += nAvailableQty;
			}
		}
		else {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id;
			
			if (mQtyWhseMap[sKey] != null) {
				nAvailableQty = mQtyWhseMap[sKey];
			}
	        // if bUseProjectInventory on then need to call getFlushQtyForWhseProject without project to get non-project qty
	        else if (bUseProjectInventory) {
	                nAvailableQty = scopes.avInv.getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id);
	        }
			else {
				nAvailableQty = getFlushQtyForWhse(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id.toString());
				mQtyWhseMap[sKey] = nAvailableQty;
				_nTotalFlushable += nAvailableQty;
			}
		}
        
        if (nAvailableQty <= 0) {
            rRevDetailItem.ordrevditem_flush_qty = 0;
            rRevDetailItem.ordrevditem_selected = 0;
            continue;
        }

        var nBackOrdQty_UomStk = rRevDetailItem.ordrevditem_backordqty_uomstk;
        if (nBackOrdQty_UomStk <= nAvailableQty) {
            rRevDetailItem.ordrevditem_flush_qty = rRevDetailItem.ordrevditem_backorder_qty;
            nAvailableQty -= nBackOrdQty_UomStk;
            rRevDetailItem.ordrevditem_selected = 1;
            _totalSelected++;
        }
        else {
            var bFloor = false;
            if (rRevDetailItem.sa_order_revd_item_to_in_item.item_decimal_places == 0) {
                bFloor = true;
            }
            rRevDetailItem.ordrevditem_selected = 0;
            var nFlushQty = globals['avCalcs_UnitOfMeasurementConversion'](rRevDetailItem.item_id, rRevDetailItem.ordrevditem_est_uom_id, rRevDetailItem.ordrevditem_stock_uom_id, nAvailableQty, 'SS')["nOutputValue"];
            if (bFloor) {
                nFlushQty = Math.floor(nFlushQty);
            }
            rRevDetailItem.ordrevditem_flush_qty = nFlushQty;
            nAvailableQty -= rRevDetailItem.ordrevditem_flushqty_uomstk;
        }

        _nTotalAllocated += rRevDetailItem.ordrevditem_flush_qty;

		if (sProject) {
			mQtyWhseProjectMap[sKey] = nAvailableQty;
		}
		else {
	        mQtyWhseMap[sKey] = nAvailableQty;
		}
    }
    
    mOldValMap = [];
}

/**
 * @public 
 * 
 * This does the flush reserved
 * 
 * @param {JSFoundSet<db:/avanti/sa_order_revd_item>} [fsRevdItem] - if called from in_reserved_dlg then it passes in the foundset to use 
 * 
 * @properties={typeid:24,uuid:"265B7874-C5DF-452A-9F01-1313412219E5"}
 */
function allocateDeficitQty(fsRevdItem) {
	if (fsRevdItem) {
	    mOldValMap = forms.in_reserved_dlg.mOldValMap;
	}
	else {
		fsRevdItem = foundset;
	}
	
    /***@type {Number}*/
    var nAvailableQty = 0.00;
    
    _nTotalFlushable = 0.00;
    _nTotalAllocated = 0.00;
    _totalSelected = 0;
    
    /***@type {Array<{String,Number}>}*/
    var mQtyWhseMap = [];
    /***@type {Array<{String,Number}>}*/
    var mQtyWhseProjectMap = [];
    
    for (var i = 1; i <= fsRevdItem.getSize(); i++) {
        var rRevDetailItem = fsRevdItem.getRecord(i);
        var bUseProjectInventory = scopes.avInv.doesItemUseProjectInventory(rRevDetailItem.item_id);
        var uOrderID = rRevDetailItem.sa_order_revd_item_to_sa_order_revision_header.ordh_id;
        var sProject = scopes.avSales.getOrderProject(null, uOrderID);
        var sKey = null;

        if (!rRevDetailItem.item_id) {
            rRevDetailItem.ordrevditem_selected = 0;
            rRevDetailItem.ordrevditem_flush_qty = 0;
            continue;
        }

		if (sProject) {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id + sProject;
			
			if (mQtyWhseProjectMap[sKey] != null) {
				nAvailableQty = mQtyWhseProjectMap[sKey];
			}
			else {
				nAvailableQty = scopes.avInv.getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id, sProject);
				mQtyWhseProjectMap[sKey] = nAvailableQty;
				_nTotalFlushable += nAvailableQty;
			}
		}
		else {
			sKey = rRevDetailItem.ordrevditem_whse_id + rRevDetailItem.item_id;
			
			if (mQtyWhseMap[sKey] != null) {
				nAvailableQty = mQtyWhseMap[sKey];
			}
	        // if bUseProjectInventory on then need to call getFlushQtyForWhseProject without project to get non-project qty
	        else if (bUseProjectInventory) {
                nAvailableQty = scopes.avInv.getFlushQtyForWhseProject(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id);
	        }
			else {
				nAvailableQty = getFlushQtyForWhse(rRevDetailItem.ordrevditem_whse_id, rRevDetailItem.item_id.toString());
				mQtyWhseMap[sKey] = nAvailableQty;
				_nTotalFlushable += nAvailableQty;
			}
		}
        
		if (nAvailableQty < 0) {
			var nDeficitQty = Math.abs(nAvailableQty);
	        var nReservedQty_UomStk = rRevDetailItem.ordrevditem_reservedqty_uomstk;
	        
	        if (nReservedQty_UomStk <= nDeficitQty) {
	            rRevDetailItem.ordrevditem_flush_qty = rRevDetailItem.ordrevditem_reserved_qty * -1;
	            nAvailableQty += nReservedQty_UomStk;
	            rRevDetailItem.ordrevditem_selected = 1;
	            _totalSelected++;
	        }
	        else {
	            var bFloor = false;
	            if (rRevDetailItem.sa_order_revd_item_to_in_item.item_decimal_places == 0) {
	                bFloor = true;
	            }
	            rRevDetailItem.ordrevditem_selected = 0;
	            var nFlushQty = globals['avCalcs_UnitOfMeasurementConversion'](rRevDetailItem.item_id, rRevDetailItem.ordrevditem_est_uom_id, rRevDetailItem.ordrevditem_stock_uom_id, nDeficitQty, 'SS')["nOutputValue"];
	            if (bFloor) {
	                nFlushQty = Math.floor(nFlushQty);
	            }
	            rRevDetailItem.ordrevditem_flush_qty = nFlushQty * -1;
	            nAvailableQty -= rRevDetailItem.ordrevditem_flushqty_uomstk;
	        }

	        _nTotalAllocated += rRevDetailItem.ordrevditem_flush_qty;
		}
		else {
            rRevDetailItem.ordrevditem_flush_qty = 0;
            rRevDetailItem.ordrevditem_selected = 0;
		}
		
		if (sProject) {
			mQtyWhseProjectMap[sKey] = nAvailableQty;
		}
		else {
	        mQtyWhseMap[sKey] = nAvailableQty;
		}
    }
    
    mOldValMap = [];
    
	if (fsRevdItem) {
		forms.in_reserved_dlg._nTotalFlushable = _nTotalFlushable;
		forms.in_reserved_dlg._nTotalAllocated = _nTotalAllocated;
	    forms.in_reserved_dlg.mOldValMap = mOldValMap;
	}
}

/**
 * @param {String} whse_id
 * @param {String} itemId
 * @return {Number}
 *
 *
 * @properties={typeid:24,uuid:"D90765A8-7451-484E-A909-590A1FF2E1CE"}
 */
function getFlushQtyForWhse(whse_id, itemId){
    if (whse_id && itemId) {
        var sql = "SELECT itemwhse_onhand_qty-itemwhse_reserved_qty-itemwhse_unavailable_qty \
                   FROM in_item_warehouse WHERE item_id = ? AND whse_id = ? AND org_id = ?"
        var args = [itemId,whse_id.toString(), scopes.globals.org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sql, args, 1);

        if (ds && ds.getMaxRowIndex() > 0) {
            return ds.getValue(1, 1);
        }
    }
    return null;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"F73C498C-25A9-4102-8245-33059A7F7D40"}
 */
function onActionFlushBackorders(event) {
	if (_bReserved) {
		flushReserved();
	} 
	else {
		flushBackorders();
	}
}

/**
 * @properties={typeid:24,uuid:"33204E8C-CA66-4393-87F5-59A1C1CB9A5F"}
 */
function flushBackorders() {
    var result = scopes.avInv.flushBackorder(foundset);
    var msg = i18n.getI18NMessage("avanti.dialog.backorderFlush_msg", [result.nFlushed]);
    
    if (result.nSkipped) {
        msg += "\n" + i18n.getI18NMessage("avanti.dialog.backorderFlushSkipped_msg", [result.nSkipped]);
    }

    globals.DIALOGS.showInfoDialog("Notification", msg, i18n.getI18NMessage("avanti.dialog.ok"));
    
    showBackorderSelectionsDialog();
}

/**
 * @properties={typeid:24,uuid:"52A99F06-C694-40F0-8F45-5BD0F6ED416E"}
 */
function flushReserved() {
    var result = scopes.avInv.flushReserved(foundset);    
    var msg = i18n.getI18NMessage("avanti.dialog.reservedFlush_msg", [result.nFlushed]);
    
    if (result.nSkipped) {
        msg += "\n" + i18n.getI18NMessage("avanti.dialog.reservedFlushSkipped_msg", [result.nSkipped]);
    }

    globals.DIALOGS.showInfoDialog("Notification", msg, i18n.getI18NMessage("avanti.dialog.ok"));
    
    showBackorderSelectionsDialog();
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F310AABF-7CAB-4737-9D27-5F4A6F0C7085"}
 */
function onAction_reallocate(event) {
	if (_bReserved) {
		allocateDeficitQty();
	}
	else {
		allocateAvailableQty();
	}
}
