customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
},\
onSortCmdMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/prod_job_cost_labour",
extendsID:"DCCB9297-4A06-4471-9BDB-AD39E7945F1C",
items:[
{
cssPosition:"66,-1,-1,599,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"599",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeWedHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn3",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"07E74AF9-418B-48AD-88FE-B71B650934AA"
},
{
cssPosition:"355,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"355",
width:"375"
},
dataProviderID:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.ordrevds_description",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_6706BDA9",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"0EE0F543-84FF-4BE5-95FF-DA8975FE504C"
},
{
cssPosition:"37,-1,-1,170,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"170",
right:"-1",
top:"37",
width:"22"
},
enabled:true,
onActionMethodID:"5B27750A-AECD-4B8D-BDE5-780AB20A22A5",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_calendarForward%%",
toolTipText:"i18n:avanti.lbl.forwardOneWeek",
visible:true
},
name:"btnLookupcc",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"10C6E798-73DE-4470-ADC0-322DE20E3F49"
},
{
cssPosition:"65,-1,-1,10,115,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"10",
right:"-1",
top:"65",
width:"115"
},
dataProviderID:"_timeWeekStart",
enabled:true,
onDataChangeMethodID:"B4CE19AD-D512-4D82-B126-129B83D9AF20",
selectOnEnter:true,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"fldWeekStart",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"12A59388-6712-42E3-9FAA-6B56CBC5D1DB"
},
{
cssPosition:"10,-1,-1,874,125,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"874",
right:"-1",
top:"10",
width:"125"
},
enabled:true,
onActionMethodID:"EC64DE40-AF05-4E77-9F57-82724F213B73",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.confirmEntries",
visible:true
},
name:"btnPost",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"1B886BD0-02D9-4DAD-97AF-0C1D91E17930"
},
{
anchors:15,
cssPosition:"165,32,186,0px,1256,84",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.#",
id:"fldNr",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"E2C2368E-990A-49ED-920C-C8FEB571C052",
valuelist:null,
visible:true,
width:30
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.empl_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.employee",
id:"fldEmpl",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"6F73B5B8-C840-491F-98FD-15ED8A387F8B",
tooltip:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_employee.empl_full_name",
valuelist:"6971DE2E-3E61-46F2-BC93-EA1CAD87E16F",
visible:true,
width:127
},
{
autoResize:true,
dataprovider:"job_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.job#",
id:"fldJobNr",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"8EFD2DA9-8933-4A56-A4E0-6FD8678CE1ED",
tooltip:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_prod_job.job_description",
valuelist:"CD488358-5875-460F-A11F-9BBF34DF118F",
visible:true,
width:94
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.ordrevds_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.section",
id:"fldSection",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"C0B44F1F-E07C-4C85-9BF7-C6C1AAA0B6E7",
tooltip:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.ordrevds_description",
valuelist:"B620A132-8A6D-401C-88FB-776BAF20A59A",
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.dept_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.department",
id:"fldDept",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"AAEC243A-022C-433B-AF41-BEC611502CF5",
valuelist:"CB3515C1-E264-4618-9B8F-E0EC4DA90A19",
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.opcat_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.category",
id:"fldCat",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"E3328DC9-59D6-477E-A4FA-64343372E722",
valuelist:"6099EF82-AA63-454C-A954-28D0339CC23F",
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.cc_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.operation",
id:"fldCostCenter",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"DAFFFB5F-2F5A-48B6-952D-8A2DEA27F5E5",
tooltip:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_cost_centre.cc_desc",
valuelist:"6A713B2B-1BAB-493F-8281-0B6B3BF18469",
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"jcl_staging_location",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.location",
id:"jcl_staging_location",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D3FBAB90-65A3-4E79-99B8-A14049EFC00B",
valuelist:null,
visible:true,
width:108
},
{
autoResize:true,
dataprovider:"prod_job_cost_labour_to_prod_job_cost.task_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.press",
id:"fldPress",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"620FE639-C07E-4272-8CC3-DFE41A388255",
valuelist:"C46064B4-778F-45E3-9517-B678C8C5CEEC",
visible:true,
width:77
},
{
autoResize:true,
dataprovider:"prod_job_cost_labo_to_prod_job_cost.prod_job_cost_to_sch_mile_grou.sch_mile_grou_to_sa_task_cost_link.sa_task_cost_link_to_app_task_oper.clc_task_operation_name",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.taskOperation",
id:"fldTaskOperation",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"92F081F1-05CD-4DAD-9D33-6BD1EBFCA3C4",
tooltip:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_cost_centre.cc_desc",
valuelist:null,
visible:true,
width:118
},
{
autoResize:true,
dataprovider:"jcl_qty_produced",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.qty",
id:"fldQty",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"04880400-639C-4CFA-928F-3E7D946FECFB",
valuelist:null,
visible:true,
width:60
},
{
autoResize:true,
dataprovider:"jcl_color_or_bw_clicks",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.color",
id:"fldClicksColor",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"1D855D8F-2274-47C9-9E82-456B492C49F9",
valuelist:"123334CD-85FE-4FF8-83A2-12C293ABDFF3",
visible:true,
width:54
},
{
autoResize:true,
dataprovider:"clc_start_time",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.startTime_sm",
id:"fldStartTime",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D72F4DC0-23B4-413D-9CF5-2518C438B734",
valuelist:null,
visible:true,
width:60
},
{
autoResize:true,
dataprovider:"clc_end_time",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.endTime_sm",
id:"fldEndTime",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D84F31FC-6879-4D9D-86C1-9639986471B5",
valuelist:null,
visible:true,
width:60
},
{
autoResize:true,
dataprovider:"elapsed_time_formatted",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.elapsedTime_sm",
id:"fldDuration",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"F878F54C-BD7F-4959-BD89-C1960E99A3AF",
valuelist:null,
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"jcl_start_datetime",
editType:"DATEPICKER",
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.startDate_sm",
id:"fldStartDate",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"77DD5F54-DEF2-410A-AA96-C201505256CF",
valuelist:null,
visible:true,
width:95
},
{
autoResize:true,
dataprovider:"jcl_end_datetime",
editType:"DATEPICKER",
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.finishDate_sm",
id:"fldFinishDate",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"3247BA78-94F5-49AC-A885-884C7CC105AA",
valuelist:null,
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"jcl_rush_code_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.rushCode_sm",
id:"fldRushCode",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"D6A1231D-7165-4A2C-8ADC-2B31D39EE1C1",
valuelist:"BC963E65-00D2-43EE-BFEC-6710C03C6C2C",
visible:true,
width:50
},
{
autoResize:true,
dataprovider:"jcl_extra_code_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.extraCode_sm",
id:"fldExtraCode",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"FBF52236-E7B0-474A-A29F-9FCF9712CFA5",
valuelist:"5BCFB235-8B72-4140-B227-A7444C0FB5BA",
visible:true,
width:50
},
{
autoResize:true,
dataprovider:"clc_completion_code",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.completionCode_sm",
id:"fldCompletionCode",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"69AD8A7A-6991-46CF-8D4B-EE6258A46561",
valuelist:"B6509DA3-3069-45B7-979D-3AD98DA4EE64",
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"jcl_comment",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.notes",
id:"fldComment",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"246491BA-B99D-4939-8B11-4BDA27E9BE04",
tooltip:"jcl_comment",
valuelist:null,
visible:true,
width:90
},
{
autoResize:true,
dataprovider:"shift_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.shift",
id:"fldShift",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"D950E813-238A-4A0F-9EC2-D1CB13390261",
valuelist:"094730C9-03C7-47B4-A15A-AB0B7D488D10",
visible:true,
width:50
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnAddRow",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined add small",
svyUUID:"842EF61C-6909-427A-AC43-90A5CA41A01F",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-center",
headerTitle:" ",
id:"btnMilestoneProgress",
maxWidth:68,
minWidth:68,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined info",
svyUUID:"C7515674-2FCA-4E8D-988C-B2B5401DC880",
tooltip:"btnMilestoneProgress_tooltip",
valuelist:null,
visible:true,
width:68
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnDelete",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined delete",
svyUUID:"03498CFE-08DE-450A-B73D-137651BF255E",
valuelist:null,
visible:true,
width:25
}
],
columnsAutoSizing:"AUTO_SIZE",
cssPosition:{
bottom:"186",
height:"84",
left:"0px",
right:"32",
top:"165",
width:"1256"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"9AE71048-08F1-49C9-8717-0CEC08CE6C79",
onColumnDataChange:"33A48B96-BA62-425A-8AA7-C18DD5E87D7F",
onReady:"1717E70E-C852-49BE-A760-65A491FF1197",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"1F4C56C2-4D64-43C0-BE93-B84E01339FEE"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"1FDFF787-2235-4B87-8B38-DCC78172FE9F"
},
{
cssPosition:"252,324,-1,0,1482,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"324",
top:"252",
width:"1482"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
visible:true
},
name:"component_CA13317F",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1FF3CD03-2EF2-4F79-BAF3-236F839944AC"
},
{
height:250,
partType:5,
typeid:19,
uuid:"22763628-98F6-4C1F-A928-E5C00FF5E04E"
},
{
cssPosition:"305,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"305",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customer",
visible:true
},
name:"component_A591043C",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"256486EA-5203-49EE-89A2-9860F79C2300"
},
{
cssPosition:"380,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"380",
width:"375"
},
dataProviderID:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_operation_category.opcat_desc",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_7AB3C1E3",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"356EB130-3E3E-4513-8F61-83314ADF1C9A"
},
{
cssPosition:"330,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"330",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jobDescription",
visible:true
},
name:"component_748373CE",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3E637484-9E5B-4E53-879B-9406260984CF"
},
{
cssPosition:"250,-1,-1,10,570,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"10",
right:"-1",
top:"250",
width:"570"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.additionalInformation",
visible:true
},
name:"component_50C95267",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4035E4CB-FE93-44C4-82CB-ACBF9E77E746"
},
{
cssPosition:"132,-1,-1,69,127,21",
json:{
cssPosition:{
bottom:"-1",
height:"21",
left:"69",
right:"-1",
top:"132",
width:"127"
},
enabled:true,
onActionMethodID:"694A55EB-169D-4FAE-A81F-542C03DE9801",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.totalHours",
visible:true
},
name:"lblTotalTime",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"4CBFEFEF-BEBC-402A-ABAC-A98424B4C309"
},
{
cssPosition:"280,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"280",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.SalesOrderDescription",
visible:true
},
name:"component_D0B3E3C7",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4D46134C-BE84-4977-810D-D85D5D31B4E8"
},
{
cssPosition:"305,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"305",
width:"375"
},
dataProviderID:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_prod_job.prod_job_to_sa_customer.cust_name",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_F19240FE",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4D51E8EA-26CB-45E6-8905-FE87080E08F7"
},
{
cssPosition:"66,-1,-1,699,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"699",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeThurHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn4",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"679A6137-CC33-4945-8A5C-DD734C595A57"
},
{
cssPosition:"66,-1,-1,799,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"799",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeFriHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn5",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"6B15E28E-60D4-477D-BB01-C7C5A30BC75D"
},
{
cssPosition:"405,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"405",
width:"375"
},
dataProviderID:"jcl_post_fail_msg",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"fldPostErrors",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6BF1251C-AE23-4648-A793-F480D12364AA"
},
{
cssPosition:"10,-1,-1,440,120,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"440",
right:"-1",
top:"10",
width:"120"
},
enabled:true,
onActionMethodID:"20C0C2D3-FDF2-4AB9-96D4-E0E80ADF0B62",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.myTeam",
visible:true
},
name:"btnMyTeam",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"7449914A-8FAF-4709-A8F7-A1A90DF25138"
},
{
cssPosition:"112,-1,-1,69,127,21",
json:{
cssPosition:{
bottom:"-1",
height:"21",
left:"69",
right:"-1",
top:"112",
width:"127"
},
enabled:true,
onActionMethodID:"03E9D13C-5BEF-446A-93DF-C10760CB9E2F",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.hoursConfirmed",
visible:true
},
name:"lblPostedTime",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"75C7CAB2-5C54-44FB-8013-98F637EA711C"
},
{
cssPosition:"140,-1,-1,32,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"32",
right:"-1",
top:"140",
width:"22"
},
enabled:true,
onActionMethodID:"033A3535-034F-41D2-9316-61A36C5D6BDC",
styleClass:"label_bts text-center",
tabSeq:0,
text:"%%globals.icon_refresh%%",
toolTipText:"i18n:avanti.lbl.refresh",
visible:true
},
name:"btnResetFilters",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"796606BB-7201-4C06-84DF-ED2405181103"
},
{
cssPosition:"37,-1,-1,10,115,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"10",
right:"-1",
top:"37",
width:"115"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.weekBeginning",
visible:true
},
name:"component_8EB81EEE",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7BB701C5-B4D4-4C36-B4E7-BCC35B242225"
},
{
cssPosition:"37,-1,-1,130,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"130",
right:"-1",
top:"37",
width:"22"
},
enabled:true,
onActionMethodID:"5A861320-3BFD-42F0-87CC-BA500E4992FC",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_calendarBack%%",
toolTipText:"i18n:avanti.lbl.backOneWeek",
visible:true
},
name:"btnLookup",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7C711A22-2976-49BE-A750-78224C5097AC"
},
{
cssPosition:"66,-1,-1,299,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"299",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeSunHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn0",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"818A900D-8B93-4088-8A3F-E862F38BD36A"
},
{
cssPosition:"92,-1,-1,69,127,21",
json:{
cssPosition:{
bottom:"-1",
height:"21",
left:"69",
right:"-1",
top:"92",
width:"127"
},
enabled:true,
onActionMethodID:"66B79073-7780-4CCE-ABDC-9E09E64D6CF6",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.hoursUnconfirmed",
visible:true
},
name:"lblUnPostedTime",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"8517963E-C994-410F-9992-BF9DFBE79009"
},
{
height:435,
partType:8,
typeid:19,
uuid:"8536C1F1-2A97-43DF-B3F7-63C19A36B870"
},
{
cssPosition:"405,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"405",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Timesheet.postError",
visible:true
},
name:"lblPostErrors",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"88D44448-41F6-4692-AC0F-B5DABCF191A6"
},
{
cssPosition:"10,-1,-1,315,120,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"315",
right:"-1",
top:"10",
width:"120"
},
enabled:true,
onActionMethodID:"B16C21E5-2FE5-4A2E-9D6E-C1D816F0A36D",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.me",
visible:true
},
name:"btnMe",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"96753BA0-C041-4A57-9085-FB72E01344A8"
},
{
cssPosition:"37,-1,-1,150,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"37",
width:"22"
},
enabled:true,
onActionMethodID:"42F5AED7-456F-417F-8444-74D48F34EE5D",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_calendarToday%%",
toolTipText:"i18n:avanti.lbl.currentWeek",
visible:true
},
name:"btnLookupc",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"98B14317-B038-4693-98C4-C5719DF6D4AE"
},
{
height:165,
partType:1,
typeid:19,
uuid:"98B8C9F4-2597-444E-AF6A-8ED4FD6D6237"
},
{
cssPosition:"66,-1,-1,199,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"199",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeTotals",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
text:"<div style='font-size:12px; font-weight:bold;'><\/div>\r\
",
visible:true
},
name:"btn7",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"A684DB0A-A135-43B3-8F76-90D48D822962"
},
{
cssPosition:"10,-1,-1,10,85,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"10",
right:"-1",
top:"10",
width:"85"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.employee",
visible:true
},
name:"component_3BF67D27",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AF53DAE3-AE01-4876-B127-388AA77CE4D2"
},
{
cssPosition:"140,-1,-1,5,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"140",
width:"25"
},
enabled:true,
onActionMethodID:"4706F4AA-6188-46F5-9F6A-CBE5C98A600A",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnLookupccc",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B618B6C9-DA35-4A17-8A6E-1B06B13186AC"
},
{
cssPosition:"10,-1,-1,565,160,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"565",
right:"-1",
top:"10",
width:"160"
},
enabled:true,
onActionMethodID:"32C4FB56-9E3A-49C8-82A1-2C1D5A88FCAE",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.unconfirmedLabor",
visible:true
},
name:"component_D45D3072",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"BD45D2C1-BD62-4424-9052-F11CE2602A44"
},
{
cssPosition:"66,-1,-1,899,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"899",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeSatHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn6",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"BF9AF8E5-9BD4-480F-9DE9-F06DD7B5FD18"
},
{
cssPosition:"66,-1,-1,399,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"399",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeMonHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn1",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"CCA623B2-BBEB-4AE7-BDFD-6C76A6B3293C"
},
{
cssPosition:"280,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"280",
width:"375"
},
dataProviderID:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_prod_job.prod_job_to_sa_order.ordh_description",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_C79D3CBC",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DA008D9A-4B12-45A0-986B-3BB3FB79B9F8"
},
{
cssPosition:"355,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"355",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sectionDescription",
visible:true
},
name:"component_71D22CD6",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DA3682F8-41F4-4D2F-8786-7404B8F53FD5"
},
{
cssPosition:"330,-1,-1,181,375,24",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"181",
right:"-1",
top:"330",
width:"375"
},
dataProviderID:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_prod_job.job_description",
editable:false,
enabled:false,
selectOnEnter:true,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_05C50FC7",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DBD25B20-268A-4649-8785-41A86B99158F"
},
{
cssPosition:"10,-1,-1,95,215,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"95",
right:"-1",
top:"10",
width:"215"
},
dataProviderID:"_filterEmplID",
enabled:true,
onDataChangeMethodID:"D2497427-93DD-412E-8D52-E4826F9D7BE9",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"FA0A0F8C-4F7B-4045-BDF5-55F48B19EF2C",
visible:true
},
name:"fldEmployee",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"DEA23E7E-39B7-4193-82E3-F2BA2ADA9557"
},
{
cssPosition:"66,-1,-1,499,100,88",
json:{
cssPosition:{
bottom:"-1",
height:"88",
left:"499",
right:"-1",
top:"66",
width:"100"
},
dataProviderID:"_timeTuesHTML",
enabled:true,
onActionMethodID:"0E555C24-E487-4A61-9F78-B702D574B1F4",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"btn2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"E900ABD2-05ED-4061-B079-2BD0F5CF7193"
},
{
cssPosition:"380,-1,-1,20,157,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"20",
right:"-1",
top:"380",
width:"157"
},
enabled:true,
styleClass:"table_fullborder label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.operationsCategory",
visible:true
},
name:"component_10F1E17A",
styleClass:"table_fullborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FA1C1730-951D-4A0C-A492-A6E339F24568"
}
],
name:"prod_daily_entry_labor_tbl",
navigatorID:"-1",
onRecordSelectionMethodID:"B4F752C8-5A8E-442E-AF62-62123F9ED936",
onShowMethodID:"585B77E5-60DB-42AC-B7E6-BDC00F68D0DE",
onSortCmdMethodID:"DABDE2E9-48F2-4127-8323-2900A84C7058",
paperPrintScale:100,
scrollbars:0,
showInMenu:true,
size:"1210,435",
styleName:null,
typeid:3,
uuid:"E628F28A-4AB0-4F06-ADDD-F6B99C7289D7",
view:0