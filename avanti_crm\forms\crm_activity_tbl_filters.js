/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"60655C57-6A08-45C3-8135-DF7416170E24"}
 */
var employee_assignment_type = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F611DDD7-9A87-43AE-8BDE-92EF93A63248"}
 */
var currentView = i18n.getI18NMessage('avanti.lbl.activityView_myOpenActivities');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"21ECE467-82B6-46D3-8F9D-44BCA27C46AB"}
 */
var dueDateFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E907128C-BB77-4CF7-B158-D7FCB8F0F275"}
 */
var typeFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C651BE01-167D-4F13-A2E9-9406E4E025BC"}
 */
var regardingFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"64987E86-8514-432E-A99B-A4F9C8B71C59"}
 */
var priorityFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"BFA8F611-0FB5-492B-A973-DB12E9BEA695"}
 */
var employeeFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"60B28B1E-123E-4F85-83A9-F1C78515D927"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	
	// On first show, reload the base date from employee settings.
	if(firstShow) {
		/** @type{JSFoundSet<db:/avanti/sys_employee_settings>} */
		var employee_setting_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_settings')
		if(employee_setting_fs.find()) {
			employee_setting_fs.empl_id = globals.avBase_employeeUUID
			employee_setting_fs.setting_key = 'avanti.opportunityView.baseDate'
			if(employee_setting_fs.search() > 0) {
				if(employee_setting_fs.setting_value != null) {
					
				}
			}
		}
	}
	// set to edit mode.
	var result =  _super.onShowForm(firstShow, event)
	applyFilters(firstShow);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * @param readonly
 *
 * @properties={typeid:24,uuid:"1002E2FF-489F-4DE3-B62E-B07EE68F6BF6"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D8B564B8-066C-4EC0-894E-1ED297E266FD"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	elements.employee.visible = false
	elements.employee_lbl.visible = false
	
	applyFilters(true);
	
	// Reload the table and run initial sort on it.
	//forms.crm_activity_tbl.foundset.loadRecords(foundset)
	
	forms.crm_activity_dtl.onRecordSelection(event, event.getFormName())
}

/**
 * Reapply filters ont he foundset
 * @param bReApplyFilters
 *
 * @properties={typeid:24,uuid:"F79C5DD9-91AA-42CE-B93B-54FB4947D417"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		// Remove any filters.
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('statusFilter');
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('employeeFilter')
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('dueDateFilter')
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('typeFilter')
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('regardingFilter')
		forms.crm_activity_tbl.foundset.removeFoundSetFilterParam('priorityFilter')
		
		// Assign employee and status filters based on selected view 
		if(currentView == i18n.getI18NMessage('avanti.lbl.activityView_myOpenActivities')) 	{
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',globals.avBase_employeeUUID,'employeeFilter')
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_status','=', i18n.getI18NMessage('avanti.lbl.activityStatus_open'),'statusFilter')
		} else if (currentView == i18n.getI18NMessage('avanti.lbl.activityView_myClosedActivities')) {
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',globals.avBase_employeeUUID,'employeeFilter')
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_status','=', i18n.getI18NMessage('avanti.lbl.activityStatus_complete'),'statusFilter')
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.activityView_myAllActivities')) {
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',globals.avBase_employeeUUID,'employeeFilter')
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.activityView_allOpenActivities')) 	{
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_status','=', i18n.getI18NMessage('avanti.lbl.activityStatus_open'),'statusFilter')
			elements.employee.visible = true
			elements.employee_lbl.visible = true			

			if(employeeFilter != i18n.getI18NMessage('avanti.lbl.all')) { 
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',employeeFilter,'employeeFilter')
			} else if(employee_assignment_type == globals.avSales_CRM_SalesManager) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','sql:in','select distinct empl_id from sys_employee where salesper_id in (select c.salesper_id from sys_employee as a inner join app_assignment_type as b on a.app_assignment_type_id = b.app_assignment_type_id inner join sys_employee_managed_reps  as c on a.empl_id = c.empl_id where a.org_id = \'' + globals.org_id + '\' and a.empl_id = \'' + globals.avBase_employeeUUID + '\' and b.assignment_desc = \'Sales Manager\')','employeeFilter')
			}
		} else if (currentView == i18n.getI18NMessage('avanti.lbl.activityView_allClosedActivities')) {
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_status','=', i18n.getI18NMessage('avanti.lbl.activityStatus_complete'),'statusFilter')
			elements.employee.visible = true
			elements.employee_lbl.visible = true			

			if(employeeFilter != i18n.getI18NMessage('avanti.lbl.all')) { 
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',employeeFilter,'employeeFilter')
			} else if(employee_assignment_type == globals.avSales_CRM_SalesManager) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','sql:in','select distinct empl_id from sys_employee where salesper_id in (select c.salesper_id from sys_employee as a inner join app_assignment_type as b on a.app_assignment_type_id = b.app_assignment_type_id inner join sys_employee_managed_reps  as c on a.empl_id = c.empl_id where a.org_id = \'' + globals.org_id + '\' and a.empl_id = \'' + globals.avBase_employeeUUID + '\' and b.assignment_desc = \'Sales Manager\')','employeeFilter')
			}
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.activityView_allActivities')) {
			elements.employee.visible = true
			elements.employee_lbl.visible = true	
			if(employeeFilter != i18n.getI18NMessage('avanti.lbl.all')) { 
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',employeeFilter,'employeeFilter')
			} else if(employee_assignment_type == globals.avSales_CRM_SalesManager) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','sql:in','select distinct empl_id from sys_employee where salesper_id in (select c.salesper_id from sys_employee as a inner join app_assignment_type as b on a.app_assignment_type_id = b.app_assignment_type_id inner join sys_employee_managed_reps  as c on a.empl_id = c.empl_id where a.org_id = \'' + globals.org_id + '\' and a.empl_id = \'' + globals.avBase_employeeUUID + '\' and b.assignment_desc = \'Sales Manager\')','employeeFilter')
			}
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.activityView_notAssigned')) {
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',null,'employeeFilter')
		} else {
			forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_assignment_empl_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
		}
		
		// Set due date filter
		if(dueDateFilter != i18n.getI18NMessage('avanti.lbl.activityDueDate_all')) {
			
			// Set todays date to today at midnight and use this to compare.
			var todaysDate = new Date(new Date().setHours(0,0,0,0))
			
			if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','<', todaysDate,'dueDateFilter')
			} else if (dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue30Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime() - (60*60*24*30*1000)), todaysDate],'dueDateFilter')
			} else if (dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue14Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime() - (60*60*24*14*1000)), todaysDate],'dueDateFilter')
			} else if (dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue7Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime() - (60*60*24*7*1000)), todaysDate],'dueDateFilter')
			} else if (dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_today')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [todaysDate, new Date(todaysDate.getTime() + (60*60*24*1000)-1)],'dueDateFilter')
			} else if (dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_todayAndOverdue')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','<', new Date(todaysDate.getTime() + (60*60*24*1000)-1),'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_tomorrow')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime() + (60*60*24*1000)), new Date(todaysDate.getTime() + (60*60*24*2*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next7Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*7*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next14Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*14*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next30Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*30*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next60Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*60*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next90Days')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*90*1000)-1)], 'dueDateFilter')
			} else if(dueDateFilter == i18n.getI18NMessage('avanti.lbl.activityDueDate_next6Months')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_due_datetime','between', [new Date(todaysDate.getTime()), new Date(todaysDate.getTime() + (60*60*24*183*1000)-1)], 'dueDateFilter')
			} 
		}
		
		// Set type filter based on Activity Type selected
		if(typeFilter != i18n.getI18NMessage('avanti.lbl.all')) {
			if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_phoneCall')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Phone Call','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_task')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Task','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_appointment')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Appointment','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_serviceTicket')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Service Ticket','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_collection')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Collection','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_other')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Other','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.activityType_inquiry')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Inquiry','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.ClientDelivery')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Client Delivery','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.ClientLunch')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Client Lunch','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.ClientVisit')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Client Visit','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.ClientVisitToUs')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Client Visit To Us','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.ConductResearch')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Conduct Research','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.email')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Email','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.Meeting')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Meeting','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.Presentation')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Presentation','typeFilter');
			} 
			else if(typeFilter == i18n.getI18NMessage('avanti.lbl.contactWorkFlowType_Samples')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_type','=', 'Samples','typeFilter');
			} 
		}
		
		// Set regarding filter based on Regarding selection
		if(regardingFilter != i18n.getI18NMessage('avanti.lbl.all')) {
			if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_customer')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_customer'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_estimate')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_estimate'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_salesOrder')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_salesOrder'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_job')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_job'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_packingSlip')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_packingSlip'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_lead')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_lead'),'regardingFilter')
			} else if(regardingFilter == i18n.getI18NMessage('avanti.lbl.activityRegarding_customerAddress')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_regarding','=', i18n.getI18NMessage('avanti.lbl.activityRegarding_customerAddress'),'regardingFilter')
			} 
		}
		
		// Set priority filter based on priority selected
		if(priorityFilter != i18n.getI18NMessage('avanti.lbl.all')) {
			if(priorityFilter == i18n.getI18NMessage('avanti.lbl.activityPriority_low')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_priority','=', i18n.getI18NMessage('avanti.lbl.activityPriority_low'),'priorityFilter')
			} else if(priorityFilter == i18n.getI18NMessage('avanti.lbl.activityPriority_medium')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_priority','=', i18n.getI18NMessage('avanti.lbl.activityPriority_medium'),'priorityFilter')
			} else if(priorityFilter == i18n.getI18NMessage('avanti.lbl.activityPriority_high')) {
				forms.crm_activity_tbl.foundset.addFoundSetFilterParam('activity_priority','=', i18n.getI18NMessage('avanti.lbl.activityPriority_high'),'priorityFilter')
			} 
		}
		
		forms.crm_activity_tbl.foundset.loadAllRecords();
		forms.crm_activity_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.crm_activity_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
	}
}

/**
 * Callback method when form is (re)loaded.
 * Load valuelists of dropdowns for filters
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"1F83D489-2473-4492-B299-9986343CDF6E"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	var vlFilterRealValues = new Array();
	var vlFilterDisplayValues = new Array();
	
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_open'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_open'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_onHold'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_onHold'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_complete'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityStatus_complete'))
	application.setValueListItems('vl_activityStatus', vlFilterDisplayValues, vlFilterRealValues);
	
	vlFilterRealValues = new Array();
	vlFilterDisplayValues = new Array();
	
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myOpenActivities'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myOpenActivities'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myClosedActivities'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myClosedActivities'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myAllActivities'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_myAllActivities'))
	// Get employee information and their assignment type. 
	/** @type{JSFoundSet<db:/avanti/sys_employee>} */
	var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
	if(employee_fs.find() || employee_fs.find()) {
		employee_fs.empl_id = globals.avBase_employeeUUID
		if(employee_fs.search() > 0)
		{
			if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
				switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
					// If an admin
					case globals.avSales_CRM_Administrator:
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allOpenActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allOpenActivities'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allClosedActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allClosedActivities'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allActivities'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_notAssigned'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_notAssigned'))
						break;
					case globals.avSales_CRM_SalesManager:
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allOpenActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allOpenActivities'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allClosedActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allClosedActivities'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allActivities'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityView_allActivities'))
						break;
					default:
						break;	
				}
			}
		}
	}
		
	
	application.setValueListItems('vl_activityView', vlFilterDisplayValues, vlFilterRealValues);
	
	
	vlFilterRealValues = new Array();
	vlFilterDisplayValues = new Array();
	
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_all'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_all'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue30Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue30Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue14Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue14Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue7Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_overdue7Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_today'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_today'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_todayAndOverdue'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_todayAndOverdue'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_tomorrow'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_tomorrow'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next7Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next7Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next14Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next14Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next30Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next30Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next60Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next60Days'))	
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next90Days'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next90Days'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next6Months'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.activityDueDate_next6Months'))	
	application.setValueListItems('vl_activityDueDate', vlFilterDisplayValues, vlFilterRealValues);
	
	// Get list of employees based on list of current activities
	/** @type{JSDataSet} */
	var employees_vl = databaseManager.convertToDataSet(['1', '2'], ['activity_assignment_empl_id', 'empl_full_name'])
	employees_vl.removeRow(1)
	employees_vl.removeRow(1)
	employees_vl.addRow(1,[i18n.getI18NMessage('avanti.lbl.all'), i18n.getI18NMessage('avanti.lbl.all')])
	
	
	// Get employee information and their assignment type. 
	/** @type{JSFoundSet<db:/avanti/sys_employee>} */
	employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
	if(employee_fs.find() || employee_fs.find()) {
		employee_fs.empl_id = globals.avBase_employeeUUID
		if(employee_fs.search() > 0)
		{
			if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
				employee_assignment_type = employee_fs.sys_employee_to_app_assignment_type.assignment_desc
				switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
					// If an admin
					case globals.avSales_CRM_Administrator:
					
						var employee_ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti,'select distinct activity_assignment_empl_id, empl_full_name from crm_activity inner join sys_employee on activity_assignment_empl_id = empl_id where crm_activity.org_id = ?',[globals.org_id], 200)
						for(var employee_idx = 1; employee_idx <= employee_ds.getMaxRowIndex(); employee_idx++) {
							employees_vl.addRow(employee_idx + 1, [employee_ds.getRowAsArray(employee_idx)[1], employee_ds.getRowAsArray(employee_idx)[0]])
						}
						application.setValueListItems('vl_activityEmployees', employees_vl.getColumnAsArray(1), employees_vl.getColumnAsArray(2));
						break;
					case globals.avSales_CRM_SalesManager:
						var managed_employee_ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti,'select distinct empl_id, empl_full_name from sys_employee where salesper_id in (select c.salesper_id from sys_employee as a inner join app_assignment_type as b on a.app_assignment_type_id = b.app_assignment_type_id inner join sys_employee_managed_reps  as c on a.empl_id = c.empl_id where a.org_id = ? and a.empl_id = ? and b.assignment_desc = \'Sales Manager\')',[globals.org_id, globals.avBase_employeeUUID], 200)
						for(var managed_employee_idx = 1; managed_employee_idx <= managed_employee_ds.getMaxRowIndex(); managed_employee_idx++) {
							employees_vl.addRow(managed_employee_idx + 1, [managed_employee_ds.getRowAsArray(managed_employee_idx)[1], managed_employee_ds.getRowAsArray(managed_employee_idx)[0]])
						}
						application.setValueListItems('vl_activityEmployees', employees_vl.getColumnAsArray(1), employees_vl.getColumnAsArray(2));
						
						break;
				}
			}
		}
	}
	
	
	return _super.onLoad(event)
}

/**
 * Perform the element default action to sort.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"E9E523F4-FD8F-4BAF-B744-411CC3281F0E"}
 */
function onActionInitialSort(event) {
	forms.crm_activity_tbl.foundset.sort('activity_due_datetime asc')
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"2BCCE12A-B09C-4511-AC13-B47EC3119302"}
 */
function onDataChange(oldValue, newValue, event) {
	refreshUI(event)
	return true
}
