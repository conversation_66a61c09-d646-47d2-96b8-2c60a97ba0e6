customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/po_receipt_detail",
extendsID:"DB916ED8-EF50-4992-A487-2EB77E04DDCD",
items:[
{
cssPosition:"314,-1,25,5,140,22",
json:{
cssPosition:{
bottom:"25",
height:"22",
left:"5",
right:"-1",
top:"314",
width:"140"
},
enabled:true,
labelFor:"total_steup_amt_todate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.TotalSetupAmountToDate",
visible:true
},
name:"total_steup_amt_todate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"06E79244-96E7-49AB-866E-F490C394361C"
},
{
cssPosition:"287,-1,52,878,120,24",
json:{
cssPosition:{
bottom:"52",
height:"24",
left:"878",
right:"-1",
top:"287",
width:"120"
},
enabled:true,
onActionMethodID:"B0DD6AAD-2E40-4E78-84B1-E3AAEC658173",
styleClass:"btn btn-default button_bts",
tabSeq:17,
text:"i18n:avanti.lbl.LandedCost",
visible:true
},
name:"btnLandedCost",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"072405B5-6A88-4B80-9887-D6D83C10D483"
},
{
cssPosition:"289,-1,52,756,120,24",
json:{
cssPosition:{
bottom:"52",
height:"24",
left:"756",
right:"-1",
top:"289",
width:"120"
},
enabled:true,
onActionMethodID:"A64F9F64-CC57-4EFC-8DF9-DC0F16E84BB9",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Fix Missing Transactions",
visible:false
},
name:"btnFixMissingTrans",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"4824E0C5-BE53-4299-9B13-D2D518C4952E",
visible:false
},
{
cssPosition:"260,-1,79,756,120,24",
json:{
cssPosition:{
bottom:"79",
height:"24",
left:"756",
right:"-1",
top:"260",
width:"120"
},
enabled:true,
onActionMethodID:"330ABAFD-9560-4EB4-A87A-339335340C99",
styleClass:"btn btn-default button_bts",
tabSeq:15,
text:"i18n:avanti.lbl.update",
visible:true
},
name:"btnUpdateReceipt",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"5C4FDCAF-C42A-49F0-9BD7-4EB7368B1CE3"
},
{
height:361,
partType:8,
typeid:19,
uuid:"5E7D9A76-CB87-42E7-916D-027A9E7A21FF"
},
{
cssPosition:"260,-1,79,5,140,22",
json:{
cssPosition:{
bottom:"79",
height:"22",
left:"5",
right:"-1",
top:"260",
width:"140"
},
enabled:true,
labelFor:"item_desc1",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.itemDescription",
visible:true
},
name:"item_desc1_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6294ECC8-1B4C-4064-9A76-13BFBEE10CD2"
},
{
anchors:15,
cssPosition:"5,0,111px,0,1334px,244",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"po_receipt_detail_to_po_purchase_detail.sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.lineNumber",
id:"sequence_nr",
maxWidth:50,
minWidth:50,
rowGroupIndex:-1,
styleClass:"text-left",
styleClassDataprovider:"clc_onRender_lineNumber",
svyUUID:"DB996060-2ED3-4505-BE5A-874C0CBB280C",
valuelist:null,
visible:true,
width:50
},
{
autoResize:false,
dataprovider:"porecd_item_code_display",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.item_id",
id:"item_code",
maxWidth:260,
minWidth:260,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"46015447-7DF5-4042-B8DE-5FEBCC65C06D",
valuelist:null,
visible:true,
width:260
},
{
autoResize:false,
dataprovider:"po_rece_deta_to_po_purc_deta.po_purc_deta_to_in_item_supp.in_item_supp_to_sys_unit_of_meas_purc_uom.uom_code",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.uom_code",
id:"uom_code",
maxWidth:60,
minWidth:60,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"A0C0EFD1-5D1F-4132-AE30-99731B882FAC",
valuelist:null,
visible:true,
width:60
},
{
autoResize:false,
dataprovider:"po_receipt_detail_to_po_purchase_detail.podet_expected_date",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.expected",
id:"podet_expected_date",
maxWidth:80,
minWidth:80,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"AC3B60E8-4067-4F07-B01C-399EA27324AC",
valuelist:null,
visible:true,
width:80
},
{
autoResize:false,
dataprovider:"po_receipt_detail_to_po_purchase_detail.podet_qty_ordered",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.qtyOrdered",
id:"podet_qty_ordered",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"18BDD3B2-C4B0-4300-A9CB-FD7498DEE75F",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"porecd_qty_received_todate",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.qtyPreviouslyReceived",
id:"porecd_qty_received_todate",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"F859A3AE-5432-4FA8-8F78-D2C4C17099DA",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"porecd_qty_received",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.qtyReceivedShort",
id:"porecd_qty_received",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"text-right",
styleClassDataprovider:"clc_onRender_qtyReceived",
svyUUID:"C73C8509-FD1D-4F6A-9808-14BFAEA63694",
valuelist:null,
visible:true,
width:90
},
{
autoResize:true,
dataprovider:"porecd_qty_per_box",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.Qty/Box",
id:"porecd_qty_per_box",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"72B1DE53-1958-47F4-B3CB-08377D8C6E34",
valuelist:null,
visible:true,
width:90
},
{
autoResize:true,
dataprovider:"porecd_num_boxes",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.#Boxes",
id:"porecd_num_boxes",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"5AEB39B3-8D49-425B-AE2D-1B82D331AC1B",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"porecd_rolls_received",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.RollsReceived",
id:"porecd_rolls_received",
maxWidth:104,
minWidth:104,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"9F91F91C-0885-4C2D-AC44-37042E0EAAFF",
valuelist:null,
visible:true,
width:104
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnRolls",
rowGroupIndex:-1,
styleClass:"material-symbols-outlined info",
svyUUID:"0406F4CC-91DE-4CCD-A0BC-AEE402D8BA75",
tooltip:"btnRolls_tooltip",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:"porecd_qty_rejected",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.qtyRejected",
id:"porecd_qty_rejected",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"09076791-6808-4897-91D7-2DC55B93A3B7",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"porecd_remaining_balance",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.qtyRemaining",
id:"porecd_receipt_balance",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"E0B66894-EEFD-4037-9ECA-62BA8F941435",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"porecd_cancel_bal_flag",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.cancelBal",
id:"porecd_cancel_bal_flag",
maxWidth:74,
minWidth:74,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"E12851AA-0DEE-4E93-A4C7-3CD9403D1DA5",
valuelist:null,
visible:true,
width:74
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnBinLocations",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClassDataprovider:"clc_onRender_btnBinLocations",
svyUUID:"67C32D30-70D5-46E0-A1E4-358192F65937",
tooltip:"btnBinLocations_tooltip",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
id:"End",
maxWidth:1,
minWidth:1,
svyUUID:"E8CC88B9-A6BD-470F-B210-05B6A258847B",
width:1
}
],
columnsAutoSizing:"AUTO_SIZE",
cssPosition:{
bottom:"111px",
height:"244",
left:"0",
right:"0",
top:"5",
width:"1334px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"4C75574B-F35A-4C1F-8B03-033786969B6D",
onColumnDataChange:"F9E2AC87-**************-0D1714EA1A8E",
onReady:"1675A8DB-4602-4101-829A-A8B388323177",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"917A3683-8683-4DB9-88CC-084EE39008B5"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"64EA0D4C-B2D7-4AF6-9F8D-A2E1E096713F"
},
{
cssPosition:"312,-1,25,878,120,24",
json:{
cssPosition:{
bottom:"25",
height:"24",
left:"878",
right:"-1",
top:"312",
width:"120"
},
enabled:true,
onActionMethodID:"32D31926-086C-4AFC-813F-F4E998FC3BB7",
styleClass:"btn btn-default button_bts",
tabSeq:19,
text:"i18n:avanti.lbl.stkAllocation",
visible:true
},
name:"btnStockAllocation",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"6F6E36EE-B8C0-4270-94FA-D50C04770BB3"
},
{
cssPosition:"287,-1,52,5,140,22",
json:{
cssPosition:{
bottom:"52",
height:"22",
left:"5",
right:"-1",
top:"287",
width:"140"
},
enabled:true,
labelFor:"itemsupp_part_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.SupplierPartNumber",
visible:true
},
name:"itemsupp_part_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"78E56D33-58F3-4BAE-85FA-81616C4FB7DB"
},
{
cssPosition:"287,-1,52,150,160,22",
enabled:false,
json:{
cssPosition:{
bottom:"52",
height:"22",
left:"150",
right:"-1",
top:"287",
width:"160"
},
dataProviderID:"po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item_supplier.itemsupp_part_number",
editable:false,
enabled:false,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:18,
visible:true
},
name:"itemsupp_part_number",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7BB3D934-1244-496F-A35D-95029FE74006"
},
{
height:250,
partType:5,
typeid:19,
uuid:"7BD2F8EA-F78E-408F-834A-96DA04429BDC"
},
{
cssPosition:"260,-1,79,878,120,24",
json:{
cssPosition:{
bottom:"79",
height:"24",
left:"878",
right:"-1",
top:"260",
width:"120"
},
enabled:true,
onActionMethodID:"AEA0DF7E-4C65-483D-9268-6627BC0DC2E1",
styleClass:"btn btn-default button_bts",
tabSeq:16,
text:"i18n:avanti.lbl.receiveAll",
visible:true
},
name:"btnReceiveAll",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"9F60055E-BBFA-4587-9F76-305B6BCB938D"
},
{
height:5,
partType:2,
typeid:19,
uuid:"ADE488F4-7196-4E30-9C55-C8546ED180A6"
},
{
cssPosition:"260,-1,79,150,482,22",
enabled:false,
json:{
cssPosition:{
bottom:"79",
height:"22",
left:"150",
right:"-1",
top:"260",
width:"482"
},
dataProviderID:"po_receipt_detail_to_po_purchase_detail.po_purchase_detail_to_in_item.item_desc1",
editable:false,
enabled:false,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:14,
visible:true
},
name:"item_desc1",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E33F9097-7085-403C-9588-C36721ACB834"
},
{
cssPosition:"314,-1,25,150,160,22",
enabled:false,
json:{
cssPosition:{
bottom:"25",
height:"22",
left:"150",
right:"-1",
top:"314",
width:"160"
},
dataProviderID:"calculate_total_setup_cost_todate",
editable:false,
enabled:false,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"total_steup_amt_todate",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E810BE59-77E4-4FB1-BABD-ADB43D03110F"
}
],
name:"po_receipt_detail_tbl",
navigatorID:"-1",
onShowMethodID:"E5E974E7-09D5-4E6C-9150-36DF12825BD6",
scrollbars:32,
size:"1334,375",
styleName:null,
typeid:3,
uuid:"C0433DFA-0C45-4D97-8BB9-EC5519EDCADE",
view:0