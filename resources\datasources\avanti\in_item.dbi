columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
creationOrderIndex:0,
dataType:1,
flags:37,
length:36,
name:"item_id"
},
{
allowNull:true,
creationOrderIndex:123,
dataType:93,
name:"created_date"
},
{
allowNull:true,
creationOrderIndex:24,
dataType:1,
flags:36,
length:36,
name:"cust_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:106,
dataType:1,
flags:36,
length:36,
name:"cust_id_invoice_account"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:107,
dataType:1,
flags:36,
length:36,
name:"custaddr_id_billto"
},
{
allowNull:true,
creationOrderIndex:53,
dataType:1,
flags:36,
length:36,
name:"div_id"
},
{
allowNull:true,
creationOrderIndex:124,
dataType:-9,
length:50,
name:"external_id"
},
{
allowNull:true,
creationOrderIndex:33,
dataType:1,
flags:36,
length:36,
name:"ingroup_id"
},
{
allowNull:true,
creationOrderIndex:111,
dataType:4,
name:"is_billable"
},
{
allowNull:true,
creationOrderIndex:17,
dataType:4,
name:"item_allow_backorders"
},
{
allowNull:true,
creationOrderIndex:15,
dataType:4,
name:"item_allow_commissions"
},
{
allowNull:true,
creationOrderIndex:16,
dataType:4,
name:"item_allow_discounts"
},
{
allowNull:true,
creationOrderIndex:11,
dataType:4,
name:"item_avg_lead_time"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:34,
dataType:8,
defaultValue:"0",
length:53,
name:"item_backord_qty"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:60,
dataType:8,
name:"item_caliper"
},
{
allowNull:true,
creationOrderIndex:63,
dataType:4,
name:"item_carbonless_nr_sheets"
},
{
allowNull:true,
creationOrderIndex:64,
dataType:4,
name:"item_carbonless_set"
},
{
allowNull:true,
creationOrderIndex:2,
dataType:-9,
defaultFormat:"|U",
length:50,
name:"item_code"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,37,0]]",
creationOrderIndex:130,
dataType:12,
length:37,
name:"item_code_no_revision"
},
{
allowNull:true,
creationOrderIndex:9,
dataType:-9,
length:36,
name:"item_color"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:35,
dataType:8,
defaultValue:"0",
length:53,
name:"item_committed_qty"
},
{
allowNull:true,
creationOrderIndex:50,
dataType:8,
length:53,
name:"item_core_diameter"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,2,0]]",
creationOrderIndex:120,
dataType:12,
length:2,
name:"item_country_of_origin"
},
{
allowNull:true,
autoEnterSubType:1,
autoEnterType:1,
creationOrderIndex:108,
dataType:93,
name:"item_creation_date"
},
{
allowNull:true,
creationOrderIndex:26,
dataType:-9,
defaultFormat:"|U",
length:50,
name:"item_cust_part_number"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:10,
dataType:4,
defaultValue:"0",
name:"item_decimal_places"
},
{
allowNull:true,
creationOrderIndex:3,
dataType:-9,
length:128,
name:"item_desc1"
},
{
allowNull:true,
creationOrderIndex:4,
dataType:-9,
length:128,
name:"item_desc2"
},
{
allowNull:true,
creationOrderIndex:18,
dataType:8,
length:53,
name:"item_dimension_heigth"
},
{
allowNull:true,
creationOrderIndex:19,
dataType:8,
length:53,
name:"item_dimension_length"
},
{
allowNull:true,
creationOrderIndex:20,
dataType:8,
length:53,
name:"item_dimension_width"
},
{
allowNull:true,
creationOrderIndex:121,
dataType:4,
name:"item_exclude_from_avatax"
},
{
allowNull:true,
creationOrderIndex:109,
dataType:93,
name:"item_expiry_date"
},
{
allowNull:true,
creationOrderIndex:58,
dataType:4,
name:"item_generate_roll_number"
},
{
allowNull:true,
creationOrderIndex:27,
dataType:-9,
length:50,
name:"item_gl_cost_of_sales"
},
{
allowNull:true,
creationOrderIndex:29,
dataType:-9,
length:50,
name:"item_gl_inventory"
},
{
allowNull:true,
creationOrderIndex:28,
dataType:-9,
length:50,
name:"item_gl_inventory_adj"
},
{
allowNull:true,
creationOrderIndex:31,
dataType:-9,
length:50,
name:"item_gl_sales"
},
{
allowNull:true,
creationOrderIndex:30,
dataType:-9,
length:50,
name:"item_gl_sales_returns"
},
{
allowNull:true,
creationOrderIndex:43,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_cost_of_sales"
},
{
allowNull:true,
creationOrderIndex:68,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_deposits"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:61,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_fg"
},
{
allowNull:true,
creationOrderIndex:45,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_inventory"
},
{
allowNull:true,
creationOrderIndex:44,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_inventory_adj"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:105,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_pending_receipt"
},
{
allowNull:true,
creationOrderIndex:47,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_sales"
},
{
allowNull:true,
creationOrderIndex:46,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_sales_returns"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:62,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_wip"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:118,
dataType:1,
flags:36,
length:36,
name:"item_glacct_id_wp"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,20,0]]",
creationOrderIndex:119,
dataType:12,
length:20,
name:"item_harmonized_code"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:36,
dataType:8,
defaultValue:"0",
length:53,
name:"item_inproduction_qty"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:37,
dataType:8,
defaultValue:"0",
length:53,
name:"item_intransit_qty"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:69,
dataType:4,
defaultValue:"0",
name:"item_is_fsc"
},
{
allowNull:true,
creationOrderIndex:122,
dataType:4,
name:"item_is_virtual"
},
{
allowNull:true,
creationOrderIndex:32,
dataType:-9,
defaultFormat:"|U",
length:13,
name:"item_isbn_number"
},
{
allowNull:true,
autoEnterSubType:3,
autoEnterType:1,
creationOrderIndex:110,
dataType:93,
name:"item_last_modified_date"
},
{
allowNull:true,
creationOrderIndex:8,
dataType:4,
name:"item_lot_item"
},
{
allowNull:true,
creationOrderIndex:21,
dataType:8,
length:53,
name:"item_max_weight"
},
{
allowNull:true,
creationOrderIndex:102,
dataType:4,
name:"item_no_bin_location"
},
{
allowNull:true,
creationOrderIndex:59,
dataType:4,
name:"item_no_packsize_adj"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:38,
dataType:8,
defaultValue:"0",
length:53,
name:"item_onhand_qty"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:39,
dataType:8,
defaultValue:"0",
length:53,
name:"item_onpo_qty"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:40,
dataType:8,
defaultValue:"0",
length:53,
name:"item_onporeq_qty"
},
{
allowNull:true,
creationOrderIndex:51,
dataType:8,
length:53,
name:"item_outside_diameter"
},
{
allowNull:true,
creationOrderIndex:57,
dataType:8,
length:53,
name:"item_overlap"
},
{
allowNull:true,
creationOrderIndex:49,
dataType:1,
flags:36,
length:36,
name:"item_purch_taxgroup_id"
},
{
allowNull:true,
creationOrderIndex:48,
dataType:-9,
length:1,
name:"item_purchtax_option"
},
{
allowNull:true,
creationOrderIndex:56,
dataType:8,
length:53,
name:"item_rate"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:66,
dataType:8,
defaultValue:"0",
name:"item_reserved_qty"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,10,0]]",
creationOrderIndex:129,
dataType:12,
length:10,
name:"item_revision"
},
{
allowNull:false,
autoEnterType:3,
creationOrderIndex:12,
dataType:-9,
defaultValue:"C",
length:1,
name:"item_salestax_option"
},
{
allowNull:true,
creationOrderIndex:65,
dataType:-9,
length:50,
name:"item_salestaxtype_code"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:41,
dataType:8,
defaultValue:"0",
length:53,
name:"item_scheduled_qty"
},
{
allowNull:true,
creationOrderIndex:127,
dataType:4,
description:"Separate cost for each bill of material item",
name:"item_separate_cost_bom"
},
{
allowNull:true,
creationOrderIndex:42,
dataType:-9,
length:1024,
name:"item_spec"
},
{
allowNull:true,
creationOrderIndex:23,
dataType:1,
flags:36,
length:36,
name:"item_standard_uom_id"
},
{
allowNull:false,
autoEnterType:3,
creationOrderIndex:7,
dataType:-9,
defaultValue:"A",
length:1,
name:"item_status"
},
{
allowNull:true,
creationOrderIndex:52,
dataType:4,
name:"item_tooling_flg"
},
{
allowNull:true,
creationOrderIndex:55,
dataType:8,
length:53,
name:"item_track_rolls"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:103,
dataType:8,
defaultValue:"0",
name:"item_unavailable_qty"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:104,
dataType:8,
defaultValue:"0",
name:"item_unusable_qty"
},
{
allowNull:true,
creationOrderIndex:67,
dataType:4,
name:"item_use_for_modelling"
},
{
allowNull:false,
creationOrderIndex:5,
dataType:1,
flags:36,
length:36,
name:"itemclass_id"
},
{
allowNull:false,
autoEnterType:3,
creationOrderIndex:6,
dataType:-9,
defaultValue:"S",
length:2,
name:"itemtype_code"
},
{
allowNull:true,
creationOrderIndex:125,
dataType:93,
name:"modified_date"
},
{
allowNull:false,
autoEnterType:5,
creationOrderIndex:1,
dataType:1,
flags:36,
length:36,
lookupValue:"globals.org_id",
name:"org_id"
},
{
allowNull:true,
creationOrderIndex:54,
dataType:1,
flags:36,
length:36,
name:"plant_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:112,
dataType:1,
flags:36,
length:36,
name:"postacct_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:117,
dataType:1,
flags:36,
length:36,
name:"postacctcontract_id"
},
{
allowNull:true,
creationOrderIndex:113,
dataType:8,
length:53,
name:"postage_rate"
},
{
allowNull:true,
creationOrderIndex:114,
dataType:1,
flags:36,
length:36,
name:"postmthd_id"
},
{
allowNull:true,
creationOrderIndex:115,
dataType:1,
flags:36,
length:36,
name:"postsort_id"
},
{
allowNull:true,
creationOrderIndex:116,
dataType:1,
flags:36,
length:36,
name:"posttype_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:126,
dataType:12,
flags:4,
length:36,
name:"replaces_item_id"
},
{
allowNull:true,
creationOrderIndex:14,
dataType:1,
flags:36,
length:36,
name:"site_id"
},
{
allowNull:true,
creationOrderIndex:22,
dataType:1,
flags:36,
length:36,
name:"sys_image_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:128,
dataType:12,
flags:4,
length:36,
name:"sys_org_language_uuid"
},
{
allowNull:true,
creationOrderIndex:13,
dataType:1,
flags:36,
length:36,
name:"taxgroup_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:70,
dataType:12,
length:255,
name:"udf1"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:71,
dataType:12,
length:255,
name:"udf10"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:72,
dataType:12,
length:255,
name:"udf11"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:73,
dataType:12,
length:255,
name:"udf12"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:74,
dataType:12,
length:255,
name:"udf13"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:75,
dataType:12,
length:255,
name:"udf14"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:76,
dataType:12,
length:255,
name:"udf15"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:77,
dataType:12,
length:255,
name:"udf16"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:78,
dataType:12,
length:255,
name:"udf17"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:79,
dataType:12,
length:255,
name:"udf18"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:80,
dataType:12,
length:255,
name:"udf19"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:81,
dataType:12,
length:255,
name:"udf2"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:82,
dataType:12,
length:255,
name:"udf20"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:83,
dataType:12,
length:255,
name:"udf21"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:84,
dataType:12,
length:255,
name:"udf22"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:85,
dataType:12,
length:255,
name:"udf23"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:86,
dataType:12,
length:255,
name:"udf24"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:87,
dataType:12,
length:255,
name:"udf25"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:88,
dataType:12,
length:255,
name:"udf26"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:89,
dataType:12,
length:255,
name:"udf27"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:90,
dataType:12,
length:255,
name:"udf28"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:91,
dataType:12,
length:255,
name:"udf29"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:92,
dataType:12,
length:255,
name:"udf3"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:93,
dataType:12,
length:255,
name:"udf30"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:94,
dataType:12,
length:255,
name:"udf31"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:95,
dataType:12,
length:255,
name:"udf32"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:96,
dataType:12,
length:255,
name:"udf4"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:97,
dataType:12,
length:255,
name:"udf5"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:98,
dataType:12,
length:255,
name:"udf6"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:99,
dataType:12,
length:255,
name:"udf7"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:100,
dataType:12,
length:255,
name:"udf8"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,255,0]]",
creationOrderIndex:101,
dataType:12,
length:255,
name:"udf9"
},
{
allowNull:true,
creationOrderIndex:25,
dataType:1,
flags:36,
length:36,
name:"worktype_id"
}
],
name:"in_item",
tableType:0