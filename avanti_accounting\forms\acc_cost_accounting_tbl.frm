customProperties:"methods:{\
onLoadMethodID:{\
arguments:null,\
parameters:null\
},\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/prod_job_cost_transactions",
extendsID:"0C9ADDE4-72BC-4AD7-BCDD-5AD8C3FCB6C3",
initialSort:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job.job_number asc",
items:[
{
anchors:15,
cssPosition:"110,0,3,0,2203px,128",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.job_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.jobNumber",
id:"job_number",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"09FDFC8B-D662-4017-A39E-ABD9474ECBC6",
valuelist:"38861BB7-BA0D-4FFA-A1E4-9E4A7A4C3E39",
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job.job_description",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.jobDescription",
id:"job_description",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"5E004385-1116-419B-9E9A-CF485E08E60B",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section_leftJoin.ordrevds_description",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.section",
id:"ordrevds_description",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"72DA7068-563E-4B8A-89D4-DD46DE93DDB9",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_tran_to_prod_job_cost.prod_job_cost_to_sch_mile_grou.sch_mile_grou_to_sch_mile.ms_oper_name",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.milestone",
id:"ms_oper_name",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"5D4CA890-D761-4311-9C7B-5DBE84EA0527",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_sys_cost_centre.cc_desc",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.costCenter",
id:"cc_desc",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"D8FA8DE4-6DD2-4115-B846-5D7DB417C066",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_sys_operation_category_leftJoin.opcat_desc",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.opCat",
id:"opcat_desc",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"091A511A-81B3-4C13-A158-8F9943E13815",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour_leftJoin.total_base_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.baseCost",
id:"jcl_base_cost",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"ECD8AAC6-3800-4050-BD69-C0DC0D4E53E8",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"prod_job_cost_tran_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labo.prod_job_cost_labo_to_gl_acco_gl.glacct_number",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.baseGL",
id:"base_gl",
maxWidth:82,
minWidth:82,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"895843F8-25C8-41BC-8668-4DAFEA00A0C7",
valuelist:null,
visible:true,
width:82
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour_leftJoin.total_labour_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.labourCost",
id:"jcl_labour_cost",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"C089DDB0-508B-434C-BA35-4E49AE378BAD",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"prod_job_cost_tran_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labo.prod_job_cost_labo_to_gl_acco_gl.glacct_number",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.labourGL",
id:"labour_gl",
maxWidth:85,
minWidth:85,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"2710F011-5EE9-4E63-A2D7-21ADFD6577CC",
valuelist:null,
visible:true,
width:85
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour_leftJoin.total_overhead_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.overheadCost",
id:"jcl_overhead_cost",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"FE61A8BB-5C6E-4747-9DBD-D19520E0A88C",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"prod_job_cost_tran_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labo.prod_job_cost_labo_to_gl_acco_gl.glacct_number",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.overheadGL",
id:"overhead_gl",
maxWidth:90,
minWidth:90,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"237CF86A-48D4-48CD-9DEE-00BE217F479D",
valuelist:null,
visible:true,
width:90
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour_leftJoin.total_total_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.totalAppliedCost",
id:"jcl_total_cost",
maxWidth:120,
minWidth:120,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"2A78B190-82A1-4C91-A77D-6B46A59498A1",
valuelist:null,
visible:true,
width:120
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_material_leftJoin.total_material_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.materialCosts",
id:"jcm_total_cost",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"C5A9E465-3A32-478D-880D-9972D326A2F6",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"getMaterialGLAccountNumber",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.materialGL",
id:"material_gl",
maxWidth:85,
minWidth:85,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"9564533B-0F92-46FC-82E6-D0431D03ED81",
valuelist:null,
visible:true,
width:85
},
{
autoResize:false,
dataprovider:"prod_job_cost_transactions_to_prod_job_cost.prod_job_cost_to_prod_job_cost_purchases_leftJoin.total_purchase_cost",
editType:null,
enableRowGroup:true,
enableSort:true,
format:"{\"converter\":{\"name\":\"GlobalMethodConverter\",\"properties\":{\"fromObjectMethodName\":\"scopes.avUtils.globalConverterObj2DB\",\"toObjectMethodName\":\"scopes.avUtils.globalConverterDB2Obj\",\"type\":\"TEXT\"}}}",
headerStyleClass:"table text-right",
headerTitle:"i18n:avanti.lbl.purchaseCosts",
id:"jobp_total_cost",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"listview text-right",
svyUUID:"EEDC5E48-51A4-4B1F-9E88-86647329539F",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"getPurchaseGLAccountNumber",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.purchaseGL",
id:"purchase_gl",
maxWidth:85,
minWidth:85,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"15C55B1F-303B-4AD7-8B1F-988F6359919B",
valuelist:null,
visible:true,
width:85
},
{
autoResize:false,
dataprovider:"created_date",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.createDate",
id:"created_date",
maxWidth:91,
minWidth:91,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"C767B7BF-97CB-4A26-8CF8-D805F5B380B4",
valuelist:null,
visible:true,
width:91
},
{
autoResize:false,
dataprovider:"jct_transaction_type",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.group.TransactionType",
id:"transactionType",
maxWidth:105,
minWidth:105,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"756F3871-525A-4310-B997-28483DCDBD2A",
valuelist:"95F2BC2D-B4F8-45E4-88A1-0E39C29BF8FB",
visible:true,
width:105
},
{
autoResize:false,
dataprovider:"jct_transaction_status",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.transactionStatus",
id:"transactionStatus",
maxWidth:120,
minWidth:120,
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"7FB9EC8D-26F9-4EE8-93CE-AC3985081125",
valuelist:"6D2C6DFB-E793-48D6-80B6-A0F542A3587F",
visible:true,
width:120
}
],
cssPosition:{
bottom:"3",
height:"128",
left:"0",
right:"0",
top:"110",
width:"2203px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onColumnDataChange:"88402A5B-15E1-4A6D-8DDB-C799113FEAA2",
onReady:"DCD3E76A-38CC-43BA-B4CA-AF95476EFFDB",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:null,
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"5EC9F075-B7DE-40C5-93D9-668DE47C9950"
},
{
cssPosition:"38,0,-1,0,1122,66",
json:{
containedForm:"047763F9-732E-4D95-915C-062250E0B0E3",
cssPosition:{
bottom:"-1",
height:"66",
left:"0",
right:"0",
top:"38",
width:"1122"
}
},
name:"formcontainer_1",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"8D73B257-6102-408F-A23A-F1F84900B164"
},
{
height:106,
partType:1,
typeid:19,
uuid:"A3F1D4A4-76CE-490E-9F14-C49950310868"
},
{
cssPosition:"1,-1,-1,0,950,35",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"35",
left:"0",
right:"-1",
top:"1",
width:"950"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"BA25C33D-36AB-41D8-8CFE-4870892244F8"
},
{
height:242,
partType:5,
typeid:19,
uuid:"D70765F1-9F4E-499E-93D5-DC6B7D6B6664"
}
],
name:"acc_cost_accounting_tbl",
navigatorID:"-1",
onLoadMethodID:"2BC36714-28BB-4B24-9C24-8943227A4125",
onShowMethodID:"DC10A597-B7EF-4F68-8554-5CBBEE7A33DA",
scrollbars:33,
size:"2203,200",
styleName:null,
typeid:3,
uuid:"********-EAC8-461D-B816-************",
view:0