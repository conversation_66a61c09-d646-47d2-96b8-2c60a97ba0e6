/**
 * Click event. dataTarget parameter is used to identify inner html elements (by their data-target attribute).
 *
 * @param {JSEvent} event
 * @param {String} dataTarget
 *
 * @private
 *
 * @properties={typeid:24,uuid:"30D3CC7D-15D1-4488-90FD-0BB111C6E55B"}
 */
function onAction_add(event, dataTarget) {
    if (globals.nav.mode != scopes.avUtils.ENUM_NAV_MODE.Browse) {
    	foundset.newRecord(false, true);
    }
}

/**
 * Called when the columns data is changed.
 *
 * @param {Number} foundsetindex
 * @param {Number} [columnindex]
 * @param [oldvalue]
 * @param [newvalue]
 * @param {JSEvent} [event]
 * @param {JSRecord} [record]
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0A633995-850C-4CED-97FA-35FE032CF8D5"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldvalue, newvalue, event, record) {
	foundset.setSelectedIndex(foundsetindex);
	
	if (columnindex < 0) {
		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col && col.id == "sys_org_language" && sys_org_language) {
		sys_org_language = sys_org_language.toUpperCase(); 
	}
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} [columnindex]
 * @param {JSRecord} [record]
 * @param {JSEvent} [event]
 *
 * @private
 *
 * @properties={typeid:24,uuid:"10C8D91B-33C1-414A-848E-FC61D85E2170"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	
	if (columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);
		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col && col.id == "btnDelete" && globals.nav.mode != scopes.avUtils.ENUM_NAV_MODE.Browse) {
    	foundset.deleteRecord();
	}
}
