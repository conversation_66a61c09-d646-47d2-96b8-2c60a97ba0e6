customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/svy_framework/nav_popmenu",
extendsID:"F92DED23-7E96-4958-98DE-F7144A15A08A",
items:[
{
height:480,
partType:5,
typeid:19,
uuid:"055B62A7-C5B0-4369-8BDB-88284740F945"
},
{
height:255,
partType:8,
typeid:19,
uuid:"45081F34-2A83-494A-AEA6-D7063223CC62"
},
{
extendsID:"386268E2-9F97-4AE9-B3DF-F450E25EE030",
height:250,
typeid:19,
uuid:"5A1A695C-CD6A-4DAA-BEAB-17CE307C1302"
},
{
anchors:15,
cssPosition:"30px,0px,5px,0px,240px,220px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"rpt_submenu_selected",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnSelect",
isEditableDataprovider:"rpt_submenu_enabled",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"C92DB04B-ECE9-482D-A21E-B0C43AF6A394",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:"label",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.rpt_submenu",
id:"fldMenu",
maxWidth:165,
minWidth:165,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"3DA38B47-CE7C-4FA2-9D60-AD54CAEA5963",
valuelist:null,
visible:true,
width:165
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnSort",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined swap_vert",
svyUUID:"2188C0FC-D91E-4099-83CD-FCD868FF8850",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnDelete",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined delete",
svyUUID:"824D3C5B-C721-40E1-B920-29338C926C70",
valuelist:null,
visible:true,
width:25
}
],
cssPosition:{
bottom:"5px",
height:"220px",
left:"0px",
right:"0px",
top:"30px",
width:"240px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"ACAEF39D-328F-479C-80C9-2FE88BD3A1A8",
onColumnDataChange:"9F1B8C64-7172-46E2-8FBF-ACAB46691582",
onReady:"29519ED1-9C67-48B0-ADAC-2A6C098C3751",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"347F7861-F77F-4B7D-92D9-25C929093837"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"67AF77DE-5B1B-4AFE-8FB7-90B1F939BD75"
},
{
cssPosition:"5,-1,-1,25,210,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"25",
right:"-1",
top:"5",
width:"210"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.addSubMenu",
visible:true
},
name:"component_4342D35E",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8D624DA5-A033-4B95-9A25-E3746144DEBD"
},
{
cssPosition:"5,-1,-1,0,24,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"0",
right:"-1",
top:"5",
width:"24"
},
enabled:true,
onActionMethodID:"AB4318D9-00B6-411B-94EF-5788FFB98610",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AD61B055-E374-4DD5-B43F-736409B54C80"
}
],
name:"rpt_report_dtl_tab_subMenus",
navigatorID:"-1",
onShowMethodID:"03FD1F64-56FD-4241-8F5F-3B1EB8411BBE",
paperPrintScale:100,
scrollbars:33,
size:"240,480",
styleClass:"border",
styleName:null,
typeid:3,
uuid:"F1A3EBFB-D0AE-4C72-A023-E0AB39288EC3",
view:0