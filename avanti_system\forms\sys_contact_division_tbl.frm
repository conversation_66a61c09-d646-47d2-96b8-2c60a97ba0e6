customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_contact_division",
extendsID:"8AAFA64F-**************-BAEFDFEB18EA",
items:[
{
height:67,
partType:1,
typeid:19,
uuid:"094DFD39-636A-4FE1-826B-7DCFB27BF67C"
},
{
height:255,
partType:8,
typeid:19,
uuid:"4F4BDD91-5AA1-4A96-93D6-F7E9780E54EF"
},
{
cssPosition:"28,-1,-1,0,951,36",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"36",
left:"0",
right:"-1",
top:"28",
width:"951"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"68CE5E6A-2678-4CD2-AB7E-3C288E148836"
},
{
anchors:15,
cssPosition:"68px,0px,5px,0px,654px,182px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-right",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined arrow_right",
svyUUID:"5564FC19-D0D6-4D2C-95A3-E0C1CEACDCC0",
valuelist:null,
visible:true,
width:20
},
{
autoResize:false,
dataprovider:"contactdiv_desc",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.program.divisionDesc",
id:"contactdiv_desc",
maxWidth:260,
minWidth:260,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"5BEC8F4B-440B-47A0-982F-ECE9745FC260",
valuelist:null,
visible:true,
width:260
}
],
cssPosition:{
bottom:"5px",
height:"182px",
left:"0px",
right:"0px",
top:"68px",
width:"654px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"DEBEB886-6A23-4847-A5B5-C75926D47DAE",
onCellDoubleClick:"10AB2CD5-24B7-44CF-ABCB-68E7E363F0E0",
onReady:"F6625876-F2F5-4A06-A83C-4DACC6327CD2",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:false,
suppressColumnFilter:true,
suppressColumnSelectAll:false,
suppressRowGroups:false,
suppressSideButtons:false,
svyUUID:"6D82B243-9B67-4EA1-8B16-09C86B96AC4B"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"929546AC-7C39-49F1-83D1-D11F1593E876"
},
{
height:250,
partType:5,
typeid:19,
uuid:"B920160C-E6AD-4AB3-8635-232DBD07E36A"
},
{
cssPosition:"0,0,-1,0,654,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"0",
width:"654"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.contactDivisionTableView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BB56F3AE-8CC1-4084-B61E-9455F2F38AA8"
}
],
name:"sys_contact_division_tbl",
navigatorID:"-1",
onShowMethodID:"881906AA-BCED-4785-915D-510A413CCF21",
scrollbars:33,
size:"960,255",
styleName:null,
typeid:3,
uuid:"0F7B943D-DC6B-4880-9767-C096D71671E3",
view:0