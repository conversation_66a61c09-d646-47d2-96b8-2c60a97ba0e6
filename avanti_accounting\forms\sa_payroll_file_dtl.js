/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DFCF6A08-FF31-42CA-8195-D779DEE1134A"}
 */
var _sZipFolder = '';

/**
 * @properties={typeid:35,uuid:"F811F6A2-0B30-4A7C-83EF-C36AF4CCBC7A",variableType:-4}
 */
var _allDataURI = null;

/**
 * @properties={typeid:35,uuid:"A664CCA1-181C-4FD9-B2EA-B31FEC1CB49A",variableType:-4}
 */
var _allDataDS = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F65ABC59-2F72-404A-A4EA-9E6F93A77F1C"}
 */
var _sDelimeter = '';

/**
 * @type {JSFoundset<db:/avanti/sys_payroll_file>}
 *
 * @properties={typeid:35,uuid:"2FE65A9A-291E-4884-A88B-7CA6F58E9152",variableType:-4}
 */
var _fs_sys_payroll_file;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"F804A4F4-74D9-452A-A846-F9E4F8951C0B",variableType:8}
 */
var _iFileIdx;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"700B9CE4-7CF7-48BA-BD89-912F10B63923"}
 */
var _sFileData = '';

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"0C9F6CDD-0B0F-44E4-97AC-A4DB74F13296",variableType:-4}
 */
var _bDryRun = false;

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"742ED4A7-2B8D-4B33-82BE-D05F14D4C117",variableType:-4}
 */
var _bReviewPost = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"032E21C3-6732-4672-97D6-996EF6C5F8ED"}
 */
var _sRecsPostedMsg = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DB353126-8913-4031-AC58-753DB946544E"}
 */
var _sPayPeriodMsg = '';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2FDD6E88-3A8D-4F29-A118-48DEC23CFFA0",variableType:4}
 */
var _iPayPeriods = null;

/**
 * @type {Number}
 * 
 * @properties={typeid:35,uuid:"B5A6FA3F-D99D-4C72-AD85-A4903523B754",variableType:8}
 */
var _nHoursInRegShift = globals.avBase_getSystemPreference_Number(150);

/**
 * @type {Number}
 * 
 * @properties={typeid:35,uuid:"CA876450-5B25-42B5-8284-9EFE8ABD5580",variableType:8}
 */
var _nHoursInRegPayPeriod = globals.avBase_getSystemPreference_Number(151);

/**
 * @type {Number}
 * 
 * @properties={typeid:35,uuid:"0891B236-35B2-4218-B5AB-617B3D24EA97",variableType:8}
 */
var _nNumDaysPerPayPeriod = globals.avBase_getSystemPreference_Number(152);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"0E18F1DF-FDB1-43E7-A23C-0A557A6E2729"}
 */
var _sOvertimeCalcType = globals.avBase_getSystemPreference_String(153);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"23953DE8-7DF5-43CC-A69D-45952C700F82"}
 */
var _sVacationExceptionUUID = globals.avBase_getSystemPreference_String(154);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"0103A047-CA72-4491-82D8-67A9C7FDB244"}
 */
var _sUnpaidTimeOffExceptionUUID = globals.avBase_getSystemPreference_String(155);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"92555D71-4A4E-4FB9-88CF-C3B36C537844"}
 */
var _sSickTimeExceptionUUID = globals.avBase_getSystemPreference_String(156);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"674E30A7-3B34-4ACF-B758-55EADEA460F6"}
 */
var _sLeavePaidExceptionUUID = globals.avBase_getSystemPreference_String(157);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"846D282C-7B12-460C-A444-7439828717E4"}
 */
var _sLeaveUnpaidExceptionUUID = globals.avBase_getSystemPreference_String(158);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"9502F38A-1CAF-454C-BFFD-944D3AFA8AE5"}
 */
var _sHolidayExceptionUUID = globals.avBase_getSystemPreference_String(159);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"F8C9602B-EF58-4880-AC3D-BDB9BCB69943"}
 */
var _sLunchCostCenterUUID = globals.avBase_getSystemPreference_String(160);

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"9FCCA9CF-63E6-4301-9885-E6B163A4AECD"}
 */
var _sBreakCostCenterUUID = globals.avBase_getSystemPreference_String(161);

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"30460C76-B7BA-4438-91EF-7E8199D97707"}
 */
function btnPost_onAction(event){
	_bDryRun = false;
	_bReviewPost = false;
	postRecs();
	forms.sa_payroll_file_tbl.setToolBarOptions();
	elements.btnPost.text = i18n.getI18NMessage('avanti.lbl.repost');
}

/**
 * @return {JSFoundset<db:/avanti/sys_payroll_file>}
 * 
 * @properties={typeid:24,uuid:"E488E770-98A5-4A0C-9C32-787A9E409786"}
 */
function getPRFilesToProcess(){
	/**@type {JSFoundset<db:/avanti/sys_payroll_file>} */
	var fsPRFs = null;
	
	if(utils.hasRecords(sa_payroll_batch_to_sa_payroll_batch_pr_file)){
		var aFileIDs = [];
		
		for(var i=1; i<=sa_payroll_batch_to_sa_payroll_batch_pr_file.getSize(); i++){
			var rPRF = sa_payroll_batch_to_sa_payroll_batch_pr_file.getRecord(i);
			
			if(rPRF.prbprf_is_selected == 1){
				aFileIDs.push(rPRF.prf_id);
			}
		}
		
		if(aFileIDs.length > 0){
			fsPRFs = scopes.avDB.getFSWhereColInValues('sys_payroll_file', 'prf_id', aFileIDs);
		}
	}
	
	return fsPRFs;
}

/**
 * @properties={typeid:24,uuid:"23D3F7CA-240C-4631-8814-ED60EF34544C"}
 */
function postRecs(){
	
	if(!foundset.prb_from_date || !foundset.prb_to_date){
		scopes.avText.showWarning('dateRangeException');
	}	
	else if(!prb_num_recs && !_bDryRun){
		scopes.avText.showWarning('nothingToPost');
	}
	else{
		
		if(!_bDryRun && !_bReviewPost){
			foundset.prb_date_posted = application.getTimeStamp();
			foundset.prb_posted_by = globals.avBase_employeeUUID;
			databaseManager.saveData(foundset);
		}
		
		_fs_sys_payroll_file = getPRFilesToProcess();
		var iNumRecs = _fs_sys_payroll_file == null ? 0 : _fs_sys_payroll_file.getSize();

		if (iNumRecs == 0) {
			scopes.avText.showWarning('noPRFiles');
		} 
		else {
			if(_bDryRun){
				scopes.avDB.deleteRecs('sa_payroll_batch_detail', ['prb_id'], [prb_id]) // clear out sa_payroll_batch_detail for this batch - it will be refilled
			}
			
			var iNumRecsPosted = 0

			if(!_bDryRun && !_bReviewPost){
				foundset.prb_date_posted = application.getTimeStamp();
				foundset.prb_posted_by = globals.avBase_employeeUUID
				databaseManager.saveData(foundset)
			}

			var rec_sys_payroll_file;
			var nNumDownloads = 0;
			_sZipFolder = null;
			
			for (_iFileIdx = 1; _iFileIdx <= iNumRecs; _iFileIdx++) {
				rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx)
				if (rec_sys_payroll_file.prf_file_placement_options == 'Download') {
					nNumDownloads++;
					if(nNumDownloads > 1){
						break;
					}
				}
			}
			
			for (_iFileIdx = 1; _iFileIdx <= iNumRecs; _iFileIdx++) {
				rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx)
				if (rec_sys_payroll_file.prf_active && initializePRFile(rec_sys_payroll_file)) {
					var bZipFile = rec_sys_payroll_file.prf_file_placement_options == 'Download' && nNumDownloads > 1;
					iNumRecsPosted = postFile(bZipFile)
				}
			}

			if(nNumDownloads > 1 && _sZipFolder){
				plugins.it2be_tools.zip(_sZipFolder);
				forms._docs_base.getFileFromServer(_sZipFolder + '.zip');
				plugins.file.deleteFolder(_sZipFolder, false);	
				plugins.file.deleteFile(_sZipFolder + '.zip');
				_sZipFolder = null;
			}
			
			foundset.prb_num_recs = iNumRecsPosted 

			if(!_bDryRun && !_bReviewPost){
				if(iNumRecsPosted>0){
					_sRecsPostedMsg = i18n.getI18NMessage('avanti.lbl.recordsPosted') + ' *'
					scopes.avText.showInfo(iNumRecsPosted + i18n.getI18NMessage('avanti.dialog.recordsHaveBeenPosted'), true);
				}
				else{
					// clear these - they were set in case they were need for the file
					foundset.prb_date_posted=null
					foundset.prb_posted_by = null
					scopes.avText.showInfo('noRecsToPost');
				}
			}

			refreshUI()
		}
	}	
}

/**
 * @param {Boolean} bZipFile
 * 
 * @return
 * @properties={typeid:24,uuid:"7141EDDB-D26D-4D08-A5BB-947FF6DA0500"}
 */
function postFile(bZipFile){
	var iNumRecs = 0 ;
	// RG 2014-10-16 SL-3355 check if null set to '', otherwise leave
	if ( ! _sFileData ) {
		_sFileData = '';
	}
	var rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx);
	
	setPayrollPreferences();

	/**@type {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_type:String}>} */
	var fsAllData = getAllDataFoundSet(rec_sys_payroll_file.prf_name);
	if(fsAllData == null) { 
		return;
	}
	
	fsAllData.loadAllRecords();
	iNumRecs = databaseManager.getFoundSetCount(fsAllData);
	
	
	//By Time Type
	if(rec_sys_payroll_file.prf_detail_level == 'T'){
		processDataByTimeType(fsAllData);
	}
	//By Employee
	else if(rec_sys_payroll_file.prf_detail_level == 'E'){
		processDataByEmployee(fsAllData);
	}
	
	if(iNumRecs>0 && !_bDryRun && !_bReviewPost){
		closePRFile(bZipFile);
	}
	
	return iNumRecs
}

/**
 * @return {JSFoundSet}
 * 
 * @param {String} sPayrollName
 * 
 * @properties={typeid:24,uuid:"42E48118-840D-48B3-8CEE-15C12D16E18A"}
 * @AllowToRunInFind
 */
function getAllDataFoundSet(sPayrollName) {
	/** @type {Number} */
	var i = 0;
	/** @type {String} */
	var sINClause = "";
	
	//Get the exception data
	/**@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	if(_bDryRun){
		oSQL.sql = "select ee.empschexcep_id , e.empl_code, ec.emplclass_code, et.exceptype_code, DATEDIFF(minute, empschexcep_start, empschexcep_end) / 60.0 as hours, \
					CONVERT(date, empschexcep_start) as start_date, DATEADD(ss,datediff(ss,empschexcep_start, empschexcep_end),CAST('00:00:00' AS TIME)) as diff \
					, case when et.exceptype_id = ? then 'UPTO' \
						when et.exceptype_id = ? then 'VAC' \
						when et.exceptype_id = ? then 'SICK' \
						when et.exceptype_id = ? then 'LP' \
						when et.exceptype_id = ? then 'LU' \
						when et.exceptype_id = ? then 'HOL' \
						else et.exceptype_code end as payroll_exceptype_code \
					from sch_empl_excep ee \
					join sys_employee e on e.empl_id = ee.empl_id \
					join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
					join sch_excep_types et on et.exceptype_id = ee.exceptype_id \
					where empschexcep_start between ? and ? \
					and ee.posted_date is null and ee.posted_by_id is null \
					and ee.org_id = ?";
		oSQL.args = [ _sUnpaidTimeOffExceptionUUID
					  , _sUnpaidTimeOffExceptionUUID
					  , _sSickTimeExceptionUUID
					  , _sLeavePaidExceptionUUID
					  , _sLeaveUnpaidExceptionUUID
					  , _sHolidayExceptionUUID
					  , foundset.prb_from_date
					  , plugins.DateUtils.addDays(foundset.prb_to_date, 1)
					  , globals.org_id
					];
	} else {
		oSQL.args = [];
		
		oSQL.args.push(_sUnpaidTimeOffExceptionUUID);
		oSQL.args.push(_sVacationExceptionUUID);
		oSQL.args.push(_sSickTimeExceptionUUID);
		oSQL.args.push(_sLeavePaidExceptionUUID);
		oSQL.args.push(_sLeaveUnpaidExceptionUUID);
		oSQL.args.push(_sHolidayExceptionUUID);
		
		var sBatchExceptionIDs = getBatchExceptionIDs();
		
		sINClause = "in (";
		var aBatchExceptionIDs = sBatchExceptionIDs.split(",");
		
		for(i = 0; i < aBatchExceptionIDs.length; i++){
			i == 0 ? sINClause += "?" : sINClause += ",?";
			oSQL.args.push( aBatchExceptionIDs[i] == '' ? null : aBatchExceptionIDs[i] );
		}
		sINClause += ")";
		
		oSQL.sql = "select e.empl_code, ec.emplclass_code, et.exceptype_code, DATEDIFF(minute, empschexcep_start, empschexcep_end) / 60.0 as hours, \
			CONVERT(date, empschexcep_start) as start_date, DATEADD(ss,datediff(ss,empschexcep_start, empschexcep_end),CAST('00:00:00' AS TIME)) as diff \
			, case when et.exceptype_id = ? then 'UPTO' \
				when et.exceptype_id = ? then 'VAC' \
				when et.exceptype_id = ? then 'SICK' \
				when et.exceptype_id = ? then 'LP' \
				when et.exceptype_id = ? then 'LU' \
				when et.exceptype_id = ? then 'HOL' \
				else et.exceptype_code end as payroll_exceptype_code \
			from sch_empl_excep ee \
			join sys_employee e on e.empl_id = ee.empl_id \
			join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
			join sch_excep_types et on et.exceptype_id = ee.exceptype_id \
			where ee.empschexcep_id " + sINClause + 
			" and ee.org_id = ?";
	
		oSQL.args.push(globals.org_id);
	}
		
	var dsExceptionData = globals.avUtilities_sqlDataset(oSQL);
	
	//Get the shift data
	if(_bDryRun){
		//if round shift time is turned on, use posted date time fields
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RoundSartAndEndShiftTime) == 1) {
			oSQL.sql = "select es.sys_employee_shift_id, e.empl_code, ec.emplclass_code, 'REG' as exceptype_code, DATEDIFF(minute, ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime))/60.0 as hours, \
				CONVERT(date, ISNULL(posted_start_datetime, start_datetime)) as start_date, DATEADD(ss,datediff(ss,ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime)),CAST('00:00:00' AS TIME)) as diff, 'REG' as payroll_exceptype_code \
				, ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime) \
				from sys_employee_shift es \
				join sys_employee e on e.empl_id = es.empl_id \
				join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
				where ISNULL(posted_start_datetime, start_datetime) between ? and ? \
				and es.posted_date is null and es.posted_by_id is null \
				and es.org_id = ?";
		}
		else {
			oSQL.sql = "select es.sys_employee_shift_id, e.empl_code, ec.emplclass_code, 'REG' as exceptype_code, DATEDIFF(minute, start_datetime, end_datetime)/60.0 as hours, \
				CONVERT(date, start_datetime) as start_date, DATEADD(ss,datediff(ss,start_datetime, end_datetime),CAST('00:00:00' AS TIME)) as diff, 'REG' as payroll_exceptype_code \
				, start_datetime, end_datetime \
				from sys_employee_shift es \
				join sys_employee e on e.empl_id = es.empl_id \
				join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
				where start_datetime between ? and ? \
				and es.posted_date is null and es.posted_by_id is null \
				and es.org_id = ?";
		}
		
		oSQL.args = [foundset.prb_from_date, plugins.DateUtils.addDays(foundset.prb_to_date, 1), globals.org_id];
	} else {
		
		oSQL.args = [];
		
		sINClause = "in (";
		var sBatchShiftIDs = getBatchShiftIDs();
		var aBatchShiftIDs = sBatchShiftIDs.split(",");
		
		for(i = 0; i < aBatchShiftIDs.length; i++){
			i == 0 ? sINClause += "?" : sINClause += ",?";
			oSQL.args.push(aBatchShiftIDs[i] == '' ? null : aBatchShiftIDs[i]);
		}
		sINClause += ")";
		
		//if round shift time is turned on, use posted date time fields
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RoundSartAndEndShiftTime) == 1) {
			oSQL.sql = "select e.empl_code, ec.emplclass_code, 'REG' as exceptype_code, DATEDIFF(minute, ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime))/60.0 as hours, \
				CONVERT(date, ISNULL(posted_start_datetime, start_datetime)) as start_date, DATEADD(ss,datediff(ss,ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime)),CAST('00:00:00' AS TIME)) as diff, 'REG' as payroll_exceptype_code \
				, ISNULL(posted_start_datetime, start_datetime), ISNULL(posted_end_datetime, end_datetime) \
				from sys_employee_shift es \
				join sys_employee e on e.empl_id = es.empl_id \
				join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
				where es.sys_employee_shift_id " + sINClause + 
				" and es.org_id = ?";
		}
		else {
			oSQL.sql = "select e.empl_code, ec.emplclass_code, 'REG' as exceptype_code, DATEDIFF(minute, start_datetime, end_datetime)/60.0 as hours, \
				CONVERT(date, start_datetime) as start_date, DATEADD(ss,datediff(ss,start_datetime, end_datetime),CAST('00:00:00' AS TIME)) as diff, 'REG' as payroll_exceptype_code \
				, start_datetime, end_datetime \
				from sys_employee_shift es \
				join sys_employee e on e.empl_id = es.empl_id \
				join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
				where es.sys_employee_shift_id " + sINClause + 
				" and es.org_id = ?";
		}
		
		oSQL.args.push(globals.org_id);
	}
	
	var dsShiftData = globals.avUtilities_sqlDataset(oSQL);
	
	//Get Break and Lunch entries
	if(_bDryRun){
		oSQL.sql = "select jcl.jcl_id, e.empl_code, ec.emplclass_code \
					, case when jc.cc_id = ? then 'LUNCH' \
					       when jc.cc_id = ? then 'BREAK' \
					       else '' \
					  end as exceptype_code \
					, DATEDIFF(minute, jcl_start_datetime, jcl_end_datetime)/60.0 as hours \
					, CONVERT(date, jcl_start_datetime) as start_date, DATEADD(ss,datediff(ss,jcl_start_datetime, jcl_end_datetime), CAST('00:00:00' AS TIME)) as diff \
					from prod_job_cost_labour jcl \
					join prod_job_cost jc on jcl.jc_id = jc.jc_id \
					join sys_employee e on e.empl_id = jc.empl_id \
					join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
					where jc.cc_id in (?,?) \
					and jcl_start_datetime between ? and ? \
					and jcl.jcl_payroll_posted_date is null and jcl.jcl_payroll_posted_by_id is null \
					and jcl.org_id = ?";
		oSQL.args = [_sLunchCostCenterUUID, _sBreakCostCenterUUID, _sLunchCostCenterUUID, _sBreakCostCenterUUID, foundset.prb_from_date, plugins.DateUtils.addDays(foundset.prb_to_date, 1), globals.org_id];
	} else {
		
		oSQL.args = [];
		
		oSQL.args.push(_sLunchCostCenterUUID);
		oSQL.args.push(_sBreakCostCenterUUID);
		
        sINClause = "in (";
		var sBatchJCLIDs = getBatchJCLIDs();
		var aBatchJCLIDs = sBatchJCLIDs.split(",");
		
		for(i = 0; i < aBatchJCLIDs.length; i++) {
		    i == 0 ? sINClause += "?" : sINClause += ",?";
		    oSQL.args.push( aBatchJCLIDs[i] == '' ? null : aBatchJCLIDs[i]);
		}
		sINClause += ")";
		
		oSQL.sql = "select e.empl_code, ec.emplclass_code \
					, case when jc.cc_id = ? then 'LUNCH' \
					       when jc.cc_id = ? then 'BREAK' \
					       else '' \
					  end as exceptype_code \
					, DATEDIFF(minute, jcl_start_datetime, jcl_end_datetime)/60.0 as hours \
					, CONVERT(date, jcl_start_datetime) as start_date, DATEADD(ss,datediff(ss,jcl_start_datetime, jcl_end_datetime), CAST('00:00:00' AS TIME)) as diff \
					from prod_job_cost_labour jcl \
					join prod_job_cost jc on jcl.jc_id = jc.jc_id \
					join sys_employee e on e.empl_id = jc.empl_id \
					join sys_employee_class ec on ec.emplclass_id = e.emplclass_id \
					where jcl.jcl_id " + sINClause + 
					" and jcl.org_id = ?";
		oSQL.args.push(globals.org_id);
	}
	
	var dsBreakAndLunchData = globals.avUtilities_sqlDataset(oSQL);
	
	if(_bDryRun){
		//add to batch detail
		addExceptionsToBatchDetail(dsExceptionData);
		addShiftsToBatchDetail(dsShiftData);
		addJCLToBatchDetail(dsBreakAndLunchData);
		dsExceptionData.removeColumn(1);
		dsShiftData.removeColumn(1);
		
		if (dsBreakAndLunchData) {
			dsBreakAndLunchData.removeColumn(1);
		}
	} else if(!_bDryRun && !_bReviewPost) {
		//mark posted
		markDataPosted();
	}	
	
	var dsAllData = databaseManager.createEmptyDataSet(0,['empl_code', 'emplclass_code', 'time_type', 'hours', 'start_date', 'diff', 'payroll_time_type', 'start_datetime','end_datetime', 'diff_text']);
	
	//Merge exceptions into all data dataset
	if (dsExceptionData) {
		for (i = 1; i <= dsExceptionData.getMaxRowIndex(); i++) {
			dsAllData.addRow(dsExceptionData.getRowAsArray(i));
		}
	}
	
	//Merge shifts into all data dataset
	for(i = 1; i <= dsShiftData.getMaxRowIndex(); i++){
		dsAllData.addRow(dsShiftData.getRowAsArray(i));
	}
	
	//sort the all data by empl_code for processing
	//['empl_code', 'emplclass_code', 'time_type', 'hours', 'start_date', 'diff']
	var uri = dsAllData.createDataSource("_dsAllData",[JSColumn.TEXT,JSColumn.TEXT,JSColumn.TEXT,JSColumn.NUMBER,JSColumn.DATETIME, JSColumn.DATETIME, JSColumn.TEXT, JSColumn.DATETIME, JSColumn.DATETIME, JSColumn.TEXT]);
	_allDataURI = uri;
	_allDataDS = dsAllData;
	
	/** @type {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_typ:String, diff_text:String}>} */
	var fsAllData = databaseManager.getFoundSet(uri);
	
	//Remove Breaks and Lunches from Regular hours
	if (dsBreakAndLunchData) {
		for (i = 1; i <= dsBreakAndLunchData.getMaxRowIndex(); i++) {
			if (fsAllData.find()) {
				fsAllData.empl_code = dsBreakAndLunchData.getValue(i, 1);
				fsAllData.time_type = "REG"
				fsAllData.start_date = dsBreakAndLunchData.getValue(i, 5);

				if (fsAllData.search() > 0) {
					fsAllData.hours = fsAllData.hours - dsBreakAndLunchData.getValue(i, 4);

					databaseManager.saveData(fsAllData);
				}
			}
		}
	}
	
	var sTime = "",
	    nTotalMins = 0,
	    nHrs = 0,
	    nMins = 0;
	for (i = 1; i <= _allDataDS.getMaxRowIndex(); i++) {
	    if (_allDataDS.getValue(i, 8) && _allDataDS.getValue(i, 9)) {
	        nTotalMins = Math.floor((new Date(_allDataDS.getValue(i, 9)) - new Date(_allDataDS.getValue(i, 8))) / 60000);
	        nHrs = Math.floor(nTotalMins/60);
	        nMins = Math.floor(nTotalMins - (nHrs * 60));
    	    sTime =  scopes.avUtils.padNumber(nHrs, 2, "0") + ":" + scopes.avUtils.padNumber(nMins, 2, "0");
    	    _allDataDS.setValue(i, 10, sTime);
	    }
	}

	return fsAllData;
}

/**
 * @properties={typeid:24,uuid:"7B7014AA-FAAC-481F-8953-0A821D7D7CFF"}
 */
function setPayrollPreferences(){
	_nHoursInRegShift = globals.avBase_getSystemPreference_Number(150);
	_nHoursInRegPayPeriod = globals.avBase_getSystemPreference_Number(151);
	_nNumDaysPerPayPeriod = globals.avBase_getSystemPreference_Number(152);
	_sOvertimeCalcType = globals.avBase_getSystemPreference_String(153);
	_sVacationExceptionUUID = globals.avBase_getSystemPreference_String(154);
	_sUnpaidTimeOffExceptionUUID = globals.avBase_getSystemPreference_String(155)
	_sSickTimeExceptionUUID = globals.avBase_getSystemPreference_String(156);
	_sLeavePaidExceptionUUID = globals.avBase_getSystemPreference_String(157);
	_sLeaveUnpaidExceptionUUID = globals.avBase_getSystemPreference_String(158);
	_sHolidayExceptionUUID = globals.avBase_getSystemPreference_String(159);
	_sLunchCostCenterUUID = globals.avBase_getSystemPreference_String(160);
	_sBreakCostCenterUUID = globals.avBase_getSystemPreference_String(161);
}

/**
 * @param {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_type:String}>} fsAllData
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"F5737F15-0893-44D5-AC32-594FBC339C2B"}
 */
function processDataByTimeType(fsAllData) {
	/** @type {Number} */
	var overTimeHours = 0;
	/** @type {Number} */
	var hoursForPeriod = 0;
	/** @type {Number} */
	var overtimeHoursForPeriod = 0;
	/** @type {Date} */
	var curShiftDate = null;
	/** @type {Date} */
	var curPeriodEndDate = null;
	/** @type {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_type:String}>} */
	var fsCurEmpData = null;
	/** @type {Number} */
	var nHoursForDay = 0;
	/** @type {Number} */
	var j = 0;
	/** @type {Number} */
	var foundCount = 0;
	

	fsAllData.loadAllRecords();
	fsAllData.sort("empl_code asc, payroll_time_type asc, start_date asc");

	var dsExportData = databaseManager.createEmptyDataSet(0,['empl_code', 'emplclass_code', 'time_type', 'hours']);
	var uri = dsExportData.createDataSource("_dsExport",[JSColumn.TEXT,JSColumn.TEXT,JSColumn.TEXT,JSColumn.NUMBER]);
	/** @type {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number}>} */
	var fsExportData = databaseManager.getFoundSet(uri);
	
	var prevEmpl = null;
	var prevTimeType = null;
	//Loop over all the data to generate the export dataset
	for(var i = 1; i <= fsAllData.getSize(); i++){

		fsAllData.setSelectedIndex(i);
		
		//new employee
		if(prevEmpl != fsAllData.empl_code){
			curShiftDate = fsAllData.start_date;
			fsExportData.newRecord();
			fsExportData.empl_code = fsAllData.empl_code;
			fsExportData.emplclass_code = fsAllData.emplclass_code;
			fsExportData.time_type = fsAllData.time_type;
			
			//If type is REG and overtime is by shift  
			if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Shift"){
				//need to check for multiple shifts in the same day
				fsCurEmpData = fsAllData.duplicateFoundSet();
				if(fsCurEmpData.find()){
					fsCurEmpData.empl_code = fsAllData.empl_code;
					fsCurEmpData.emplclass_code = fsAllData.emplclass_code; 
					fsCurEmpData.time_type = fsAllData.time_type;
					fsCurEmpData.start_date = fsAllData.start_date;
					
					foundCount = fsCurEmpData.search();
					
					//duplicate shifts in a day
					if(foundCount > 1){
						nHoursForDay = 0;

						for(j = 1; j <= foundCount; j++){
							fsCurEmpData.setSelectedIndex(j);
							nHoursForDay += fsCurEmpData.hours;
						}
						
						if(nHoursForDay > _nHoursInRegShift){
							overTimeHours = nHoursForDay - _nHoursInRegShift;
							fsExportData.hours += _nHoursInRegShift;
							
							//Add the overtime hours
							fsExportData.newRecord();
							fsExportData.empl_code = fsAllData.empl_code;
							fsExportData.emplclass_code = fsAllData.emplclass_code;
							fsExportData.time_type = "OT";
							fsExportData.hours = overTimeHours;
								
						} else {
							fsExportData.hours += nHoursForDay;
						}
					//a single shift in the day
					} else if(foundCount == 1){
						overTimeHours = 0;
						if(fsAllData.hours > _nHoursInRegShift){
							overTimeHours = fsAllData.hours - _nHoursInRegShift;
							fsExportData.hours += _nHoursInRegShift;
							
							//Add the overtime hours
							fsExportData.newRecord();
							fsExportData.empl_code = fsAllData.empl_code;
							fsExportData.emplclass_code = fsAllData.emplclass_code;
							fsExportData.time_type = "OT";
							fsExportData.hours = overTimeHours;
						} else {
							fsExportData.hours += fsAllData.hours;
						}
						curShiftDate = fsAllData.start_date;
					}
				}
			//If type is REG and overtime is by pay period	
			} else if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Pay Period"){
				curPeriodEndDate = plugins.DateUtils.addDays(foundset.prb_from_date, _nNumDaysPerPayPeriod);
				//set the correct period end date
				while(fsAllData.start_date >= curPeriodEndDate && curPeriodEndDate < foundset.prb_to_date){
					curPeriodEndDate = plugins.DateUtils.addDays(curPeriodEndDate,_nNumDaysPerPayPeriod);
					hoursForPeriod = 0;
					overtimeHoursForPeriod = 0;
				}
				
				if(fsAllData.hours > _nHoursInRegPayPeriod){
					overtimeHoursForPeriod = fsAllData.hours - _nHoursInRegPayPeriod;
					fsExportData.hours = _nHoursInRegPayPeriod;
					hoursForPeriod = _nHoursInRegPayPeriod;
					
					fsExportData.newRecord();
					fsExportData.empl_code = fsAllData.empl_code;
					fsExportData.emplclass_code = fsAllData.emplclass_code;
					fsExportData.time_type = "OT";
					fsExportData.hours = overtimeHoursForPeriod;
				} else {
					hoursForPeriod += fsAllData.hours;
					fsExportData.hours = fsAllData.hours;
				}
			} else {
				fsExportData.hours = fsAllData.hours;
			}
		//new time type	
		} else if(prevTimeType != fsAllData.time_type){
			curShiftDate = fsAllData.start_date;
			fsExportData.newRecord();
			fsExportData.empl_code = fsAllData.empl_code;
			fsExportData.emplclass_code = fsAllData.emplclass_code;
			fsExportData.time_type = fsAllData.time_type;
			
			//If type is REG and overtime is by shift  
			if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Shift"){
				//need to check for multiple shifts in the same day
				fsCurEmpData = fsAllData.duplicateFoundSet();
				if(fsCurEmpData.find()){
					fsCurEmpData.empl_code = fsAllData.empl_code;
					fsCurEmpData.emplclass_code = fsAllData.emplclass_code; 
					fsCurEmpData.time_type = fsAllData.time_type;
					fsCurEmpData.start_date = fsAllData.start_date;
					
					foundCount = fsCurEmpData.search();
					
					//duplicate shifts in a day
					if(foundCount > 1){
						nHoursForDay = 0;

						for(j = 1; j <= foundCount; j++){
							fsCurEmpData.setSelectedIndex(j);
							nHoursForDay += fsCurEmpData.hours;
						}
						
						if(nHoursForDay > _nHoursInRegShift){
							overTimeHours = nHoursForDay - _nHoursInRegShift;
							fsExportData.hours += _nHoursInRegShift;
							
							//Add the overtime hours
							fsExportData.newRecord();
							fsExportData.empl_code = fsAllData.empl_code;
							fsExportData.emplclass_code = fsAllData.emplclass_code;
							fsExportData.time_type = "OT";
							fsExportData.hours = overTimeHours;
								
						} else {
							fsExportData.hours += nHoursForDay;
						}
					//a single shift in the day
					} else if(foundCount == 1){
						overTimeHours = 0;
						if(fsAllData.hours > _nHoursInRegShift){
							overTimeHours = fsAllData.hours - _nHoursInRegShift;
							fsExportData.hours += _nHoursInRegShift;
							
							//Add the overtime hours
							fsExportData.newRecord();
							fsExportData.empl_code = fsAllData.empl_code;
							fsExportData.emplclass_code = fsAllData.emplclass_code;
							fsExportData.time_type = "OT";
							fsExportData.hours = overTimeHours;
						} else {
							fsExportData.hours += fsAllData.hours;
						}
						curShiftDate = fsAllData.start_date;
					}
				}
			//If type is REG and overtime is by pay period	
			} else if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Pay Period"){
				curPeriodEndDate = plugins.DateUtils.addDays(foundset.prb_from_date, _nNumDaysPerPayPeriod);
				//set the correct period end date
				while(fsAllData.start_date >= curPeriodEndDate && curPeriodEndDate < foundset.prb_to_date){
					curPeriodEndDate = plugins.DateUtils.addDays(curPeriodEndDate,_nNumDaysPerPayPeriod);
					hoursForPeriod = 0;
					overtimeHoursForPeriod = 0;
				}
				
				if(fsAllData.hours > _nHoursInRegPayPeriod){
					overtimeHoursForPeriod = fsAllData.hours - _nHoursInRegPayPeriod;
					fsExportData.hours = _nHoursInRegPayPeriod;
					hoursForPeriod = _nHoursInRegPayPeriod;
					
					fsExportData.newRecord();
					fsExportData.empl_code = fsAllData.empl_code;
					fsExportData.emplclass_code = fsAllData.emplclass_code;
					fsExportData.time_type = "OT";
					fsExportData.hours = overtimeHoursForPeriod;
				} else {
					hoursForPeriod += fsAllData.hours;
					fsExportData.hours = fsAllData.hours;
				}
			} else {
				fsExportData.hours = fsAllData.hours;
			}
		//add to existing
		} else {
			if(fsExportData.find()){
				fsExportData.empl_code = fsAllData.empl_code;
				fsExportData.time_type = fsAllData.time_type;
				
				if(fsExportData.search() == 1){
					//If type is REG and overtime is by shift  
					if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Shift"){
						
						//need to check for multiple shifts in the same day
						fsCurEmpData = fsAllData.duplicateFoundSet();
						if(fsCurEmpData.find()){
							fsCurEmpData.empl_code = fsAllData.empl_code;
							fsCurEmpData.emplclass_code = fsAllData.emplclass_code; 
							fsCurEmpData.time_type = fsAllData.time_type;
							fsCurEmpData.start_date = fsAllData.start_date;
							
							foundCount = fsCurEmpData.search();
							
							//duplicate shifts in a day
							if(foundCount > 1){
								//We only want to run this once per day
								if(curShiftDate != fsAllData.start_date){
									nHoursForDay = 0;
	
									for(j = 1; j <= foundCount; j++){
										fsCurEmpData.setSelectedIndex(j);
										nHoursForDay += fsCurEmpData.hours;
									}
									
									if(nHoursForDay > _nHoursInRegShift){
										overTimeHours = nHoursForDay - _nHoursInRegShift;
										fsExportData.hours += _nHoursInRegShift;
										
										//Add the overtime hours
										//Might already have an OT entry so need to check first
										if(fsExportData.find()){
											fsExportData.empl_code = fsAllData.empl_code;
											fsExportData.time_type = "OT";
											
											if(fsExportData.search() == 1){
												fsExportData.hours += overTimeHours;
											} else {
												fsExportData.newRecord();
												fsExportData.empl_code = fsAllData.empl_code;
												fsExportData.emplclass_code = fsAllData.emplclass_code;
												fsExportData.time_type = "OT";
												fsExportData.hours = overTimeHours;
											}
										}
									} else {
										fsExportData.hours += nHoursForDay;
									}
								}
								curShiftDate = fsAllData.start_date;
							//a single shift in the day
							} else if(foundCount == 1){
								overTimeHours = 0;
								if(fsAllData.hours > _nHoursInRegShift){
									overTimeHours = fsAllData.hours - _nHoursInRegShift;
									fsExportData.hours += _nHoursInRegShift;
									
									//Add the overtime hours
									//Might already have an OT entry so need to check first
									if(fsExportData.find()){
										fsExportData.empl_code = fsAllData.empl_code;
										fsExportData.time_type = "OT";
										
										if(fsExportData.search() == 1){
											fsExportData.hours += overTimeHours;
										} else {
											fsExportData.newRecord();
											fsExportData.empl_code = fsAllData.empl_code;
											fsExportData.emplclass_code = fsAllData.emplclass_code;
											fsExportData.time_type = "OT";
											fsExportData.hours = overTimeHours;
										}
									}
								} else {
									fsExportData.hours += fsAllData.hours;
								}
								curShiftDate = fsAllData.start_date;
							}
						}
					//If type is REG and overtime is by pay period	
					} else if(fsAllData.payroll_time_type == "REG" && _sOvertimeCalcType == "By Pay Period") {
	
						//set the correct period end date
						while(fsAllData.start_date >= curPeriodEndDate && curPeriodEndDate < foundset.prb_to_date){
							curPeriodEndDate = plugins.DateUtils.addDays(curPeriodEndDate,_nNumDaysPerPayPeriod);
							hoursForPeriod = 0;
							overtimeHoursForPeriod = 0;
						}
						
						if(overtimeHoursForPeriod > 0){
							if(fsExportData.find()){
								fsExportData.empl_code = fsAllData.empl_code;
								fsExportData.time_type = "OT";
								
								if(fsExportData.search() == 1){
									overtimeHoursForPeriod += fsAllData.hours; 
									fsExportData.hours = overtimeHoursForPeriod;
								}
							}	
						} else if((hoursForPeriod + fsAllData.hours) > _nHoursInRegPayPeriod) {
							overtimeHoursForPeriod = (hoursForPeriod + fsAllData.hours) - _nHoursInRegPayPeriod;
							fsExportData.hours = _nHoursInRegPayPeriod;
							hoursForPeriod = _nHoursInRegPayPeriod;
							
							fsExportData.newRecord();
							fsExportData.empl_code = fsAllData.empl_code;
							fsExportData.emplclass_code = fsAllData.emplclass_code;
							fsExportData.time_type = "OT";
							fsExportData.hours = overtimeHoursForPeriod;
						} else {
							hoursForPeriod += fsAllData.hours;
							fsExportData.hours += fsAllData.hours;
						}
					} else {
						fsExportData.hours += fsAllData.hours;
					}
				}
			}
		}
		prevEmpl = fsAllData.empl_code;
		prevTimeType = fsAllData.time_type;
	}
	
	fsExportData.loadAllRecords();
	fsExportData.sort('empl_code asc');

	//apply sorts
	fsExportData.sort(getSortString());
	//write to file
	writeRecToPRFile(fsExportData);
}

/**
 * @AllowToRunInFind
 * @param {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_type:String}>} fsAllData  
 *
 * @properties={typeid:24,uuid:"EDBAB5AF-8146-4E50-9E66-5200C762A57D"}
 */
function processDataByEmployee(fsAllData) {
	/** @type {Number} */
	var overTimeHours = 0;
	/** @type {Number} */
	var hoursForPeriod = 0;
	/** @type {Number} */
	var overtimeHoursForPeriod = 0;
	/** @type {Date} */
	var curShiftDate = null;
	/** @type {Date} */
	var curPeriodEndDate = null;
	/** @type {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number, start_date:Date, diff:Date, payroll_time_type:String}>} */
	var fsCurEmpData = null;
	/** @type {Number} */
	var nHoursForDay = 0;
	/** @type {Number} */
	var j = 0;
	

	fsAllData.loadAllRecords();
	fsAllData.sort("empl_code asc, payroll_time_type asc, start_date asc");

	var dsExportData = databaseManager.createEmptyDataSet(0,['empl_code', 'emplclass_code', 'reg_hours', 'ot_hours', 'vac_hours', 'upto_hours', 'sick_hours', 'lu_hours', 'lp_hours', 'hol_hours']);
	var uri = dsExportData.createDataSource("_dsEmplExport",[JSColumn.TEXT,JSColumn.TEXT,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER,JSColumn.NUMBER]);
	/** @type {JSFoundSet<{empl_code:String, emplclass_code:String, reg_hours:Number, ot_hours:Number, vac_hours:Number, upto_hours:Number, sick_hours:Number, lu_hours:Number, lp_hours:Number, hol_hours:Number}>} */
	var fsExportData = databaseManager.getFoundSet(uri);
	
	var prevEmpl = null;
	//Loop over all the data to generate the export dataset
	for(var i = 1; i <= fsAllData.getSize(); i++){

		fsAllData.setSelectedIndex(i);
		
		//new employee
		if(prevEmpl != fsAllData.empl_code){
			curShiftDate = fsAllData.start_date;
			fsExportData.newRecord();
			fsExportData.empl_code = fsAllData.empl_code;
			fsExportData.emplclass_code = fsAllData.emplclass_code;
			hoursForPeriod = 0;
			overtimeHoursForPeriod = 0;
			
			if(fsAllData.payroll_time_type == "REG")
			{	
				if(_sOvertimeCalcType == "By Shift"){
					//need to check for multiple shifts in the same day
					fsCurEmpData = fsAllData.duplicateFoundSet();
					if(fsCurEmpData.find()){
						fsCurEmpData.empl_code = fsAllData.empl_code;
						fsCurEmpData.emplclass_code = fsAllData.emplclass_code; 
						fsCurEmpData.time_type = "REG";
						fsCurEmpData.start_date = fsAllData.start_date;
						
						var foundCount = fsCurEmpData.search();
						
						//duplicate shifts in a day
						if(foundCount > 1){
							nHoursForDay = 0;

							for(j = 1; j <= foundCount; j++){
								fsCurEmpData.setSelectedIndex(j);
								nHoursForDay += fsCurEmpData.hours;
							}
							
							if(nHoursForDay > _nHoursInRegShift){
								overTimeHours = nHoursForDay - _nHoursInRegShift;
								fsExportData.reg_hours = _nHoursInRegShift;
								
								//Add the overtime hours
								fsExportData.ot_hours = overTimeHours;
									
							} else {
								fsExportData.reg_hours = nHoursForDay;
							}
						//a single shift in the day
						} else if(foundCount == 1){
							overTimeHours = 0;
							if(fsAllData.hours > _nHoursInRegShift){
								overTimeHours = fsAllData.hours - _nHoursInRegShift;
								fsExportData.reg_hours = _nHoursInRegShift;
								
								//Add the overtime hours
								fsExportData.ot_hours = overTimeHours;
							} else {
								fsExportData.reg_hours = fsAllData.hours;
							}
							curShiftDate = fsAllData.start_date;
						}
					}
				} else if (_sOvertimeCalcType == "By Pay Period"){
					curPeriodEndDate = plugins.DateUtils.addDays(foundset.prb_from_date, _nNumDaysPerPayPeriod);
					//set the correct period end date
					while(fsAllData.start_date >= curPeriodEndDate && curPeriodEndDate < foundset.prb_to_date){
						curPeriodEndDate = plugins.DateUtils.addDays(curPeriodEndDate,_nNumDaysPerPayPeriod);
						hoursForPeriod = 0;
						overtimeHoursForPeriod = 0;
					}
					
					if(fsAllData.hours > _nHoursInRegPayPeriod){
						overtimeHoursForPeriod = fsAllData.hours - _nHoursInRegPayPeriod;
						fsExportData.reg_hours = _nHoursInRegPayPeriod;
						hoursForPeriod = _nHoursInRegPayPeriod;
						
						fsExportData.ot_hours = overtimeHoursForPeriod;
					} else {
						hoursForPeriod += fsAllData.hours;
						fsExportData.reg_hours = fsAllData.hours;
					}
				}else {
					fsExportData.reg_hours = fsAllData.hours;
				}
			} else if (fsAllData.payroll_time_type == "UPTO"){
				fsExportData.upto_hours = fsAllData.hours;
			} else if (fsAllData.payroll_time_type == "VAC") {
				fsExportData.vac_hours = fsAllData.hours;
			} else if (fsAllData.payroll_time_type == "SICK") {
				fsExportData.sick_hours = fsAllData.hours;
 			} else if (fsAllData.payroll_time_type == "LP") {
 				fsExportData.lp_hours = fsAllData.hours;
			} else if (fsAllData.payroll_time_type == "LU") {
				fsExportData.lu_hours = fsAllData.hours;
			} else if (fsAllData.payroll_time_type == "HOL") {
				fsExportData.hol_hours = fsAllData.hours;
			}
		//add to existing
		} else {
			if(fsExportData.find()){
				fsExportData.empl_code = fsAllData.empl_code;
				
				if(fsExportData.search() == 1){
					if(fsAllData.payroll_time_type == "REG")
					{
						if(_sOvertimeCalcType == "By Shift"){
							//need to check for multiple shifts in the same day
							fsCurEmpData = fsAllData.duplicateFoundSet();
							if(fsCurEmpData.find()){
								fsCurEmpData.empl_code = fsAllData.empl_code;
								fsCurEmpData.emplclass_code = fsAllData.emplclass_code; 
								fsCurEmpData.time_type = "REG";
								fsCurEmpData.start_date = fsAllData.start_date;
								
								foundCount = fsCurEmpData.search();
								
								//duplicate shifts in a day
								if(foundCount > 1){
									//only once on duplicate days
									if(curShiftDate != fsAllData.start_date){
										nHoursForDay = 0;
	
										for(j = 1; j <= foundCount; j++){
											fsCurEmpData.setSelectedIndex(j);
											nHoursForDay += fsCurEmpData.hours;
										}
										
										if(nHoursForDay > _nHoursInRegShift){
											overTimeHours = nHoursForDay - _nHoursInRegShift;
											
											if(fsExportData.reg_hours != null){
												fsExportData.reg_hours += _nHoursInRegShift;
											} else {
												fsExportData.reg_hours = _nHoursInRegShift;
											}
											
											//Add the overtime hours
											if(fsExportData.ot_hours != null){
												fsExportData.ot_hours += overTimeHours;
											} else {
												fsExportData.ot_hours = overTimeHours;
											}
												
										} else {
											if(fsExportData.reg_hours != null){
												fsExportData.reg_hours += nHoursForDay;
											} else {
												fsExportData.reg_hours = nHoursForDay;
											}
										}
									}
								//a single shift in the day
								} else if(foundCount == 1){
									overTimeHours = 0;
									if(fsAllData.hours > _nHoursInRegShift){
										overTimeHours = fsAllData.hours - _nHoursInRegShift;
										if(fsExportData.reg_hours != null){
											fsExportData.reg_hours += _nHoursInRegShift;
										} else {
											fsExportData.reg_hours = _nHoursInRegShift;
										}
										
										//Add the overtime hours
										if(fsExportData.ot_hours != null){
											fsExportData.ot_hours += overTimeHours;
										} else {
											fsExportData.ot_hours = overTimeHours;
										}
									} else {
										if(fsExportData.reg_hours != null){
											fsExportData.reg_hours += fsAllData.hours;
										} else {
											fsExportData.reg_hours = fsAllData.hours;
										}
									}
								}
							}
						} else if (_sOvertimeCalcType == "By Pay Period"){
							//set the correct period end date
							while(fsAllData.start_date >= curPeriodEndDate && curPeriodEndDate < foundset.prb_to_date){
								curPeriodEndDate = plugins.DateUtils.addDays(curPeriodEndDate,_nNumDaysPerPayPeriod);
								hoursForPeriod = 0;
								overtimeHoursForPeriod = 0;
							}
							
							if(overtimeHoursForPeriod > 0){
								overtimeHoursForPeriod += fsAllData.hours;
								if(fsExportData.ot_hours != null){
									fsExportData.ot_hours = overtimeHoursForPeriod;
								} else {
									fsExportData.ot_hours = overtimeHoursForPeriod;
								}
							} else if((hoursForPeriod + fsAllData.hours) > _nHoursInRegPayPeriod) {
								if(fsExportData.reg_hours != null){
									overtimeHoursForPeriod = (fsExportData.reg_hours + fsAllData.hours) - _nHoursInRegPayPeriod;
								} else {
									overtimeHoursForPeriod = fsAllData.hours - _nHoursInRegPayPeriod;
								}
								
								fsExportData.reg_hours = _nHoursInRegPayPeriod;
								hoursForPeriod = _nHoursInRegPayPeriod;
								fsExportData.ot_hours = overtimeHoursForPeriod;
								
							} else {
								hoursForPeriod += fsAllData.hours;
								
								if(fsExportData.reg_hours != null){
									fsExportData.reg_hours += fsAllData.hours;
								} else {
									fsExportData.reg_hours = fsAllData.hours;
								}
							}
						} else {
							if(fsExportData.reg_hours != null){
								fsExportData.reg_hours += fsAllData.hours;
							} else {
								fsExportData.reg_hours = fsAllData.hours;
							}
						}
					} else if (fsAllData.payroll_time_type == "UPTO"){
						if(fsExportData.upto_hours != null){
							fsExportData.upto_hours += fsAllData.hours;
						} else {
							fsExportData.upto_hours = fsAllData.hours;
						}
					} else if (fsAllData.payroll_time_type == "VAC") {
						if(fsExportData.vac_hours != null){
							fsExportData.vac_hours += fsAllData.hours;
						} else {
							fsExportData.vac_hours = fsAllData.hours;
						}
					} else if (fsAllData.payroll_time_type == "SICK") {
						if(fsExportData.sick_hours != null){
							fsExportData.sick_hours += fsAllData.hours;
						} else {
							fsExportData.sick_hours = fsAllData.hours;
						}
		 			} else if (fsAllData.payroll_time_type == "LP") {
						if(fsExportData.lp_hours != null){
							fsExportData.lp_hours += fsAllData.hours;
						} else {
							fsExportData.lp_hours = fsAllData.hours;
						}
					} else if (fsAllData.payroll_time_type == "LU") {
						if(fsExportData.lu_hours != null){
							fsExportData.lu_hours += fsAllData.hours;
						} else {
							fsExportData.lu_hours = fsAllData.hours;
						}
					} else if (fsAllData.payroll_time_type == "HOL") {
						if(fsExportData.hol_hours != null){
							fsExportData.hol_hours += fsAllData.hours;
						} else {
							fsExportData.hol_hours = fsAllData.hours;
						}
					}
				}
			}
			curShiftDate = fsAllData.start_date;
		}
		prevEmpl = fsAllData.empl_code;
	}
	
	fsExportData.loadAllRecords();
	fsExportData.sort('empl_code asc');
	
	//apply sorts
	fsExportData.sort(getSortString());
	//write to file
	writeRecToPRFile(fsExportData);
}

/**
*
*@return {String}
*
* @properties={typeid:24,uuid:"09D6E2E0-29F1-4FAD-95CC-D817C496A366"}
*/
function getSortString(){
	var rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx)
	var iNumFields = rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getSize()
	var sSortDir=''
	var sSort=''
	
	rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.sort('prff_sort_num asc')
	for (var i = 1; i <= iNumFields; i++) {
		var rec_sys_payroll_file_field = rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getRecord(i)
		if(rec_sys_payroll_file_field.prff_sort_num){
			if(rec_sys_payroll_file_field.sys_payroll_file_field_to_app_avail_payroll_fields){
				if(rec_sys_payroll_file_field.sys_payroll_file_field_to_app_avail_payroll_fields.aprf_field_name){

					if(utils.stringTrim(sSort) != ""){
						sSort += ", ";
					}

					switch (rec_sys_payroll_file_field.sys_payroll_file_field_to_app_avail_payroll_fields.aprf_field_name) {
						case "Employee Code":
							sSort += "empl_code";
							break;
						case "Employee Class Code":
							sSort += "emplclass_code";
							break;
						case "Time Type Code":
							sSort += "time_type";
							break;
						case "Time Type Per Period":
							sSort += "hours";
							break;
						case "Unpaid Time Off":
							sSort += "upto_hours";
							break;	
						case "Vacation Time":
							sSort += "vac_hours";
							break;	
						case "Regular Hours":
							sSort += "reg_hours";
							break;	
						case "Overtime Hours":
							sSort += "ot_hours";
							break;	
						case "Sick Time":
							sSort += "sick_hours";
							break;	
						case "Leave (paid) Hours":
							sSort += "lp_hours";
							break;	
						case "Leave (unpaid) Hours":
							sSort += "lu_hours";
							break;
						case "Holiday":
							sSort += "hol_hours";
							break;
					}
				}
			}

			if(sSort){
				sSortDir = rec_sys_payroll_file_field.prff_sort_dir
				if(!sSortDir){
					sSortDir='ASC'
				}
				
				sSort += ' ' + sSortDir
			}
		}
	}
	
	return sSort;
}

/**
 * @param {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number}>|
 * JSFoundSet<{empl_code:String, emplclass_code:String, reg_hours:Number, ot_hours:Number, vac_hours:Number, upto_hours:Number, sick_hours:Number, lu_hours:Number, lp_hours:Number, hol_hours:Number}>} fsExportData
 * 
 * @properties={typeid:24,uuid:"7C691A9D-92DD-4B8D-95A1-75C4C057A911"}
 */
function writeRecToPRFile(fsExportData){
	var rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx)
	var iNumFields = rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getSize()
	var sLine = ''
	
	rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.sort('sequence_nr asc')
	
	for (var i = 1; i <= fsExportData.getSize(); i++){
		fsExportData.setSelectedIndex(i);
		sLine = "";
		for (var j = 1; j <= iNumFields; j++) {
			if (j > 1) {
				if (rec_sys_payroll_file.prf_file_format == 'Tab Delimited') {
					sLine += '\t'
				} else if (rec_sys_payroll_file.prf_file_format == 'Comma Delimited') {
					sLine += ','
				} else {
					sLine += ' ' ; // default to a space if nothing specified
				}
	
			}
	
			if(rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getRecord(j)){
				var fieldRec = rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getRecord(j)
				var val = null;
				
				if(fieldRec.prff_use_database_field_flag == 0){
					val = fieldRec.prff_user_defined_value;
				} else if(fieldRec.aprf_id){
					val = getFieldValue(fieldRec.aprf_id, fsExportData);
				}

				if(!_bDryRun && !_bReviewPost){
					sLine += formatFieldValue(fieldRec.aprf_id, val) 
				}
			}
		}

		if(!_bDryRun && !_bReviewPost){
			_sFileData += sLine + '\n'
		}
	}
}

/**
 * @param {Number} aprf_id
 * @param val
 *
 * @return
 * @properties={typeid:24,uuid:"A205A311-E1C9-4AE1-B3E3-2AFCE0CA3821"}
 */
function formatFieldValue(aprf_id, val){
	if(val){
		var rec_app_avail_payroll_fields = getAppPRFieldRec(aprf_id)
		
		if(rec_app_avail_payroll_fields){
			var rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx)

			if(rec_sys_payroll_file.prf_date_format && rec_sys_payroll_file.prf_date_format != 'LONG FORMAT' && rec_app_avail_payroll_fields.aprf_data_type == 'date'){
				val = formatDate(val, rec_sys_payroll_file.prf_date_format)
			}
			else if(rec_app_avail_payroll_fields.aprf_data_type == 'currency'){
				if(rec_sys_payroll_file.prf_currency_round){
					val = val.toFixed(2)
				}
				if(rec_sys_payroll_file.prf_currency_symbol){
					val = '$' + val.toString();
				}
				if(rec_sys_payroll_file.prf_currency_k_separator){
					val = addCommas(val.toString());
				}
			}
		}
	}
	
	return '"' + val + '"'; // surround with quotes
}

/**
 * @param {Number} aprf_id
 * @param {JSFoundSet<{empl_code:String, emplclass_code:String, time_type:String, hours:Number}>|
 * JSFoundSet<{empl_code:String, emplclass_code:String, reg_hours:Number, ot_hours:Number, vac_hours:Number, upto_hours:Number, sick_hours:Number, lu_hours:Number, lp_hours:Number, hol_hours:Number}>} fsExportData
 *
 * @return
 * @properties={typeid:24,uuid:"92376CC4-CC87-4FB0-A87F-F661FCE33960"}
 */
function getFieldValue(aprf_id, fsExportData) {
	/***@type {String|Number|Date} */
	var retVal=null
	/**  */
	var rec_app_avail_payroll_fields = getAppPRFieldRec(aprf_id)
	
	if(rec_app_avail_payroll_fields){
		if(rec_app_avail_payroll_fields.aprf_field_name){
			switch (rec_app_avail_payroll_fields.aprf_field_name) {
				case "Employee Code":
					retVal = fsExportData.empl_code;
					break;
				case "Employee Class Code":
					retVal = fsExportData.emplclass_code;
					break;
				case "Time Type Code":
					retVal = fsExportData.time_type;
					break;
				case "Time Type Per Period":
					retVal = fsExportData.hours;
					break;
				case "Unpaid Time Off":
					retVal = fsExportData.upto_hours;
					break;	
				case "Vacation Time":
					retVal = fsExportData.vac_hours;
					break;	
				case "Regular Hours":
					retVal = fsExportData.reg_hours;
					break;	
				case "Overtime Hours":
					retVal = fsExportData.ot_hours;
					break;	
				case "Sick Time":
					retVal = fsExportData.sick_hours;
					break;	
				case "Leave (paid) Hours":
					retVal = fsExportData.lp_hours;
					break;	
				case "Leave (unpaid) Hours":
					retVal = fsExportData.lu_hours;
					break;
				case "Holiday":
					retVal = fsExportData.hol_hours;
					break;
				default:
					retVal = "";
					break;
			}
		}
	}
	return retVal
}

/**
 * @param {String} nStr
 *
 * @return
 * @properties={typeid:24,uuid:"DED483DB-CB1D-413D-AFA1-10902ACB4373"}
 */
function addCommas(nStr) {
    /***@type {String} */
    var x = nStr.split('.');
    /***@type {String} */
    var x1 = x[0];
    /***@type {String} */
    var x2 = x.length > 1 ? '.' + x[1] : '';
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
            x1 = x1.replace(rgx, '$1' + ',' + '$2');
    }
    return x1 + x2;
}

/**
 * @param {String|Number|Date} dDate
 * @param {String} sFormat
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"38E36BC7-F44D-4D01-955C-ECBF538F49C2"}
 */
function formatDate(dDate, sFormat){
	var retval = dDate.toString()
	
	switch(sFormat)
	{
		case 'MM/DD/YYYY':
			retval = formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate()) + '/' + dDate.getFullYear().toString()
			break;
		case 'DD/MM/YYYY':
			retval = formatDay(dDate.getDate()) + '/' + formatMonth(dDate.getMonth()) + '/' + dDate.getFullYear().toString()
			break;
		case 'YYYY/MM/DD':
			retval = dDate.getFullYear().toString() + '/' + formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate()) 
			break;

		case 'MM-DD-YYYY':
			retval = formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate()) + '-' + dDate.getFullYear().toString()
			break;
		case 'DD-MM-YYYY':
			retval = formatDay(dDate.getDate()) + '-' + formatMonth(dDate.getMonth()) + '-' + dDate.getFullYear().toString()
			break;
		case 'YYYY-MM-DD':
			retval = dDate.getFullYear().toString() + '-' + formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate()) 
			break;
		
		case 'MMDDYYYY':
			retval = formatMonth(dDate.getMonth()) + formatDay(dDate.getDate()) + dDate.getFullYear().toString()
			break;
		case 'DDMMYYYY':
			retval = formatDay(dDate.getDate()) + formatMonth(dDate.getMonth()) + dDate.getFullYear().toString()
			break;
		case 'YYYYMMDD':
			retval = dDate.getFullYear().toString() + formatMonth(dDate.getMonth()) + formatDay(dDate.getDate()) 
			break;
	}
	
	return retval
	
}

/**
 * @param {Number} month
 * @return {String}
 *
 * @properties={typeid:24,uuid:"67C2E593-9508-48BE-94BC-398F318D182A"}
 */
function formatMonth(month){
	var retval = ''
		
	month++
	if(month<10){
		retval = '0' + month.toString()
	}
	else{
		retval = month.toString()
	}		
		
	return retval
}

/**
 * @param {Number} day
 * @return {String}
 *
 * @properties={typeid:24,uuid:"9C8019E6-581D-42F0-A7A2-D31C11B569D4"}
 */
function formatDay(day){
	var retval = ''
		
	if(day<10){
		retval = '0' + day.toString()
	}
	else{
		retval = day.toString()
	}		
		
	return retval
}

/**
 * @param {JSRecord<db:/avanti/sys_payroll_file>} rec_sys_payroll_file
 *
 * @return
 * @properties={typeid:24,uuid:"BF70D572-858C-4F97-B1E2-459BD6F15EA5"}
 */
function initializePRFile(rec_sys_payroll_file) {
	var iNumFields = rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getSize() ;
	var sLine = '' ;
	if (iNumFields > 0) {
		if (rec_sys_payroll_file.prf_incl_col_headers) {

			rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.sort('sequence_nr asc')
			for (var i = 1; i <= iNumFields; i++) {
				if (i > 1) {
					if (rec_sys_payroll_file.prf_file_format == 'Tab Delimited') {
						sLine += '\t' ;
					} else if (rec_sys_payroll_file.prf_file_format == 'Comma Delimited') {
						sLine += ',' ;
					} else {
						sLine += ' ' ; // default to a space if nothing specified
					}
				}
				sLine += rec_sys_payroll_file.sys_payroll_file_to_sys_payroll_file_field.getRecord(i).prff_col_header ;
			}
		}

		_sFileData = sLine + '\n' ;
		
		if (rec_sys_payroll_file.prf_file_format == 'Comma Delimited'){
			_sDelimeter = 'comma';
		}
		else if (rec_sys_payroll_file.prf_file_format == 'Tab Delimited'){
			_sDelimeter = 'tab';
		}
		else{
			_sDelimeter = null;
		}
		
		return true ;
	} else {
		_sFileData=''
		scopes.avText.showWarning(i18n.getI18NMessage('avanti.dialog.noColsInCBFile') + rec_sys_payroll_file.prf_file_name, true);
		return false ;
	}
}

/**
 * @param {Boolean} bZipFile
 * 
 * @properties={typeid:24,uuid:"6264EC83-2B60-4561-977F-8AF2E31CD38E"}
 */
function closePRFile (bZipFile) {
	/** @type {plugins.file.JSFile} */
	var jsf;
	var rec_sys_payroll_file = _fs_sys_payroll_file.getRecord(_iFileIdx);
	var aFileNameAndExt = getPRFileNameAndExt(rec_sys_payroll_file);
	var sFileName = aFileNameAndExt[0];
	var sExt = aFileNameAndExt[1];

	if (rec_sys_payroll_file.prf_file_placement_options == 'Hot Folder') {
		if (rec_sys_payroll_file.prf_output_folder) {
			var sFilePath = rec_sys_payroll_file.prf_output_folder + '\\' + sFileName + sExt;

			jsf = plugins.file.createFile(sFilePath);

			if (jsf.exists()) {
				plugins.file.deleteFile(sFilePath);
			}

			if (jsf.createNewFile()) {
				plugins.file.writeTXTFile(jsf, _sFileData)
			}
		}

	}
	else if (rec_sys_payroll_file.prf_file_placement_options == 'Download') {
		// if creating a temp file using createTempFile then the filename will be appended with an alphanumeric string to make it unique . add an '-' between it and the user defined file name
		sFileName += "-";

		jsf = plugins.file.createTempFile(sFileName, sExt);

		if (jsf) {
			plugins.file.writeTXTFile(jsf, _sFileData);
			
			if (bZipFile) {
				var sOldPathFile = jsf.getAbsolutePath();
				var sFile = jsf.getName();
				if (!_sZipFolder) {
					_sZipFolder = sOldPathFile.substr(0, sOldPathFile.length - sFile.length) + 'payroll' + scopes.avUtils.getTimeStamp();
					plugins.file.createFolder(_sZipFolder);
				}
				var sNewPathFile = _sZipFolder + '\\' + sFile;

				plugins.file.moveFile(sOldPathFile, sNewPathFile);
			}
			else {
				forms._docs_base.getFileFromServer(jsf.getAbsolutePath());
			}
		}
	}
}

/**
 * @private 
 * 
 * @param {JSRecord<db:/avanti/sys_payroll_file>} rec_sys_payroll_file
 * 
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"B604F94E-B3B1-46FC-B7A9-998E4355F86B"}
 */
function getPRFileNameAndExt(rec_sys_payroll_file) {
	var sFileName = rec_sys_payroll_file.prf_file_name;
	var sExt = "";
	var sDateTime = getDateTimeString();
	
	if (sFileName) {
		sExt = scopes.avUtils.getFiletExt(sFileName, true);
		sFileName = sFileName.substr(0, sFileName.length - sExt.length);
		
		if (rec_sys_payroll_file.prf_append_date_time) {
			sFileName += sDateTime;
		}
	}
	else {
		sFileName = "Payroll-" + sDateTime;
	}

	return [sFileName, sExt];
}

/**
 * @return
 * @properties={typeid:24,uuid:"9F0ADFFC-1972-4E0A-90C9-4F383AB601EC"}
 */
function getDateTimeString(){
	var d = new Date()
	
	return d.getFullYear().toString() + format(d.getMonth()+1) + format(d.getDate()) + format(d.getHours()) + format(d.getMinutes()) + format(d.getSeconds())
}

/**
 * @param {Number} num
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"B715CE07-BC74-4D9D-A88B-B2F35DF53542"}
 */
function format(num){
	if(num < 10){
		return '0' + num.toString()
	}
	else{
		return num.toString()
	}
}

/**
 * @param {String} sFileName
 *
 * @return
 * @properties={typeid:24,uuid:"CE2B3001-877E-4D59-A0DB-E38E3362AD7E"}
 */
function geFiletExt(sFileName){
	var sExt = ''
	var iDotPos = sFileName.lastIndexOf('.')
	  
	if(iDotPos > -1){
		sExt = sFileName.substr(iDotPos + 1)
	}
	
	return sExt
}

/**
 * @param {Number} aprf_id
 * 
 * @return {JSRecord<db:/avanti/app_avail_payroll_fields>}
 *
 * @properties={typeid:24,uuid:"8E52B9AB-CE84-4195-B7A1-5067273D7BF6"}
 * @AllowToRunInFind
 */
function getAppPRFieldRec(aprf_id){
	/***@type {JSFoundset<db:/avanti/app_avail_payroll_fields>} */
	var fs_app_avail_payroll_fields = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'app_avail_payroll_fields')
	if (fs_app_avail_payroll_fields.find() || fs_app_avail_payroll_fields.find()) {
		fs_app_avail_payroll_fields.aprf_id = aprf_id
		if(fs_app_avail_payroll_fields.search() > 0){
			return fs_app_avail_payroll_fields.getRecord(1)
		}
	}
	
	return null
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"93FEEBDE-2EA3-4769-BB11-9649B937F185"}
 */
function btnReview_onAction(event) {
	if(!prb_num_recs){
		globals.showWarning('NoRecsToReview')
	}			
	else{
		
		//need to rerun the query to populate the datasource
		_bDryRun = false;
		_bReviewPost = true;
		postRecs();
		
		//create and show the review form
		showReviewForm();
	}
}

/**
 * @properties={typeid:24,uuid:"2DF45A64-2F78-489D-86AA-BADD7B769F90"}
 */
function showReviewForm() {
    // If the form exists remove it because the data may have changed or we might need to point the form to a new datasource
    if (solutionModel.getForm("sa_payroll_review_popup")) {
        solutionModel.removeForm("sa_payroll_review_popup");
    }
    
    // Create the review popup form
    var reviewForm = solutionModel.newForm("sa_payroll_review_popup", _allDataURI, "Avanti", false, 800, 400);
    reviewForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER | SM_SCROLLBAR.VERTICAL_SCROLLBAR_AS_NEEDED;
    reviewForm.useCssPosition = true; // Enable CSS positioning
    
    // Add onShow method to load records
    if (!reviewForm.getMethod("onShowForm")) {
        reviewForm.newMethod('function onShowForm() {\n' +
            '   // Load records into the grid foundset\n' +
            '   elements.grid.myFoundset.foundset.loadRecords(foundset);\n' +
            '   return;\n' +
            '}');
        reviewForm.onShow = reviewForm.getMethod("onShowForm");
    }
    
    // Create header and body parts
    reviewForm.newHeaderPart(5);
    
    // Create the grid component
    var grid = reviewForm.newWebComponent("grid", "aggrid-groupingtable");
    grid.cssPosition.t("5").l("0").r("0").b("0");
    
    // If the vl_timeTypes valuelist doesn't exist yet create it
    if (!solutionModel.getValueList("vl_timeTypes")) {
        solutionModel.newValueList("vl_timeTypes", JSValueList.CUSTOM_VALUES);
    }
    var aDisplayValues = ["Regular Hours", "Overtime Hours", "Vacation Time", "Unpaid Time Off", "Sick Time", "Leave (paid) Hours", "Leave (unpaid) Hours", "Holiday"];
    var aRealValues = ["REG", "OT", "VAC", "UPTO", "SICK", "LP", "LU", "HOL"];
    application.setValueListItems("vl_timeTypes", aDisplayValues, aRealValues);
    
    // Define grid columns
    var columns = [
        {
            "headerTitle": "Employee Code",
            "dataprovider": "empl_code",
            "width": 150
        },
        {
            "headerTitle": "Employee Class Code",
            "dataprovider": "emplclass_code",
            "width": 200
        },
        {
            "headerTitle": "Time Type",
            "dataprovider": "payroll_time_type",
            "width": 200,
            "valuelist": "vl_timeTypes"
        },
        {
            "headerTitle": "Hours",
            "dataprovider": "diff_text",
            "width": 100,
            "format": "####0.00",
            "styleClass": "text-center"
        },
        {
            "headerTitle": "Shift Start Date",
            "dataprovider": "start_date",
            "width": 150,
            "format": "MM/dd/yy",
            "styleClass": "text-center"
        }
    ];
    
    // Set grid properties
    grid.setJSONProperty("columns", columns);
    grid.setJSONProperty("styleClass", "table");
    grid.setJSONProperty("responsiveHeight", 400);
    grid.setJSONProperty("rowHeight", 22);
    grid.setJSONProperty("enableColumnResize", true);
    grid.setJSONProperty("enableSorting", true);
    
    // Show the form in modal dialog
    globals.DIALOGS.showFormInModalDialog(forms["sa_payroll_review_popup"], -1, -1, -1, -1, i18n.getI18NMessage('avanti.dialog.batchShiftsAndExceptions'), true, false, "dlgPayrollFileReview", true);
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B21A24B7-FA69-48D2-BD4E-F56C6C3241B0"}
 */
function frmDate_onDataChange(oldValue, newValue, event) {
    if (newValue) {
        newValue.setHours(0, 0, 0);
    }
	
	calculatePayPeriods();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"00500E99-666B-4214-8966-DA8D59969053"}
 */
function toDate_onDataChange(oldValue, newValue, event) {
    if (newValue) {
        newValue.setHours(0, 0, 0);
    }
	
	calculatePayPeriods();
	return true;
}

/**
 * @properties={typeid:24,uuid:"F02E0A56-DD4A-418F-BBC4-EB5E0798AC3D"}
 */
function calculatePayPeriods(){
	if(prb_from_date != null && prb_to_date != null){
		if(_nNumDaysPerPayPeriod != null && _nNumDaysPerPayPeriod != 0){
			var iTotalDays = plugins.DateUtils.getDayDifference(prb_from_date,prb_to_date);
			
			if(iTotalDays > 0){
				_iPayPeriods = Math.ceil(iTotalDays / _nNumDaysPerPayPeriod);
				
				if(_iPayPeriods > 1){
					_sPayPeriodMsg = i18n.getI18NMessage('avanti.lbl.payPeriods');
				} else if(_iPayPeriods == 1){
					_sPayPeriodMsg = i18n.getI18NMessage('avanti.lbl.payPeriod');
				}
				
				_sPayPeriodMsg += " " + i18n.getI18NMessage('svy.fr.lbl.selected');
				return;
			}
		}
	}
	
	_iPayPeriods = null;
	_sPayPeriodMsg = "";
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"66D07530-ED03-4538-A686-863660B23FDD"}
 */
function btnRunQuery_onAction(event) {
	_bDryRun = true;
	_bReviewPost = false;
	postRecs()
}

/**
 * @properties={typeid:24,uuid:"D47C2310-D4F6-484A-90EF-6CD40B72B9A2"}
 */
function refreshUI(){
	elements.txtBatchName.enabled = !foundset.prb_date_posted
	elements.txtBatchName.enabled = !foundset.prb_date_posted
	elements.btnRunQuery.enabled = !foundset.prb_date_posted
	//elements.btnPost.enabled = !foundset.prb_date_posted

	elements.txtDateFrom.enabled != (foundset.prb_date_posted != null)
	elements.txtDateTo.enabled != (foundset.prb_date_posted != null)

	if(foundset.prb_num_recs){
		if(foundset.prb_date_posted){
			_sRecsPostedMsg = i18n.getI18NMessage('avanti.lbl.recordsPosted') + ' *'
		}
		else{
			_sRecsPostedMsg = i18n.getI18NMessage('avanti.lbl.recordsToPost') + ' *'
		}
	} else {
		_sRecsPostedMsg = "";
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"1C649DC2-D387-40CA-A58F-8C8DEDA09A6C"}
 */
function btnNewBatch_onAction(event) {
	foundset.newRecord();
	foundset.prb_date_created = new Date;
	foundset.prb_created_by = globals.avBase_employeeUUID;
	refreshUI();
}

/**
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @properties={typeid:24,uuid:"0D77D1CD-B7C4-4D4F-8BFB-ACCC22FB9344"}
 */
function dc_new(_event, _triggerForm){
	//_super.dc_new(_event,_triggerForm);
	foundset.newRecord();
	foundset.prb_date_created = new Date;
	foundset.prb_created_by = globals.avBase_employeeUUID;
	refreshUI();
}

/**
 * @properties={typeid:24,uuid:"640DC15B-04FA-4C1F-9593-FB6A0B7148A7"}
 */
function createBatchPRFileRecs(){
	/**@type {JSFoundset<db:/avanti/sys_payroll_file>} */
	var fsActivePRFs = scopes.avDB.getFS('sys_payroll_file', ['prf_active'], [1]);
	
	if(fsActivePRFs && fsActivePRFs.getSize() > 0){
		for(var i=1; i<= fsActivePRFs.getSize(); i++){
			var rPRF = fsActivePRFs.getRecord(i);
			/**@type {JSRecord<db:/avanti/sa_payroll_batch_pr_file>} */
			var rBatchPRF = scopes.avDB.newRecord('sa_payroll_batch_pr_file')
			
			rBatchPRF.prb_id = forms.sa_payroll_file_dtl.prb_id;
			rBatchPRF.prf_id = rPRF.prf_id;
			rBatchPRF.org_id = globals.org_id;
			rBatchPRF.prbprf_is_selected = 1;
			
			databaseManager.saveData(rBatchPRF);
		}
	}
}

/**
 *
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 *
 * @return
 * @properties={typeid:24,uuid:"637A20DF-4F70-4D59-A0F2-A8D988D6FAEC"}
 */
function onShowForm(_firstShow, _event) {
	// RG 2014-09-16 SL-3162 set date formats to global instead of hard coded
	elements.txtDateFrom.format = globals.avBase_dateFormat ;
	elements.txtDateTo.format = globals.avBase_dateFormat ;
    application.executeLater(setReadonly, 500, [false]);
	return _super.onShowForm(_firstShow, _event)
}

/**
 * @param readonly
 *
 * @properties={typeid:24,uuid:"8E64CC52-C1D3-4986-B182-B46B554B2512"}
 */
function setReadonly(readonly) {
	controller.readOnly = readonly;
}

/**
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"2CE90E7A-F0A9-4E77-B212-3972FB6FE806"}
 */
function getBatchExceptionIDs(){
	var sExceptionIDs = "";
	
	var curRec = null;
	for(var i = 1; i <= foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getSize(); i++){
		curRec = foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getRecord(i);
		if(curRec.empschexcep_id != null){
			if(sExceptionIDs == ""){
				sExceptionIDs += curRec.empschexcep_id; 
			} else {
				sExceptionIDs += "," + curRec.empschexcep_id;
			}
		}
	}
	return sExceptionIDs;
}

/**
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"7820BF9D-4B59-40CC-AD3C-4BDA27FB7262"}
 */
function getBatchShiftIDs(){
	var sShiftIDs = "";
	
	var curRec = null;
	for(var i = 1; i <= foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getSize(); i++){
		curRec = foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getRecord(i);
		if(curRec.sys_employee_shift_id != null){
			if(sShiftIDs == ""){
				sShiftIDs += curRec.sys_employee_shift_id; 
			} else {
				sShiftIDs += "," + curRec.sys_employee_shift_id;
			}
		}
	}
	return sShiftIDs;
}

/**
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"B5112616-3F5E-4D1E-8E81-300C7A6D8A18"}
 */
function getBatchJCLIDs(){
	var sJCLIDs = "";
	
	var curRec = null;
	for(var i = 1; i <= foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getSize(); i++){
		curRec = foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getRecord(i);
		if(curRec.jcl_id != null){
			if(sJCLIDs == ""){
				sJCLIDs += curRec.jcl_id; 
			} else {
				sJCLIDs += "," + curRec.jcl_id;
			}
		}
	}
	return sJCLIDs;
}

/**
 * @param {JSDataSet} dsExceptions
 * @properties={typeid:24,uuid:"5AD4EF81-3BB2-4FE3-8046-BCDDF8E96019"}
 */
function addExceptionsToBatchDetail(dsExceptions){
	for(var i = 1; i <= dsExceptions.getMaxRowIndex(); i++){
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.newRecord();
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.prf_id = _fs_sys_payroll_file.prf_id;
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.empschexcep_id = dsExceptions.getValue(i,1);
	}
	
	databaseManager.saveData(foundset.sa_payroll_batch_to_sa_payroll_batch_detail);
}

/**
 * @param dsShifts
 *
 * @properties={typeid:24,uuid:"EE4D0A9B-2D13-4F84-8C36-A442EBA472BC"}
 */
function addShiftsToBatchDetail(dsShifts){
	for(var i = 1; i <= dsShifts.getMaxRowIndex(); i++){
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.newRecord();
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.prf_id = _fs_sys_payroll_file.prf_id;
		foundset.sa_payroll_batch_to_sa_payroll_batch_detail.sys_employee_shift_id = dsShifts.getValue(i,1);
	}
	
	databaseManager.saveData(foundset.sa_payroll_batch_to_sa_payroll_batch_detail);
}

/**
 * @param dsJCL
 *
 * @properties={typeid:24,uuid:"FF6C4B53-0F0B-4691-9AA0-B488A19A0CEC"}
 */
function addJCLToBatchDetail (dsJCL) {
	if (dsJCL) {
		for (var i = 1; i <= dsJCL.getMaxRowIndex(); i++) {
			foundset.sa_payroll_batch_to_sa_payroll_batch_detail.newRecord();
			foundset.sa_payroll_batch_to_sa_payroll_batch_detail.prf_id = _fs_sys_payroll_file.prf_id;
			foundset.sa_payroll_batch_to_sa_payroll_batch_detail.jcl_id = dsJCL.getValue(i, 1);
		}
	}

	databaseManager.saveData(foundset.sa_payroll_batch_to_sa_payroll_batch_detail);
}

/**
 * @properties={typeid:24,uuid:"45782979-920A-456D-8634-938DC4F8036A"}
 * @AllowToRunInFind
 */
function markDataPosted(){
	/**@type {JSFoundset<db:/avanti/sys_employee_shift>} */
	var fsShifts = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sys_employee_shift");
	/**@type {JSFoundset<db:/avanti/sch_empl_excep>} */
	var fsExceptions = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sch_empl_excep");
	/**@type {JSFoundset<db:/avanti/prod_job_cost_labour>} */
	var fsJCL = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"prod_job_cost_labour");

	
	var curRec = null;
	for(var i = 1; i <= foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getSize(); i++){
		curRec = foundset.sa_payroll_batch_to_sa_payroll_batch_detail.getRecord(i);
		if(curRec.empschexcep_id != null){
			if(fsExceptions.find()){
				fsExceptions.empschexcep_id = curRec.empschexcep_id.toString();
				if(fsExceptions.search() == 1){
					fsExceptions.posted_date = foundset.prb_date_posted;
					fsExceptions.posted_by_id = foundset.prb_posted_by;
					
					databaseManager.saveData(fsExceptions);
					removeDuplicateExceptions(curRec.empschexcep_id.toString());
				}
			}
		} else if(curRec.sys_employee_shift_id != null){
			if(fsShifts.find()){
				fsShifts.sys_employee_shift_id = curRec.sys_employee_shift_id.toString();
				if(fsShifts.search() == 1){
					fsShifts.posted_date = foundset.prb_date_posted;
					fsShifts.posted_by_id = foundset.prb_posted_by;
					
					databaseManager.saveData(fsShifts);
					removeDuplicateShifts(curRec.sys_employee_shift_id.toString());
				}
			}
		} else if(curRec.jcl_id != null){
			if(fsJCL.find()){
				fsJCL.jcl_id = curRec.jcl_id.toString();
				if(fsJCL.search() == 1){
					fsJCL.jcl_payroll_posted_date = foundset.prb_date_posted;
					fsJCL.jcl_payroll_posted_by_id = foundset.prb_posted_by;
						
					databaseManager.saveData(fsJCL);
					removeDuplicateBreaksAndLunchs(curRec.jcl_id.toString());
				}
			}
		}
	}
}

/**
 * Removes Exceptions from other unposted batches.
 * 
 * @param {String} exceptionID
 * 
 * @properties={typeid:24,uuid:"7F0B1A5B-4A58-476B-AF62-49CD5065F7E3"}
 * @AllowToRunInFind
 */
function removeDuplicateExceptions(exceptionID){
	/** @type {Number} */
	var i = 0;
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch_detail>} */
	var fsPayrollBatchDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch_detail");
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch>} */
	var fsPayrollBatch = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch");
	/**@type {Array<JSRecord>} */  
	var aRecToDelete = [];
	
	if(fsPayrollBatchDetail.find()){
		fsPayrollBatchDetail.empschexcep_id = exceptionID;
		fsPayrollBatchDetail.prb_id = "!" + foundset.prb_id.toString();
		var foundCount = fsPayrollBatchDetail.search();
		fsPayrollBatchDetail.sort("prb_id asc");
		var sPrevPRB_ID = null;
		
		for(i = 1; i <= foundCount; i++){
			var curRec = fsPayrollBatchDetail.getRecord(i);
			var sCurPRB_ID = curRec.prb_id.toString();
			if(sPrevPRB_ID != sCurPRB_ID){
				if(fsPayrollBatch.find()){
					fsPayrollBatch.prb_id = sCurPRB_ID;
					
					if(fsPayrollBatch.search() == 1){
						fsPayrollBatch.prb_num_recs--;
						databaseManager.saveData(fsPayrollBatch);
					}
				}
			}
			aRecToDelete.push(curRec);
			sPrevPRB_ID = sCurPRB_ID;
		}
	}
	
	for(i = 0; i < aRecToDelete.length; i++){
		fsPayrollBatchDetail.deleteRecord(aRecToDelete[i]);
	}
}

/**
 * Removes Shifts from other unposted batches.
 * 
 * @param {String} shiftID
 *
 * @properties={typeid:24,uuid:"37198219-87CF-49B9-8489-A602AE6279D1"}
 * @AllowToRunInFind
 */
function removeDuplicateShifts(shiftID){
	/** @type {Number} */
	var i = 0;
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch_detail>} */
	var fsPayrollBatchDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch_detail");
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch>} */
	var fsPayrollBatch = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch");
	/** @type {Array<JSRecord>} */  
	var aRecToDelete = [];
	
	if(fsPayrollBatchDetail.find()){
		fsPayrollBatchDetail.sys_employee_shift_id = shiftID;
		fsPayrollBatchDetail.prb_id = "!" + foundset.prb_id.toString();
		var foundCount = fsPayrollBatchDetail.search();
		fsPayrollBatchDetail.sort("prb_id asc");
		var sPrevPRB_ID = null;
		
		for(i = 1; i <= foundCount; i++){
			var curRec = fsPayrollBatchDetail.getRecord(i);
			var sCurPRB_ID = curRec.prb_id.toString();
			if(sPrevPRB_ID != sCurPRB_ID){
				if(fsPayrollBatch.find()){
					fsPayrollBatch.prb_id = sCurPRB_ID;
					
					if(fsPayrollBatch.search() == 1){
						fsPayrollBatch.prb_num_recs--;
						databaseManager.saveData(fsPayrollBatch);
					}
				}
			}
			aRecToDelete.push(curRec);
			sPrevPRB_ID = sCurPRB_ID;
		}
	}
	
	for(i = 0; i < aRecToDelete.length; i++){
		fsPayrollBatchDetail.deleteRecord(aRecToDelete[i]);
	}
}

/**
 * Removes Breaks and Lunches from other unposted batches.
 * 
 * @param {String} jclID
 *
 * @properties={typeid:24,uuid:"8A0C51BC-34FA-4E13-968E-CEB955E0BDFB"}
 * @AllowToRunInFind
 */
function removeDuplicateBreaksAndLunchs(jclID){
	/** @type {Number} */
	var i = 0;
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch_detail>} */
	var fsPayrollBatchDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch_detail");
	/**@type {JSFoundset<db:/avanti/sa_payroll_batch>} */
	var fsPayrollBatch = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sa_payroll_batch");
	/** @type {Array<JSRecord>} */  
	var aRecToDelete = [];
	
	if(fsPayrollBatchDetail.find()){
		fsPayrollBatchDetail.jcl_id = jclID;
		fsPayrollBatchDetail.prb_id = "!" + foundset.prb_id.toString();
		var foundCount = fsPayrollBatchDetail.search();
		fsPayrollBatchDetail.sort("prb_id asc");
		var sPrevPRB_ID = null;
		
		for(i = 1; i <= foundCount; i++){
			var curRec = fsPayrollBatchDetail.getRecord(i);
			var sCurPRB_ID = curRec.prb_id.toString();
			if(sPrevPRB_ID != sCurPRB_ID){
				if(fsPayrollBatch.find()){
					fsPayrollBatch.prb_id = sCurPRB_ID;
					
					if(fsPayrollBatch.search() == 1){
						fsPayrollBatch.prb_num_recs--;
						databaseManager.saveData(fsPayrollBatch);
					}
				}
			}
			aRecToDelete.push(curRec);
			sPrevPRB_ID = sCurPRB_ID;
		}
	}
	
	for(i = 0; i < aRecToDelete.length; i++){
		fsPayrollBatchDetail.deleteRecord(aRecToDelete[i]);
	}
}

/**
 * @properties={typeid:24,uuid:"33113C15-3287-461B-BDF1-F3ADF668362A"}
 */
function test(){
	application.output("Vacation: " + globals.avBase_getSystemPreference_String(154));
	application.output("Unpaid Time Off: " + globals.avBase_getSystemPreference_String(155));
	application.output("Sick Time: " + globals.avBase_getSystemPreference_String(156));
	application.output("Leave (Paid): " + globals.avBase_getSystemPreference_String(157));
	application.output("Leave (Unpaid): " + globals.avBase_getSystemPreference_String(158));
	application.output("Holiday: " + globals.avBase_getSystemPreference_String(159));
	application.output("Lunch: " + globals.avBase_getSystemPreference_String(160));
	application.output("Break: " + globals.avBase_getSystemPreference_String(161));
}

/**
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"B23E1DB7-97B0-4D37-8917-B36AEFE80E42"}
 * @override
 */
function getExportFormName(_form) {
	return "sa_payroll_file_tbl";
}
