customProperties:"useCssPosition:true",
extendsID:"6342701E-9C09-48D4-B926-B8E76DB7FAAA",
items:[
{
cssPosition:"130,-1,-1,33,105,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"33",
right:"-1",
top:"130",
width:"105"
},
enabled:true,
onActionMethodID:"7CF231B7-3573-456C-B819-D07BE01F891F",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Import Data",
visible:true
},
name:"btnImportData",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"0A2E1D74-784E-4C17-89EF-2904CCCAD078"
},
{
cssPosition:"260,-1,-1,55,176,16",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"16",
left:"55",
right:"-1",
top:"260",
width:"176"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"%%msNumDeletes%%",
visible:true
},
name:"msNumDeletes",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"163DA389-E05F-435A-9249-A6E1831C2F3A"
},
{
cssPosition:"632,-1,-1,775,109,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"775",
right:"-1",
top:"632",
width:"109"
},
enabled:true,
onActionMethodID:"6A688092-E9E4-4FB9-8C86-F3DF584E0923",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.ImportNotes",
visible:true
},
name:"importNotes",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"2C984AA0-C78E-4F5F-9068-3332ACD8797A"
},
{
cssPosition:"99,-1,-1,644,161,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"644",
right:"-1",
top:"99",
width:"161"
},
dataProviderID:"sAppendReplace",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"F77F4FC8-FE9B-498E-A4A9-FB0759E15F0F",
styleClass:"choicegroup_bts",
tabSeq:0,
valuelistID:"C28C5C26-41EC-4452-86E5-12BDE5EA1212",
visible:true
},
name:"optAppendReplace",
styleClass:"choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"2D3CEF9B-858C-43CF-BBD4-168F34F4A5F1"
},
{
cssPosition:"100,-1,-1,644,105,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"644",
right:"-1",
top:"100",
width:"105"
},
enabled:true,
onActionMethodID:"DDDB44A5-DCFA-464E-98BA-D2460EF0CA23",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Purge Data",
visible:true
},
name:"btnPurge",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"312A3D1C-FDAB-4B57-8147-61A78BF90CA4"
},
{
cssPosition:"163,-1,-1,644,254,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"644",
right:"-1",
top:"163",
width:"254"
},
dataProviderID:"_bOverwriteChartOfAccounts",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.dialog.updateAlreadyImportedGLAccounts",
visible:true
},
name:"chkOverwriteExistingChartOfAccounts",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"352908DB-1581-48DA-89C5-9EF910A2BA86"
},
{
cssPosition:"11,-1,-1,0,902,32",
json:{
cssPosition:{
bottom:"-1",
height:"32",
left:"0",
right:"-1",
top:"11",
width:"902"
},
enabled:true,
styleClass:"label_bts text-center",
tabSeq:-1,
text:"i18n:avanti.utils.dataImport.Title",
visible:true
},
name:"component_60665357",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"40A10F50-B2EE-4839-B2F0-61B6A456252A"
},
{
cssPosition:"131,-1,-1,644,216,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"644",
right:"-1",
top:"131",
width:"216"
},
dataProviderID:"mbTurnOffHaltingAfterMaxErrors",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"Turn off halting after Max errors",
visible:true
},
name:"component_D9402A7C",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"4202F412-3CC7-4105-94CB-E094E739F706"
},
{
cssPosition:"243,-1,-1,55,176,16",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"16",
left:"55",
right:"-1",
top:"243",
width:"176"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"%%msNumUpdates%%",
visible:true
},
name:"msNumUpdates",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"47FFCCE7-7217-40FE-B12C-CD9CCFB14ADB"
},
{
cssPosition:"276,-1,-1,55,176,16",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"16",
left:"55",
right:"-1",
top:"276",
width:"176"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"%%msNumRecsFailed%%",
visible:true
},
name:"msNumRecsFailed",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4BB45E92-418F-45E7-9666-DA67F7742ADB"
},
{
cssPosition:"226,-1,-1,55,176,16",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"16",
left:"55",
right:"-1",
top:"226",
width:"176"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"%%msNumInserts%%",
visible:true
},
name:"msNumInserts",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4E7810A4-D76C-43F6-B119-9519C5AB7677"
},
{
cssPosition:"160,-1,-1,34,105,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"34",
right:"-1",
top:"160",
width:"105"
},
enabled:true,
onActionMethodID:"D1725863-6DF4-46DF-88F8-EA84B070680D",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.ExportData",
visible:true
},
name:"btnExportData",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"6CD41BC6-F850-453F-9A0B-29E36736A624"
},
{
cssPosition:"242,-1,-1,33,772,340",
formIndex:5,
json:{
containedForm:"522FAAE1-B6E0-4C39-ADDA-ECF2902F7357",
cssPosition:{
bottom:"-1",
height:"340",
left:"33",
right:"-1",
top:"242",
width:"772"
},
formIndex:5,
visible:true
},
name:"tabAutoImport",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"82263B26-36D3-43F9-8B68-07D9242092A6"
},
{
cssPosition:"294,-1,-1,55,176,16",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"16",
left:"55",
right:"-1",
top:"294",
width:"176"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"%%msNumErrors%%",
visible:true
},
name:"msNumErrors",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8E467FFA-4D56-4962-8A13-9E8EB8FCDB61"
},
{
height:687,
partType:5,
typeid:19,
uuid:"ABF0055C-F664-4972-BDD3-153BFD3C99FF"
},
{
cssPosition:"60,-1,-1,149,260,24",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"24",
left:"149",
right:"-1",
top:"60",
width:"260"
},
dataProviderID:"msImportType",
enabled:true,
formIndex:0,
onDataChangeMethodID:"7E955BBB-EDAE-418B-BC94-1B674A8CD8B9",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"D8D6A8B1-C72C-4493-807E-684C829A5AD7",
visible:true
},
name:"cboImportType",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"AE65A2C9-BC17-4F90-9E42-207276EABD54"
},
{
borderType:"TitledBorder,i18n:avanti.lbl.shippingCarrier_results,4,0,DialogInput.italic,1,12,DialogInput,#333333",
cssPosition:"204,-1,-1,17,870,400",
lineSize:1,
location:"31,202",
name:"fraResults",
typeid:21,
uuid:"C090174C-30AD-4C40-8D6F-D7030977946E"
},
{
cssPosition:"60,-1,-1,459,80,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"459",
right:"-1",
top:"60",
width:"80"
},
enabled:true,
labelFor:"cboSubType",
styleClass:"label_bts",
tabSeq:-1,
text:"Sub Type",
visible:false
},
name:"lblSubType",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C5784F27-AE0C-4F55-B3B0-FF06D977DC89",
visible:false
},
{
cssPosition:"60,-1,-1,33,86,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"33",
right:"-1",
top:"60",
width:"86"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"Import Type",
visible:true
},
name:"component_4C99F825",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C75EAAC8-31E5-4E81-9C54-6893DF396421"
},
{
cssPosition:"100,-1,-1,33,105,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"33",
right:"-1",
top:"100",
width:"105"
},
enabled:true,
onActionMethodID:"3B6F7B25-67A4-46F1-8401-53B77693791D",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Select File",
visible:true
},
name:"btnSelectFile",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"C9EF0F1D-6D1A-414F-B02F-E1D74BD08C71"
},
{
cssPosition:"100,-1,-1,149,475,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"149",
right:"-1",
top:"100",
width:"475"
},
enabled:true,
formIndex:0,
styleClass:"label_bts",
tabSeq:-1,
text:"%%lblNumRecs%%",
visible:true
},
name:"lblFileNameAndRecs",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CB57E649-7FF3-4B73-AB0C-159D9598446B"
},
{
cssPosition:"343,-1,-1,55,808,242",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"242",
left:"55",
right:"-1",
top:"343",
width:"808"
},
dataProviderID:"msErrorMsgs",
editable:true,
enabled:true,
formIndex:7,
styleClass:"textarea_bts",
tabSeq:0,
visible:true
},
name:"txtFailedRecs",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"D11BA3DE-32AB-496A-BAE8-DCBF8BA602F9"
},
{
cssPosition:"323,-1,-1,55,190,20",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"55",
right:"-1",
top:"323",
width:"190"
},
enabled:true,
formIndex:3,
styleClass:"label_bts",
tabSeq:-1,
text:"Failed Records:",
visible:true
},
name:"lblFailedRecs",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D547F13B-1323-4D84-9675-EBFE4A93074A"
},
{
cssPosition:"60,-1,-1,557,260,24",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"24",
left:"557",
right:"-1",
top:"60",
width:"260"
},
dataProviderID:"msSubType",
enabled:true,
formIndex:0,
onDataChangeMethodID:"08C22344-**************-9506F924D7BA",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7A59093E-DAC3-4B64-B2EF-54189C524B60",
visible:true
},
name:"cboSubType",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"E746FE7F-954A-4981-BF50-4FC5A9540B9A"
}
],
name:"utils_dataImport_dtl",
onLoadMethodID:"174246E8-A3EC-49E0-A336-D6DFA1BF62D0",
onShowMethodID:"C871D3CA-DF82-47E1-9392-7A8760AA31A2",
scrollbars:33,
size:"903,586",
typeid:3,
uuid:"3FD3879E-49CB-47FE-8FD9-51827B9D0BFD"