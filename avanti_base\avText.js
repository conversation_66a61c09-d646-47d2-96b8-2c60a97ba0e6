/**
 * @properties={typeid:35,uuid:"B3E418B1-35B7-4291-B24B-1151928FA682",variableType:-4}
 */
var DONT_SHOW_MSG_AGAIN_FOR_EMP = {
	vmRoundingWarning: 1,
	FifoRebuildWarning:2,
    MAX: 2
};

/**
 * @public 
 * 
 * @type {Array<String>}
 * 
 * @properties={typeid:35,uuid:"06A55D86-B1F6-45A9-B8C3-12055446B811",variableType:-4}
 */
var asMsgsShown = [];

/**
 * @properties={typeid:35,uuid:"7CBFD7FB-3042-4E4B-AB3F-8257D9AA01E9",variableType:-4}
 */
var ok = getDlgMsg('ok')

/**
 * @properties={typeid:35,uuid:"B33B6BBC-235F-40EA-9EFF-7C7411C6D87E",variableType:-4}
 */
var yes = getDlgMsg('yes')

/**
 * @properties={typeid:35,uuid:"076E89A9-18D0-4786-852C-27542C50090A",variableType:-4}
 */
var no = getDlgMsg('no')

/**
 * @param {String} sKey
 *
 * @return
 * @properties={typeid:24,uuid:"613C4A8E-8CE8-40F9-8522-A2A988A7AC8B"}
 */
function getDlgMsg(sKey) {
	return i18n.getI18NMessage('avanti.dialog.' + sKey);
}

/**
 * @param {String} sKey
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"3C49F98C-999E-4B2A-AB4F-EF4A216081EC"}
 */
function getLblMsg(sKey) {
	return i18n.getI18NMessage('avanti.lbl.' + sKey);
}

/**
 * @param {String} sKey
 *
 * @return
 * @properties={typeid:24,uuid:"87028E32-316C-4C00-9794-311BF132ADC8"}
 */
function getTTMsg(sKey) {
	return i18n.getI18NMessage('avanti.tooltip.' + sKey);
}

/**
 * @private 
 * 
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Array<String|Number>} [aParams]
 * @param {String} sMsgType
 * @param {String} [sTitleHeader]
 * @param {Boolean} [bDontShowSameMsgMoreThanOnce]
 * @param {Number} [width]
 * @param {Number} [height]
 * 
 * @return {String} - return the msg in case we want to do something else with it - eg logging
 *
 * @properties={typeid:24,uuid:"E66EA171-59D7-4F69-A688-2BF393CF6CBE"}
 */
function showMsg(msg, bHardCodedMsg, aParams, sMsgType, sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height){
	var sNewMsg;
	
	if(bHardCodedMsg){
		sNewMsg = msg;
	}
	else if(aParams){
		sNewMsg = createParamMsg('avanti.dialog.' + msg, aParams);
	}
	else{
		sNewMsg =  getDlgMsg(msg);
	}
	
	if(bDontShowSameMsgMoreThanOnce){
		// this msg has already been shown, dont show it again
		if(asMsgsShown.indexOf(sNewMsg) > -1){
			return null;
		}
		else{
			asMsgsShown.push(sNewMsg);
		}
	}

    if(width){
        globals.DIALOGS.setDialogWidth(width);
    }
    if(height){
        globals.DIALOGS.setDialogHeight(height);
    }
    
	if(!scopes.avSystem.isUserlessProcess()) {
		if(sMsgType == 'WARNING'){
			if(sTitleHeader){
				globals.DIALOGS.showWarningDialog(sTitleHeader, sNewMsg, ok);
			}
			else{
				globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), sNewMsg, ok);
			}
		}
		else if(sMsgType == 'ERROR'){
			if(sTitleHeader){
				globals.DIALOGS.showErrorDialog(sTitleHeader, sNewMsg, ok);
			}
			else{
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('servoy.general.error'), sNewMsg, ok);
			}
		}
		else if(sMsgType == 'INFO'){
			if(sTitleHeader){
				globals.DIALOGS.showInfoDialog(sTitleHeader, sNewMsg, ok);
			}
			else{
				globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('servoy.general.info'), sNewMsg, ok);
			}
		}
	}
	else{//log the msg in the log file in non-ui context
		var level = LOGGINGLEVEL.DEBUG;
		if(sMsgType == 'WARNING'){
			level = LOGGINGLEVEL.WARNING
		}
		else if(sMsgType == 'ERROR'){
			level = LOGGINGLEVEL.ERROR
		}
		else if(sMsgType == 'INFO'){
			level = LOGGINGLEVEL.INFO
		}
		application.output("User message log in non-ui context: "+sNewMsg,level);
	}

	if(width || height){
        globals.DIALOGS.resetDialogSize();
    }
		
	return sNewMsg;
}

/**
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Array<String|Number>} [aParams]
 * @param {String} [sTitleHeader]
 * @param {Boolean} [bDontShowSameMsgMoreThanOnce]
 * @param {Number} [width]
 * @param {Number} [height]
 * 
 * @return {String} - return the msg in case we want to do something else with it - eg logging
 *
 * @properties={typeid:24,uuid:"CAE8185B-47C8-4CD8-92B6-EF96FE7FD850"}
 */
function showWarning(msg, bHardCodedMsg, aParams, sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height){
	return showMsg(msg, bHardCodedMsg, aParams, 'WARNING', sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height);
}

/**
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Array<String|Number>} [aParams]
 * @param {String} [sTitleHeader]
 * @param {Boolean} [bDontShowSameMsgMoreThanOnce]
 * @param {Number} [width]
 * @param {Number} [height]
 *
 * @return
 * @properties={typeid:24,uuid:"94334B24-59BA-4597-B7A8-6FAA4CE9EF0C"}
 */
function showError(msg, bHardCodedMsg, aParams, sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height){
    return showMsg(msg, bHardCodedMsg, aParams, 'ERROR', sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height);
}

/**
 * @param {Boolean} bTest
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Array<String|Number>} [aParams]
 *
 * @return
 * @properties={typeid:24,uuid:"E82EE190-2106-43C5-A3B1-8DEC1C84949D"}
 */
function showCondWarning(bTest, msg, bHardCodedMsg, aParams){
	if(bTest){
		showWarning(msg, bHardCodedMsg, aParams);
		return false;
	}
	
	return true;
}

/**
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Array<String|Number>} [aParams]
 * @param {String} [sTitleHeader]
 * @param {Boolean} [bDontShowSameMsgMoreThanOnce]
 * @param {Number} [width]
 * @param {Number} [height]
 *
 * @return
 * @properties={typeid:24,uuid:"88A97D86-779A-4771-BFAE-CD3031F4BAF2"}
 */
function showInfo(msg, bHardCodedMsg, aParams, sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height){
	return showMsg(msg, bHardCodedMsg, aParams, 'INFO', sTitleHeader, bDontShowSameMsgMoreThanOnce, width, height);
}

/**
 * @param {String} msg
 * @param {Boolean} [bHardCodedMsg]
 * @param {Number} [width]
 * @param {Number} [height]
 * @param {Array} [aParams] (<String|Number>)
 *
 * @return
 * @properties={typeid:24,uuid:"20FA79DB-BE9C-4F88-A5D8-98094A793CD3"}
 */
function showYesNoQuestion(msg, bHardCodedMsg, width, height, aParams){
	/*
	 * OCT/12/2023 - THIS FUNCTION ISNT WORKING IN DEV ENV (WORKING FINE IN DEPLOYED ENVS). IT ISNT RETURNING A VALUE BECAUSE EXECUTION JUMPS TO THE CALLING
	 * FUNCTION WHEN THE DIALOG OPENS. WE SHOULD STOP USING THIS FUNCTION GOING FORWARDS.
	 */
	
	if(width){
		globals.DIALOGS.setDialogWidth(width);
	}
	if(height){
		globals.DIALOGS.setDialogHeight(height);
	}
	
	var answer='';
	if(bHardCodedMsg){
		answer = globals.DIALOGS.showQuestionDialog(getDlgMsg('Question'), msg, yes, no);
	}
	else if(aParams){
		answer = globals.DIALOGS.showQuestionDialog(getDlgMsg('Question'), createParamMsg('avanti.dialog.' + msg, aParams), yes, no);
	}
	else{
		answer = globals.DIALOGS.showQuestionDialog(getDlgMsg('Question'), getDlgMsg(msg), yes, no);
	}
	
	if(width || height){
		globals.DIALOGS.resetDialogSize();
	}
	
	return answer;
}

/**
 * @param {String} title
 * @param {String} msg
 * @param {Number} [width]
 * @param {Number} [height]
 * @param {Boolean} [bHardCodedText]
 *
 * @properties={typeid:24,uuid:"C2A58F50-F499-4BAA-B83D-42FB3369248C"}
 */
function showCustomInfo(title, msg, width, height, bHardCodedText) {
	if (width) {
		globals.DIALOGS.setDialogWidth(width);
	}
	if (height) {
		globals.DIALOGS.setDialogHeight(height);
	}
	
	var sTitle = bHardCodedText ? title : scopes.avText.getLblMsg(title);
	var sMSG = bHardCodedText ? msg : scopes.avText.getLblMsg(msg);

	globals.DIALOGS.showInfoDialog(sTitle, sMSG);

	if (width || height) {
		globals.DIALOGS.resetDialogSize();
	}
}


/**
 * @return
 * @properties={typeid:24,uuid:"87840631-7DC1-4491-91F7-1CEA633DC236"}
 */
function confirmDeleteRecord(){
	return showYesNoQuestion(i18n.getI18NMessage('svy.fr.dlg.delete'), true) == yes	
}

/**
 * @param {String} sKey
 * @param {Array} aParams (<String|Number>)
 *
 * @return
 * @properties={typeid:24,uuid:"C28DCC13-ADDB-4293-987C-A101781DBB5E"}
 */
function createParamMsg(sKey, aParams){
	var msg = i18n.getI18NMessage(sKey)
	
	if(msg && aParams){
		for(var i=0;i<aParams.length;i++){
			if(aParams[i] != null){
				var paramNum = i+1
				var param = aParams[i]['toString']()
				msg = msg.replace('<param' + paramNum +'>', param)
			}
		}
	}
	
	return msg
}

/**
 * @param {String} fieldNameKey
 * @param {Boolean} [bHardCodedFieldName]
 * @param {String} [sFieldNamePrefix]
 *
 * @properties={typeid:24,uuid:"2FCA46A4-41F1-48BC-B591-3375804D3221"}
 */
function pleaseEnterAValueFor(fieldNameKey, bHardCodedFieldName, sFieldNamePrefix){
	var sFieldName = bHardCodedFieldName ? fieldNameKey : i18n.getI18NMessage(fieldNameKey);
	
	if(sFieldNamePrefix){
		sFieldName = sFieldNamePrefix + sFieldName;
	}
	
	scopes.avText.showWarning('pleaseEnterAValueFor', null, [sFieldName])
}

/**
 * @param {String} fieldNameKey
 * @param {String} [sLeadingTextKey]
 * @param {String} [sTrailingTextKey]
 *
 * @return
 * @properties={typeid:24,uuid:"09E44B48-A1C9-4E42-8883-4F437235857C"}
 */
function didntEnterAValueFor(fieldNameKey, sLeadingTextKey, sTrailingTextKey){
	var msg = createParamMsg('avanti.dialog.didntEnterAValueFor', [i18n.getI18NMessage(fieldNameKey)]);
	
	if(sLeadingTextKey){
		msg = getDlgMsg(sLeadingTextKey) + ' ' + msg;
	}
	if(sTrailingTextKey){
		msg += ' ' + getDlgMsg(sTrailingTextKey);
	}

	return showWarning(msg, true);
}

/**
 * @public 
 * 
 * @param {String} str
 * @param {String} separator
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"866617D4-B545-4573-9939-405DFCC54AD6"}
 */
function getLastSegment(str, separator) {
	var aSegs = str.split(separator);

	if (aSegs.length > 0) {
		return aSegs[aSegs.length - 1];
	}
	else {
		return "";
	}
}

/**
 * @public 
 * 
 * @param {String} sFileName
 * @param {String} sNewExt
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"********-2E73-498D-83E8-5E4A59581FF2"}
 */
function replaceFileExtension(sFileName, sNewExt) {
	var aSegs = sFileName.split(".");
	var sReturn = sFileName;
	
	if (aSegs.length > 1) {
		aSegs[aSegs.length - 1] = sNewExt;
		sReturn = aSegs.join("."); 
	}
	
	return sReturn;
}

/**
 * @param {Array} array
 * @param {String} separator
 * @param {String} [sSurroundElementChars]
 *
 * @return
 * @properties={typeid:24,uuid:"07C48D34-CE62-46FA-BC64-B589DAA3C623"}
 */
function arrayToString(array, separator, sSurroundElementChars){
	var s = '';
		
	for(var i=0;i<array.length;i++){
		if(array[i] != null){
			if(s != ''){
				s += separator;
			}
			if(sSurroundElementChars){
				s += sSurroundElementChars + array[i]['toString']() + sSurroundElementChars;
			}
			else{
				s += array[i]['toString']();
			}
		}
	}
	
	return s;
}

/**
 * @param {String} str
 * @param {String} find
 * @param {String} replace
 *
 * @return
 * @properties={typeid:24,uuid:"711D4F9A-F38F-4FCA-A04D-3995FB2C0E7D"}
 */
function replaceAll(str, find, replace) {
	if (str == null) {
		return null;
	}
	else {
		// Fixing Trailing exception
		if (find.equals('\\')) {
			var sResult = str;
			while (sResult.indexOf(find) >= 0) {
				sResult = sResult.replace(find,replace);
			}
			return sResult;
		}
		return str.replace(new RegExp(find, 'g'), replace);
	}
}

/**
 * @param {String} sString
 * @param {Number} nNumRepeats
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"1FC117A0-9873-4B2B-9693-F1E9FAF03D7F"}
 */
function repeatString(sString, nNumRepeats){
	var sReturn = '';
	
	for(var i=1;i<=nNumRepeats;i++){
		sReturn += sString;
	}
	
	return sReturn;
}

/**
 * @public 
 * 
 * @param {String} sString
 * @param {Number} nNumChars
 * 
 * @return {String}
 *
 * @return
 * @properties={typeid:24,uuid:"B954C5A8-4E43-4E3D-9540-B6F3E708FC26"}
 */
function getLastNumChars(sString, nNumChars){
	if(sString.length <= nNumChars){
		return sString;
	}
	else{
		return sString.substr(sString.length-nNumChars, nNumChars);
	}
}

/**
 * @public 
 * 
 * @param {String} sString
 * @param {Number} nNumChars
 * 
 * @return {String}
 *
 * @return
 * @properties={typeid:24,uuid:"5F0C515D-6B91-4080-B3A4-69BA7C3ED869"}
 */
function getFirstNumChars(sString, nNumChars){
	if(sString.length <= nNumChars){
		return sString;
	}
	else{
		return sString.substr(0, nNumChars);
	}
}

/**
 * @param {String} sString
 * @param {String} sPadChar
 * @param {Number} nPadCharNum
 *
 * @return
 * @properties={typeid:24,uuid:"4B3428F8-C20F-4269-B5FD-61CC65FE103F"}
 */
function padBefore(sString, sPadChar, nPadCharNum){
	if(sString.length >= nPadCharNum){
		return sString;
	}
	else{
		var sLen = sString.length;
		for(var i=1; i<= nPadCharNum - sLen; i++){
			sString = sPadChar + sString;
		}
		
		return sString;
	}
}

/**
 * @param {String} sString
 * @param {String} sPadChar
 * @param {Number} nPadCharNum
 *
 * @return
 * @properties={typeid:24,uuid:"358E9460-5FF5-49A5-871F-FB4DB1DE3470"}
 */
function padAfter(sString, sPadChar, nPadCharNum){
	if(sString.length >= nPadCharNum){
		return sString;
	}
	else{
		var sStringLen = sString.length;
		
		for(var i=1; i<= nPadCharNum - sStringLen; i++){
			sString += sPadChar;
		}
		
		return sString;
	}
}

/**
 * @param {String} sString
 * @param {Number} nSize
 * @param {Number} [nRightJustify]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"FD380CB3-1A9F-4406-9257-8F9F535F34A9"}
 */
function setStringSize(sString, nSize, nRightJustify){
	if(sString == null || sString == undefined){
		sString = '';
	}
	
	if(sString.length == nSize){
		return sString;
	}
	else if(sString.length > nSize){
		return sString.substr(0, nSize);
	}
	else{ // less than
	    var sFiller = '';
		while((sString.length + sFiller.length) < nSize){
		    sFiller += ' ';
		}
		
        if (nRightJustify) {
            sString = sFiller + sString;
        }
        else {
            sString += sFiller;
        }
		
		return sString;
	}
}

/**
 * @param {String} sTestValue
 * @param {String} sSearchValue
 * @param {Boolean} [bCaseSensitive]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2800C05E-DB8C-47E4-B1A9-87395A79B7AC"}
 */
function startsWith(sTestValue, sSearchValue, bCaseSensitive){
	if(!bCaseSensitive){
		sTestValue = sTestValue.toLowerCase();
		sSearchValue = sSearchValue.toLowerCase()
	}

	return sTestValue.indexOf(sSearchValue) == 0;
}

/**
 * @param {String} sTestValue
 * @param {String} sSearchValue
 * @param {Boolean} [bCaseSensitive]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"435175E8-6A92-408C-9558-14C322A1BAED"}
 */
function contains(sTestValue, sSearchValue, bCaseSensitive){
	if(!bCaseSensitive){
		sTestValue = sTestValue.toLowerCase();
		sSearchValue = sSearchValue.toLowerCase()
	}

	return sTestValue.indexOf(sSearchValue) > -1;
}

/**
 * @param anyVal
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"6B31ACA7-D552-4E47-B818-46E959D24F81"}
 */
function toString(anyVal){
	if(anyVal == null || anyVal == undefined){
		return '';
	}
	else{
		return anyVal.toString();
	}
}

/**
 * @param {String} s1
 * @param {String} s2
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"D93C9E64-85A7-4DBC-B0F9-1A8A90192909"}
 */
function eq(s1, s2){
	if(s1 == null) s1 = '';
	if(s2 == null) s2 = '';
	
	return s1 == s2;
}

/**
 * @param {String} fieldNameKey1
 * @param {String} fieldNameKey2
 * @param {Boolean} [bHardCodedFieldNames]
 *
 * @properties={typeid:24,uuid:"27DD7317-2AC1-41EA-96C9-8342A222685F"}
 */
function cantBeGreaterThan(fieldNameKey1, fieldNameKey2, bHardCodedFieldNames){
	if(bHardCodedFieldNames){
		scopes.avText.showWarning('cantBeGreaterThan', null, [fieldNameKey1, fieldNameKey2])
	}
	else{
		scopes.avText.showWarning('cantBeGreaterThan', null, [getLblMsg(fieldNameKey1), getLblMsg(fieldNameKey2)])
	}
}

/**
 * @public 
 * @param {String} fieldNameKey1
 * @param {String} fieldNameKey2
 * @param {Boolean} [bHardCodedFieldNames]
 *
 * @properties={typeid:24,uuid:"13DE7BCD-8F67-43BD-BCAB-6A5DCD5BA051"}
 */
function cantBeLessThan(fieldNameKey1, fieldNameKey2, bHardCodedFieldNames){
	if(bHardCodedFieldNames){
		scopes.avText.showWarning('cantBeLessThan', null, [fieldNameKey1, fieldNameKey2])
	}
	else{
		scopes.avText.showWarning('cantBeLessThan', null, [getLblMsg(fieldNameKey1), getLblMsg(fieldNameKey2)])
	}
}

/**
 * @public 
 * @param {String} fieldNameKey
 * @param {Number} nCompareValue
 *
 * @properties={typeid:24,uuid:"2E4A7528-A40C-45EC-B4F0-01B72BD5443D"}
 */
function cantBeGreaterThanValue(fieldNameKey, nCompareValue){
	scopes.avText.showWarning('cantBeGreaterThan', null, [getLblMsg(fieldNameKey), nCompareValue])
}

/**
 * @public 
 * @param {String} fieldNameKey
 * @param {Number} nCompareValue
 *
 * @properties={typeid:24,uuid:"358C0054-D8B7-4F93-8398-2F98C30B101F"}
 */
function cantBeLessThanValue(fieldNameKey, nCompareValue){
	scopes.avText.showWarning('cantBeLessThan', null, [getLblMsg(fieldNameKey), nCompareValue])
}

/**
 * @public 
 * @param {String} fieldNameKey
 * @param {Number} nCompareValue
 *
 * @properties={typeid:24,uuid:"0CB36B35-5539-4D02-B925-FD4330EB2385"}
 */
function mustBeGreaterThanValue(fieldNameKey, nCompareValue){
	scopes.avText.showWarning('mustBeGreaterThan', null, [getLblMsg(fieldNameKey), nCompareValue])
}

/**
 * @public 
 * @param {String} fieldNameKey
 * @param {Number} nCompareValue
 *
 * @properties={typeid:24,uuid:"318E4F45-913D-466C-B4EF-8193ADD60546"}
 */
function mustBeLessThanValue(fieldNameKey, nCompareValue){
	scopes.avText.showWarning('mustBeLessThan', null, [getLblMsg(fieldNameKey), nCompareValue])
}

/**
 * @param {String} sMainString
 * @param {String} sSearchString
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"A291F4FD-17FF-4B76-9021-1FC23CACC005"}
 * @AllowToRunInFind
 */
function getNumTimesStringAppears(sMainString, sSearchString){
	var regExp = new RegExp(sSearchString, "g");
	return (sMainString.match(regExp) || []).length;
}

/**
 * @param {Array<String>} asKeys
 * @param {String} [sSeparator]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"1F715D85-8875-4E31-B32E-E4339DCF79DC"}
 */
function getCompoundLblMsg(asKeys, sSeparator){
	var sMsg = '';
	
	if(asKeys.length > 0){
		if(!sSeparator) sSeparator = ' ';
		
		for(var i=0; i<asKeys.length; i++){
			if(sMsg != '') sMsg += sSeparator;
			sMsg += getLblMsg(asKeys[i]);
		}
	}
	
	return sMsg;
}

/**
 * @public 
 * 
 * @param {String} sErrMsg - ex.message from catch block
 * @param {String} sFuncName
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"3955086E-97A9-4AA1-A84B-5129E45031D0"}
 */
function errorOccurredInFunc(sErrMsg, sFuncName){
	if(sErrMsg && sFuncName){
		var aParams = [sFuncName, sErrMsg];
		
		return showMsg('errOccurredCallAvanti', false, aParams, 'ERROR');
	}
	else{
		return null;
	}
}

/**
 * @param sErrMsg
 *
 * @properties={typeid:24,uuid:"CAA185FC-D579-45BE-BAE7-FAAA46796C1B"}
 */
function showRunTimeErrorDlg(sErrMsg){
	var sMsg = i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + '\n\n' + sErrMsg + '\n\n' +
		i18n.getI18NMessage('avanti.dialog.contactSupport');

	globals.DIALOGS.setDialogWidth(750);
	globals.DIALOGS.setDialogHeight(250);
	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('servoy.general.error'), sMsg, i18n.getI18NMessage('avanti.dialog.ok'));
	globals.DIALOGS.resetDialogSize();
}

/**
 * @param {String} sMainString
 * @param {String} sReplaceString
 * @param {Number} nReplacePos
 *
 * @return
 * @properties={typeid:24,uuid:"3967CE41-8E4D-4697-9827-2E740A115E9D"}
 */
function replaceCharsAt(sMainString, sReplaceString, nReplacePos){
	var sStart = '';
	var sEnd = '';
	
	if(nReplacePos > 1){
		sStart = sMainString.substr(0, nReplacePos-1); 
	}
	
	if(sMainString.length >= nReplacePos + sReplaceString.length){
		sEnd = sMainString.substr(nReplacePos + sReplaceString.length-1); 
	}
	 
	return sStart + sReplaceString + sEnd;
}

/**
 * displays warning for duplicate code - pass in type of code
 * 
 * @public 
 * 
 * @param {String} sCodeTypeKey
 *
 * @properties={typeid:24,uuid:"5EF14D01-9F45-4547-9EB4-68A8C8AC7E95"}
 */
function duplicateCodeWarning(sCodeTypeKey){
	showWarning('duplicateCode', false, [i18n.getI18NMessage(sCodeTypeKey)]);
}

/**
 * @public 
 * 
 * clears the asMsgsShown array which is used to prevent the same msg from appearing more than once in the same processing cycle
 * 
 * @properties={typeid:24,uuid:"6BA93845-77D7-4DF9-ACB8-1CE3CFCD53B9"}
 */
function clearMsgsShown(){
	asMsgsShown = [];
}

/**
 * @public 
 * @param {JSFoundSet} [foundset]
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"2BCC711B-4B55-425A-82E9-4C1BC0B39785"}
 */
function areYouSureYouWantToDeleteThisRec(foundset){
	var bDelete = globals.DIALOGS.showQuestionDialog(getDlgMsg('Question'), 
		i18n.getI18NMessage('svy.fr.dlg.delete'), yes, no) == yes;

	if(bDelete && foundset){
		foundset.deleteRecord();
	}
	
	return bDelete;
}

/**
 * @public 
 * @param {JSFoundSet} [foundset]
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"32EE0B4B-75E4-4027-A330-5D55DEC637A7"}
 */
function areYouSureYouWantToDeleteAllRecs(foundset){
	var bDelete = globals.DIALOGS.showQuestionDialog(getDlgMsg('Question'), 
		i18n.getI18NMessage('avanti.dialog.deleteAllMsg'), yes, no) == yes;

	if(bDelete && foundset){
		foundset.deleteAllRecords();
	}
	
	return bDelete;
}

/**
 * @public 
 * 
 * @param {String} sFieldNameKey
 *
 * @properties={typeid:24,uuid:"DA4C96D5-AEB8-43EB-B8D7-EEC4E1C2EBB9"}
 */
function mandatoryField(sFieldNameKey){
    var msg = getLblMsg(sFieldNameKey) + getDlgMsg('isAMandatoryField');
    showWarning(msg, true);
}

/**
 * This appends a '\' or '/' (it decides which to use based on which one already exists in sFolderPath) to the end of sFolderPath 
 * if it isnt already there.
 * 
 * @public 
 * 
 * @param {String} sFolderPath
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"E97B4368-74E1-47DF-BDAC-A02D723DC792"}
 */
function appendFolderPathSeparator(sFolderPath) {
    if (sFolderPath) {
        var sLastChar = sFolderPath.charAt(sFolderPath.length - 1);
        var sSlashChar = null;
        
        if (sFolderPath.indexOf('\\') > -1) {
            sSlashChar = '\\';
        }
        else if (sFolderPath.indexOf('/') > -1) {
            sSlashChar = '/';
        }

        if (sSlashChar && sLastChar != sSlashChar) {
            sFolderPath += sSlashChar;
        }
    }
    
    return sFolderPath;
}

/**
 * @public 
 * 
 * @param {String} s
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"BCFB0298-BD09-4ED4-84CF-A9BC55F8F5FD"}
 */
function trim(s) {
	if (s && typeof s == "string") {
		return plugins.it2be_tools.trim(s);
	}
	else {
		return null;
	}
}

/**
 * @public 
 * 
 * @param {String} sFileName
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"36DF8512-8439-40DD-AC65-E08000F5A59F"}
 */
function removeInvalidFileNameCharacters(sFileName) {
	if (sFileName) {
		// these are all the allowed chars (disallowed are \/:*?"<>|)
		sFileName = sFileName.replace(/[^a-zA-Z0-9-_\. !@#$%^(){}\[\];,'`~+=]/g, '');
	}
	
	return sFileName;
}

/**
 * We sometimes use uuid in a dialog form name, to make it unique. but servoy doesnt like '-' in form name, it gives a warning. this func replaces '-' with '_'. it doesnt account for
 * other invalid chars. as they come to light we can update this func.
 * 
 * @public 
 * 
 * @param {String} sFormName
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"0E91D6A7-B3FE-4F55-B1A1-49768B318CAD"}
 */
function cleanFormName(sFormName) {
	return replaceAll(sFormName, "-", "_");
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sys_employee>} rEmp
 * @param {Number} nMsgFlag
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E208C146-60AF-4EDD-859D-14B08445287F"}
 */
function allowEmpMsgToBeShownAgain(rEmp, nMsgFlag) {
	if (scopes.avUtils.doesFlagsetContainFlag(rEmp.empl_dont_show_msg_again_flagset, nMsgFlag, DONT_SHOW_MSG_AGAIN_FOR_EMP.MAX)) {
		return false;
	}
	else {
		return true;
	}
}

/**
 * @public 
 * 
 * @param {String} s
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"1A74C8F3-5B2B-4486-88AF-9D2F177898CB"}
 */
function incrementAlphanumeric(s) {
    // Match the trailing digit or letter segment
	const match = s.match(/(\d+|[A-Za-z]+)$/);
	
	if (!match) {
		// Input must end with digits or letters.
		return null;
	}

	const prefix = s.slice(0, match.index);
	const core = match[0];

	var incremented;

	if (/^\d+$/.test(core)) {
		// It's numeric: increment and preserve leading zeros
		const num = parseInt(core, 10) + 1;
		incremented = num.toString().padStart(core.length, '0');
	}
	else if (/^[A-Za-z]+$/.test(core)) {
		// It's alphabetic: increment like base-26
		incremented = incrementAlpha(core);
	}
	else {
		// Core segment must be digits or letters only.
		return null;
	}

    return prefix + incremented;
}

/**
 * @public 
 * 
 * @param {String} s
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"DA7F424E-7A61-4172-A7F1-0D152D82A9E2"}
 */
function incrementAlpha(s) {
    s = s.toUpperCase().split('');
    var i = s.length - 1;

	while (i >= 0) {
		if (s[i] === 'Z') {
			s[i] = 'A';
			i--;
		}
		else {
			s[i] = String.fromCharCode(s[i].charCodeAt(0) + 1);
			return s.join('');
		}
	}

    // If all characters were 'Z', prepend an 'A'
    return 'A' + s.join('');
}

/**
 * @public 
 * 
 * @param {String} str
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"A11476DA-4C40-48C9-9678-B7BA405E154D"}
 */
function isAlphanumeric(str) {
	  return /^[a-zA-Z0-9]+$/.test(str);
}
