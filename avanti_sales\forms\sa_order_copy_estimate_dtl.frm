customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/tmp_estimate_copy_detail",
extendsID:"8ADD6C71-2C60-4BC3-B464-5D14D2B883EE",
items:[
{
height:135,
partType:8,
typeid:19,
uuid:"2B5938FD-FD48-4BEE-8E67-E8CB1A72DE77"
},
{
height:130,
partType:5,
typeid:19,
uuid:"3D0F3FBA-3946-404F-AC61-D1BF91B6637F"
},
{
cssPosition:"4,-1,-1,120,110,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"120",
right:"-1",
top:"4",
width:"110"
},
enabled:true,
onActionMethodID:"F56EF92A-E7CF-4F75-8A6D-F9008B3F1D89",
styleClass:"btn btn-default button_bts",
tabSeq:2,
text:"Check None",
visible:true
},
name:"btnUnCheckAll",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"5697EA6E-C6E7-4709-B8A0-3320C6AC0D69"
},
{
height:30,
partType:1,
typeid:19,
uuid:"83EBD2CA-3E8B-451F-B84B-53A42A657716"
},
{
anchors:15,
cssPosition:"30px,0px,5px,0px,617px,100px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"tmp_select_flag",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.copy",
id:"tmp_select_flag",
maxWidth:40,
minWidth:40,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"4114F1F0-F9D7-4BD4-B9CD-FC84F3523D16",
valuelist:null,
visible:true,
width:40
},
{
autoResize:false,
dataprovider:"tmp_estimate_copy_detail_to_sa_order_revision_detail_leftJoin.sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.lineNumber",
id:"sequence_nr",
maxWidth:60,
minWidth:60,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"3B0B9866-E97F-4ED0-BB77-4B6E302621FB",
valuelist:null,
visible:true,
width:60
},
{
autoResize:true,
dataprovider:"tmp_estimate_copy_detail_to_sa_order_revision_detail_leftJoin.ordrevd_prod_desc",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.description",
id:"ordrevd_prod_desc",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"0D78CE52-4DA4-4162-9242-E075BF6D51AC",
valuelist:null,
visible:true,
width:251
},
{
autoResize:true,
dataprovider:"tmp_estimate_copy_detail_to_sa_order_revision_detail_qty_leftJoin.clc_price",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"globals.avBase_currencyFormat",
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.extendedPrice",
id:"ordrevd_extended_price",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"F4DFD464-3DF4-421E-9D5F-5E491B42F59D",
valuelist:null,
visible:true,
width:120
},
{
autoResize:true,
dataprovider:"ordrevdqty_id",
editType:"COMBOBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.orderQuantity",
id:"tmp_qty_selected",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"34BB70EA-4A5C-4635-AE4A-0F2B09739B5F",
valuelist:"FAFE7425-6CB5-4F2D-9F6E-E322F9DBDAE6",
visible:true,
width:98
},
{
autoResize:false,
dataprovider:"tmp_multiple_qty_flag",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.blank",
id:"tmp_multiple_qty_flag",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"F1008FB1-7106-4796-A064-76546A5F7B45",
tooltip:"tmp_multiple_qty_flag_tooltip",
valuelist:null,
visible:true,
width:20
}
],
cssPosition:{
bottom:"5px",
height:"100px",
left:"0px",
right:"0px",
top:"30px",
width:"617px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"F23A2FB7-4797-4950-B241-C6E1FFE7B745",
onReady:"AD7B6012-046B-4972-8372-B5C8AB2FCE4F",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"5738FACA-9879-472F-A4C1-840C8770C622"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"E0C2BC95-F038-417E-ABB6-B3B2759F9C20"
},
{
cssPosition:"4,-1,-1,5,110,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"4",
width:"110"
},
enabled:true,
onActionMethodID:"0A9E0A57-04DA-4957-8C4E-C98534607E3C",
styleClass:"btn btn-default button_bts",
tabSeq:1,
text:"Check All",
visible:true
},
name:"btnCheckAll",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"FD4195DC-9A87-4DF5-95BA-816C5526BB68"
}
],
name:"sa_order_copy_estimate_dtl",
navigatorID:"-1",
onShowMethodID:"5E291101-A84B-4C17-A46B-9A652517C585",
scrollbars:33,
size:"617,98",
styleName:null,
typeid:3,
uuid:"26F29983-BD37-4FD6-980E-9CFCB6D385BD",
view:0