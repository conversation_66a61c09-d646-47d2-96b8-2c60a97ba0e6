customProperties:"methods:{\
onRecordSelectionMethodID:{\
arguments:null,\
parameters:null\
},\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/svy_framework/nav_popmenu",
extendsID:"F92DED23-7E96-4958-98DE-F7144A15A08A",
items:[
{
cssPosition:"5,-1,-1,0,24,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"0",
right:"-1",
top:"5",
width:"24"
},
enabled:true,
onActionMethodID:"7003B879-2B9C-4281-9FA6-268C7BACDA5F",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0532D2DB-BF6A-46BE-ABEF-4DA0F4CB28EA"
},
{
anchors:15,
cssPosition:"30px,0px,5px,0px,240px,220px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"rpt_menu_selected",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnSelect",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
showAs:"html",
svyUUID:"16D435A6-5F3F-4722-BA91-7E2C4409B13F",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:"label",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.rpt_menu",
id:"fldMenu",
maxWidth:165,
minWidth:165,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"B2AE72E7-5663-461E-8141-CB3E8B3474A6",
valuelist:null,
visible:true,
width:165
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnSort",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined swap_vert",
svyUUID:"CBB183C2-EC8D-4F13-8F1B-C45373173683",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnDelete",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined delete",
svyUUID:"69391683-174B-44B5-8530-A4A0A5DEFA80",
valuelist:null,
visible:true,
width:25
}
],
cssPosition:{
bottom:"5px",
height:"220px",
left:"0px",
right:"0px",
top:"30px",
width:"240px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"0666EE44-FEBE-4DD4-B725-88A529ED17D0",
onColumnDataChange:"DD583838-0E75-481C-BEBF-8241EBE2D827",
onReady:"AA04B889-3B54-42C2-9A0C-CB871DBB3A49",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"B0D39670-A6A9-4D4C-82EA-04A64E2C31EB"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"518A706E-BC2F-4356-B821-4A3276A87591"
},
{
height:255,
partType:8,
typeid:19,
uuid:"90231205-131C-47A0-B5F8-946C2C613F79"
},
{
height:480,
partType:5,
typeid:19,
uuid:"A5D36A8B-7B6A-422B-9C16-8DB1361C0178"
},
{
cssPosition:"5,-1,-1,25,210,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"25",
right:"-1",
top:"5",
width:"210"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.addMenu",
visible:true
},
name:"component_E6501B5E",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ACB03BD7-4678-4BC5-8AD9-9377458FB105"
},
{
extendsID:"386268E2-9F97-4AE9-B3DF-F450E25EE030",
height:250,
typeid:19,
uuid:"E6399D64-AB5B-4D55-802C-FB29BC0391AC"
}
],
name:"rpt_report_dtl_tab_menus",
navigatorID:"-1",
onRecordSelectionMethodID:"-1",
onShowMethodID:"4420B5E7-9EF5-4B25-8683-72C63EDA4E8C",
paperPrintScale:100,
scrollbars:33,
size:"240,480",
styleClass:"border",
styleName:null,
typeid:3,
uuid:"11ABA6FB-4771-4DA1-A6D7-F39AFB85A1D7",
view:0