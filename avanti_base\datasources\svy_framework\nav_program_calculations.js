/**
 * @properties={type:12,typeid:36,uuid:"3BB679CF-6BB0-4674-B9DD-581DC7401E60"}
 */
function rpt_selected_system()
{
	/** @type {JSFoundSet} */
	var fs = eval("rpt_report_system_nav_program_to_app_nav_popmenu$avrpt_selectedreport");
	if(utils.hasRecords(fs)) //trying the following change for titanium checkbox display, by commenting out the checkbox icon html value
	{
		return 1;//globals.icon_checkbox_checked;
	}
	else
	{
		return 0;//globals.icon_checkbox_unchecked;
	}
}

/**
 * @properties={type:12,typeid:36,uuid:"D67DBDFE-0B22-44E4-A089-8CBE49B907F8"}
 */
function rpt_selected()
{
	
	if(utils.hasRecords(rpt_report_nav_program_to_nav_popmenu$avrpt_selectedreport))
	{
		return 1;//globals.icon_checkbox_checked; 
	}
	else
	{
		return 0;//globals.icon_checkbox_unchecked;
	}
}
