/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C01E21EA-7175-4CED-B3CF-50658B485B9C"}
 */
var oldPathUUID = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0E0BE6F7-9374-43B5-A923-06A191B903F1"}
 */
var selected_udf_code = '';

/**
 * Reload the tree based on file  
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"238D3A7E-30D0-4CD0-977C-0BBF5327179A"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	
	var listOfItems = application.getValueListItems('vl_AppUDFCode')
	selected_udf_code = listOfItems.getValue(1,1)
	refreshTree();
	return
}

/**
 * Make the form editable on show.
 *  
 * @param _firstShow
 * @param _event
 *
 * @return
 * @properties={typeid:24,uuid:"2FFB7C55-8919-419D-8E43-BAF7A6BC0108"}
 */
function onShowForm(_firstShow, _event) {
	var result =  _super.onShowForm(_firstShow, _event);
	controller.readOnly = false;
	globals.svy_nav_setFieldsColor(controller.getName(), 'edit');
	return result;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"EB04AF28-9989-4B1B-8D9B-C550302CCF29"}
 * @AllowToRunInFind
 */
function onDataChangeUDFCode(oldValue, newValue, event) {
	foundset.clear();
	refreshTree();
	return true;
}

/**
 * Reload the tree in the UDF Maintenance screen. 
 * 
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"6FBE3A5D-D3C5-4078-985B-76FF04D18F32"}
 */
function refreshTree() {
	if(selected_udf_code == 'CUSTOMER'){
		setSplitBillFieldNums()	
	}
	
	// Remove roots of tree
	elements.sys_udf_tree.removeAllRoots()
	forms.sys_udf_type_dtl_field_move.elements.sys_udf_tree_move.removeAllRoots()

	// Reset binding of tree.
	var udf_binding = elements.sys_udf_tree;
	udf_binding.setChildSortDataprovider(foundset.getDataSource(),'sys_udf_sort')
	udf_binding.setNRelationName(foundset.getDataSource(),'sys_udf_type_to_sys_udf_type_parent_child') 
	udf_binding.setTextDataprovider(foundset.getDataSource(),'udf_display_value')
	
	// Reset binding of tree.
	var udf_move_binding = forms.sys_udf_type_dtl_field_move.elements.sys_udf_tree_move;
	udf_move_binding.setChildSortDataprovider(foundset.getDataSource(),'sys_udf_sort')
	udf_move_binding.setNRelationName(foundset.getDataSource(),'sys_udf_type_to_sys_udf_type_parent_child') 
	udf_move_binding.setTextDataprovider(foundset.getDataSource(),'udf_display_value')
	if (foundset.find())
	{
		foundset.udf_code = selected_udf_code
		foundset.parent_sys_udf_type_id = '^||0'
		if (foundset.search() > 0) {
			foundset.sort('sequence_nr asc')
			elements.sys_udf_tree.addRoots(foundset);
			forms.sys_udf_type_dtl_field_move.elements.sys_udf_tree_move.addRoots(foundset)
		}
	}
		
	
	
	// Add nodes of tree.
	
	
	// Reload the associated Customer UDF tree.
	forms['sys_udf_tree'].loadTree();

}

/**
 * @properties={typeid:24,uuid:"0F275454-0630-4AC8-86EA-16047B42707B"}
 */
function setSplitBillFieldNums(){
	/***@type {JSFoundset<db:/avanti/sys_udf_type>} */
	var fs_sys_udf_type = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type')
	fs_sys_udf_type.loadAllRecords()
	fs_sys_udf_type.sort('sequence_nr asc')
	
	var iNumRecs = fs_sys_udf_type.getSize()
	if(iNumRecs > 0){
		var iNextSplitBillFieldNum=1
		for(var i=1;i<=iNumRecs;i++){
			var rec_sys_udf_type = fs_sys_udf_type.getRecord(i)
			if(rec_sys_udf_type.udf_split_bill_field){
				rec_sys_udf_type.udf_split_bill_field_num=iNextSplitBillFieldNum
				iNextSplitBillFieldNum++
			}
			else{
				rec_sys_udf_type.udf_split_bill_field_num=0
			}
		}

		databaseManager.saveData(fs_sys_udf_type)
	}
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"6D4D2EA3-5DAD-4F02-9A36-DAF7DA0593FA"}
 */
function onActionAddField(event) {
	
	// Remove any existing searchs and create a new record so we can associate any table values if needed.
	foundset.clear();
	foundset.newRecord();
	databaseManager.saveData();
	
	// Default the type to TEXT 
	forms.sys_udf_type_dtl_field.udf_field_type_input = 'TEXT'
	forms.sys_udf_type_dtl_field.udf_field_type_mode = 'add'
		
	// Associate the new dialog with the new record created.
	forms.sys_udf_type_dtl_field.foundset.loadRecords(foundset.sys_udf_type_id)
	forms.sys_udf_type_dtl_field.udf_field_input = ''
	
	// Get user input from Add Field dialog
	globals.DIALOGS.showFormInModalDialog(forms.sys_udf_type_dtl_field, -1, -1, -1, -1, i18n.getI18NMessage('avanti.lbl.UDFAddField'), false, false, 'newPreferenceFolder', true)

}

/**
 * @properties={typeid:24,uuid:"5BBA0928-6D3B-48EB-AA90-F76A36D55DAD"}
 */
function getTreeSelectionPath(){
	if (elements.sys_udf_tree.getSelectionPath() && elements.sys_udf_tree.getSelectionPath().length>0) {
		return elements.sys_udf_tree.getSelectionPath()[elements.sys_udf_tree.getSelectionPath().length-1]
	}
	return null;
}

/**
 * Actions to perform after OK is pressed in Add dialog
 * 
 * @properties={typeid:24,uuid:"AB6C4DF1-14AB-4BC3-8887-FF428C99DAA5"}
 */
function addField(result) {
		
	if (result != 'OK')
	{
		forms.sys_udf_type_dtl_field.udf_field_input = ''
		foundset.deleteRecord();
		
	} else {
		// Update the new record with entries from the user dialog.
		foundset.udf_code = selected_udf_code
		var pathId = getTreeSelectionPath();
		if (pathId != foundset.sys_udf_type_id) {
			foundset.parent_sys_udf_type_id = pathId;
		}
		foundset.udf_field = forms.sys_udf_type_dtl_field.udf_field_input
		foundset.udf_field_type = forms.sys_udf_type_dtl_field.udf_field_type_input
		
		// Added this if statement to create a record in sys_udf_values to pre-load the multi-select and table value input options.
		// If this isn't here, when you create a UDF field, the list won't populate until the second time you load the list in the Customer Screen.
		if(foundset.udf_field_type == 'MULTI_SELECT' || foundset.udf_field_type == 'TABLE_VALUE'){
			/*** @type {JSFoundSet<db:/avanti/sys_udf_values>} */
			var sa_customer_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_values')
			var newCustomerUDFRec = sa_customer_fs.getRecord(sa_customer_fs.newRecord())
			newCustomerUDFRec.sys_udf_type_id = foundset.sys_udf_type_id
			newCustomerUDFRec.udf_answer = ''
			
			if(selected_udf_code == 'CUSTOMER') {
				/*** @type {JSFoundSet<db:/avanti/sa_customer>} */
				var customer_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer').duplicateFoundSet()
				customer_fs.loadAllRecords()
				customer_fs.sort('cust_code asc')
				if(customer_fs.getSize() > 0) {
					var customerRec = customer_fs.getRecord(1)
					newCustomerUDFRec.unique_id = customerRec.cust_id
				} else {
					newCustomerUDFRec.unique_id = null
				}
			} else if(selected_udf_code == 'CONTACT') {
				/*** @type {JSFoundSet<db:/avanti/sa_customer_contact>} */
				var contact_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_contact').duplicateFoundSet()
				contact_fs.loadAllRecords()
				contact_fs.sort('custcontact_id asc')
				if(contact_fs.getSize() > 0) {
					var contactRec = contact_fs.getRecord(1)
					newCustomerUDFRec.unique_id = contactRec.custcontact_id
				} else {
					newCustomerUDFRec.unique_id = null
				}
			}
			
		}
		
		var newRec = foundset.getSelectedRecord()
		onInsertRecord_sequenceNr(newRec)
		setDepth(newRec)
		
		var success = databaseManager.saveData()
		if (!success) {
			var failedRecords = databaseManager.getFailedRecords();
			for (var i = 0; i < failedRecords.length; i++) {
				var failedRecord = failedRecords[i]
				application.output(failedRecord.exception,LOGGINGLEVEL.ERROR);
			}
		}

		scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);
	}	
	refreshTree();
}

/**
 * Set correct sequence number for new record.
 *  
 * @param {JSRecord<db:/avanti/sys_udf_type>} jsRec
 *
 * @return
 * @properties={typeid:24,uuid:"1016DFDA-725D-4978-9AA7-2819FB6185A2"}
 * @AllowToRunInFind
 */
function onInsertRecord_sequenceNr(jsRec)
{
	if (jsRec.sequence_nr > 0) return true
	
	/** @type{JSFoundSet<db:/avanti/sys_udf_type>} */
	var duplicate_fs = jsRec.foundset.duplicateFoundSet()
	
	// Find the max sequence for that parent and set sequence to that plus 1 (to set it to bottom of parent)
	if(duplicate_fs.find() || duplicate_fs.find()) {
		duplicate_fs.udf_code = selected_udf_code
		if(jsRec.parent_sys_udf_type_id == null) {
			duplicate_fs.parent_sys_udf_type_id = '^' 
		} else {
			duplicate_fs.parent_sys_udf_type_id = jsRec.parent_sys_udf_type_id
		}
		
		if(duplicate_fs.search() > 0) {
			duplicate_fs.sort('sequence_nr desc')
			jsRec.sequence_nr = duplicate_fs.getRecord(1).sequence_nr + 1
			databaseManager.saveData(duplicate_fs);
		}
	}
	// If the first record, then set to 1.
	if (jsRec.sequence_nr == null)
	{
		jsRec.sequence_nr = 1
	}
	
	return true
}

/**
 * Set depth of new record. 
 * 
 * @param {JSRecord<db:/avanti/sys_udf_type>} jsRec
 *
 * @properties={typeid:24,uuid:"0129AAF5-01FB-41FC-BB5B-29619089E204"}
 * @AllowToRunInFind
 */
function setDepth(jsRec) {
	if(jsRec.parent_sys_udf_type_id == null) {
		jsRec.depth = 1
	} else {
		var duplicate_fs = jsRec.foundset.duplicateFoundSet()
		if(duplicate_fs.find() || duplicate_fs.find()) {
			duplicate_fs.sys_udf_type_id = jsRec.parent_sys_udf_type_id
			if(duplicate_fs.search() > 0) {
				jsRec.depth = duplicate_fs.depth + 1
			} else {
				jsRec.depth = 1
			}
		}
	}
}

/**
 * Delete a field based on user selection.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D77CAEA6-9407-499F-B5D4-02D06428C05E"}
 * @AllowToRunInFind
 */
function onActionDeleteField(event) {
	
	var pathUUID = getTreeSelectionPath();
	
	if(pathUUID == null) {
		return
	}
	
	if(foundset.find()) {
		// Load foundset record based on the selected path
		foundset.sys_udf_type_id = pathUUID
		if(foundset.search()) { 
			forms.sys_udf_type_dtl_field_delete.selectedRecord = foundset.udf_field
			// get confirmation from user to delete it.
			globals.DIALOGS.showFormInModalDialog(forms.sys_udf_type_dtl_field_delete, -1, -1, -1, -1, i18n.getI18NMessage('avanti.dialog.deleteNotification'), false, false, 'newPreferenceFolder', true)
			
		}
	}
}

/**
 * Actions to perform after OK is pressed in Delete dialog
 * 
 * @param {Object} result
 *
 * @properties={typeid:24,uuid:"9CA18449-2B91-4476-86E3-938EBAC6BD8C"}
 */
function deleteField(result) { 
	if(result == 'OK') {
		var origRec = foundset.getSelectedRecord();
		
		// Delete the selected record and associated table values.
		foundset.deleteRecord();
		
		onDeleteRecord_sequenceNr(origRec);
		
		scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);		
	}
	refreshTree();
	
}

/**
 * Update the sequences by -1 after a delete.
 * 
 * @param {JSRecord<db:/avanti/sys_udf_type>} jsRec
 *
 * @return
 * @properties={typeid:24,uuid:"937D32F3-64C7-446A-A882-E4D620CF39A8"}
 * 
 * @AllowToRunInFind
 * 
 */
function onDeleteRecord_sequenceNr(jsRec)
{
	/** @type{JSFoundSet<db:/avanti/sys_udf_type>} */
	var duplicate_fs = jsRec.foundset.duplicateFoundSet()
	
	// Find the records that follow the deleted record in sequence number and reduce each by one.
	if(duplicate_fs.find()) {
		
		// Search criteria for sequence number greater than and setting parent UUID.
		duplicate_fs.udf_code = selected_udf_code
		duplicate_fs.sequence_nr = '>' + jsRec.sequence_nr
		if(jsRec.parent_sys_udf_type_id == null) {
			duplicate_fs.parent_sys_udf_type_id = '^'
		} else{
			duplicate_fs.parent_sys_udf_type_id = jsRec.parent_sys_udf_type_id
		}
		
		if(duplicate_fs.search() > 0) {
			duplicate_fs.sort('sequence_nr asc')
			
			for (var index = 1; index <= duplicate_fs.getSize(); index++) {
				duplicate_fs.getRecord(index).sequence_nr = duplicate_fs.getRecord(index).sequence_nr - 1  
			}
			databaseManager.saveData(duplicate_fs)
		}
	}
	duplicate_fs.clear()
	return true
}

/**
 * Load the Move Field dialog
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A744350E-49C9-4F85-B663-771D0648EA0D"}
 * 
 * @AllowToRunInFind
 */
function onActionMoveField(event) {

var pathUUID = getTreeSelectionPath();
	
	if(pathUUID == null) {
		return
	}
	
	if(foundset.find()) {
		foundset.sys_udf_type_id = pathUUID
		if(foundset.search()) { 
			
		}
	}
    var rMove = foundset.getSelectedRecord();
    // GD - Dec 6, 2021: SL-22734 Josh says do not allow split bill fields to move.
    if (!rMove || rMove.udf_split_bill_field == 1) {
        return;
    }
	if(foundset.find()) {
		// Get list of Fields based on selected UDF Code.
		foundset.udf_code = selected_udf_code
		if(foundset.search() > 0){
			
			oldPathUUID = getTreeSelectionPath();
			refreshTree();
			// Get user input from Add Field dialog
			globals.DIALOGS.showFormInModalDialog(forms.sys_udf_type_dtl_field_move, -1, -1, -1, -1, i18n.getI18NMessage('avanti.dialog.parentSelect'), false, false, 'newPreferenceFolder', true)
		}
	}
}

/**
 * Actions to perform after OK is pressed in Move dialog
 * 
 * @properties={typeid:24,uuid:"3B593F1B-2BF3-47D3-94C7-623CFCA00AAE"}
 * 
 * @AllowToRunInFind
 */
function moveField(result) { 

	// Get selected UUID.
	var newPathUUID = null;
	var selectionPath = forms.sys_udf_type_dtl_field_move.elements.sys_udf_tree_move.getSelectionPath();
	if (selectionPath && selectionPath.length > 0) {
		newPathUUID = selectionPath[selectionPath.length - 1];
	}
	if (result != 'ROOT') {
		var isChildPath = true
		for (var path_idx = 0; path_idx < elements.sys_udf_tree.getSelectionPath().length; path_idx++) {
			if (elements.sys_udf_tree.getSelectionPath()[path_idx] != forms.sys_udf_type_dtl_field_move.elements.sys_udf_tree_move.getSelectionPath()[path_idx]) {
				isChildPath = false
				break
			}
		}
		if (isChildPath) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.UDFMoveParentChildError'),
				i18n.getI18NMessage('avanti.lbl.UDFMoveParentChildError'),
				i18n.getI18NMessage('avanti.dialog.ok'))
		}
	}
	// Ensure it's not the same path.
	if(!isChildPath && (result == 'ROOT' || oldPathUUID != newPathUUID)) {
		if(foundset.find())
		{
			foundset.sys_udf_type_id = oldPathUUID
			if(foundset.search() > 0){
				if(result == 'ROOT') {
					updateSequenceNumbers(foundset.getSelectedRecord(), null)
					foundset.parent_sys_udf_type_id = null
					foundset.depth = 1
				} else {
					updateSequenceNumbers(foundset.getSelectedRecord(), newPathUUID)
					foundset.parent_sys_udf_type_id = newPathUUID
					setDepth(foundset.getSelectedRecord())
				}
				databaseManager.saveData(foundset)
			}
		}
		refreshTree()
	}
	scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);	
}

/**
 * Function to update sequence number based on record and parent UUID passed in.
 * 
 * @param {JSRecord<db:/avanti/sys_udf_type>} record_param
 * @param {UUID} parentUUID
 *
 * @properties={typeid:24,uuid:"48D8AB97-9AF9-4E2D-B022-087A061F3289"}
 * @AllowToRunInFind
 */
function updateSequenceNumbers(record_param, parentUUID) {
	
	// Get the top sequence number based on parent UUID
	var destSeq = getDestinationSequence(parentUUID) 
	var duplicate_fs = record_param.foundset.duplicateFoundSet()
		
	// After moving a record to a new parent, update the sequence numbers of the remaining items of the original parent.
	if(duplicate_fs.find() || duplicate_fs.find()) {
		duplicate_fs.udf_code = selected_udf_code
		duplicate_fs.sequence_nr = '>' + record_param.sequence_nr
		
		// Set parent UUID in search
		if(record_param.parent_sys_udf_type_id == null ) {
			duplicate_fs.parent_sys_udf_type_id = '^'
		} else {
			duplicate_fs.parent_sys_udf_type_id = record_param.parent_sys_udf_type_id
		}
		
		if(duplicate_fs.search() > 0) {
			 for (var index = 1; index <= duplicate_fs.getSize(); index++) {
				 duplicate_fs.getRecord(index).sequence_nr = duplicate_fs.getRecord(index).sequence_nr - 1  
			 }
			 databaseManager.saveData(duplicate_fs)
		}
		record_param.sequence_nr = destSeq + 1
	}
}

/**
 * Get the max sequence number based on the parent UUID.
 * 
 * @param {UUID} parentUUID
 * 
 * @return
 * @properties={typeid:24,uuid:"AF008595-ABFA-4C7C-8596-FE5D6B016930"}
 * @AllowToRunInFind
 */
function getDestinationSequence(parentUUID) {
	
	/*** @type {JSFoundSet<db:/avanti/sys_udf_type>} */
 	var udf_type_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type').duplicateFoundSet()
	
	if(udf_type_fs.find() || udf_type_fs.find()) {
		udf_type_fs.udf_code = selected_udf_code
		
		// Set parent UUID in search
		if(parentUUID == null ) {
			udf_type_fs.parent_sys_udf_type_id = '^'
		} else {
			udf_type_fs.parent_sys_udf_type_id = parentUUID
		}
		
		// If a parent has children, return the max sequence number of the children of the parent.
		if(udf_type_fs.search() > 0) {
			udf_type_fs.sort('sequence_nr desc')
			return udf_type_fs.getRecord(1).sequence_nr
		} 
		// If a parent ID has no children, return the sequence number of the parent.
		else {
			return 0
		}
	}
	// No entries in the tree.
	return 0
}

/**
 * Actions to perform after Move Up is pressed 
 * 
 * @param {Object} event
 *
 * @properties={typeid:24,uuid:"462C8ED9-7A02-4B07-B110-115EBD9831F1"}
 * 
 * @AllowToRunInFind
 * 
 */
function onActionMoveFieldUp(event) {
	
	var move_fs = foundset.duplicateFoundSet(),
        rMove = move_fs.getSelectedRecord();
    // GD - Dec 6, 2021: SL-22734 Josh says do not allow split bill fields to move.
    if (rMove.udf_split_bill_field == 1) {
        return;
    }
	var duplicate_fs = foundset.duplicateFoundSet();
	
	if(move_fs.find() || move_fs.find()) {
		// Get list of Fields based on selected UDF Code.
		move_fs.udf_code = selected_udf_code
		move_fs.sys_udf_type_id = getTreeSelectionPath();
		
		
		if(move_fs.search() > 0){
			var origSeq = move_fs.sequence_nr
			// Return if it is the first entry.
			if(move_fs.sequence_nr == 1) {
				return
			}
			var moveRec = move_fs.getSelectedRecord()
		
			//Set the sequence number to one less than the current.
			var destSeq = getDestinationSequenceWithMax(move_fs.parent_sys_udf_type_id, move_fs.sequence_nr)
			
			moveRec.sequence_nr = destSeq
			databaseManager.saveData(move_fs)
			
			duplicate_fs.addFoundSetFilterParam('sys_udf_type_id','!=',moveRec.sys_udf_type_id)
			
			// Set the sequence number of the previous record to one greater.
			if(duplicate_fs.find() || duplicate_fs.find()) {
				duplicate_fs.udf_code = selected_udf_code 
				duplicate_fs.sequence_nr = destSeq + '...' + origSeq
				if(move_fs.parent_sys_udf_type_id == null) {
					duplicate_fs.parent_sys_udf_type_id = '^'
				} else{
					duplicate_fs.parent_sys_udf_type_id = move_fs.parent_sys_udf_type_id
				}
				
				if(duplicate_fs.search() > 0) {
					for (var index = 1; index <= duplicate_fs.getSize(); index++) {
						duplicate_fs.getRecord(index).sequence_nr = duplicate_fs.getRecord(index).sequence_nr + 1  
					 }
					 databaseManager.saveData(duplicate_fs)
				}
			}
			
			databaseManager.saveData(duplicate_fs)
			duplicate_fs.removeFoundSetFilterParam('sys_udf_type_id')
			duplicate_fs.clear()
			refreshTree()
		}
	}
	scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);
}

/**
 * Returns the sequence number based on max limit
 * 
 * @param {UUID} parentUUID
 * @param {Number} maxLimit
 *
 * @return
 * @properties={typeid:24,uuid:"E49EFABB-3633-4741-99A3-D576152DE0F3"}
 * @AllowToRunInFind
 */
function getDestinationSequenceWithMax(parentUUID, maxLimit) {
	
	/*** @type {JSFoundSet<db:/avanti/sys_udf_type>} */
	var udf_type_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type').duplicateFoundSet()
	
	if(udf_type_fs.find() || udf_type_fs.find()) {
		udf_type_fs.udf_code = selected_udf_code
		udf_type_fs.sequence_nr = '<' + maxLimit
		
		// Set parent UUID in search
		if(parentUUID == null ) {
			udf_type_fs.parent_sys_udf_type_id = '^'
		} else {
			udf_type_fs.parent_sys_udf_type_id = parentUUID
		}
		
		// If a parent has children, return the max sequence number of the children of the parent.
		if(udf_type_fs.search() > 0) {
			udf_type_fs.sort('sequence_nr desc')
			return udf_type_fs.getRecord(1).sequence_nr
		} 
		// If a parent ID has no children, return the sequence number of the parent.
		else {
			udf_type_fs.clear()
			udf_type_fs.loadAllRecords()
			if(udf_type_fs.find() || udf_type_fs.find()) {
				udf_type_fs.udf_code = selected_udf_code
				udf_type_fs.sys_udf_type_id = parentUUID
				udf_type_fs.sequence_nr = '<' + maxLimit
				if(udf_type_fs.search() > 0) {
					udf_type_fs.sort('sequence_nr desc')
					return udf_type_fs.sequence_nr
				}
			}
		}
	}
	// No entries in the tree.
	return 1
}

/**
 * Actions to perform after Move Down is pressed 
 * 
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"E89E7C76-2374-45B6-9EBE-5B22A0DF53AD"}
 * 
 * @AllowToRunInFind
 */
function onActionMoveFieldDown(event) {
	
	var move_fs = foundset.duplicateFoundSet(),
	    rMove = move_fs.getSelectedRecord();
	// GD - Dec 6, 2021: SL-22734 Josh says do not allow split bill fields to move.
	if (rMove.udf_split_bill_field == 1) {
	    return;
	}
	var duplicate_fs = foundset.duplicateFoundSet();
		
	if(move_fs.find() || move_fs.find()) {
		// Get list of Fields based on selected UDF Code.
		move_fs.udf_code = selected_udf_code
		move_fs.sys_udf_type_id = getTreeSelectionPath();
	
		if(move_fs.search() > 0){
			if(move_fs.sequence_nr == getDestinationSequence(move_fs.parent_sys_udf_type_id)) {
				return
			}
			var moveRec = move_fs.getSelectedRecord()
			
			// Set the sequence number of the record to one greater.
			var destSeq = getDestinationSequenceWithMin(move_fs.parent_sys_udf_type_id, move_fs.sequence_nr)
			
			moveRec.sequence_nr = destSeq
			databaseManager.saveData(move_fs)
			
			duplicate_fs.addFoundSetFilterParam('sys_udf_type_id','!=',moveRec.sys_udf_type_id)
			
			// Set the sequence number of the previous record to one lesser.
			if(duplicate_fs.find() || duplicate_fs.find()) {
				duplicate_fs.udf_code = selected_udf_code 
				duplicate_fs.sequence_nr = destSeq 
				
				if(move_fs.parent_sys_udf_type_id == null) {
					duplicate_fs.parent_sys_udf_type_id = '^'
				} else{
					duplicate_fs.parent_sys_udf_type_id = move_fs.parent_sys_udf_type_id
				}
				if(duplicate_fs.search() > 0) {
					duplicate_fs.sequence_nr = destSeq - 1  
					databaseManager.saveData(duplicate_fs)
				}
			}
			
			databaseManager.saveData(duplicate_fs)
			duplicate_fs.removeFoundSetFilterParam('sys_udf_type_id')
			duplicate_fs.clear()
			refreshTree()
		}
	}
	scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);
}

/**
 * Returns the sequence number based on minimum limit
 * 
 * @param {UUID} parentUUID
 * @param {Number} minLimit
 *
 * @return
 * @properties={typeid:24,uuid:"1B9799C3-A9A0-422F-8766-A7AADA13514F"}
 * @AllowToRunInFind
 */
function getDestinationSequenceWithMin(parentUUID, minLimit) {
	
	/*** @type {JSFoundSet<db:/avanti/sys_udf_type>} */
	var udf_type_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type').duplicateFoundSet()
	
	if(udf_type_fs.find() || udf_type_fs.find()) {
		udf_type_fs.udf_code = selected_udf_code
		udf_type_fs.sequence_nr = '>' + minLimit
		
		// Set parent UUID in search
		if(parentUUID == null ) {
			udf_type_fs.parent_sys_udf_type_id = '^'
		} else {
			udf_type_fs.parent_sys_udf_type_id = parentUUID
		}
		
		// If a parent has children, return the max sequence number of the children of the parent.
		if(udf_type_fs.search() > 0) {
			udf_type_fs.sort('sequence_nr asc')
			return udf_type_fs.getRecord(1).sequence_nr
		} 
		// If a parent ID has no children, return the sequence number of the parent.
		else {
			udf_type_fs.clear()
			udf_type_fs.loadAllRecords()
			if(udf_type_fs.find() || udf_type_fs.find()) {
				udf_type_fs.udf_code = selected_udf_code
				udf_type_fs.sys_udf_type_id = parentUUID
				udf_type_fs.sequence_nr = '>' + minLimit
				if(udf_type_fs.search() > 0) {
					udf_type_fs.sort('sequence_nr asc')
					return udf_type_fs.sequence_nr
				}
			}
		}
	}
	// No entries in the tree.
	return 1
}

/**
 * Load the Edit Field screen to get user input
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D46994C2-E070-4268-9D04-B277FA827290"}
 * @AllowToRunInFind
 */
function onActionEditField(event) {
	var pathUUID = getTreeSelectionPath();
	
	if(foundset.find()) {
		if(pathUUID == null) {
			foundset.udf_code = selected_udf_code
		} else {
			// Get record based on Field selected
			foundset.sys_udf_type_id = pathUUID
		}
		if(foundset.search()) {
			foundset.sort('sequence_nr asc')
			// Load foundset and parameters of form based on Field selected.
			forms.sys_udf_type_dtl_field.foundset.loadRecords(foundset)
			forms.sys_udf_type_dtl_field.udf_field_input = foundset.udf_field
			forms.sys_udf_type_dtl_field.udf_field_type_input = foundset.udf_field_type
			forms.sys_udf_type_dtl_field.udf_field_type_mode = 'edit'
			
			// Show the edit dialog.
			globals.DIALOGS.showFormInModalDialog(forms.sys_udf_type_dtl_field, -1, -1, -1, -1, i18n.getI18NMessage('avanti.lbl.UDFEditField'), false, false, 'newPreferenceFolder', true)	
			
			
		}
	}
}

/**
 * Actions to perform after OK is pressed in Edit dialog
 * 
 * @param {String} result
 *
 * @properties={typeid:24,uuid:"9522BE49-5A3D-4E17-B1A5-6BD501DE1778"}
 */
function editField(result) { 
	// If you update a Multi_Select field, then update all entries in Customer UDF.
	if(forms.sys_udf_type_dtl_field.udf_field_type_input == 'MULTI_SELECT') {
		updateSequenceForMultiSelect()
	}
	
	if (result == 'OK')
	{
		if (forms.sys_udf_type_dtl_field.udf_field_input == null || forms.sys_udf_type_dtl_field.udf_field_input.length == 0 || forms.sys_udf_type_dtl_field.udf_field_type_input == null || forms.sys_udf_type_dtl_field.udf_field_type_input.length == 0)
		{
			plugins.dialogs.showErrorDialog(i18n.getI18NMessage('avanti.dialog.noInfo'),i18n.getI18NMessage('avanti.dialog.noInfo'))
		} else {
			// Update database with Field information.
			foundset.udf_field = forms.sys_udf_type_dtl_field.udf_field_input
			foundset.udf_field_type = forms.sys_udf_type_dtl_field.udf_field_type_input
			
			databaseManager.saveData()
			scopes.avUDF.refreshDuplicateUDFFields(selected_udf_code);
			refreshTree()
		}
		
	} else {
		forms.sys_udf_type_dtl_field.udf_field_input = ''		
		refreshTree()
	}
}

/**
 * Update Customer UDF after updating the Multi-Select field in the UDF Setup.
 * 
 * @properties={typeid:24,uuid:"7086175A-A0E9-4E21-B345-6A74E929DF62"}
 * @AllowToRunInFind
 */
function updateSequenceForMultiSelect() {
//	var uuidList = ''
//	var udfAnswerList = ''
//	var sequenceList = ''
		
	/*** @type {JSFoundSet<db:/avanti/sys_udf_values>} */
	var customer_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_values');
	
	if(customer_fs.find() || customer_fs.find()) {
		// Find all the entries in the Customer UDF field with answers.
		customer_fs.sys_udf_type_id = forms.sys_udf_type_dtl_field_table_values.foundset.sys_udf_type_id
		if(customer_fs.search() > 0) {
			// Loop through all the values that has an answer to this UDF Field.
			for (var customer_fs_idx = 1; customer_fs_idx <= customer_fs.getSize(); customer_fs_idx++)
			{
				var tempArray = new Array()
				var customerUDFRec = customer_fs.getRecord(customer_fs_idx)
				var customerValueUUIDList = customerUDFRec.udf_answer_multi_select
				if(customerValueUUIDList != null &&  customerValueUUIDList.length > 0){
					var customerValueUUIDArray = customerValueUUIDList.split(', ')
					
					// Loops through comma delimited list of UUIDs as set in sys_udf_values of selected UUIDs.
					for (var list_idx = 0; list_idx < customerValueUUIDArray.length; list_idx++) {
						var tv_fs = forms.sys_udf_type_dtl_field_table_values.foundset.duplicateFoundSet()
						if(tv_fs.find() || tv_fs.find()) { 
							tv_fs.sys_udf_type_table_value_id = customerValueUUIDArray[list_idx]
							if(tv_fs.search()> 0){
								tempArray.push(tv_fs.sys_udf_type_table_value_id + ' ' + tv_fs.udf_table_value + ' ' + tv_fs.sequence_nr)
							}
						}
					}
					
				}
				
				// Sort and then parse UUIDs based on sequence number and create ordered strings to put in sys_udf_values
				tempArray = tempArray.sort(globals.udfSorter)
				var udf_field_value_input = ''
				var udf_field_value_display = ''
				var udf_field_value_sequence = ''
				for each (var field_value in tempArray) {
					/** @type{String} */
					var field_value_string = field_value
					udf_field_value_input +=  field_value_string.substring(0, 36) + ', '
					udf_field_value_display +=  field_value_string.substring(37, field_value_string.lastIndexOf(' ')) + ', '
					udf_field_value_sequence +=  field_value_string.substring(field_value_string.lastIndexOf(' ')+1) + ', '
				}
				if(tempArray.length > 0 ) {
					udf_field_value_input = udf_field_value_input.substring(0, udf_field_value_input.length - 2)
					udf_field_value_display = udf_field_value_display.substring(0, udf_field_value_display.length -2)
					udf_field_value_sequence = udf_field_value_sequence.substring(0, udf_field_value_sequence.length - 2)
				}
				customerUDFRec.udf_answer_multi_select = udf_field_value_input
				customerUDFRec.udf_answer_multi_select_seq = udf_field_value_sequence
				customerUDFRec.udf_answer = udf_field_value_display
				
				databaseManager.saveData(customerUDFRec)
			}
			databaseManager.saveData(customer_fs)
		}
	}
	
	if(selected_udf_code == 'CUSTOMER') {
		forms.sys_udf_tree.formUDFCode = 'CUSTOMER'
		forms.sys_udf_tree.formSource = 'Customer'
		forms.sys_udf_tree_answers.refreshFieldValues()
	} else if (selected_udf_code == 'CONTACT') {
		forms.sys_udf_tree.formUDFCode = 'CONTACT'
		forms.sys_udf_tree.formSource = 'Contact'
		forms.sys_udf_tree_answers.refreshFieldValues()
	}
	
	
}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"A563E5EF-2747-4737-A161-B7488359D304"}
 */
function onHide(event) {
    _super.onHide(event);
    
    if (udf_code == 'CUSTOMER') {
        setSplitBillFieldNums();
    }

    return true;
}
