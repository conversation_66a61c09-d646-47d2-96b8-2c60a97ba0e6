/* 
 * Special UDF Table Column Handling Module Explanaton:
 * ----------------------------------------------------
 * 
 * This module was created to support special configurable UDF columns in 
 * some table forms (SL-1175/SL-7476). 
 * 
 * Users can configure up to a certain number of UDF columns to be visible 
 * on the table by checking the "Show in Table View?" checkbox under 
 * System Setup->General->UDF Type for each individual field.
 * 
 * (Note: at the time of writing, only the Suppliers and Items tables support  
 * this feature).
 * 
 * To overcome Servoy's "idiosyncrasies", it was recommended by CH to 
 * duplicate the data for the UDF answers into generic database columns 
 * for the supporting tables named "udf1", "udf2", ... ,"udfN" (The code in
 * this module keeps the duplicated data in sync with the source data).
 * 
 * Requirements:
 * 
 * Logic has been written so that special UDF form fields must be named
 * "fld_udf1","fld_udf2", ... ,"fld_udfN". This logic appears in this 
 * avUDF.js module and also in Quick Search routines in av_base_form.js
 * All "fld_udf1",..."fld_udf2" forms must be set to invisible in the form 
 * design view. The logic will make them visible during runtime if 
 * applicable.
 * 
 * Logic has been written so that the Labels for the "fld_udf1", "fld_udf2", 
 * ... , "fld_udfN" fields must have corresponding labels with their text set
 * to "%%udf1text%%", "%%udf2text%%", ... , "%%udfNtext%%". The forms must have 
 * form variables defined with the names "udf1text", "udf2text", ... "udfNtext"
 * with their initial values set to "__UDF1__", "__UDF2__", ... "__UDFN__". 
 * Logic in this avUDF.js module and also Quick Search routines in 
 * av_base_form.js rely on these naming conventions.
 * 
 * *!* WARNINGS *!*: 
 * 
 * - According to CH, Servoy does not recommend having more than 20 columns 
 * visible on a form, custom udf fields should be used sparingly and ideally, 
 * the sum of the available udf fields and non-udf fields should be 20 or 
 * less.
 * 
 * - search speed will decrease as more udf fields are added to the Database 
 * tables 
 */

/**
 * @properties={typeid:35,uuid:"AC80C5BA-32F9-4741-A991-899CECA6D3F6",variableType:-4}
 */
var bInCRMCustomers = false;

// *** CONSTANTS: ***

/**
 * Maximun number of allowed visible UDF columns in suppliers table (match this to number of udfN cols in ap_suppler)
 * @type {Number}
 * @properties={typeid:35,uuid:"DD717031-84D7-4465-A4B9-9716B5395406",variableType:4}
 */
var UDFTYPE_MAX_SEARCHABLE_SUPPLIERS = 32;

/**
 * Maximun number of allowed visible UDF columns in items table (match this to number of udfN cols in in_items)
 * @type {Number}
 * @properties={typeid:35,uuid:"448198FA-9C1C-4D81-808D-FFB6C32C9D2E",variableType:4}
 */
var UDFTYPE_MAX_SEARCHABLE_ITEMS = 32;

// *** CLASSES ***:

/**
 * @class
 * @classdesc stores heading information
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 * 
 * @properties={typeid:24,uuid:"2F4A2D4F-89AC-40FC-B78B-09BD9BD6F9E3"}
 */
function UDFHeading() {
	
	var self = this;
	
	// PROPERTIES:

	/**
	 * @name UDFHeading#sSysUDFTypeId
	 * @type {UUID}
	 * @default ''
	 */		
	self.sSysUDFTypeId = '';
	
	/**
	 * @name UDFHeading#sHeading
	 * @type {String}
	 * @default ''
	 */	
	self.sHeading = '';
	
	/**
	 * @name UDFHeading#bShowInTable
	 * @type {Boolean}
	 * @default false;
	 */		
	self.bShowInTable = false;
	
	/**
	 * @name UDFHeading#nSequenceNr
	 * @type {Number}
	 * @default -1
	 */	
	self.nSequenceNr = -1;
}


// *** FUNCTIONS: ***

/**
 * This is the main function that should be called from the table form with 
 * the special UDFS on it.
 * 
 * Updates the UDF column headings from generic UDF1, UDF2...UDFN to the UDF 
 * field question text.
 * 
 * Sample Usage:
 * 
 * function onShowForm(_firstShow, _event) {
 * 
 *    controller.recreateUI(); // Ensures visible/invisible columns will be refreshed after UDF Type changes
 *    databaseManager.refreshRecordFromDatabase(foundset,-1); // Ensures visible data aligns with columns after any udf changes 
 *    scopes.avUDF.handleUDFTableCols('ITEM', forms.in_item_tbl); // shows any custom UDF columns set to visible
 *    // ... put any non-related code here
 *    return;
 * }
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 *
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM)
 * @param {RuntimeForm} oForm - the form
 *
 * @properties={typeid:24,uuid:"00F18F86-CAA1-43B9-AEAC-C42DC8C0AEA1"}
 * 
 * @returns {Boolean} - true if successful, false otherwise
 */
function handleUDFTableCols(sUDFType, oForm) {
	
	var aUDFHeadings = scopes.avUDF.getUDFHeadings(sUDFType);
	var iNumColsToShow = 0;
	
	switch (sUDFType) {
		
		case 'SUPPLIER':
			iNumColsToShow = scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_SUPPLIERS;
			break;
			
		case 'ITEM':
			iNumColsToShow = scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_ITEMS;
			break;
			
		default:
			application.output('handleUDFTableCols(): ERROR: received unsupported UDF Type: '+sUDFType, LOGGINGLEVEL.ERROR);
			return false;
	}
	
	if (aUDFHeadings.length < iNumColsToShow) {
		iNumColsToShow = aUDFHeadings.length;
	}
	
	var aSequenceNrList = scopes.avUDF.updateUDFHeadings(oForm, aUDFHeadings, iNumColsToShow);
	
	if (aSequenceNrList.length < iNumColsToShow) {
		iNumColsToShow = aSequenceNrList.length;
	}
	
	var bSuccessful = scopes.avUDF.showUDFCols(oForm.elements, iNumColsToShow); 
	
	return bSuccessful;
}


/**
 * @AllowToRunInFind
 * 
 * Returns a list of sequence numbers for the given UDF Type.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 * 
 * @param {String} sUDFType - SUPPLIER|ITEM|Etc.
 *
 * @properties={typeid:24,uuid:"D7ED9C4F-72D6-4BF7-A0AE-702CC75DC722"}
 * 
 * @return {Array} - the list of sequence_nr (numbers)
 */
function getVisibleSequenceNumberList(sUDFType) {
	
	// Part I: Get sequence_nr+'.'+depth
	
	var aHeadings = [];
	
	/** @Type JSFoundSet<db:/avanti/sys_udf_type> */
	var fsUDFHeadings = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type');
	
	if (fsUDFHeadings.find()) {
		
		fsUDFHeadings.udf_code = sUDFType; // filter by sUDFType (SUPPLIER|ITEM|CUSTOMER)
		fsUDFHeadings.udf_show = 1;
		
		if (fsUDFHeadings.search() > 0) {
			
			fsUDFHeadings.sort('sequence_nr');			
			
			for (var i=1; i<=fsUDFHeadings.getSize(); i++) {
				/** @Type JSRecord<db:/avanti/sys_udf_type> */
				var rHeading = fsUDFHeadings.getRecord(i);
				aHeadings.push(rHeading['sequence_nr']+'.'+rHeading['depth']); 
			}
		}
		
	}
	
	// part II: Sort by sequence_nr, depth (Master#, Minor#):

	var aMajorNums = [];
	var aFullySortedNums = [];
	
	// Step 1: Get sorted Major Number List:
	    
	for (var j=0; j<aHeadings.length; j++) {
		var aParts = aHeadings[j]['split']('.');
		aMajorNums.push(aParts[0]);
	}
	aMajorNums.sort(function(a, b){return a-b});

	// Step 2: Traverse Minor Number Lists and Add Major.Minor to full list in sequencial order:	
	
	for (var k=0; k<aMajorNums.length; k++) { // TODO this section could be re-factored to reduce redundant work for duplicates
		
		var aMinorRelatedNums = [];
		var nMajorNum = aMajorNums[k];
		
		for (var l=0; l<aHeadings.length; l++) {
			
			var bParts = aHeadings[l]['split']('.');
			
			if (bParts[0] != nMajorNum) {
				continue; // these are not the elements we are looking for
			}
			else {
				aMinorRelatedNums.push(bParts[1]);
			}
		}
		aMinorRelatedNums.sort(function(a, b){return a-b});
		
		for (var m=0; m<aMinorRelatedNums.length; m++) {
			
			var sFullNum = nMajorNum+'.'+aMinorRelatedNums[m];
			
			if (-1 == aFullySortedNums.indexOf(sFullNum)) { // do not add duplicates
				aFullySortedNums.push(sFullNum);
			}
		}
		
	}
	
	return aFullySortedNums; // eg: ["1.1","1.2","1.3","2.1","2.2","3.1","4.1"]
}

/**
 * @AllowToRunInFind
 * 
 * Returns an array of UDF Type field headings for the given type. 
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 *
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM|CUSTOMER)
 *
 * @properties={typeid:24,uuid:"4E9BCBCB-E373-42B7-BB39-5F4C7D5D59A4"}
 *
 * @return {Array} - the UDF headings in sequential order
 */
function getUDFHeadings(sUDFType) {
	
	var aHeadings = [];
	
	/** @Type JSFoundSet<db:/avanti/sys_udf_type> */
	var fsUDFHeadings = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type');
	
	if (fsUDFHeadings.find()) {
		
		fsUDFHeadings.udf_code = sUDFType; // filter by sUDFType (SUPPLIER|ITEM|CUSTOMER)
		
		if (fsUDFHeadings.search() > 0) {
			
			fsUDFHeadings.sort('sequence_nr, depth');			
			
			for (var i=1; i<=fsUDFHeadings.getSize(); i++) {
				
				/**@type {JSRecord<db:/avanti/sys_udf_type>}*/
				var rCurrent = fsUDFHeadings.getRecord(i);
				
				if (sUDFType == rCurrent.udf_code) {
					
					var oUDFHeading = new UDFHeading();
					
					oUDFHeading.sSysUDFTypeId = rCurrent.sys_udf_type_id;
					oUDFHeading.sHeading = rCurrent.udf_field;
					oUDFHeading.bShowInTable = (1 === rCurrent.udf_show) ? true : false;
					oUDFHeading.nSequenceNr = rCurrent.sequence_nr;
					
					aHeadings.push(oUDFHeading); // udf_field := UDF question
				}
			}			
			
		}
	}
	
	return aHeadings;
}

/**
 * This will update the form variables 'udf1text', 'udf2text', ... 'udfNtext'
 * to the corresponding UDF field labels in the given aUDFHeadings array in 
 * the given order they are in the array.
 * 
 * The iMaxNumUDFs will limit how many UDF heading form variables this function 
 * will look for. 
 * 
 * Note: Requirements:
 * 
 * - the form js file must have variables with the name "udfNtext" where N is a number
 * - label field "lblUdfN" should point to "%%udfNtext%%" where N is a number
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 * 
 * @param {RuntimeForm} oForm - the form to update the variables on
 * @param {Array} aUDFHeadings - the list of headings in the display order
 * @param {Number} iMaxNumUDFs - the maximum # of headings to update
 *
 * @properties={typeid:24,uuid:"3FAC4C36-C99C-438D-937A-BF0D3FDCE85A"}
 *  
 * @return {Array} - list sequence_nr of visible UDF columns 
 */
function updateUDFHeadings(oForm, aUDFHeadings, iMaxNumUDFs) {

	// Show headings only for the UDF fields that have bShowInTable set to true; display them in their given order skipping ones with bShowInTable set to false

	var aSequenceNrList = [];
	var iTableCol = 1;

	for (var iHeading=0; iHeading<aUDFHeadings.length; iHeading++) {
		
		/** @type UDFHeading */
		var oUDFHeading = aUDFHeadings[iHeading];
		
		if (oUDFHeading.bShowInTable) {
			var sLabelName = "udf"+iTableCol+"text";
			
			if (oForm[sLabelName]) {
				oForm[sLabelName] = oUDFHeading.sHeading;
				aSequenceNrList.push(oUDFHeading.nSequenceNr);
				iTableCol++;
				
				if (iTableCol > iMaxNumUDFs) {
					return aSequenceNrList; // only a limited number of columns are available
				}
				
			}
			else {
				application.output("updateUDFHeadings(): ERROR: the given form does not contain the variable "+sLabelName);
				return aSequenceNrList;				
			}
		}
		
	}

	return aSequenceNrList;
}

/**
 * Sets the appropriate UDF columns to visible.
 * 
 * Note: Requirements:
 * 
 * - the udf form field must have a name with the format 'fld_udfN' where N is a number
 * - fld_udfN should be set to invisible in the form design, this function will make it visible if appropriate
 * - the label for that column (lblUdfN) must have its labelFor value set to point to fld_udfN
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-25
 * 
 * @param {elements} oElements - the form elements
 * @param {Number} iMaxNumUDFs - the maximum # of headings to update
 *
 * @properties={typeid:24,uuid:"2963393B-5B9A-4F90-A8C3-A66FBB6D7A62"}
 * 
 * @return {Boolean} - true upon success, false upon failure
 */
function showUDFCols(oElements, iMaxNumUDFs) {
	
	for (var i=0; i<iMaxNumUDFs; i++) {
		var sFieldName = 'fld_udf'+(i+1);
		
		if (oElements[sFieldName]) {
			oElements[sFieldName].visible = true;
		}
		else {
			application.output("toogleUDFVisible(): ERROR: the given form does not contain the field "+sFieldName);
			return false;			
		}
	}
	
	return true;
}

/**
 * @AllowToRunInFind
 * 
 * This will fetch an array of unique ids for records related to the given UDF Type.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-25
 * 
 * @param {String} sUDFType - the UDF type (used to determine which field unique id is in)
 * 
 * @properties={typeid:24,uuid:"A063AD53-8C73-46CF-994F-FF81CA2ACC13"}
 * 
 * @return {Array} - the list of unique ids or [] upon error
 */
function getUniqueIds(sUDFType) {

	var fsRecords;	
	var sIdFieldName = '';
	var aUniqueIds = []; 
	
	switch (sUDFType) {
		
		case 'SUPPLIER':
			/**@type {JSRecord<db:/avanti/ap_supplier>}*/			
			fsRecords = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'ap_supplier');
			sIdFieldName = 'supplier_id';			
			break;
			
		case 'ITEM':
			/**@type {JSRecord<db:/avanti/in_item>}*/
			fsRecords = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
			sIdFieldName = 'item_id';
			break;
			
		case 'CUSTOMER':
			/**@type {JSRecord<db:/avanti/sa_customer>}*/
			fsRecords = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer');
			sIdFieldName = 'cust_id';			
			break;
	
		case 'CONTACT':
			/**@type {JSRecord<db:/avanti/sa_customer_contact>}*/
			fsRecords = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer_contact');
			sIdFieldName = 'custcontact_id';			
			break;				
			
		default:
			application.output('getUniqueIds(): ERROR: Received unsupported sUDFType: '+sUDFType, LOGGINGLEVEL.ERROR);
			return aUniqueIds;
	}
	
	if (fsRecords.find() && fsRecords.search() > 0) {
	
		for (var i=1; i<=fsRecords.getSize(); i++) {
			var rCurrent = fsRecords.getRecord(i);
			aUniqueIds.push(rCurrent[sIdFieldName]);
		}
		
	}
	
	return aUniqueIds;
}

/**
 * Returns the table name and id field name for the given UDF Type.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-27
 * 
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM)
 *
 * @properties={typeid:24,uuid:"F52B7412-DDEF-4F79-A172-153D1F575E05"}
 * 
 * @returns {Array} - [sTableName:table_name,sIDFieldName:id_field_name] or null
 */
function getTableAndFieldNameForUDFType(sUDFType) {
	
	var aDBNames = [];	
	
	switch (sUDFType) {
		
		case 'SUPPLIER':
			aDBNames['sTableName'] = 'ap_supplier';
			aDBNames['sIDFieldName'] = 'supplier_id';
			break;
			
		case 'ITEM':
			aDBNames['sTableName'] = 'in_item';
			aDBNames['sIDFieldName'] = 'item_id';			
			break;
			
		case 'CUSTOMER':
			// no break
		case 'CONTACT':
			application.output('duplicateUDFFields(): ERROR: '+sUDFType+' is not supported yet for searchable UDF fields.');
			return null;
			break;			
		
		default:
			application.output('duplicateUDFFields(): ERROR: '+sUDFType+' is not a supported UDF Type for this function.');
			return null;
	}

	return aDBNames;
}

/**
 * This will clear the data from the UDF fields in the end of the given table.
 * 
 * Note: Tables such as the "Suppliers" table and "Items" table have a fixed 
 * number of UDF (User-Defined-Fields) fields that are possible to be 
 * displayed along with the regular Slingshot fields. The data present in 
 * these fields is duplicated from the answers/values in the 
 * "sys_udf_values" table corresponding to the questions in the "sys_udf_type"
 * table.
 * 
 * This function is a utility function that can be used to clear the duplicate 
 * data prior to regenerating the duplicated data from the source data.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-20
 *
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM|CUSTOMER)
 * @param {Number} iMaxNumUDFs - the maximum headings to update
 *
 * @properties={typeid:24,uuid:"0D77AD97-8318-4AD1-8A06-14754637080D"}
 * 
 * @return {Boolean} - true upon success, false upon failure
 */
function clearDuplicateUDFFields(sUDFType, iMaxNumUDFs) {
	
	var aDBNames = getTableAndFieldNameForUDFType(sUDFType);
	
	if (null == aDBNames) {
		return false; // unsupported sUDFType
	}
	
	var sTableName = aDBNames['sTableName']+"";
	
	// sSQL := UPDATE ap_supplier SET ap_supplier.udf1=NULL, ap_supplier.udf2=NULL...	
	var sSQL = 'UPDATE '+sTableName+' SET ';
	
	for (var i=1; i<=iMaxNumUDFs; i++) {
		
		if (i != 1) {
			sSQL = sSQL+',udf'+i+'=NULL';
		}
		else {
			sSQL = sSQL+'udf'+i+'=NULL';			
		}
	}
	
	sSQL += " WHERE org_id = '" + globals.org_id + "'";
	//This has never worked, so deleting udf field currently leaves the values in the items, we should revisit this in future.
	var bSuccessful = plugins.rawSQL.executeSQL(globals.avBase_dbase_avanti, sSQL);
	
    if (sTableName == scopes.avCache.TABLE.Ap_Supplier) {
        scopes.avCache.expireCacheForTable(scopes.avCache.TABLE.Ap_Supplier);
    }
	
	return bSuccessful;
}

/**
 * @AllowToRunInFind
 * 
 * This will duplicate data for the UDF fields into the table related to 
 * the given UDF type
 * 
 * Note: Tables such as the "Suppliers" table and "Items" table have a fixed 
 * number of UDF (User-Defined-Fields) fields that are possible to be 
 * displayed along with the regular Slingshot fields. The data present in 
 * these fields is duplicated from the answers/values in the 
 * "sys_udf_values" table corresponding to the questions in the "sys_udf_type"
 * table.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-25
 *
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM|CUSTOMER)
 * @param {Number} iMaxNumUDFs - the maximum headings to update
 * 
 * @properties={typeid:24,uuid:"C3B1F08B-FAC1-48A6-B47C-13DC5D1EAAA1"}
 * 
 * @return {Boolean} - true upon success, false upon failure
 */
function duplicateUDFFields(sUDFType, iMaxNumUDFs) {
	
	// Get DB Table/Field names:
	
	var aDBNames = getTableAndFieldNameForUDFType(sUDFType);
	
	if (null == aDBNames) {
		return false; // unsupported sUDFType
	}
	
	var sTableName = aDBNames['sTableName']+"";
	var sIDFieldName = aDBNames['sIDFieldName']+"";
	
	// Get Unique ID field (supplier_id|item_id|etc.)	
	
	var aUniqueIds = getUniqueIds(sUDFType);
	
	// Load UDF Type Data:
	
	/** @Type JSFoundSet<db:/avanti/sys_udf_type> */
	var fsUDFType = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type');
	
	if (fsUDFType.find()) {
		
		fsUDFType.udf_code = sUDFType; // filter by sUDFType (SUPPLIER|ITEM)
		fsUDFType.udf_show = 1;
		
		if (fsUDFType.search() > 0) {
			fsUDFType.sort('sequence_nr');
		}
		else {
			return true; // nothing to do
		}

	}	
	
	// For each unique ID Field, find corresponding UDFs and populate special columns:
	
	for (var iItemIndex=0; iItemIndex<aUniqueIds.length; iItemIndex++) {
		
		// Look duplicate visible UDF fields into the given table columns in sequence number order:
				
		for (var iTableDisplayCol=1; iTableDisplayCol<=fsUDFType.getSize(); iTableDisplayCol++) {
			
			/** @Type JSRecord<db:/avanti/sys_udf_type> */
			var rCurrent = fsUDFType.getRecord(iTableDisplayCol);
			
			var iSequenceNr = rCurrent['sequence_nr'];			
			
			if (0 >= iSequenceNr) { // TODO - figure out how cases where sequence_nr == 0 should be handled
				continue;
			}
			
			if (iSequenceNr > iMaxNumUDFs) {
				continue; // Ensure we don't pass the limit of how many columns are available
			}
			
			var sSysUDFTypeID = rCurrent['sys_udf_type_id'];
			var sFieldType = rCurrent['udf_field_type'];
			var sUniqueID = aUniqueIds[iItemIndex];
			var sUDFFieldName = 'udf'+iTableDisplayCol;
			
			var bSuccessful = duplicateIndividualUDFField(
									sTableName,
									sIDFieldName,
									sSysUDFTypeID,
									sUniqueID,
									sFieldType,
									sUDFFieldName
								);
			
			if (!bSuccessful) {
				return false;
			}
			
		}
		
	}
	
	return true;
}



/**
 * @AllowToRunInFind
 * 
 * This will update an individual duplicate UDF data column in the given 
 * table with the given data.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-28
 * 
 * @param {String} sTableName - name of table to update UDF fields in (ap_supplier|in_item|etc.)
 * @param {String} sIDFieldName - name of id field (supplier_id|item_id|etc.)
 * @param {UUID} sSysUDFTypeID - sys_udf_type_id
 * @param {UUID} sUniqueID - sys_udf_values.unique_id
 * @param {String} sFieldType - TEXT|INTEGER|NUMBER|DATETIME|MULTI_SELECT|TABLE_VALUE
 * @param {String} sUDFFieldName - the name of the database field to update (format: udfN where N is an integer)
 *
 * @properties={typeid:24,uuid:"CC997221-409E-4C47-998D-AE39F6D423F9"}
 * 
 * @return {Boolean} - true upon success, false otherwise
 */
function duplicateIndividualUDFField(
	sTableName,
	sIDFieldName,
	sSysUDFTypeID,
	sUniqueID,
	sFieldType,
	sUDFFieldName
) {
	
	/** @Type JSFoundSet<db:/avanti/sys_udf_values> */
	var fsUDFValues = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_values');
	
	if (fsUDFValues.find()) {
		
		fsUDFValues.sys_udf_type_id = sSysUDFTypeID;
		fsUDFValues.unique_id = sUniqueID; // supplier_id|item_id
		
		if (fsUDFValues.search() > 0) {
			
			/**@type {JSRecord<db:/avanti/sys_udf_values>} */
			var rUDFValue = fsUDFValues.getRecord(1);
			
			var sUDFAnswerText = '';
			
			switch (sFieldType) {
				
				case 'TEXT':
					sUDFAnswerText = rUDFValue.udf_answer_text;
					break;
					
				case 'INTEGER':
					sUDFAnswerText = rUDFValue.udf_answer_integer;
					break;
					
				case 'NUMBER':
					sUDFAnswerText = rUDFValue.udf_answer_number;
					break;
					
				case 'DATETIME':
					sUDFAnswerText = rUDFValue.udf_answer_datetime;
					break;
					
				case 'MULTI_SELECT':
					sUDFAnswerText = rUDFValue.udf_answer;					
					break;
					
				case 'TABLE_VALUE':
					sUDFAnswerText = rUDFValue.udf_answer;
					break;
					
				default:
					application.output('duplicateUDFFields(): WARNING: Assuming text for unsupported/undefined/null field type: '+sFieldType, LOGGINGLEVEL.WARNING);
					sUDFAnswerText = rUDFValue.udf_answer_text;
			}
			
			// Peform Update in Database:

			var sSQL  = 'UPDATE '+sTableName+' SET '+sUDFFieldName+"='"+sUDFAnswerText+"'";
			    sSQL += ' WHERE '+sIDFieldName+"='"+sUniqueID+"' AND org_id = '" + globals.org_id + "'";
			    
			if (!plugins.rawSQL.executeSQL(globals.avBase_dbase_avanti, sSQL)) {
				application.output('duplicateUDFFields(): The following SQL Update failed: '+sSQL, LOGGINGLEVEL.ERROR);
				return false;
			}
		}
		
	}
	
	return true;
}

/**
 * Updates duplicated data in UDF tables that support display UDF columns in
 * the tables. Call this whenever the number or structure of questions changes.
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-26
 * 
 * @param {String} sUDFType - the UDF Type (SUPPLIER|ITEM)
 *
 * @properties={typeid:24,uuid:"5D5A574D-0DE1-4659-BC3A-5DBB77F3204C"}
 */
function refreshDuplicateUDFFields(sUDFType) {
	
	// clear and re-duplicate fields where appropriate:
	switch (sUDFType) {
		
		case 'SUPPLIER':
			clearDuplicateUDFFields('SUPPLIER',scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_SUPPLIERS);
			duplicateUDFFields('SUPPLIER',scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_SUPPLIERS);
			break;
			
		case 'ITEM':
			clearDuplicateUDFFields('ITEM',scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_ITEMS);
			duplicateUDFFields('ITEM',scopes.avUDF.UDFTYPE_MAX_SEARCHABLE_ITEMS);				
			break;
			
		default:
			// do nothing, no other types supported yet
	}
	
	return;
}

/**
 * @AllowToRunInFind
 * 
 * This handles when a single UDF field value changes (is created or edited).
 * 
 * <AUTHOR> Meixner
 * @since 2016-04-28
 * 
 * @param {UUID} sSysUDFTypeId - sys_udf_type.sys_udf_type_id
 * @param {UUID} sUniqueId - supplier_id|item_id|etc.
 * @param {String} sFieldType - TEXT|INTEGER|NUMBER|DATETIME|MULTI_SELECT|TABLE_VALUE
 *
 * @properties={typeid:24,uuid:"364A9F51-50C5-4AD2-B76A-0EFAC0D748D9"}
 * 
 * @return {Boolean} - true upon success, false upon failure
 */
function handleChangedUDFValue(
	sSysUDFTypeId,
	sUniqueId,
	sFieldType
) {
	
	// Step 1: Determine the UDF Type:
	
	/** @Type JSFoundSet<db:/avanti/sys_udf_type> */
	var fsUDFType = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_type');
	
	if (fsUDFType.find()) {
		
		fsUDFType.sys_udf_type_id = sSysUDFTypeId;
		fsUDFType.udf_show = 1; // we only duplicate the ones flagged to be displayed
		
		if (fsUDFType.search() <= 0) {
			return true; // nothing to do
		}

	}
	
	var sUDFType = fsUDFType['udf_code'];
	var iSequenceNr = fsUDFType['sequence_nr']+'.'+fsUDFType['depth'];
	
	// Get DB Table/Field names:
	
	var aDBNames = getTableAndFieldNameForUDFType(sUDFType);
	
	if (null == aDBNames) {
		return false; // unsupported sUDFType
	}
	
	var sTableName = aDBNames['sTableName']+"";
	var sIDFieldName = aDBNames['sIDFieldName']+"";
	
	// Determine the column name to be updated:
	
	var aSeqNumList = getVisibleSequenceNumberList(sUDFType);
	var iSeqNumIndex = aSeqNumList.indexOf(iSequenceNr);
	
	if (-1 == iSeqNumIndex) {
		return true; // the sequence_nr (sequence number) was not found in the list of visible UDF columns, nothing to do
	}
	
	
	var sUDFFieldName = 'udf'+(1+iSeqNumIndex);
	
	// Update the duplicate field value:
	
	var bSuccessful = duplicateIndividualUDFField(
							sTableName,
							sIDFieldName,
							sSysUDFTypeId,
							sUniqueId,
							sFieldType,
							sUDFFieldName
						);	
	
	return bSuccessful;
}