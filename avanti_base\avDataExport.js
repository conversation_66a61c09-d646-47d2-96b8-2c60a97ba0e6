
/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"AC65E4FA-CF5E-46F9-97C5-9BEFAEAC1C51",variableType:-4}
 */
var ENUM_IMPORT_TYPES = {
	Customers: i18n.getI18NMessage('avanti.utils.dataImport.vl_dataImportType.Customers'),
	Suppliers: i18n.getI18NMessage('avanti.utils.dataImport.vl_dataImportType.Vendors'),
	CostCenters: 'Cost Centers',
	Inventory: 'Inventory',
	Employees: 'Employees',
	Notes: 'Notes',
	CRM: 'CRM',
	UDFQuestions: 'UDF Questions',
	UDFAnswers: 'UDF Answers',
	SalesOrders: i18n.getI18NMessage('avanti.lbl.SalesOrdersImport'),
	Taxes: i18n.getI18NMessage('avanti.lbl.Taxes'),
	Invoices: i18n.getI18NMessage('avanti.lbl.InvoicesHistorical'),
	Projects: i18n.getI18NMessage('avanti.program.projects'),
	PriceRules: i18n.getI18NMessage('avanti.lbl.PriceRules'),
	Accounting: i18n.getI18NMessage('avanti.lbl.accounting')
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"BE1373F6-E785-4EF6-9022-9A3D8F0627E3",variableType:-4}
 */
var ENUM_CUSTOMER_SUBTYPES = {
	Customers: '1 - Customers',
	Addresses: '2 - Addresses',
	Contacts: '3 - Contacts',
	DivisionsPlants: '4 - Divisions/Plants'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"23281F21-34F2-4044-B158-011ED4425E46",variableType:-4}
 */
var ENUM_SUPPLIER_SUBTYPES = {
	Suppliers: '1 - Suppliers',
	Addresses: '2 - Addresses',
	Contacts: '3 - Contacts'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"C1D3DBC2-**************-D224360A91DD",variableType:-4}
 */
var ENUM_COSTCENTER_SUBTYPES = {
	Departments: '1 - Departments',
	Categories: '2 - Categories',
	Operations: '3 - Operations'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"D0B23A78-FE40-4F56-92A8-50B972CA6006",variableType:-4}
 */
var ENUM_INVENTORY_SUBTYPES = {
	SubstrateTypes: '1 - Substrate Types',
	SubstrateFinishes: '2 - Substrate Finishes',
	InventoryItems: '3 - Inventory Items',
	BinLocations: '4 - Bin Locations',
	InventoryItemsMultiLocations: '5 - Inventory Items Multiple Locations',
	ItemDocuments: '6 - Item Documents',
	ItemBOM: '7 - Bill of Materials'
}

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"E7BD4E37-696C-4553-806D-E9D4FBC80F6A",variableType:-4}
 */
var ENUM_EMPLOYEE_SUBTYPES = {
	SalesReps: '1 - Sales Reps',
	Employees: '2 - Employees'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"CFC830D9-A201-46B3-A63A-7EE64ECDF3B5",variableType:-4}
 */
var ENUM_NOTES_SUBTYPES = {
	Activity : 'Activity',
	Customer : 'Customer',
	CustomerContact : 'Customer Contact',
	Estimate : 'Estimate',
	Lead : 'Lead',
	SalesOrder : 'Sales Order',
	Supplier: 'Supplier'
}

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"A807563D-0F8D-4807-9DE2-F9E3720D5BA2",variableType:-4}
 */
var ENUM_CRM_SUBTYPES = {
	Leads: '1 - Leads',
	Activities: '2 - Activities'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"3F91E250-2F23-49EF-BB4B-459434AF5E10",variableType:-4}
 */
var ENUM_UDF_QUESTIONS_SUBTYPES = {
	UDFQuestions: '1 - UDF Questions',
	UDFQuestionTblOptions: '2 - UDF Question Table Options'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"E64E392F-459E-4FCF-AB3C-8233A4E54565",variableType:-4}
 */
var ENUM_UDF_ANSWERS_SUBTYPES = {
	Customer: 'Customer',
	CustomerContact: 'Customer Contact',
	Item: 'Item',
	Suppliers: 'Suppliers'
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"1CE7BB5B-6123-44A1-8C54-CC311FBE01F4",variableType:-4}
 */
var ENUM_TAXES_SUBTYPES = {
	TaxItems: '1 - Tax Items', 
	TaxGroups: '2 - Tax Groups', 
	TaxItemRates: '3 - Tax Item Rates'
};

/**
 * @properties={typeid:35,uuid:"3A7A10E3-4C81-4B10-9327-A22617DD2A66",variableType:-4}
 */
var ENUM_SALES_ORDER_SUBTYPES = {
	SalesOrders: '1 - Sales Orders', 
	SalesOrderDtl: '2 - Sales Order Details', 
	SalesOrderMultiShip: '3 - Sales Order Multiship', 
	SalesOrderMultiShipQty: '4 - Sales Order Multiship Qtys'
};

/**
 * @properties={typeid:35,uuid:"351FF67A-DA58-4E8D-9EB9-0690BB3F9BAF",variableType:-4}
 */
var ENUM_ACCOUNTING_SUBTYPES = {
	ChartOfAccounts: '1 - Chart of Accounts' 
};

/**
 * @type {Array<String>}
 *
* @properties={typeid:35,uuid:"5895FCA8-8D19-4D2A-9B47-097CA882635A",variableType:-4}
*/
var maCustomerColNames = ['Customer Code', 'Name','Type','Category','Class','Territory','Group','Parent  Customer','Salesperson','Status',
'Currency','Payment Terms','Website','SIC Code','Shipping Method','Credit Limit','Payment Method','Industry','Minimim Order Amount','PO Required',
'Ship Complete','Amounts On Packing Slip','Exclude Price On Packing Slip','Tax Group','Date Created','Created By Employee',
'Shipto Address Code','Billto Address Code','Warehouse Code','CSR','Accepts Back Orders','Proof Contact','Samples Contact','Chargeback Code',
'Priority','Tax Reg Number','Invoice Print Default','Account Code','MTD Sales','MTD Profit','YTD Profit','YTD Sales', 'Over/Under Threshold (%)',
'Default Invoice Freight Revenue', 'Project Required','Account - Default Shipper - Use Customer Address', 
'Account - Default Shipper - Customer Code', 'Account - Default Shipper - Ship-From Contact First And Last', 
'Account - Default Shipper - Ship-From Address Code', 'FOB Customer', 'FOB Address Code', 'FOB Address Customer Code','Default Shipping Markup %',
'Address Name', 'Phone 1', 'Phone 1 Ext', 'Phone 2', 'Phone 2 Ext', 'Phone 3', 'Phone 3 Ext', 'Fax', 'Fax Ext', 'Shipping Method', 'Tax Group Code', 
'Salesperson', 'Territory', 'Address Active', 'Warehouse', 'Contact First And Last', 'Default Shipper - Use Customer Address', 
'Default Shipper - Customer Code', 'Default Shipper - Ship-From Contact First And Last', 'Default Shipper - Ship-From Address Code', 'Address 1', 'Address 2',
'Address 3', 'City', 'State/Province Code', 'Zip/Postal Code', 'Country Code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"76451ECB-232A-466B-BCBF-6D0C5844D277",variableType:-4}
 */
var maCustomerAddressColNames = ['Customer Code', 'Address Code', 'Address Name', 'Phone 1', 'Phone 1 Ext', 'Phone 2', 'Phone 2 Ext', 'Phone 3', 
'Phone 3 Ext', 'Fax', 'Fax Ext', 'Shipping Method', 'Tax Group Code', 'Salesperson', 'Territory', 'Address Active', 'Warehouse', 'Contact First And Last', 
'Default Shipper - Use Customer Address', 'Default Shipper - Customer Code', 'Default Shipper - Ship-From Contact First And Last', 
'Default Shipper - Ship-From Address Code', 'Address 1', 'Address 2', 'Address 3', 'City', 'State/Province Code', 'Zip/Postal Code', 'Country Code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"890321D8-A48D-4359-B20C-1DB6625A033A",variableType:-4}
 */
var maCustomerContactColNames = ['Customer Code', 'Customer Address Code', 'Title', 'First Name', 'Middle Name', 'Last Name', 'Suffix', 
'Use Default Address', 'Business Phone', 'Business Ext', 'Mobile Phone', 'Home Phone', 'Other Phone', 'Business Fax', 'Home Fax', 'Business Email', 
'Home Email', 'Other Email', 'Job Title', 'Contact Type', 'Department', 'Division', 'Contact Active'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"6FB3F913-AF40-4577-AE60-C626AE40CFB5",variableType:-4}
 */
var maSupplierColNames = ['Supplier Code', 'Supplier Name', 'Supplier Active', 'Currency Code', 'Supplier Website', 'Supplier Since', 'Buyer', 
'Minimim PO Value', 'Shipping Method', 'Account Number', 'Supplier Accepts Backorders', 'Supplier Confirmation Required', 'Freight Bill Method', 
'Freight Cost Method', 'Payment Terms', 'Default Lead Time', 'Primary Address Code', 'Matching Method'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"2EDC3202-7827-4FA5-8BFA-06173A744176",variableType:-4}
 */
var maSupplierAddressColNames = ['Supplier Code', 'Address Code', 'Address Name', 'Phone 1', 'Phone 1 Ext', 'Phone 2', 'Phone 2 Ext', 'Phone 3', 
'Phone 3 Ext', 'Fax', 'Fax Ext', 'Supplier Contact', 'Address Active', 'Address 1', 'Address 2', 'Address 3', 'City', 'State/Province Code', 
'Zip/Postal Code', 'Country Code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"AAB90FB6-2095-41DA-9B86-CDE8D903E78A",variableType:-4}
 */
var maSupplierContactColNames = ['Supplier Code', 'Supplier Address Code', 'Title', 'First Name', 'Middle Name', 'Last Name', 'Suffix', 
'Use Default Address', 'Business Phone', 'Business Ext', 'Mobile Phone', 'Home Phone', 'Other Phone', 'Business Fax', 'Home Fax', 'Business Email', 
'Home Email', 'Other Email', 'Job Title', 'Contact Type', 'Department', 'Division', 'Contact Active'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"********-91CE-44D7-9208-28E31829F04E",variableType:-4}
 */
var maCostCenterDepartmentColNames = ['Department Code', 'Plant Code', 'Description', 'Short Description', 'Schedule', 'Active', 'Shift Code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"293A9BA7-A726-4400-B904-F7007B679C74",variableType:-4}
 */
var maCustomerDivisionPlantColNames = ['Customer Code', 'Division Code', 'Plant Code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"3653331E-A9D3-4CE4-9613-A56148EC0230",variableType:-4}
 */
var maCostCenterCategoryColNames = ['Department Code', 'Category Code', 'Description', 'Short Description', 'Active', 'GL Cost of Sales', 'GL Sales', 
'External Device'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"7A1C62A1-6329-4C7F-BC88-2CAF49DE239F",variableType:-4}
 */
var maCostCenterOperationColNames = ['Department Code','Category Code','Operation Code','Description','Short Description','Type', 'Base Rate','Overhead','Labor','Chargeable','Qty Required','Active',
'Division Code','Plant Code','Total Rate','Group by Category','Default Lag','Default Lag Units','Default Lag Type','Default Successor Lag','Default Successor Lag Units','Default Successor Lag Type',
'QA Check Field 1','QA Check Field 2','QA Check Field 3','QA Check Field 1 Enabled','QA Check Field 2 Enabled','QA Check Field 3 Enabled','JDF Type', 'Lag Time Based On'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D89298F8-DE24-4CDD-AEA9-49D8F7101FAE",variableType:-4}
 */
var maInventorySubstrateTypesColNames = ['papergrade_name','papergrade_active','papergrade_width','papergrade_length','papergrade_is_roll','papergrade_weight_by','papergrade_charge_partial'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"1BD4AEFA-DF4E-4DC6-A2F0-473BB9BDBE22",variableType:-4}
 */
var maInventorySubstrateFinishesColNames = ['papergrade_name','paperbrand_name','paperbrand_active','paperbrand_paper_full_desc','paperbrand_basis_weight','paperbrand_finish_back','paperbrand_finish_front',
'paperbrand_width','paperbrand_length'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"BC03C00C-484A-41C4-AE8B-59C9D664E845",variableType:-4}
 */
var maInventoryItemsColNames = ['item_code','Revision','description1','description2','item_class_code','item_type_code','status','lot_item','color',
'decimal_places','avg_lead_time','salestax_option','tax_group_code','allow_commissions','allow_discounts','allow_backorders','dimension_height',
'dimension_length','dimension_width','max_weight','uom_code','cust_code','worktype_code','cust_part_number','isbn_number','group_code',
'backorder_qty','committed_qty','inproduction_qty','intransit_qty','onhand_qty','onpo_qty','onporeq_qty','scheduled_qty','specifications',
'glacct_code_inventory_adj','glacct_code_cost_of_sales','glacct_code_inventory','glacct_code_sales_returns','glacct_code_sales','ink_type_code',
'cylinder_teeth','cylinder_diameter','plate_run_length','paper_grade_name','paper_brand_name','paper_first_dim','paper_second_dim',
'paper_caliper','item_number_of_tabs','paper_roll_weight','paper_m_weight','paper_weight','Warehouse Code','primary_supplier_code',
'primary_supplier_uom_code','primary_supplier_purchase_cost_uom_code','primary_supplier_list_price','selling_list_price','selling_uom_code',
'primary_supplier_leadtime_flag','selling_uom_conversion_factor','pricing_uom_code','primary_supplier_uom_conversion_factor', 
'substrate - brand name','Selling Units->Per Quantity Of', 'Warehouse Estimating Units (code)', 'Landed Cost Costing Units Factor', 'Supplier Details Item Number', 
'Supplier Details Item Description', 'Supplier Details Reorder Multiple', 'Supplier Details Minimum Order Qty', 'Warehouse Average Cost',
'Default Receipt Location', 'No Bin Location Tracking', 'creation_date','expiry_date','Default Issue Location','Project', 'Harmonized Code', 'Manufacture Country', 
'Virtual Item', 'Language'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"EB2AAE2F-32C9-4018-806E-7BF41D2082C9",variableType:-4}
 */
var maInventoryBinColNames = ['Warehouse Code','Level 1','Level 2','Level 3','Level 4','Level 5','Minimum Qty','Maximum Qty','Active flag (Y/N)'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"5E789988-3809-4715-9EBB-EE543B12BCB0",variableType:-4}
 */
var maInventoryItemsBinColNames = ['item_code','Warehouse Code', 'Warehouse Bin Location','onhand_qty','Estimating Units','Reorder Method','Min On Hand','Max On Hand','Default Receipt Location',
'Default Issue Location','Project'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"67B383C3-E76A-46EE-B115-8CE4638814FF",variableType:-4}
 */
var maInventoryItemDocumentsColNames = ['Item Code', 'Title', 'File Path', 'BOM Item', 'Section', 'Category', 'Proof', 'For Job', 'OKD', 
'OKD By', 'OKD Date', 'Show on Job Ticket', 'Show on Quote'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"8DB7F79E-0CD1-47DD-8B5E-27AE9CC869DC",variableType:-4}
 */
var maBOM = ['Item Code', 'Bill of Material - Item Code', 'Bill of Material - Qty'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"EA04F918-C015-4774-82B2-3839CB98362E",variableType:-4}
 */
var maEmployeesSalesRepColNames = ['salesperson_code','territory_code','salesperson_name','salesperson_active','salesperson_commission','salesper_commission_type'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"C84DDB06-69B3-4BDD-AEF3-6D7A5CCD9EC6",variableType:-4}
 */
var maEmployeesColNames = ['empl_code','empl_first_name','empl_last_name','emplclass_code','empl_active','empl_date_format','salesper_code','app_assignment_type_descr'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"CAF43C15-9065-43A7-AE52-97E41337F2FC",variableType:-4}
 */
var maNotesActivityColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"441BD05F-42CE-4480-BA67-EF757123E71B",variableType:-4}
 */
var maNotesCustomerColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"1A744283-284F-4E4A-A715-3AACB4690AC9",variableType:-4}
 */
var maNotesCustomerContactColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code','note_contact_first_and_last'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"2D2B28D1-7305-4730-A362-4370B2B25106",variableType:-4}
 */
var maNotesEstimateColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code','note_estimate_number'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D6CF986D-F74E-46DB-8286-DCEA8D756181",variableType:-4}
 */
var maNotesLeadColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code','note_lead_number'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"190A8231-AE0D-4B2B-A773-2837A0BB66A9",variableType:-4}
 */
var maNotesSalesOrderColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_cust_code','note_sales_order_number'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"25AF3EBE-BA9C-4074-A367-763F5A06D985",variableType:-4}
 */
var maNotesSupplierColNames = ['note_creation_date','note_creation_empl_code','note_last_modified_date','note_last_modified_empl_code','note_text',
'notetype_descr','note_title','internal_only','note_supplier_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D09B0ACB-A8EA-4464-A22C-8A7593D664F2",variableType:-4}
 */
var maCRMActivityColNames = ['activity_type','activity_subject','activity_regarding','activity_assignment_empl_code','activity_customer_code',
'activity_contact_first_and_last','activity_priority','activity_end_datetime','activity_estimate_number','activity_sales_order_number',
'activity_job_number','activity_packing_slip_number','activity_status','activity_due_datetime','activity_objective_complete',
'activity_objective','note','created_date','created_by_emp','activity_lead_number'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"66C912C2-DE5B-47C4-96C8-0779A45F22E7",variableType:-4}
 */
var maCRMLeadColNames = ['lead_number','cust_code','lead_date_time','contact_first_and_last','lead_date_time_passed_to_rep','salesper_code',
'csr_code','is_active','est_number','crm_lead_stage_descr','created_date_time','created_by_emp_code','modified_date_time',
'modified_by_emp_code','crm_lead_rating_descr','crm_lead_rating_after_descr','lead_owner_emp_code','crm_lead_source_descr'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"6EE1A908-479D-4DAF-97EE-043AD0A7C0D8",variableType:-4}
 */
var maUDFQuestionColNames = ['udf_category','udf_field','udf_field_type','udf_parent_field'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"E1B5D274-8685-4B12-94A6-C5819B247868",variableType:-4}
 */
var maUdfQuestionTableOptionColNames = ['udf_category','udf_field','udf_table_option'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"1C3282ED-5115-4690-B0D0-8C7A9AD1546E",variableType:-4}
 */
var maUDFAnswerCustomerColNames = ['udf_descr','udf_answer','customer_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"55E50F03-DE73-449A-AD45-95A1CEA5E99A",variableType:-4}
 */
var maUDFAnswerCustomerContactColNames = ['udf_descr','udf_answer','customer_code','contact_first_and_last'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"DAE0224C-EBC1-487C-A1FB-5DE0E55338E7",variableType:-4}
 */
var maUDFAnswerItemColNames = ['udf_descr','udf_answer','item_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"281AC2E3-315A-49DF-9D7A-0902E38F2BF9",variableType:-4}
 */
var maUDFAnswerSupplierColNames = ['udf_descr','udf_answer','supplier_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"4D4BA829-8841-4228-A88A-67A02DBE71DA",variableType:-4}
 */
var maTaxItems = ['Tax Item Code','Tax Type','Tax Item Description','Tax Registration No.','G/L Account','Tax Based On','Based On Tax Item','Rounding Rule','Taxable Tax','Active','Tax Included','Tax Refundable'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"B77C2697-A5F4-46A5-8A6C-DE2443BBF729",variableType:-4}
 */
var maTaxGroups = ['Tax Group Code','Description','Active','Tax Shipping Charges','Add Tax Item Codes From Here (note: Tax Items must exist)'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"3974711B-8344-4FE4-A0A8-C42579F8980D",variableType:-4}
 */
var maTaxItemRates = ['Tax Item Code','Effective Date','Tax Percentage'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"40ADDB05-FDD9-442D-8E33-E5BFF4682642",variableType:-4}
 */
var maInvoiceColNames = ['Customer Code', 'Invoice Date', 'Invoice Number', 'Item Code', 'Invoice Total'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"32EE5546-6DA8-461E-8112-5B8BB900FD32",variableType:-4}
 */
var maProjectColNames = ['Project','Customer Code','Allow Project to control the tax','Sales Tax Option	Tax Group','Tax Exempt Reason'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"3B20B456-1CB2-4024-9D1F-0A9B2ABB26B2",variableType:-4}
 */
var maSalesOrderColNames = ['ordh_document_num', 'cust_code', 'ordh_shipto_custaddr_code', 'ordh_billto_custaddr_code', 'ordh_description', 'currency_code', 
'salesterr_code', 'ordh_order_date', 'ordh_customer_po', 'campaign_code', 'rush_code', 'custproj_code', 'ordtype_code', 'ordh_salesper_code', 'ordh_csr_empl_code', 
'plant_code', 'ordh_document_stream', 'shipmethod_code', 'custcontact_first_and_last', 'ordh_priority', 'ordh_staging_location', 'paymethod_code', 
'ordh_chargeback_code', 'ordrevh_order_status', 'ordrevh_promise_date', 'ordrevh_expected_date', 'ordrevh_exchange_rate', 'ordrevh_total_order_qty', 
'ordrevh_total_ship_qty', 'ordrevh_total_backorder_qty', 'ordrevh_subtotal_amount', 'ordrevh_markup_pct', 'ordrevh_markup_amount', 'ordrevh_shipping_amount', 
'ordrevh_warehouse_amount', 'ordrevh_rush_amount', 'ordrevh_total_amount', 'ordrevh_onhold', 'ordrevh_total_taxes', 'ordrevh_is_active', 'ordrevh_total_commission', 
'bill_ordaddr_name', 'bill_ordaddr_phone1', 'bill_ordaddr_phone1_ext', 'bill_ordaddr_phone2', 'bill_ordaddr_phone2_ext', 'bill_ordaddr_phone3', 'bill_ordaddr_phone3_ext', 
'bill_ordaddr_fax', 'bill_ordaddr_fax_ext', 'bill_ordaddr_fax_home', 'bill_ordaddr_email_business', 'bill_ordaddr_email_home', 'bill_ordaddr_email_other', 
'bill_custcontact_first_and_last', 'bill_addr_address1', 'bill_addr_address2', 'bill_addr_address3', 'bill_addr_city', 'bill_stateprov_code', 'bill_addr_postal', 
'bill_country_code', 'ship_ordaddr_name', 'ship_ordaddr_phone1', 'ship_ordaddr_phone1_ext', 'ship_ordaddr_phone2', 'ship_ordaddr_phone2_ext', 'ship_ordaddr_phone3', 
'ship_ordaddr_phone3_ext', 'ship_ordaddr_fax', 'ship_ordaddr_fax_ext', 'ship_ordaddr_fax_home', 'ship_ordaddr_email_business', 'ship_ordaddr_email_home', 
'ship_ordaddr_email_other', 'ship_custcontact_first_and_last', 'ship_addr_address1', 'ship_addr_address2', 'ship_addr_address3', 'ship_addr_city', 'ship_stateprov_code', 
'ship_addr_postal', 'ship_country_code', 'cust_addr_code'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"C9D58D84-E624-48A8-83AF-F541209E6C99",variableType:-4}
 */
var maSalesOrderDetailColNames = ['sales_order_num', 'ordrevd_line_num', 'ordrevd_prod_desc', 'whse_code', 'uom_code', 'ordrevd_qty_ordered', 'ordrevd_qty_shipped', 'ordrevd_qty_backord', 
'ordrevd_sell_price', 'ordrevd_disc_amt', 'ordrevd_extended_price', 'inventory_item_code', 'ordrevd_unit_cost', 'ordrevd_extended_cost', 'sysworktype_code', 'worktype_descr', 'ordrevd_unit_price', 
'ordrevd_staging_location', 'ordrevd_ext_price_over'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"D6EAF4C6-B7CD-427A-B6F0-5344C5425E1D",variableType:-4}
 */
var maSalesOrderMultiShipColNames = ['sales_order_num', 'sequence_nr', 'cust_code', 'custaddr_code', 'shipmethod_code', 'ordrevhms_instructions', 'ordrevhms_event_date', 
'ordrevhms_must_arrive_date', 'ordrevhms_ship_date', 'ordrevhms_shipping_charges', 'custcontact_first_and_last'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"45EE1EA9-D164-4E45-8915-C82EEE431674",variableType:-4}
 */
var maSalesOrderMultiShipQtyColNames = ['sales_order_num', 'detail_line_num', 'multi_ship_line_num', 'ordrevdms_qty', 'ordrevdms_qty_backord', 'ordrevdms_qty_shipped'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"********-A8D1-4801-BBE7-B7928FE7FE1E",variableType:-4}
 */
var maChartOfAccountsColNames = ['Account','Description','Account Type'];

/**
 * @properties={typeid:35,uuid:"7C579725-BE2D-4E72-9E62-5C1192F71DA2",variableType:-4}
 */
var contactFirstAndLastNameCache = {};

/**
 * @properties={typeid:35,uuid:"BD49A08A-A628-4525-AC37-EF275C7C2163",variableType:-4}
 */
var addressCache = {};

/**
 * @properties={typeid:35,uuid:"3E1A4A7A-3EAC-4BD0-A0A0-DC4BEAEA28A8",variableType:-4}
 */
var contactCache = {};

/**
 * @properties={typeid:35,uuid:"0D7F90A4-86A0-4972-A0ED-AB22C16C1C78",variableType:-4}
 */
var taxGroupCache = {};

/**
 * @properties={typeid:35,uuid:"DC71E773-C506-4583-B827-0DFA896CFA8C",variableType:-4}
 */
var itemClassCache = {};

/**
 * @properties={typeid:35,uuid:"05570AE3-1FC0-45DE-86C3-5BBCC6C7DBA7",variableType:-4}
 */
var sysUnitOfMeasureCache = {};

/**
 * @properties={typeid:35,uuid:"0E685B6C-543D-4BCB-8BD3-CB049BE0B3C5",variableType:-4}
 */
var workTypeCache = {};

/**
 * @properties={typeid:35,uuid:"99D44560-6DE1-4078-ABA0-6F29DC1A8C8F",variableType:-4}
 */
var customerCache = {};

/**
 * @properties={typeid:35,uuid:"3FF23432-8478-4C4A-86C3-010BA3E1416E",variableType:-4}
 */
var inventoryGroupCache = {};

/**
 * @properties={typeid:35,uuid:"2C806CF7-9DB0-4377-BB34-47D6D59E41D5",variableType:-4}
 */
var glAccountCache = {};

/**
 * @properties={typeid:35,uuid:"C80482B0-A4AE-4221-92B1-79EAAB79299E",variableType:-4}
 */
var itemPaperCache = {};

/**
 * @properties={typeid:35,uuid:"2859399B-409A-4B0B-B2EA-396710E1A9D7",variableType:-4}
 */
var warehouseCache = {};

/**
 * @properties={typeid:35,uuid:"F36767E1-C72C-4028-B53B-0EDDB0CDB22D",variableType:-4}
 */
var warehouseLocationCache = {};

/**
 * @properties={typeid:35,uuid:"********-25DE-4966-80B0-4421D17C1545",variableType:-4}
 */
var warehouseLevelCodeCache = {};

/**
 * @properties={typeid:35,uuid:"FB54EC91-DB6E-4B5F-B12E-677078F8FB3C",variableType:-4}
 */
var supplierCache = {};

/**
 * @properties={typeid:35,uuid:"C3BE0DB5-EA2C-4DBB-8303-A73039B5E045",variableType:-4}
 */
var projectDescByCustCache = {};

/**
 * @properties={typeid:35,uuid:"C7B3D8BB-197A-46F8-8229-48C1CDB1922F",variableType:-4}
 */
var inventoryItemCache = {};

/**
 * @properties={typeid:35,uuid:"01D80775-7815-4409-829E-E92B088E1BEC",variableType:-4}
 */
var taskWorkTypeSectionCache = {};

/**
 * @properties={typeid:35,uuid:"703AF6B4-31F0-4FAC-97F3-5126C9CF2A12",variableType:-4}
 */
var docsManagementCategoryCache = {};

/**
 * @properties={typeid:35,uuid:"A1152309-0B31-40FF-AC0F-66679D779E32",variableType:-4}
 */
var sysEmployeeCache = {};

/**
 * @properties={typeid:35,uuid:"D1CE48C9-CFC3-417C-91F1-A78805B5101A",variableType:-4}
 */
var sysCurrencyCache = {};

/**
 * @properties={typeid:35,uuid:"EB93235C-FAD6-47CF-9F87-7E7E8A7288A8",variableType:-4}
 */
var saTerritoryCache = {};

/**
 * Main function to call for exporting the data
 * @param importType
 * @param [subType]
 *
 * @return
 * @properties={typeid:24,uuid:"9E2A5458-47B6-4A75-9302-7DDC562A6133"}
 * @AllowToRunInFind
 */
function exportData(importType, subType) {
	/**@type {JSFoundSet} */
	var fsTable = null;
	var sOutput = null;
	var sSQL = null;
	/***@type {{sql:String,
     *          args:Array,
     *          server:String,
     *          maxRows:Number,
     *          table:String}}
     */
    var oSQL = { };
    
    //Create the foundset for the selected export layout
	if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers) {
		sSQL = "SELECT cust_id FROM sa_customer WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sa_customer>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_customer',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses) {
		sSQL = "SELECT custaddr_id FROM sa_customer_address WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sa_customer_address>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_customer_address',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts) {
		sSQL = "SELECT custcontact_id FROM sa_customer_contact WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sa_customer_contact>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_customer_contact',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants) {
		sSQL = "SELECT division_plant_id FROM sa_division_plant WHERE org_id = ? AND object_type = 'CUST'";
		/**@type {JSFoundSet<db:/avanti/sa_division_plant>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_division_plant',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers) {
		sSQL = "SELECT supplier_id FROM ap_supplier WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/ap_supplier>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'ap_supplier',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses) {
		sSQL = "SELECT suppaddr_id FROM ap_supplier_address WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/ap_supplier_address>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'ap_supplier_address',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts) {
		sSQL = "SELECT suppcontact_id FROM ap_supplier_contact WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/ap_supplier_contact>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'ap_supplier_contact',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments) {
		sSQL = "SELECT dept_id FROM sys_department WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_department>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_department',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories) {
		sSQL = "SELECT opcat_id FROM sys_operation_category WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_operation_category>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_operation_category',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations) {
		sSQL = "SELECT cc_id FROM sys_cost_centre WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_cost_centre>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_cost_centre',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes) {
		sSQL = "SELECT papergrade_id FROM in_paper_grade WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_paper_grade>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_paper_grade',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes) {
		sSQL = "SELECT paperbrand_id FROM in_paper_brand WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_paper_brand>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_paper_brand',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems) {
		var nUseItemRevision = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseItemRevisionField);
		
		sSQL = "SELECT item_id FROM in_item WHERE org_id = ? AND itemtype_code != ?";
		/**@type {JSFoundSet<db:/avanti/in_item>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_item',[globals.org_id, scopes.avUtils.ITEM_TYPE.OutsourcedService]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations) {
		sSQL = "SELECT whseloc_id FROM in_warehouse_location WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_warehouse_location>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_warehouse_location',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations) {
		sSQL = "SELECT itemwhse_id FROM in_item_warehouse WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_item_warehouse>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_item_warehouse',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments) {
		sSQL = "SELECT item_doc_id FROM in_item_doc WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_item_doc>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'in_item_doc',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM) {
		sSQL = "SELECT itembom_id FROM in_item_bill_of_material WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/in_item_bill_of_material>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL, 'in_item_bill_of_material', [globals.org_id]);
		
		if (utils.hasRecords(fsTable)) {
			fsTable.sort("in_item_bill_of_material_to_in_item.item_code ASC, sequence_nr ASC");
		}
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Employees && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps) {
		sSQL = "SELECT salesper_id FROM sa_sales_person WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sa_sales_person>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_sales_person',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Employees && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees) {
		sSQL = "SELECT empl_id FROM sys_employee WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_employee>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_employee',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && 
			(subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Activity || subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Customer)) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Customer', 'Customer']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Customer', 'Contact']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Customer', 'Estimate']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Customer', 'Lead']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Customer', 'Order']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier) {
		sSQL = "SELECT note_id FROM sys_note WHERE org_id = ? AND note_object_source_type = ? AND note_object_relation_type = ?";
		/**@type {JSFoundSet<db:/avanti/sys_note>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_note',[globals.org_id, 'Supplier', 'Supplier']);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CRM && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads) {
		sSQL = "SELECT crm_lead_id FROM crm_lead WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/crm_lead>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'crm_lead',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CRM && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities) {
		sSQL = "SELECT crm_activity_id FROM crm_activity WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/crm_activity>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'crm_activity',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions) {
		sSQL = "SELECT sys_udf_type_id FROM sys_udf_type WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_udf_type>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_udf_type',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions) {
		sSQL = "SELECT sys_udf_type_table_value_id FROM sys_udf_type_table_values WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_udf_type_table_values>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_udf_type_table_values',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers) {
		var udfCode = null;
		if (subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer) {
			udfCode = 'CUSTOMER';
		}
		else if (subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact) {
			udfCode = 'CONTACT';
		}
		else if (subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item) {
			udfCode = 'ITEM';
		}
		else if (subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers) {
			udfCode = 'SUPPLIER';
		}
		else {
			return null;
		}
		
		sSQL = "SELECT sys_udf_values.sys_udf_values_id \
				  FROM sys_udf_values \
			INNER JOIN sys_udf_type ON sys_udf_values.sys_udf_type_id = sys_udf_type.sys_udf_type_id \
				 WHERE sys_udf_values.org_id = ? \
				   AND sys_udf_type.udf_code = ?";
		/**@type {JSFoundSet<db:/avanti/sys_udf_values>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_udf_values',[globals.org_id, udfCode]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems) {
		sSQL = "SELECT taxitem_id FROM sys_sales_tax_item WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_sales_tax_item>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_sales_tax_item',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups) {
		sSQL = "SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_sales_tax_group>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_sales_tax_group',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates) {
		sSQL = "SELECT taxrate_id FROM sys_sales_tax_rate WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sys_sales_tax_rate>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sys_sales_tax_rate',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Invoices) {
		sSQL = "SELECT invreg_id FROM sa_invoice_register WHERE org_id = ? AND invreg_number = 'IMPORT' AND is_hidden = 1";
		/**@type {JSFoundSet<db:/avanti/sa_invoice_register>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_invoice_register',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Projects) {
		sSQL = "SELECT custproj_id FROM sa_customer_project WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/sa_customer_project>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_customer_project',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders) {
		sSQL = "SELECT ordh_id FROM sa_order WHERE org_id = ? AND ordh_historical = 1";
		/**@type {JSFoundSet<db:/avanti/sa_order>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'sa_order',[globals.org_id]);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl) {
		oSQL = {};
        oSQL.server = globals.avBase_dbase_avanti;
        oSQL.table = "sa_order_revision_detail";
        oSQL.args = [globals.org_id];
        oSQL.sql = "SELECT revd.ordrevd_id \
                      FROM sa_order_revision_detail AS revd \
                INNER JOIN sa_order_revision_header AS revh ON revd.ordrevh_id = revh.ordrevh_id \
                INNER JOIN sa_order AS ord ON revh.ordh_id = ord.ordh_id \
                     WHERE revd.org_id = ? \
                       AND ord.ordh_historical = 1 \
					   AND revh.ordrevh_revision = 0";
		/**@type {JSFoundSet<db:/avanti/sa_order_revision_detail>} */
		fsTable = globals["avUtilities_sqlFoundset"](oSQL);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip) {
		oSQL = {};
        oSQL.server = globals.avBase_dbase_avanti;
        oSQL.table = "sa_order_revh_multi_ship";
        oSQL.args = [globals.org_id];
        oSQL.sql = "SELECT revhms.ordrevhms_id \
        			  FROM sa_order_revh_multi_ship AS revhms \
        		INNER JOIN sa_order_revision_header AS revh ON revhms.ordrevh_id = revh.ordrevh_id \
        		INNER JOIN sa_order AS ord ON revh.ordh_id = ord.ordh_id \
        			 WHERE revhms.org_id = ? \
        			   AND ord.ordh_historical = 1 \
        			   AND revh.ordrevh_revision = 0";
		/**@type {JSFoundSet<db:/avanti/sa_order_revh_multi_ship>} */
		fsTable = globals["avUtilities_sqlFoundset"](oSQL);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty) {
		oSQL = {};
        oSQL.server = globals.avBase_dbase_avanti;
        oSQL.table = "sa_order_revd_multi_ship_qty";
        oSQL.args = [globals.org_id];
        oSQL.sql = "SELECT revdmsqty.ordrevdms_id \
        			  FROM sa_order_revd_multi_ship_qty AS revdmsqty \
        		INNER JOIN sa_order_revision_detail AS revd ON revdmsqty.ordrevd_id = revd.ordrevd_id \
        		INNER JOIN sa_order_revision_header AS revh ON revd.ordrevh_id = revh.ordrevh_id \
        		INNER JOIN sa_order AS ord ON revh.ordh_id = ord.ordh_id \
        			 WHERE revdmsqty.org_id = ? \
        			   AND ord.ordh_historical = 1 \
        			   AND revh.ordrevh_revision = 0";
		/**@type {JSFoundSet<db:/avanti/sa_order_revd_multi_ship_qty>} */
		fsTable = globals["avUtilities_sqlFoundset"](oSQL);
	}
	else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Accounting && subType == scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts) {
		sSQL = "SELECT glacct_id FROM gl_account WHERE org_id = ?";
		/**@type {JSFoundSet<db:/avanti/gl_account>} */
		fsTable = scopes.avDB.getFSFromSQL(sSQL,'gl_account',[globals.org_id]);
	}
	else {
		return null;
	}
	
	if (!fsTable || fsTable.getSize() == 0) {
		scopes.avText.showInfo('fileExportNoData');
		return null;
	}

	//Go through each record in the foundset and create the row for export
	for (var i = 1; i <= fsTable.getSize(); i++) {
		var rRecord = fsTable.getRecord(i);
		var strLine = null;
		if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers) {
			strLine = CustomerExport(rRecord, subType);
		} 
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers) {
			strLine = SupplierExport(rRecord, subType);
		} 
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters) {
			strLine = CostCenterExport(rRecord, subType);
		} 
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory) {
			strLine = InventoryExport(rRecord, subType, nUseItemRevision);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Employees) {
			strLine = EmployeeExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes) {
			strLine = NoteExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CRM) {
			strLine = CRMExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions) {
			strLine = UDFQuestionExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers) {
			strLine = UDFAnswerExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes) {
			strLine = TaxExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Invoices) {
			strLine = InvoiceExport(rRecord);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Projects) {
			strLine = ProjectExport(rRecord);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders) {
			strLine = SalesOrderExport(rRecord, subType);
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Accounting) {
			strLine = AccountingExport(rRecord, subType);
		}

		if (strLine) {
			sOutput += strLine;
		}
	}
	
	//If there are data to export then create the layout header
	if (sOutput) {
		var aColHeaders;
		if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers) {
			aColHeaders = maCustomerColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses) {
			aColHeaders = maCustomerAddressColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts) {
			aColHeaders = maCustomerContactColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Customers && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants) {
			aColHeaders = maCustomerDivisionPlantColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers) {
			aColHeaders = maSupplierColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses) {
			aColHeaders = maSupplierAddressColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Suppliers && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts) {
			aColHeaders = maSupplierContactColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments) {
			aColHeaders = maCostCenterDepartmentColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories) {
			aColHeaders = maCostCenterCategoryColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CostCenters && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations) {
			aColHeaders = maCostCenterOperationColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes) {
			aColHeaders = maInventorySubstrateTypesColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes) {
			aColHeaders = maInventorySubstrateFinishesColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems) {
			aColHeaders = maInventoryItemsColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations) {
			aColHeaders = maInventoryBinColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations) {
			aColHeaders = maInventoryItemsBinColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments) {
			aColHeaders = maInventoryItemDocumentsColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Inventory && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM) {
			aColHeaders = maBOM.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Employees && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps) {
			aColHeaders = maEmployeesSalesRepColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Employees && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees) {
			aColHeaders = maEmployeesColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Activity) {
			aColHeaders = maNotesActivityColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Customer) {
			aColHeaders = maNotesCustomerColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact) {
			aColHeaders = maNotesCustomerContactColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate) {
			aColHeaders = maNotesEstimateColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead) {
			aColHeaders = maNotesLeadColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder) {
			aColHeaders = maNotesSalesOrderColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Notes && subType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier) {
			aColHeaders = maNotesSupplierColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CRM && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities) {
			aColHeaders = maCRMActivityColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.CRM && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads) {
			aColHeaders = maCRMLeadColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions) {
			aColHeaders = maUDFQuestionColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFQuestions && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions) {
			aColHeaders = maUdfQuestionTableOptionColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers && subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer) {
			aColHeaders = maUDFAnswerCustomerColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers && subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact) {
			aColHeaders = maUDFAnswerCustomerContactColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers && subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item) {
			aColHeaders = maUDFAnswerItemColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.UDFAnswers && subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers) {
			aColHeaders = maUDFAnswerSupplierColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems) {
			aColHeaders = maTaxItems.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups) {
			aColHeaders = maTaxGroups.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Taxes && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates) {
			aColHeaders = maTaxItemRates.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Invoices) {
			aColHeaders = maInvoiceColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Projects) {
			aColHeaders = maProjectColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders) {
			aColHeaders = maSalesOrderColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl) {
			aColHeaders = maSalesOrderDetailColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip) {
			aColHeaders = maSalesOrderMultiShipColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.SalesOrders && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty) {
			aColHeaders = maSalesOrderMultiShipQtyColNames.slice(0).join('\t');
		}
		else if (importType == scopes.avDataExport.ENUM_IMPORT_TYPES.Accounting && subType == scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts) {
			aColHeaders = maChartOfAccountsColNames.slice(0).join('\t');
		}
									
		sOutput = aColHeaders + '\n' + sOutput;
		

		var sFileName = 'Export_' + importType + '_' + (subType ? subType.split('- ').pop().replace(/\s+/g, '_') + '_' : '') + scopes.avUtils.getTimeStamp("-") + '.txt';

		// prompt for folder to save to if smart client
		if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
			sFileName = plugins.file.showFileSaveDialog(sFileName);
		}

		if (sFileName && plugins.file.writeTXTFile(sFileName, sOutput)) {
			scopes.avText.showInfo('fileExportSuccess');
		}
		clearCache();
	}
	else {
		scopes.avText.showInfo('fileExportUnsuccessful');
	}
}

/**
 * Get the string value of a boolean
 * @param value
 *
 * @return
 * @properties={typeid:24,uuid:"E93CA42D-B192-4E17-BA13-CCAF01596B35"}
 */
function getYesNo(value) {
	var objYN = {
		0: 'N',
		1: 'Y'
	};
	
	return objYN[value] != null ? objYN[value] : null;
}

/**
 * Get the UOM record
 * @param uomId
 *
 * @return
 * @properties={typeid:24,uuid:"7DED4262-BCBE-4CEE-B585-E4F29A100A48"}
 */
function getUOMRecord(uomId) {
	var res = null;
	if (!uomId) {
		return res;
	}
	var sSQL = "SELECT org_id, uom_id \
		  		  FROM sys_unit_of_measure \
		  		 WHERE org_id = ? \
		  		   AND uom_id = ?";
	/** @type {JSRecord<db:/avanti/sys_unit_of_measure>} **/
	res = scopes.avDB.getRecFromSQL(sSQL, 'sys_unit_of_measure', [globals.org_id.toString(), uomId.toString()]);
	return res;
}


/**
 * Clear all the cache variables
 * @properties={typeid:24,uuid:"F5645C44-7C16-4367-AC89-0BF67074CC15"}
 */
function clearCache() {
	contactFirstAndLastNameCache = {};
	addressCache = {};
	contactCache = {};
	taxGroupCache = {};
	itemClassCache = {};
	sysUnitOfMeasureCache = {};
	workTypeCache = {};
	customerCache = {};
	inventoryGroupCache = {};
	glAccountCache = {};
	itemPaperCache = {};
	warehouseCache = {};
	warehouseLocationCache = {};
	warehouseLevelCodeCache = {};
	supplierCache = {};
	projectDescByCustCache = {};
	inventoryItemCache = {};
	taskWorkTypeSectionCache = {};
	docsManagementCategoryCache = {};
	sysEmployeeCache = {};
	sysCurrencyCache = {};
	saTerritoryCache = {};
};

/**
 * Export all the customer related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"D5850ADF-3084-4C82-9207-1B213206612E"}
 */
function CustomerExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer;
	/**@type {JSRecord<db:/avanti/sa_customer_address>} */
	var rCustomerAddress;
	/**@type {JSRecord<db:/avanti/sa_customer_contact>} */
	var rCustomerContact;
	/**@type {JSRecord<db:/avanti/sa_division_plant>} */
	var rCustomerDivisionPlant;
	
	if (rFSRecord && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers) {
		rCustomer = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses) {
		rCustomerAddress = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts) {
		rCustomerContact = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants) {
		rCustomerDivisionPlant = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		
		if (rCustomer && recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers) {
			sOutput += rCustomer.cust_code + '\t';
			sOutput += rCustomer.cust_name + '\t';
			sOutput += getCustomerType(rCustomer.cust_type) + '\t';
			sOutput += getCustCategory(rCustomer) + '\t';
			sOutput += getCustClass(rCustomer) + '\t';
			sOutput += getTerritory(rCustomer) + '\t';
			sOutput += getCustGroup(rCustomer) + '\t';
			sOutput += getParentCustomer(rCustomer) + '\t';
			sOutput += getSalesPerson(rCustomer) + '\t';
			sOutput += getCustomerStatus(rCustomer.cust_status) + '\t';
			sOutput += getCurrencyCode(rCustomer) + '\t';
			sOutput += getPaymentTerms(rCustomer) + '\t';
			sOutput += (rCustomer.cust_website != null ? rCustomer.cust_website : '') + '\t';
			sOutput += getCustSIC(rCustomer) + '\t';
			sOutput += getShipMethod(rCustomer) + '\t';
			sOutput += (rCustomer.cust_credit_limit != null ? rCustomer.cust_credit_limit : '') + '\t';
			sOutput += getPaymentMethod(rCustomer) + '\t';
			sOutput += getIndustry(rCustomer) + '\t';
			sOutput += (rCustomer.cust_min_order_amount != null ? rCustomer.cust_min_order_amount : '') + '\t';
			sOutput += (getYesNo(rCustomer.cust_po_required) != null ? getYesNo(rCustomer.cust_po_required) : '') + '\t';
			sOutput += (getYesNo(rCustomer.cust_ship_complete) != null ? getYesNo(rCustomer.cust_ship_complete) : '') + '\t';
			sOutput += (getYesNo(rCustomer.cust_amounts_on_pkgslip) != null ? getYesNo(rCustomer.cust_amounts_on_pkgslip) : '') + '\t';
			sOutput += (getYesNo(rCustomer.cust_exclude_price_on_pkgslip) != null ? getYesNo(rCustomer.cust_exclude_price_on_pkgslip) : '') + '\t';
			sOutput += getTaxGroup(rCustomer) + '\t';
			sOutput += scopes.avDate.formatDate(rCustomer.cust_date_created,'YYYYMMDD') + '\t';
			sOutput += getEmployeeCode(rCustomer.cust_createdby_user_id) + '\t';
			sOutput += getShipToAddressCode(rCustomer) + '\t';
			sOutput += getBillToAddressCode(rCustomer) + '\t';
			sOutput += getWarehouseCode(rCustomer) + '\t';
			sOutput += getEmployeeCode(rCustomer.cust_csr_empl_id) + '\t';
			sOutput += (getYesNo(rCustomer.cust_accept_bo) != null ? getYesNo(rCustomer.cust_accept_bo) : '') + '\t';
			sOutput += getContactNameByCustContactId(rCustomer.cust_proof_contact_id) + '\t';
			sOutput += getContactNameByCustContactId(rCustomer.cust_samples_contact_id) + '\t';
			sOutput += (rCustomer.cust_chargeback_code != null ? rCustomer.cust_chargeback_code : '') + '\t';
			sOutput += (rCustomer.cust_priority != null ? rCustomer.cust_priority : '') + '\t';
			sOutput += (rCustomer.cust_tax_reg_number != null ? rCustomer.cust_tax_reg_number : '') + '\t';
			sOutput += getInvoicePrintDefault(rCustomer.cust_invoice_detail_level) + '\t';
			sOutput += (rCustomer.cust_account_code != null ? rCustomer.cust_account_code : '') + '\t';
			sOutput += (rCustomer.clc_mtd_sales != null ? rCustomer.clc_mtd_sales : '') + '\t';
			sOutput += (rCustomer.clc_mtd_profit != null ? rCustomer.clc_mtd_profit : '') + '\t';
			sOutput += (rCustomer.clc_ytd_profit != null ? rCustomer.clc_ytd_profit : '') + '\t';
			sOutput += (rCustomer.clc_ytd_sales != null ? rCustomer.clc_ytd_sales : '') + '\t';
			sOutput += (rCustomer.cust_over_under_threshold != null ? rCustomer.cust_over_under_threshold * 100 : '') + '\t';
			sOutput += getInvoiceFreightRevenueDefault(rCustomer.default_inv_freight_revenue) + '\t';
			sOutput += (getYesNo(rCustomer.cust_project_required) != null ? getYesNo(rCustomer.cust_project_required) : '') + '\t';
			sOutput += (getYesNo(rCustomer.custaddr_use_default_shipper) != null ? getYesNo(rCustomer.custaddr_use_default_shipper) : '') + '\t';
			sOutput += getDefaultShipperCustCode(rCustomer) + '\t';
			sOutput += getContactNameByCustContactId(rCustomer.custcontact_id_ship) + '\t';
			sOutput += getDefaultShipperAddressCode(rCustomer) + '\t';
			sOutput += (getYesNo(rCustomer.cust_is_fob) != null ? getYesNo(rCustomer.cust_is_fob) : '') + '\t';
			sOutput += getFOBCustomerAndAddress(rCustomer, 'custaddr_code') + '\t';
			sOutput += getFOBCustomerAndAddress(rCustomer, 'cust_code') + '\t';
			sOutput += (rCustomer.default_shipping_markup != null ? rCustomer.default_shipping_markup : '') + '\t';
			
			var oCustPrimaryAddress = getCustomerAddressInfo(rCustomer, recordType);
			sOutput += (oCustPrimaryAddress.addressName != null ? oCustPrimaryAddress.addressName : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone1 != null ? oCustPrimaryAddress.phone1 : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone1ext != null ? oCustPrimaryAddress.phone1ext : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone2 != null ? oCustPrimaryAddress.phone2 : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone2ext != null ? oCustPrimaryAddress.phone2ext : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone3 != null ? oCustPrimaryAddress.phone3 : '') + '\t';
			sOutput += (oCustPrimaryAddress.phone3ext != null ? oCustPrimaryAddress.phone3ext : '') + '\t';
			sOutput += (oCustPrimaryAddress.fax != null ? oCustPrimaryAddress.fax : '') + '\t';
			sOutput += (oCustPrimaryAddress.faxExt != null ? oCustPrimaryAddress.faxExt : '') + '\t';
			sOutput += (oCustPrimaryAddress.shipMethodCode != null ? oCustPrimaryAddress.shipMethodCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.taxGroupCode != null ? oCustPrimaryAddress.taxGroupCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.salesPersonCode != null ? oCustPrimaryAddress.salesPersonCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.territoryCode != null ? oCustPrimaryAddress.territoryCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.addressActive != null ? oCustPrimaryAddress.addressActive : '') + '\t';
			sOutput += (oCustPrimaryAddress.warehouseCode != null ? oCustPrimaryAddress.warehouseCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.contactFirstAndLastName != null ? oCustPrimaryAddress.contactFirstAndLastName : '') + '\t';
			sOutput += (oCustPrimaryAddress.useDefaultShipper != null ? oCustPrimaryAddress.useDefaultShipper : '') + '\t';
			sOutput += (oCustPrimaryAddress.defaultShipperCustCode != null ? oCustPrimaryAddress.defaultShipperCustCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.defaultShipperContactFirstAndLastName != null ? oCustPrimaryAddress.defaultShipperContactFirstAndLastName : '') + '\t';
			sOutput += (oCustPrimaryAddress.defaultShipperAddressCode != null ? oCustPrimaryAddress.defaultShipperAddressCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.addr1 != null ? oCustPrimaryAddress.addr1 : '') + '\t';
			sOutput += (oCustPrimaryAddress.addr2 != null ? oCustPrimaryAddress.addr2 : '') + '\t';
			sOutput += (oCustPrimaryAddress.addr3 != null ? oCustPrimaryAddress.addr3 : '') + '\t';
			sOutput += (oCustPrimaryAddress.city != null ? oCustPrimaryAddress.city : '') + '\t';
			sOutput += (oCustPrimaryAddress.stateProvCode != null ? oCustPrimaryAddress.stateProvCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.zipPostalCode != null ? oCustPrimaryAddress.zipPostalCode : '') + '\t';
			sOutput += (oCustPrimaryAddress.countryCode != null ? oCustPrimaryAddress.countryCode : '') + '\t';
			
		}
		else if (rCustomerAddress && recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses) {
			sOutput += getCustCode(rCustomerAddress, recordType) + '\t';
			
			var oCustAddress = getCustomerAddressInfo(rCustomerAddress, recordType);
			sOutput += (oCustAddress.addressCode != null ? oCustAddress.addressCode : '') + '\t';
			sOutput += (oCustAddress.addressName != null ? oCustAddress.addressName : '') + '\t';
			sOutput += (oCustAddress.phone1 != null ? oCustAddress.phone1 : '') + '\t';
			sOutput += (oCustAddress.phone1ext != null ? oCustAddress.phone1ext : '') + '\t';
			sOutput += (oCustAddress.phone2 != null ? oCustAddress.phone2 : '') + '\t';
			sOutput += (oCustAddress.phone2ext != null ? oCustAddress.phone2ext : '') + '\t';
			sOutput += (oCustAddress.phone3 != null ? oCustAddress.phone3 : '') + '\t';
			sOutput += (oCustAddress.phone3ext != null ? oCustAddress.phone3ext : '') + '\t';
			sOutput += (oCustAddress.fax != null ? oCustAddress.fax : '') + '\t';
			sOutput += (oCustAddress.faxExt != null ? oCustAddress.faxExt : '') + '\t';
			sOutput += (oCustAddress.shipMethodCode != null ? oCustAddress.shipMethodCode : '') + '\t';
			sOutput += (oCustAddress.taxGroupCode != null ? oCustAddress.taxGroupCode : '') + '\t';
			sOutput += (oCustAddress.salesPersonCode != null ? oCustAddress.salesPersonCode : '') + '\t';
			sOutput += (oCustAddress.territoryCode != null ? oCustAddress.territoryCode : '') + '\t';
			sOutput += (oCustAddress.addressActive != null ? oCustAddress.addressActive : '') + '\t';
			sOutput += (oCustAddress.warehouseCode != null ? oCustAddress.warehouseCode : '') + '\t';
			sOutput += (oCustAddress.contactFirstAndLastName != null ? oCustAddress.contactFirstAndLastName : '') + '\t';
			sOutput += (oCustAddress.useDefaultShipper? oCustAddress.useDefaultShipper : '') + '\t';
			sOutput += (oCustAddress.defaultShipperCustCode != null ? oCustAddress.defaultShipperCustCode : '') + '\t';
			sOutput += (oCustAddress.defaultShipperContactFirstAndLastName != null ? oCustAddress.defaultShipperContactFirstAndLastName : '') + '\t';
			sOutput += (oCustAddress.defaultShipperAddressCode != null ? oCustAddress.defaultShipperAddressCode : '') + '\t';
			sOutput += (oCustAddress.addr1 != null ? oCustAddress.addr1 : '') + '\t';
			sOutput += (oCustAddress.addr2 != null ? oCustAddress.addr2 : '') + '\t';
			sOutput += (oCustAddress.addr3 != null ? oCustAddress.addr3 : '') + '\t';
			sOutput += (oCustAddress.city != null ? oCustAddress.city : '') + '\t';
			sOutput += (oCustAddress.stateProvCode != null ? oCustAddress.stateProvCode : '') + '\t';
			sOutput += (oCustAddress.zipPostalCode != null ? oCustAddress.zipPostalCode : '') + '\t';
			sOutput += (oCustAddress.countryCode != null ? oCustAddress.countryCode : '') + '\t';
		}
		else if (rCustomerContact && recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts) {
			sOutput += getCustCode(rCustomerContact, recordType) + '\t';
			sOutput += getCustAddressCode(rCustomerContact, recordType) + '\t';
			
			var oCustContact = getCustomerContactInfo(rCustomerContact, recordType);
			sOutput += (oCustContact.title != null ? oCustContact.title : '') + '\t';
			sOutput += (oCustContact.firstName != null ? oCustContact.firstName : '') + '\t';
			sOutput += (oCustContact.middleName != null ? oCustContact.middleName : '') + '\t';
			sOutput += (oCustContact.lastName != null ? oCustContact.lastName : '') + '\t';
			sOutput += (oCustContact.suffix != null ? oCustContact.suffix : '') + '\t';
			sOutput += (oCustContact.useDefaultAddress != null ? oCustContact.useDefaultAddress : '') + '\t';
			sOutput += (oCustContact.businessPhone != null ? oCustContact.businessPhone : '') + '\t';
			sOutput += (oCustContact.businessPhoneExt != null ? oCustContact.businessPhoneExt : '') + '\t';
			sOutput += (oCustContact.mobilePhone != null ? oCustContact.mobilePhone : '') + '\t';
			sOutput += (oCustContact.homePhone != null ? oCustContact.homePhone : '') + '\t';
			sOutput += (oCustContact.otherPhone != null ? oCustContact.otherPhone : '') + '\t';
			sOutput += (oCustContact.businessFax != null ? oCustContact.businessFax : '') + '\t';
			sOutput += (oCustContact.homeFax != null ? oCustContact.homeFax : '') + '\t';
			sOutput += (oCustContact.businessEmail != null ? oCustContact.businessEmail : '') + '\t';
			sOutput += (oCustContact.homeEmail != null ? oCustContact.homeEmail : '') + '\t';
			sOutput += (oCustContact.otherEmail != null ? oCustContact.otherEmail : '') + '\t';
			sOutput += (oCustContact.jobTitle != null ? oCustContact.jobTitle : '') + '\t';
			sOutput += (oCustContact.contactType != null ? oCustContact.contactType : '') + '\t';
			sOutput += (oCustContact.department != null ? oCustContact.department : '') + '\t';
			sOutput += (oCustContact.division != null ? oCustContact.division : '') + '\t';
			sOutput += (oCustContact.contactActive != null ? oCustContact.contactActive : '') + '\t';
		}
		else if (rCustomerDivisionPlant && recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants) {
			sOutput += getCustCode(rCustomerDivisionPlant, recordType) + '\t';
			sOutput += getDivisionCode(rCustomerDivisionPlant) + '\t';
			sOutput += getPlantCode(rCustomerDivisionPlant) + '\t';
		}
	}
	
	function getCustomerType(custType) {
		var oCustomerTypes = {
			'C': 'Customer',
			'P': 'Prospect',
			'S': 'Suspect'
		}
		if(oCustomerTypes[custType]) {
			return oCustomerTypes[custType];
		}
		return '';
	}
	
	function getCustCategory(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_category)) {
			return rRecord.sa_customer_to_sa_customer_category.custcat_code;
		}
		return '';
	}
	
	function getCustClass(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_class)) {
			return rRecord.sa_customer_to_sa_customer_class.custclass_code;
		}
		return '';
	}
	
	function getTerritory(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_territory)) {
			return rRecord.sa_customer_to_sa_territory.terr_code;
		}
		return '';
	}
	
	function getCustGroup(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_sales_group)) {
			return rRecord.sa_customer_to_sa_customer_sales_group.custgrp_code;
		}
		return '';
	}
	
	function getParentCustomer(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer$parent)) {
			return rRecord.sa_customer_to_sa_customer$parent.cust_code;
		}
		return '';
	}
	
	function getSalesPerson(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_sales_person)) {
			return rRecord.sa_customer_to_sa_sales_person.salesper_code;
		}
		return '';
	}
	
	function getCustomerStatus(custStatus) {
		var oCustomerTypes = {
			'A': 'Active',
			'I': 'Inactive',
			'F': 'Flagged For Deletion'
		}
		if(oCustomerTypes[custStatus]) {
			return oCustomerTypes[custStatus];
		}
		return '';
	}
	
	function getCurrencyCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sys_currency)) {
			return rRecord.sa_customer_to_sys_currency.curr_iso_code;
		}
		return '';
	}
	
	function getPaymentTerms(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sys_payment_terms)) {
			return rRecord.sa_customer_to_sys_payment_terms.terms_code;
		}
		return '';
	}
	
	function getCustSIC(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_sic)) {
			return rRecord.sa_customer_to_sa_customer_sic.custsic_code;
		}
		return '';
	}
	
	function getShipMethod(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sys_shipping_method)) {
			return rRecord.sa_customer_to_sys_shipping_method.shipmethod_code;
		}
		return '';
	}
	
	function getPaymentMethod(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_payment_method)) {
			return rRecord.sa_customer_to_sa_payment_method.paymethod_code;
		}
		return '';
	}
	
	function getIndustry(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_industry)) {
			return rRecord.sa_customer_to_sa_customer_industry.custindustry_code;
		}
		return '';
	}
	
	function getTaxGroup(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sys_sales_tax_group)) {
			return rRecord.sa_customer_to_sys_sales_tax_group.taxgroup_code;
		}
		return '';
	}
	
	function getEmployeeCode(emplId) {
		if (!emplId) {
			return '';
		}
		var sSQL = "SELECT empl_id \
					  FROM sys_employee \
					 WHERE org_id = ?";
		// If it's a uniqueidentifier then search against empl_id else search user_id because cust_csr_empl_id in sa_customer contains both user_id integer and UUID.
		if (emplId.toString().length == 36) {
			sSQL += " AND empl_id = ?";
		}
		else {
			sSQL += " AND user_id = ?";
		}
		/**@type {JSRecord<db:/avanti/sys_employee>} */
		var rEmployee = scopes.avDB.getRecFromSQL(sSQL,'sys_employee',[globals.org_id.toString(), emplId.toString()]);
		if (rEmployee && rEmployee.empl_code) {
			return rEmployee.empl_code;
		}
		return '';
	}
	
	function getShipToAddressCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_address_shipto)) {
			return rRecord.sa_customer_to_sa_customer_address_shipto.custaddr_code;
		}
		return '';
	}
	
	function getBillToAddressCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_address_billto)) {
			return rRecord.sa_customer_to_sa_customer_address_billto.custaddr_code;
		}
		return '';
	}
	
	function getWarehouseCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_in_warehouse)) {
			return rRecord.sa_customer_to_in_warehouse.whse_code;
		}
		return '';
	}
	
	function getContactNameByCustContactId(custContactId) {
		if (!custContactId) {
			return '';
		}
		var sSQL = "SELECT contact_id \
					  FROM sys_contact \
					 WHERE org_id = ? \
					   AND custcontact_id = ?";
		/**@type {JSRecord<db:/avanti/sys_contact>} */
		var rContact = scopes.avDB.getRecFromSQL(sSQL,'sys_employee',[globals.org_id.toString(), custContactId.toString()]);
		if (rContact && rContact.contact_first_and_last) {
			return rContact.contact_first_and_last;
		}
		return '';
	}
	
	function getInvoicePrintDefault(custInvoiceDetailLevel) {
		var oInvoicePrintDefault = {
			'O': 'Sales Order',
			'L': 'Sales Order Line'
		}
		if(oInvoicePrintDefault[custInvoiceDetailLevel]) {
			return oInvoicePrintDefault[custInvoiceDetailLevel];
		}
		return '';
	}
	
	function getInvoiceFreightRevenueDefault(defaultInvFreightRevenue) {
		var oDefaultInvFreightRevenue = {
			1: 'Negotiated Carrier Rate',
			2: 'Published Carrier Rate',
			3: 'Sales Order Shipping Cost'
		}
		if(oDefaultInvFreightRevenue[defaultInvFreightRevenue]) {
			return oDefaultInvFreightRevenue[defaultInvFreightRevenue];
		}
		return '';
	}
	
	function getDefaultShipperCustCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer$cust_id_ship)) {
			return rRecord.sa_customer_to_sa_customer$cust_id_ship.cust_code;
		}
		return '';
	}
	
	function getDefaultShipperAddressCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_customer_to_sa_customer_address$custaddr_id_ship)) {
			return rRecord.sa_customer_to_sa_customer_address$custaddr_id_ship.custaddr_code;
		}
		return '';
	}
	
	function getFOBCustomerAndAddress(rRecord, fieldName) {
		if (!fieldName || !rRecord || !rRecord.cust_fob_address_id) {
			return '';
		}
		
		var sSQL = "SELECT " + fieldName + " \
					  FROM sa_customer_address CA \
				INNER JOIN sa_customer C ON CA.cust_id = C.cust_id \
					 WHERE CA.org_id = ? \
					   AND CA.custaddr_id = ?";

		var result = scopes.avDB.SQLQuery(sSQL,null,[globals.org_id.toString(), rRecord.cust_fob_address_id.toString()]);
		if (result) {
			return result;
		}
		return '';
	}
	
	function getCustomerAddressInfo(rRecord, recordType) {
		var returnObj = {
			addressCode: null,
			addressName: null,
			phone1: null,
			phone1ext: null,
			phone2: null,
			phone2ext: null,
			phone3: null,
			phone3ext: null,
			fax: null,
			faxExt: null,
			shipMethodCode: null,
			taxGroupCode: null,
			salesPersonCode: null,
			territoryCode: null,
			addressActive: null,
			warehouseCode: null,
			contactFirstAndLastName: null,
			useDefaultShipper: null,
			defaultShipperCustCode: null,
			defaultShipperContactFirstAndLastName: null,
			defaultShipperAddressCode: null,
			addr1: null,
			addr2: null,
			addr3: null,
			city: null,
			stateProvCode: null,
			zipPostalCode: null,
			countryCode: null
		};
		
		var contactId;
		var addrId;
		
		if (!recordType || !rRecord) {
			return returnObj;
		}
		/**@type {JSRecord<db:/avanti/sa_customer_address>} */
		var rCustAddress;
		
		if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Customers && utils.hasRecords(rRecord.sa_customer_to_sa_customer_address_primary)) {
			rCustAddress = rRecord.sa_customer_to_sa_customer_address_primary;
		} 
		else if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses) {
			rCustAddress = rRecord;
		} 
		else {
			return returnObj;
		}

		returnObj.addressCode = rCustAddress.custaddr_code;
		returnObj.addressName = rCustAddress.custaddr_address_name;
		returnObj.phone1 = rCustAddress.custaddr_phone1;
		returnObj.phone1ext = rCustAddress.custaddr_phone1_ext;
		returnObj.phone2 = rCustAddress.custaddr_phone2;
		returnObj.phone2ext = rCustAddress.custaddr_phone2_ext;
		returnObj.phone3 = rCustAddress.custaddr_phone3;
		returnObj.phone3ext = rCustAddress.custaddr_phone1_ext;
		returnObj.fax = rCustAddress.custaddr_fax;
		returnObj.faxExt = rCustAddress.custaddr_fax_ext;

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sys_shipping_method)) {
			returnObj.shipMethodCode = rCustAddress.sa_customer_address_to_sys_shipping_method.shipmethod_code;
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sys_sales_tax_group)) {
			returnObj.taxGroupCode = rCustAddress.sa_customer_address_to_sys_sales_tax_group.taxgroup_code;
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_sales_person)) {
			returnObj.salesPersonCode = rCustAddress.sa_customer_address_to_sa_sales_person.salesper_code;
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_territory)) {
			returnObj.territoryCode = rCustAddress.sa_customer_address_to_sa_territory.terr_code;
		}

		if (getYesNo(rCustAddress.custaddr_active)) {
			returnObj.addressActive = getYesNo(rCustAddress.custaddr_active);
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_in_warehouse)) {
			returnObj.warehouseCode = rCustAddress.sa_customer_address_to_in_warehouse.whse_code;
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer_contact)) {
			contactId = rCustAddress.sa_customer_address_to_sa_customer_contact.contact_id.toString();
			if (contactId) {
				if (contactFirstAndLastNameCache.hasOwnProperty(contactId)) {
					returnObj.contactFirstAndLastName = contactFirstAndLastNameCache[contactId];
				} 
				else if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
					returnObj.contactFirstAndLastName = rCustAddress.sa_customer_address_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
					contactFirstAndLastNameCache[contactId] = returnObj.contactFirstAndLastName;
				}
				else {
					contactFirstAndLastNameCache[contactId] = null;
				}
			}
		}

		if (getYesNo(rCustAddress.custaddr_use_default_shipper)) {
			returnObj.useDefaultShipper = getYesNo(rCustAddress.custaddr_use_default_shipper);
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer$cust_id_ship)) {
			returnObj.defaultShipperCustCode = rCustAddress.sa_customer_address_to_sa_customer$cust_id_ship.cust_code;
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer_contact$custcontact_id_ship)) {
			contactId = rCustAddress.sa_customer_address_to_sa_customer_contact$custcontact_id_ship.contact_id.toString();
			if (contactId) {
				if (contactFirstAndLastNameCache.hasOwnProperty(contactId)) {
					returnObj.defaultShipperContactFirstAndLastName = contactFirstAndLastNameCache[contactId];
				} 
				else if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer_contact$custcontact_id_ship.sa_customer_contact_to_sys_contact)) {
					returnObj.defaultShipperContactFirstAndLastName = rCustAddress.sa_customer_address_to_sa_customer_contact$custcontact_id_ship.sa_customer_contact_to_sys_contact.contact_first_and_last;
					contactFirstAndLastNameCache[contactId] = returnObj.defaultShipperContactFirstAndLastName;
				}
				else {
					contactFirstAndLastNameCache[contactId] = null;
				}
			}
		}

		if (utils.hasRecords(rCustAddress.sa_customer_address_to_sa_customer_address$custaddr_id_ship)) {
			returnObj.defaultShipperAddressCode = rCustAddress.sa_customer_address_to_sa_customer_address$custaddr_id_ship.custaddr_code;
		}

		addrId = rCustAddress.addr_id != null ? rCustAddress.addr_id.toString() : null;
		if (addrId) {
			if (addressCache.hasOwnProperty(addrId)) {
				returnObj.addr1 = addressCache[addrId] ? addressCache[addrId].addr1 : null;
				returnObj.addr2 = addressCache[addrId] ? addressCache[addrId].addr2 : null;
				returnObj.addr3 = addressCache[addrId] ? addressCache[addrId].addr3 : null;
				returnObj.city = addressCache[addrId] ? addressCache[addrId].city : null;
				returnObj.zipPostalCode = addressCache[addrId] ? addressCache[addrId].zipPostalCode : null;
				returnObj.stateProvCode = addressCache[addrId] ? addressCache[addrId].stateProvCode : null;
				returnObj.countryCode = addressCache[addrId] ? addressCache[addrId].countryCode : null;

			} else if (utils.hasRecords(rCustAddress.sa_customer_address_to_sys_address)) {
				returnObj.addr1 = rCustAddress.sa_customer_address_to_sys_address.addr_address1;
				returnObj.addr2 = rCustAddress.sa_customer_address_to_sys_address.addr_address2;
				returnObj.addr3 = rCustAddress.sa_customer_address_to_sys_address.addr_address3;
				returnObj.city = rCustAddress.sa_customer_address_to_sys_address.addr_city;
				returnObj.zipPostalCode = rCustAddress.sa_customer_address_to_sys_address.addr_postal;
				if (utils.hasRecords(rCustAddress.sa_customer_address_to_sys_address.sys_address_to_sys_state_province)) {
					returnObj.stateProvCode = rCustAddress.sa_customer_address_to_sys_address.sys_address_to_sys_state_province.stateprov_code;
				}

				if (utils.hasRecords(rCustAddress.sa_customer_address_to_sys_address.sys_address_to_sys_country)) {
					returnObj.countryCode = rCustAddress.sa_customer_address_to_sys_address.sys_address_to_sys_country.country_code;
				}
				
				addressCache[addrId] = {
					addr1: returnObj.addr1,
					addr2: returnObj.addr2,
					addr3: returnObj.addr3,
					city: returnObj.city,
					zipPostalCode: returnObj.zipPostalCode,
					stateProvCode: returnObj.stateProvCode,
					countryCode: returnObj.countryCode
				};
			}
			else {
				addressCache[addrId] = {
					addr1: null,
					addr2: null,
					addr3: null,
					city: null,
					zipPostalCode: null,
					stateProvCode: null,
					countryCode: null
				};
			}
		}
		
		var tempKeys = Object.keys(returnObj);
		for (var j = 0; j < tempKeys.length; j++) {
			if (returnObj[tempKeys[j]]) {
				returnObj[tempKeys[j]] = returnObj[tempKeys[j]].replace(/[\t\n\r]/gm,' ');
			}
		}
		
		return returnObj;
	}
	
	function getCustCode(rRecord, recordType) {
		var sSQL;
		if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Addresses && utils.hasRecords(rRecord.sa_customer_address_to_sa_customer)) {
			return rRecord.sa_customer_address_to_sa_customer.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts && utils.hasRecords(rRecord.sa_customer_contact_to_sa_customer)) {
			return rRecord.sa_customer_contact_to_sa_customer.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.DivisionsPlants) {
			sSQL = "SELECT C.cust_code \
			  		  FROM sa_division_plant dp \
			  	INNER JOIN sa_customer C ON dp.object_id = C.cust_id \
			  		 WHERE dp.org_id = ? \
			  		   AND dp.object_id = ? \
			  		   AND dp.object_type = 'CUST'";

			var res = scopes.avDB.SQLQuery(sSQL,null,[globals.org_id.toString(), rRecord.object_id.toString()]);
			if (res) {
				return res;
			}
		}
		return '';
	}
	
	function getCustAddressCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts && utils.hasRecords(rRecord.sa_customer_contact_to_sa_customer_address)) {
			return rRecord.sa_customer_contact_to_sa_customer_address.custaddr_code;
		}
		return '';
	}
	
	function getCustomerContactInfo(rRecord, recordType) {
		var returnObj = {
			title: null,
			firstName: null,
			middleName: null,
			lastName: null,
			suffix: null,
			useDefaultAddress: null,
			businessPhone: null,
			businessPhoneExt: null,
			mobilePhone: null,
			homePhone: null,
			otherPhone: null,
			businessFax: null,
			homeFax: null,
			businessEmail: null,
			homeEmail: null,
			otherEmail: null,
			jobTitle: null,
			contactType: null,
			department: null,
			division: null,
			contactActive: null
		};
		
		if (!recordType || !rRecord) {
			return returnObj;
		}
		/**@type {JSRecord<db:/avanti/sys_contact>} */
		var rContact;
		
		if (recordType == scopes.avDataExport.ENUM_CUSTOMER_SUBTYPES.Contacts && utils.hasRecords(rRecord.sa_customer_contact_to_sys_contact)) {
			rContact = rRecord.sa_customer_contact_to_sys_contact;
		}
		else {
			return returnObj;
		}
		
		var contactId = rContact.contact_id.toString();
		
		if (contactCache.hasOwnProperty(contactId)) {
			returnObj = contactCache[contactId];
		}
		else {
			var contactJobTitle = null;
			var contactType = null;
			var contactDept = null;
			var contactDivision = null;
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_job_title)) {
				contactJobTitle = rContact.sys_contact_to_sys_contact_job_title.jobtitle_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_type)) {
				contactType = rContact.sys_contact_to_sys_contact_type.contacttype_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_department)) {
				contactDept = rContact.sys_contact_to_sys_contact_department.contactdept_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_division)) {
				contactDivision = rContact.sys_contact_to_sys_contact_division.contactdiv_desc;
			}
			
			returnObj = {
				title: rContact.contact_title,
				firstName: rContact.contact_first_name,
				middleName: rContact.contact_middle_name,
				lastName: rContact.contact_last_name,
				suffix: rContact.contact_suffix,
				useDefaultAddress: getYesNo(rContact.contact_use_default_address),
				businessPhone: rContact.contact_business_phone,
				businessPhoneExt: rContact.contact_business_ext,
				mobilePhone: rContact.contact_mobile_phone,
				homePhone: rContact.contact_home_phone,
				otherPhone: rContact.contact_other_phone,
				businessFax: rContact.contact_business_fax,
				homeFax: rContact.contact_home_fax,
				businessEmail: rContact.contact_business_email,
				homeEmail: rContact.contact_home_email,
				otherEmail: rContact.contact_other_email,
				jobTitle: contactJobTitle,
				contactType: contactType,
				department: contactDept,
				division: contactDivision,
				contactActive: getYesNo(rContact.contact_active)
			};
			
			contactCache[contactId] = returnObj;
		}
		
		var tempKeys = Object.keys(returnObj);
		for (var j = 0; j < tempKeys.length; j++) {
			if (returnObj[tempKeys[j]]) {
				returnObj[tempKeys[j]] = returnObj[tempKeys[j]].replace(/[\t\n\r]/gm,' ');
			}
		}
		
		return returnObj;
	}
	
	function getDivisionCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_division_plant_to_sys_division)) {
			return rRecord.sa_division_plant_to_sys_division.div_code;
		}
		return '';
	}
	
	function getPlantCode(rRecord) {
		if (utils.hasRecords(rRecord.sa_division_plant_to_sys_plant)) {
			return rRecord.sa_division_plant_to_sys_plant.plant_code;
		}
		return '';
	}
}


/**
 * Export supplier related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"69460733-2E56-4734-BAED-0046991F5672"}
 */
function SupplierExport(rFSRecord, subType) {
	var sOutput = null;
	var sSuppCode = null;
	
	/**@type {JSRecord<db:/avanti/ap_supplier>} */
	var rSupplier;
	/**@type {JSRecord<db:/avanti/ap_supplier_address>} */
	var rSupplierAddress;
	/**@type {JSRecord<db:/avanti/ap_supplier_contact>} */
	var rSupplierContact;
	
	if (rFSRecord && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers) {
		rSupplier = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses) {
		rSupplierAddress = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts) {
		rSupplierContact = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		
		if (rSupplier && recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Suppliers) {
			sOutput += (rSupplier.supplier_code != null ? rSupplier.supplier_code : '') + '\t';
			sOutput += (rSupplier.supplier_name != null ? rSupplier.supplier_name : '') + '\t';
			sOutput += (getYesNo(rSupplier.supplier_active) != null ? getYesNo(rSupplier.supplier_active) : '') + '\t';
			sOutput += getCurrencyCode(rSupplier) + '\t';
			sOutput += (rSupplier.supplier_website != null ? rSupplier.supplier_website : '') + '\t';
			sOutput += scopes.avDate.formatDate(rSupplier.supplier_since,'YYYYMMDD') + '\t';
			sOutput += getEmployeeCode(rSupplier) + '\t';
			sOutput += (rSupplier.supplier_minimim_po_value != null ? rSupplier.supplier_minimim_po_value : '') + '\t';
			sOutput += getShippingMethodCode(rSupplier) + '\t';
			sOutput += (rSupplier.supplier_our_acct_number != null ? rSupplier.supplier_our_acct_number : '') + '\t';
			sOutput += (getYesNo(rSupplier.supplier_accept_bo) != null ? getYesNo(rSupplier.supplier_accept_bo) : '') + '\t';
			sOutput += (getYesNo(rSupplier.supplier_confirmation_req) != null ? getYesNo(rSupplier.supplier_confirmation_req) : '') + '\t';
			sOutput += getFreightBillMethod(rSupplier.supplier_freight_bill_method) + '\t';
			sOutput += getFreightCostMethod(rSupplier.supplier_freight_cost_method) + '\t';
			sOutput += getPaymentTermsCode(rSupplier) + '\t';
			sOutput += (rSupplier.supplier_default_leadtime != null ? rSupplier.supplier_default_leadtime : '') + '\t';
			sOutput += getPrimaryAddressCode(rSupplier) + '\t';
			sOutput += getMatchingMethod(rSupplier.supplier_matching_method) + '\t';
		}
		else if (rSupplierAddress && recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses) {
			sSuppCode = getSuppCode(rSupplierAddress, recordType);
			if (!sSuppCode || sSuppCode == '') {
				sOutput = null;
				return; 
			}
			sOutput += sSuppCode + '\t';
			
			var oSuppAddress = getSupplierAddressInfo(rSupplierAddress, recordType);
			sOutput += (oSuppAddress.addressCode != null ? oSuppAddress.addressCode : '') + '\t';
			sOutput += (oSuppAddress.addressName != null ? oSuppAddress.addressName : '') + '\t';
			sOutput += (oSuppAddress.phone1 != null ? oSuppAddress.phone1 : '') + '\t';
			sOutput += (oSuppAddress.phone1ext != null ? oSuppAddress.phone1ext : '') + '\t';
			sOutput += (oSuppAddress.phone2 != null ? oSuppAddress.phone2 : '') + '\t';
			sOutput += (oSuppAddress.phone2ext != null ? oSuppAddress.phone2ext : '') + '\t';
			sOutput += (oSuppAddress.phone3 != null ? oSuppAddress.phone3 : '') + '\t';
			sOutput += (oSuppAddress.phone3ext != null ? oSuppAddress.phone3ext : '') + '\t';
			sOutput += (oSuppAddress.fax != null ? oSuppAddress.fax : '') + '\t';
			sOutput += (oSuppAddress.faxExt != null ? oSuppAddress.faxExt : '') + '\t';
			sOutput += (oSuppAddress.supplierContact != null ? oSuppAddress.supplierContact : '') + '\t';
			sOutput += (oSuppAddress.addressActive != null ? oSuppAddress.addressActive : '') + '\t';
			sOutput += (oSuppAddress.addr1 != null ? oSuppAddress.addr1 : '') + '\t';
			sOutput += (oSuppAddress.addr2 != null ? oSuppAddress.addr2 : '') + '\t';
			sOutput += (oSuppAddress.addr3 != null ? oSuppAddress.addr3 : '') + '\t';
			sOutput += (oSuppAddress.city != null ? oSuppAddress.city : '') + '\t';
			sOutput += (oSuppAddress.stateProvCode != null ? oSuppAddress.stateProvCode : '') + '\t';
			sOutput += (oSuppAddress.zipPostalCode != null ? oSuppAddress.zipPostalCode : '') + '\t';
			sOutput += (oSuppAddress.countryCode != null ? oSuppAddress.countryCode : '') + '\t';
		}
		else if (rSupplierContact && recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts) {
			sSuppCode = getSuppCode(rSupplierContact, recordType);
			if (!sSuppCode || sSuppCode == '') {
				sOutput = null;
				return; 
			}
			sOutput += getSuppAddressCode(rSupplierContact, recordType) + '\t';
			
			var oSuppContact = getSupplierContactInfo(rSupplierContact, recordType);
			sOutput += (oSuppContact.title != null ? oSuppContact.title : '') + '\t';
			sOutput += (oSuppContact.firstName != null ? oSuppContact.firstName : '') + '\t';
			sOutput += (oSuppContact.middleName != null ? oSuppContact.middleName : '') + '\t';
			sOutput += (oSuppContact.lastName != null ? oSuppContact.lastName : '') + '\t';
			sOutput += (oSuppContact.suffix != null ? oSuppContact.suffix : '') + '\t';
			sOutput += (oSuppContact.useDefaultAddress != null ? oSuppContact.useDefaultAddress : '') + '\t';
			sOutput += (oSuppContact.businessPhone != null ? oSuppContact.businessPhone : '') + '\t';
			sOutput += (oSuppContact.businessPhoneExt != null ? oSuppContact.businessPhoneExt : '') + '\t';
			sOutput += (oSuppContact.mobilePhone != null ? oSuppContact.mobilePhone : '') + '\t';
			sOutput += (oSuppContact.homePhone != null ? oSuppContact.homePhone : '') + '\t';
			sOutput += (oSuppContact.otherPhone != null ? oSuppContact.otherPhone : '') + '\t';
			sOutput += (oSuppContact.businessFax != null ? oSuppContact.businessFax : '') + '\t';
			sOutput += (oSuppContact.homeFax != null ? oSuppContact.homeFax : '') + '\t';
			sOutput += (oSuppContact.businessEmail != null ? oSuppContact.businessEmail : '') + '\t';
			sOutput += (oSuppContact.homeEmail != null ? oSuppContact.homeEmail : '') + '\t';
			sOutput += (oSuppContact.otherEmail != null ? oSuppContact.otherEmail : '') + '\t';
			sOutput += (oSuppContact.jobTitle != null ? oSuppContact.jobTitle : '') + '\t';
			sOutput += (oSuppContact.contactType != null ? oSuppContact.contactType : '') + '\t';
			sOutput += (oSuppContact.department != null ? oSuppContact.department : '') + '\t';
			sOutput += (oSuppContact.division != null ? oSuppContact.division : '') + '\t';
			sOutput += (oSuppContact.contactActive != null ? oSuppContact.contactActive : '') + '\t';
		}
	}
	
	function getCurrencyCode(rRecord) {
		if (utils.hasRecords(rRecord.ap_supplier_to_sys_currency)) {
			return rRecord.ap_supplier_to_sys_currency.curr_iso_code;
		}
		return '';
	}
	
	function getEmployeeCode(rRecord) {
		if (utils.hasRecords(rRecord.ap_supplier_to_sys_employee)) {
			return rRecord.ap_supplier_to_sys_employee.empl_code;
		}
		return '';
	}
	
	function getShippingMethodCode(rRecord) {
		if (utils.hasRecords(rRecord.ap_supplier_to_sys_shipping_method)) {
			return rRecord.ap_supplier_to_sys_shipping_method.shipmethod_code;
		}
		return '';
	}
	
	function getFreightBillMethod(freightBillMethod) {
		var oFreightBillMethod = {
			'S': 'Supplier',
			'O': 'Other'
		}
		if(oFreightBillMethod[freightBillMethod]) {
			return oFreightBillMethod[freightBillMethod];
		}
		return '';
	}
	
	function getFreightCostMethod(freightCostMethod) {
		var oFreightCostMethod = {
			'L': 'Landed',
			'E': 'Expensed'
		}
		if(oFreightCostMethod[freightCostMethod]) {
			return oFreightCostMethod[freightCostMethod];
		}
		return '';
	}
	
	function getPaymentTermsCode(rRecord) {
		if (utils.hasRecords(rRecord.ap_supplier_to_sys_payment_terms)) {
			return rRecord.ap_supplier_to_sys_payment_terms.terms_code;
		}
		return '';
	}
	
	function getPrimaryAddressCode(rRecord) {
		if (utils.hasRecords(rRecord.ap_supplier_to_ap_supplier_address$primary)) {
			return rRecord.ap_supplier_to_ap_supplier_address$primary.suppaddr_code;
		}
		return '';
	}
	
	function getMatchingMethod(matchingMethod) {
		var oMatchingMethod = {
			0: 'None',
			3: '3 Ways'
		}
		if(oMatchingMethod[matchingMethod]) {
			return oMatchingMethod[matchingMethod];
		}
		return '';
	}
	
	function getSuppCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses && utils.hasRecords(rRecord.ap_supplier_address_to_ap_supplier)) {
			return rRecord.ap_supplier_address_to_ap_supplier.supplier_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts && utils.hasRecords(rRecord.ap_supplier_contact_to_ap_supplier)) {
			return rRecord.ap_supplier_contact_to_ap_supplier.supplier_code;
		}
		return '';
	}
	
	function getSuppAddressCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts && utils.hasRecords(rRecord.ap_supplier_contact_to_ap_supplier_address)) {
			return rRecord.ap_supplier_contact_to_ap_supplier_address.suppaddr_code;
		}
		return '';
	}
	
	function getSupplierAddressInfo(rRecord, recordType) {
		var returnObj = {
			addressCode: null,
			addressName: null,
			phone1: null,
			phone1ext: null,
			phone2: null,
			phone2ext: null,
			phone3: null,
			phone3ext: null,
			fax: null,
			faxExt: null,
			supplierContact: null,
			addressActive: null,
			addr1: null,
			addr2: null,
			addr3: null,
			city: null,
			stateProvCode: null,
			zipPostalCode: null,
			countryCode: null
		};
		
		var contactId;
		var addrId;
		
		if (!recordType || !rRecord) {
			return returnObj;
		}
		/**@type {JSRecord<db:/avanti/ap_supplier_address>} */
		var rSuppAddress;
		
		if (rRecord && recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Addresses) {
			rSuppAddress = rRecord;
		} 
		else {
			return returnObj;
		}

		returnObj.addressCode = rSuppAddress.suppaddr_code;
		returnObj.addressName = rSuppAddress.suppaddr_address_name;
		returnObj.phone1 = rSuppAddress.suppaddr_phone1;
		returnObj.phone1ext = rSuppAddress.suppaddr_phone1_ext;
		returnObj.phone2 = rSuppAddress.suppaddr_phone2;
		returnObj.phone2ext = rSuppAddress.suppaddr_phone2_ext;
		returnObj.phone3 = rSuppAddress.suppaddr_phone3;
		returnObj.phone3ext = rSuppAddress.suppaddr_phone1_ext;
		returnObj.fax = rSuppAddress.suppaddr_fax;
		returnObj.faxExt = rSuppAddress.suppaddr_fax_ext;

		if (utils.hasRecords(rSuppAddress.ap_supplier_address_to_ap_supplier_contact)) {
			contactId = rSuppAddress.ap_supplier_address_to_ap_supplier_contact.contact_id.toString();
			if (contactId) {
				if (contactFirstAndLastNameCache.hasOwnProperty(contactId)) {
					returnObj.supplierContact = contactFirstAndLastNameCache[contactId];
				} 
				else if (utils.hasRecords(rSuppAddress.ap_supplier_address_to_ap_supplier_contact.ap_supplier_contact_to_sys_contact)) {
					returnObj.supplierContact = rSuppAddress.ap_supplier_address_to_ap_supplier_contact.ap_supplier_contact_to_sys_contact.contact_first_and_last;
					contactFirstAndLastNameCache[contactId] = returnObj.supplierContact;
				}
				else {
					contactFirstAndLastNameCache[contactId] = null;
				}
			}
		}
		
		if (getYesNo(rSuppAddress.suppaddr_active)) {
			returnObj.addressActive = getYesNo(rSuppAddress.suppaddr_active);
		}

		addrId = rSuppAddress.addr_id != null ? rSuppAddress.addr_id.toString() : null;
		if (addrId) {
			if (addressCache.hasOwnProperty(addrId)) {
				returnObj.addr1 = addressCache[addrId] ? addressCache[addrId].addr1 : null;
				returnObj.addr2 = addressCache[addrId] ? addressCache[addrId].addr2 : null;
				returnObj.addr3 = addressCache[addrId] ? addressCache[addrId].addr3 : null;
				returnObj.city = addressCache[addrId] ? addressCache[addrId].city : null;
				returnObj.zipPostalCode = addressCache[addrId] ? addressCache[addrId].zipPostalCode : null;
				returnObj.stateProvCode = addressCache[addrId] ? addressCache[addrId].stateProvCode : null;
				returnObj.countryCode = addressCache[addrId] ? addressCache[addrId].countryCode : null;

			} else if (utils.hasRecords(rSuppAddress.ap_supplier_address_to_sys_address)) {
				returnObj.addr1 = rSuppAddress.ap_supplier_address_to_sys_address.addr_address1;
				returnObj.addr2 = rSuppAddress.ap_supplier_address_to_sys_address.addr_address2;
				returnObj.addr3 = rSuppAddress.ap_supplier_address_to_sys_address.addr_address3;
				returnObj.city = rSuppAddress.ap_supplier_address_to_sys_address.addr_city;
				returnObj.zipPostalCode = rSuppAddress.ap_supplier_address_to_sys_address.addr_postal;
				if (utils.hasRecords(rSuppAddress.ap_supplier_address_to_sys_address.sys_address_to_sys_state_province)) {
					returnObj.stateProvCode = rSuppAddress.ap_supplier_address_to_sys_address.sys_address_to_sys_state_province.stateprov_code;
				}

				if (utils.hasRecords(rSuppAddress.ap_supplier_address_to_sys_address.sys_address_to_sys_country)) {
					returnObj.countryCode = rSuppAddress.ap_supplier_address_to_sys_address.sys_address_to_sys_country.country_code;
				}
				
				addressCache[addrId] = {
					addr1: returnObj.addr1,
					addr2: returnObj.addr2,
					addr3: returnObj.addr3,
					city: returnObj.city,
					zipPostalCode: returnObj.zipPostalCode,
					stateProvCode: returnObj.stateProvCode,
					countryCode: returnObj.countryCode
				};
			}
			else {
				addressCache[addrId] = {
					addr1: null,
					addr2: null,
					addr3: null,
					city: null,
					zipPostalCode: null,
					stateProvCode: null,
					countryCode: null
				};
			}
		}
		
		var tempKeys = Object.keys(returnObj);
		for (var j = 0; j < tempKeys.length; j++) {
			if (returnObj[tempKeys[j]]) {
				returnObj[tempKeys[j]] = returnObj[tempKeys[j]].replace(/[\t\n\r]/gm,' ');
			}
		}
		
		return returnObj;
	}
	
	function getSupplierContactInfo(rRecord, recordType) {
		var returnObj = {
			title: null,
			firstName: null,
			middleName: null,
			lastName: null,
			suffix: null,
			useDefaultAddress: null,
			businessPhone: null,
			businessPhoneExt: null,
			mobilePhone: null,
			homePhone: null,
			otherPhone: null,
			businessFax: null,
			homeFax: null,
			businessEmail: null,
			homeEmail: null,
			otherEmail: null,
			jobTitle: null,
			contactType: null,
			department: null,
			division: null,
			contactActive: null
		};
		
		if (!recordType || !rRecord) {
			return returnObj;
		}
		/**@type {JSRecord<db:/avanti/sys_contact>} */
		var rContact;
		
		if (recordType == scopes.avDataExport.ENUM_SUPPLIER_SUBTYPES.Contacts && utils.hasRecords(rRecord.ap_supplier_contact_to_sys_contact)) {
			rContact = rRecord.ap_supplier_contact_to_sys_contact;
		}
		else {
			return returnObj;
		}
		
		var contactId = rContact.contact_id.toString();
		
		if (contactCache.hasOwnProperty(contactId)) {
			returnObj = contactCache[contactId];
		}
		else {
			var contactJobTitle = null;
			var contactType = null;
			var contactDept = null;
			var contactDivision = null;
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_job_title)) {
				contactJobTitle = rContact.sys_contact_to_sys_contact_job_title.jobtitle_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_type)) {
				contactType = rContact.sys_contact_to_sys_contact_type.contacttype_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_department)) {
				contactDept = rContact.sys_contact_to_sys_contact_department.contactdept_desc;
			}
			
			if (utils.hasRecords(rContact.sys_contact_to_sys_contact_division)) {
				contactDivision = rContact.sys_contact_to_sys_contact_division.contactdiv_desc;
			}
			
			returnObj = {
				title: rContact.contact_title,
				firstName: rContact.contact_first_name,
				middleName: rContact.contact_middle_name,
				lastName: rContact.contact_last_name,
				suffix: rContact.contact_suffix,
				useDefaultAddress: getYesNo(rContact.contact_use_default_address),
				businessPhone: rContact.contact_business_phone,
				businessPhoneExt: rContact.contact_business_ext,
				mobilePhone: rContact.contact_mobile_phone,
				homePhone: rContact.contact_home_phone,
				otherPhone: rContact.contact_other_phone,
				businessFax: rContact.contact_business_fax,
				homeFax: rContact.contact_home_fax,
				businessEmail: rContact.contact_business_email,
				homeEmail: rContact.contact_home_email,
				otherEmail: rContact.contact_other_email,
				jobTitle: contactJobTitle,
				contactType: contactType,
				department: contactDept,
				division: contactDivision,
				contactActive: getYesNo(rContact.contact_active)
			};
			
			contactCache[contactId] = returnObj;
		}
		
		var tempKeys = Object.keys(returnObj);
		for (var j = 0; j < tempKeys.length; j++) {
			if (returnObj[tempKeys[j]]) {
				returnObj[tempKeys[j]] = returnObj[tempKeys[j]].replace(/[\t\n\r]/gm,' ');
			}
		}
		
		return returnObj;
	}
	
}



/**
 * Export cost center related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"142E8150-A557-4F8B-A521-679EEBF59762"}
 */
function CostCenterExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sys_department>} */
	var rDepartment;
	
	/**@type {JSRecord<db:/avanti/sys_operation_category>} */
	var rCategory;
	
	/**@type {JSRecord<db:/avanti/sys_cost_centre>} */
	var rOperation;

	
	if (rFSRecord && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments) {
		rDepartment = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories) {
		rCategory = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations) {
		rOperation = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		
		if (rDepartment && recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments) {
			if (!rDepartment.dept_code || rDepartment.dept_code == '') {
				sOutput = null;
				return;
			}
			sOutput += (rDepartment.dept_code != null ? rDepartment.dept_code : '') + '\t';
			sOutput += getPlantCode(rDepartment, recordType) + '\t';
			sOutput += (rDepartment.dept_desc != null ? rDepartment.dept_desc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rDepartment.dept_shortdesc != null ? rDepartment.dept_shortdesc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (getYesNo(rDepartment.dept_schedule_flag) != null ? getYesNo(rDepartment.dept_schedule_flag) : '') + '\t';
			sOutput += (getYesNo(rDepartment.dept_active) != null ? getYesNo(rDepartment.dept_active) : '') + '\t';
			sOutput += getShiftCode(rDepartment) + '\t';
		}
		else if (rCategory && recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories) {
			sOutput += getDepartmentCode(rCategory, recordType) + '\t';
			sOutput += (rCategory.opcat_code != null ? rCategory.opcat_code : '') + '\t';
			sOutput += (rCategory.opcat_desc != null ? rCategory.opcat_desc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rCategory.opcat_shortdesc != null ? rCategory.opcat_shortdesc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (getYesNo(rCategory.opcat_active) != null ? getYesNo(rCategory.opcat_active) : '') + '\t';
			sOutput += getGLCostOfSales(rCategory) + '\t';
			sOutput += getGLSales(rCategory) + '\t';
			sOutput += (rCategory.opcat_external_device_id != null ? rCategory.opcat_external_device_id : '') + '\t';
		}
		else if (rOperation && recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations) {
			sOutput += getDepartmentCode(rOperation, recordType) + '\t';
			sOutput += getCategoryCode(rOperation, recordType) + '\t';
			sOutput += (rOperation.cc_op_code != null ? rOperation.cc_op_code : '') + '\t';
			sOutput += (rOperation.cc_desc != null ? rOperation.cc_desc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rOperation.cc_short_desc != null ? rOperation.cc_short_desc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rOperation.cc_type != null ? rOperation.cc_type : '') + '\t';
			sOutput += (rOperation.cc_base_rate != null ? rOperation.cc_base_rate : '') + '\t';
			sOutput += (rOperation.cc_overhead != null ? rOperation.cc_overhead : '') + '\t';
			sOutput += (rOperation.cc_labour != null ? rOperation.cc_labour : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_chargable) != null ? getYesNo(rOperation.cc_chargable) : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_qty_required) != null ? getYesNo(rOperation.cc_qty_required) : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_active) != null ? getYesNo(rOperation.cc_active) : '') + '\t';
			sOutput += getDivisionCode(rOperation, recordType) + '\t';
			sOutput += getPlantCode(rOperation, recordType) + '\t';
			sOutput += (rOperation.cc_total_rate != null ? rOperation.cc_total_rate : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_group_by_category) != null ? getYesNo(rOperation.cc_group_by_category) : '') + '\t';
			sOutput += (rOperation.cc_dflt_lag != null ? rOperation.cc_dflt_lag : '') + '\t';
			sOutput += getLagUnit(rOperation.cc_dflt_lag_units) + '\t';
			sOutput += (rOperation.cc_dflt_lag_type != null ? rOperation.cc_dflt_lag_type : '') + '\t';
			sOutput += (rOperation.cc_dflt_succr_lag != null ? rOperation.cc_dflt_succr_lag : '') + '\t';
			sOutput += getLagUnit(rOperation.cc_dflt_succr_lag_units) + '\t';
			sOutput += (rOperation.cc_dflt_succr_lag_type != null ? rOperation.cc_dflt_succr_lag_type : '') + '\t';
			sOutput += (rOperation.cc_qa_check_field_1 != null ? rOperation.cc_qa_check_field_1 : '') + '\t';
			sOutput += (rOperation.cc_qa_check_field_2 != null ? rOperation.cc_qa_check_field_2 : '') + '\t';
			sOutput += (rOperation.cc_qa_check_field_3 != null ? rOperation.cc_qa_check_field_3 : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_qa_check_field_1_enabled) != null ? getYesNo(rOperation.cc_qa_check_field_1_enabled) : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_qa_check_field_2_enabled) != null ? getYesNo(rOperation.cc_qa_check_field_2_enabled) : '') + '\t';
			sOutput += (getYesNo(rOperation.cc_qa_check_field_3_enabled) != null ? getYesNo(rOperation.cc_qa_check_field_3_enabled) : '') + '\t';
			sOutput += getJDFTypeCode(rOperation, recordType) + '\t';
			sOutput += (rOperation.cc_lag_uses_shift_time == 1 ? 'Shift Time' : 'Passage of Time') + '\t';
		}
	}
	
	function getPlantCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Departments && utils.hasRecords(rRecord.sys_department_to_sys_plant)) {
			return rRecord.sys_department_to_sys_plant.plant_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations && utils.hasRecords(rRecord.sys_cost_centre_to_sys_plant$outerjoin)) {
			return rRecord.sys_cost_centre_to_sys_plant$outerjoin.plant_code;
		}
		return '';
	}
	
	function getShiftCode(rRecord) {
		if (utils.hasRecords(rRecord.sys_department_to_sch_shift)) {
			return rRecord.sys_department_to_sch_shift.shift_code;
		}
		return '';
	}
	
	function getDepartmentCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Categories && utils.hasRecords(rRecord.sys_operation_category_to_sys_department)) {
			return rRecord.sys_operation_category_to_sys_department.dept_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations && utils.hasRecords(rRecord.sys_cost_centre_to_sys_department$outerjoin)) {
			return rRecord.sys_cost_centre_to_sys_department$outerjoin.dept_code;
		}
		return '';
	}
	
	function getGLCostOfSales(rRecord) {
		if (utils.hasRecords(rRecord.sys_operation_category_to_gl_account_segment$costofsales)) {
			return rRecord.sys_operation_category_to_gl_account_segment$costofsales.glacctseg_number;
		}
		return '';
	}
	
	function getGLSales(rRecord) {
		if (utils.hasRecords(rRecord.sys_operation_category_to_gl_account_segment$sales)) {
			return rRecord.sys_operation_category_to_gl_account_segment$sales.glacctseg_number;
		}
		return '';
	}
	
	function getCategoryCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations && utils.hasRecords(rRecord.sys_cost_centre_to_sys_operation_category$outerjoin)) {
			return rRecord.sys_cost_centre_to_sys_operation_category$outerjoin.opcat_code;
		}
		return '';
	}
	
	function getDivisionCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations && utils.hasRecords(rRecord.sys_cost_centre_to_sys_division$outerjoin)) {
			return rRecord.sys_cost_centre_to_sys_division$outerjoin.div_code;
		}
		return '';
	}
	
	function getLagUnit(lagUnit) {
		var oLagUnit = {
			'm': 'M',
			'h': 'H'
		}
		if(oLagUnit[lagUnit]) {
			return oLagUnit[lagUnit];
		}
		return '';
	}
	
	function getJDFTypeCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_COSTCENTER_SUBTYPES.Operations && utils.hasRecords(rRecord.sys_cost_centre_to_app_cost_center_jdf_type)) {
			return rRecord.sys_cost_centre_to_app_cost_center_jdf_type.cc_jdf_type_desc;
		}
		return '';
	}
}


/**
 * Export Inventory related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * @param {Number} [nUseItemRevision]
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"A670DB40-B588-4714-A70F-57206D533E47"}
 */
function InventoryExport(rFSRecord, subType, nUseItemRevision) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/in_paper_grade>} */
	var rSubstrateType;
	
	/**@type {JSRecord<db:/avanti/in_paper_brand>} */
	var rSubstrateFinish;
	
	/**@type {JSRecord<db:/avanti/in_item>} */
	var rInItem;
	
	/**@type {JSRecord<db:/avanti/in_warehouse_location>} */
	var rInBinLocation;
	
	/**@type {JSRecord<db:/avanti/in_item_warehouse>} */
	var rInItemWarehouse;
	
	/**@type {JSRecord<db:/avanti/in_item_doc>} */
	var rInItemDocument;
	
	/**@type {JSRecord<db:/avanti/in_item_bill_of_material>} */
	var rInItemBOM;
	
	
	if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes) {
		rSubstrateType = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes) {
		rSubstrateFinish = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems) {
		rInItem = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations) {
		rInBinLocation = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations) {
		rInItemWarehouse = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments) {
		rInItemDocument = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM) {
		rInItemBOM = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		var papergradeName = null;
		var itemCode = null;
		
		if (rSubstrateType && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateTypes) {
			sOutput += (rSubstrateType.papergrade_name != null ? rSubstrateType.papergrade_name : '') + '\t';
			sOutput += (getYesNo(rSubstrateType.papergrade_active) != null ? getYesNo(rSubstrateType.papergrade_active) : '') + '\t';
			sOutput += (rSubstrateType.papergrade_width != null ? rSubstrateType.papergrade_width : '') + '\t';
			sOutput += (rSubstrateType.papergrade_length != null ? rSubstrateType.papergrade_length : '') + '\t';
			sOutput += (getYesNo(rSubstrateType.papergrade_is_roll) != null ? getYesNo(rSubstrateType.papergrade_is_roll) : '') + '\t';
			sOutput += getPapergradeWeightBy(rSubstrateType.papergrade_weight_by) + '\t';
			sOutput += (getYesNo(rSubstrateType.papergrade_charge_partial) != null ? getYesNo(rSubstrateType.papergrade_charge_partial) : '') + '\t';
		}
		else if (rSubstrateFinish && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes) {
			papergradeName = getPapergradeName(rSubstrateFinish, recordType);
			if (!papergradeName || papergradeName == '') {
				sOutput = null;
				return;
			}
			sOutput +=  papergradeName + '\t';
			sOutput += (rSubstrateFinish.paperbrand_name != null ? rSubstrateFinish.paperbrand_name : '') + '\t';
			sOutput += (getYesNo(rSubstrateFinish.paperbrand_active) != null ? getYesNo(rSubstrateFinish.paperbrand_active) : '') + '\t';
			sOutput += (rSubstrateFinish.paperbrand_paper_full_desc != null ? rSubstrateFinish.paperbrand_paper_full_desc.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rSubstrateFinish.paperbrand_basis_weight != null ? rSubstrateFinish.paperbrand_basis_weight : '') + '\t';
			sOutput += (rSubstrateFinish.paperbrand_finish_back != null ? rSubstrateFinish.paperbrand_finish_back : '') + '\t';
			sOutput += (rSubstrateFinish.paperbrand_finish_front != null ? rSubstrateFinish.paperbrand_finish_front : '') + '\t';
		}
		else if (rInItem && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems) {
			if (!rInItem.item_code || rInItem.item_code == '') {
				sOutput = null;
				return;
			}
			
			if (nUseItemRevision && rInItem.item_code_no_revision != null && rInItem.item_code_no_revision != '') {
				sOutput += rInItem.item_code_no_revision + '\t';
			}
			else {
				sOutput += (rInItem.item_code != null ? rInItem.item_code : '') + '\t';
			}

			if (nUseItemRevision && rInItem.item_revision != null && rInItem.item_revision != '') {
				sOutput += rInItem.item_revision + '\t';
			}
			else {
				sOutput += '\t';
			}
			
			sOutput += (rInItem.item_desc1 != null ? rInItem.item_desc1.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (rInItem.item_desc2 != null ? rInItem.item_desc2.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += getItemClassCode(rInItem, recordType) + '\t';
			sOutput += getItemTypeCode(rInItem.itemtype_code) + '\t';
			sOutput += getItemStatus(rInItem.item_status) + '\t';
			sOutput += (getYesNo(rInItem.item_lot_item) != null ? getYesNo(rInItem.item_lot_item) : '') + '\t';
			sOutput += (rInItem.item_color != null ? rInItem.item_color : '') + '\t';
			sOutput += (rInItem.item_decimal_places != null ? rInItem.item_decimal_places : '') + '\t';
			sOutput += (rInItem.item_avg_lead_time != null ? rInItem.item_avg_lead_time : '') + '\t';
			sOutput += getSalesTaxOption(rInItem.item_salestax_option) + '\t';
			sOutput += getTaxGroupCode(rInItem, recordType) + '\t';
			sOutput += (getYesNo(rInItem.item_allow_commissions) != null ? getYesNo(rInItem.item_allow_commissions) : '') + '\t';
			sOutput += (getYesNo(rInItem.item_allow_discounts) != null ? getYesNo(rInItem.item_allow_discounts) : '') + '\t';
			sOutput += (getYesNo(rInItem.item_allow_backorders) != null ? getYesNo(rInItem.item_allow_backorders) : '') + '\t';
			sOutput += (rInItem.item_dimension_heigth != null ? rInItem.item_dimension_heigth : '') + '\t';
			sOutput += (rInItem.item_dimension_length != null ? rInItem.item_dimension_length : '') + '\t';
			sOutput += (rInItem.item_dimension_width != null ? rInItem.item_dimension_width : '') + '\t';
			sOutput += (rInItem.item_max_weight != null ? rInItem.item_max_weight : '') + '\t';
			sOutput += getUOMCode(rInItem, recordType) + '\t';
			sOutput += getCustomerCode(rInItem, recordType) + '\t';
			sOutput += getWorkTypeCode(rInItem, recordType) + '\t';
			sOutput += (rInItem.item_cust_part_number != null ? rInItem.item_cust_part_number : '') + '\t';
			sOutput += (rInItem.item_isbn_number != null ? rInItem.item_isbn_number : '') + '\t';
			sOutput += getInventoryGroupCode(rInItem, recordType) + '\t';
			sOutput += '\t';
			sOutput += '\t';
			sOutput += '\t';
			sOutput += '\t';
			sOutput += (rInItem.item_onhand_qty != null ? rInItem.item_onhand_qty : '') + '\t';
			sOutput += '\t';
			sOutput += '\t';
			sOutput += '\t';
			sOutput += (rInItem.item_spec != null ? rInItem.item_spec.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += getGLAccountCode(rInItem, recordType, 'inventory_adj') + '\t';
			sOutput += getGLAccountCode(rInItem, recordType, 'cost_of_sales') + '\t';
			sOutput += getGLAccountCode(rInItem, recordType, 'inventory') + '\t';
			sOutput += getGLAccountCode(rInItem, recordType, 'sales_returns') + '\t';
			sOutput += getGLAccountCode(rInItem, recordType, 'sales') + '\t';
			
			var oPaperInfo = null;
			if (rInItem.itemclass_id && itemClassCache.hasOwnProperty(rInItem.itemclass_id.toString())) {
				if (itemClassCache[rInItem.itemclass_id.toString()] && itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'I') {
					sOutput += getInkTypeCode(rInItem, recordType) + '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
				}
				else if (itemClassCache[rInItem.itemclass_id.toString()] && itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'CY') {
					sOutput += '\t';
					sOutput += getCylinderTeeth(rInItem, recordType) + '\t';
					sOutput += getCylinderDiameter(rInItem, recordType) + '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
				}
				else if (itemClassCache[rInItem.itemclass_id.toString()] && itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'PL') {
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += getPlateRunLength(rInItem, recordType) + '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
				}
				else if (itemClassCache[rInItem.itemclass_id.toString()] && 
						(itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'P' || 
						itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'R' || 
						itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'TA' || 
						itemClassCache[rInItem.itemclass_id.toString()].itemclass_type == 'EN')) {
					oPaperInfo = getPaperInfo(rInItem, recordType);
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += (oPaperInfo.PaperGradeName != null ? oPaperInfo.PaperGradeName : '') + '\t';
					sOutput += (oPaperInfo.PaperBrandName != null ? oPaperInfo.PaperBrandName : '') + '\t';
					sOutput += (oPaperInfo.PaperFirstDim != null ? oPaperInfo.PaperFirstDim : '') + '\t';
					sOutput += (oPaperInfo.PaperSecondDim != null ? oPaperInfo.PaperSecondDim : '') + '\t';
					sOutput += (oPaperInfo.PaperCaliper != null ? oPaperInfo.PaperCaliper : '') + '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += (oPaperInfo.PaperWeight != null ? oPaperInfo.PaperWeight : '') + '\t';
				}
				else {
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
					sOutput += '\t';
				}
			}
			else {
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
				sOutput += '\t';
			}
			var oWarehouseInfo = getPrimaryWarehouseInfo(rInItem, recordType);
			sOutput += (oWarehouseInfo.WarehouseCode != null ? oWarehouseInfo.WarehouseCode : '') + '\t';
			var oSupplierInfo = getSupplierInfo(rInItem, recordType);
			sOutput += (oSupplierInfo.SupplierCode != null ? oSupplierInfo.SupplierCode : '') + '\t';
			sOutput += (oSupplierInfo.SupplierUOMCode != null ? oSupplierInfo.SupplierUOMCode : '') + '\t';
			sOutput += (oSupplierInfo.SupplierLandedCostUOMCode != null ? oSupplierInfo.SupplierLandedCostUOMCode : '') + '\t';
			sOutput += (oSupplierInfo.SupplierListPrice != null ? oSupplierInfo.SupplierListPrice : '') + '\t';
			
			var oSellingUOMInfo = getItemSellingUOMInfo(rInItem, recordType);
			sOutput += (oSellingUOMInfo.UOMListPrice != null ? oSellingUOMInfo.UOMListPrice : '') + '\t';
			sOutput += (oSellingUOMInfo.SellingUOMCode != null ? oSellingUOMInfo.SellingUOMCode : '') + '\t';
			sOutput += (oSupplierInfo.SupplierLeadTimeFlag != null ? oSupplierInfo.SupplierLeadTimeFlag : '') + '\t';
			sOutput += (oSellingUOMInfo.SellingUOMConvFactor != null ? oSellingUOMInfo.SellingUOMConvFactor : '') + '\t';
			sOutput +=(oSellingUOMInfo.PricingUOMCode != null ?  oSellingUOMInfo.PricingUOMCode : '') + '\t';
			sOutput += (oSupplierInfo.SupplierUOMConvFactor != null ? oSupplierInfo.SupplierUOMConvFactor : '') + '\t';
			
			if (oPaperInfo) {
				sOutput += (oPaperInfo.SubstrateBrandName != null ? oPaperInfo.SubstrateBrandName : '') + '\t';
			}
			else {
				sOutput += '\t';
			}
			sOutput += (oSellingUOMInfo.PricingUOMConvFactor != null ? oSellingUOMInfo.PricingUOMConvFactor : '') + '\t';
			sOutput += (oWarehouseInfo.WarehouseEstUnitsUOMCode != null ? oWarehouseInfo.WarehouseEstUnitsUOMCode : '') + '\t';
			sOutput += (oSupplierInfo.LandedCostUOMFactor != null ? oSupplierInfo.LandedCostUOMFactor : '') + '\t';
			sOutput += (oSupplierInfo.SupplierItemNumber != null ? oSupplierInfo.SupplierItemNumber : '') + '\t';
			sOutput += (oSupplierInfo.SupplierItemDesc != null ? oSupplierInfo.SupplierItemDesc : '') + '\t';
			sOutput += (oSupplierInfo.SupplierReorderMultipe != null ? oSupplierInfo.SupplierReorderMultipe : '') + '\t';
			sOutput += (oSupplierInfo.SupplierMinOrderQty != null ? oSupplierInfo.SupplierMinOrderQty : '') + '\t';
			sOutput += (oWarehouseInfo.WarehouseAverageCost != null ? oWarehouseInfo.WarehouseAverageCost : '') + '\t';
			sOutput += (oWarehouseInfo.WarehouseReceiptBinLocation != null ? oWarehouseInfo.WarehouseReceiptBinLocation : '') + '\t';
			sOutput += (getYesNo(rInItem.item_no_bin_location) != null ? getYesNo(rInItem.item_no_bin_location) : '') + '\t';
			sOutput += (rInItem.item_creation_date != null ? scopes.avDate.formatDate(rInItem.item_creation_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rInItem.item_expiry_date != null ? scopes.avDate.formatDate(rInItem.item_expiry_date,'YYYYMMDD') : '') + '\t';
			sOutput += (oWarehouseInfo.WarehouseIssueBinLocation != null ? oWarehouseInfo.WarehouseIssueBinLocation : '') + '\t';
			sOutput += getProjectDescByCustId(rInItem) + '\t';
			sOutput += (rInItem.item_harmonized_code != null ? rInItem.item_harmonized_code : '') + '\t';
			sOutput += (rInItem.item_country_of_origin != null ? rInItem.item_country_of_origin : '') + '\t';
			sOutput += (rInItem.item_is_virtual == 1 ? 'Y' : 'N') + '\t';
			sOutput += getLanguage(rInItem) + '\t';
		}
		else if (rInBinLocation && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations) {
			sOutput += getWarehouseCode(rInBinLocation, recordType) + '\t';
			sOutput += getWarehouseLevelCode(rInBinLocation, recordType, 1) + '\t';
			sOutput += getWarehouseLevelCode(rInBinLocation, recordType, 2) + '\t';
			sOutput += getWarehouseLevelCode(rInBinLocation, recordType, 3) + '\t';
			sOutput += getWarehouseLevelCode(rInBinLocation, recordType, 4) + '\t';
			sOutput += getWarehouseLevelCode(rInBinLocation, recordType, 5) + '\t';
			sOutput += (rInBinLocation.whseloc_min_qty != null ? rInBinLocation.whseloc_min_qty : '') + '\t';
			sOutput += (rInBinLocation.whseloc_max_qty != null ? rInBinLocation.whseloc_max_qty : '') + '\t';
			sOutput += (getYesNo(rInBinLocation.whseloc_active) != null ? getYesNo(rInBinLocation.whseloc_active) : '') + '\t';
		}
		else if (rInItemWarehouse && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations) {
			itemCode = getItemCode(rInItemWarehouse, recordType);
			if (!itemCode || itemCode == '') {
				sOutput = null;
				return;
			}
			sOutput += itemCode + '\t';
			sOutput += getWarehouseCode(rInItemWarehouse, recordType) + '\t';
			sOutput += getWarehousePrimaryBinLocation(rInItemWarehouse, recordType) + '\t';
			sOutput += (rInItemWarehouse.itemwhse_onhand_qty != null ? rInItemWarehouse.itemwhse_onhand_qty : '') + '\t';
			sOutput += getWarehouseEstUnitsUOMCode(rInItemWarehouse, recordType) + '\t';
			sOutput += getReorderMethod(rInItemWarehouse.itemwhse_reorder_method) + '\t';
			sOutput += (rInItemWarehouse.itemwhse_min_qty != null ? rInItemWarehouse.itemwhse_min_qty : '') + '\t';
			sOutput += (rInItemWarehouse.itemwhse_max_qty != null ? rInItemWarehouse.itemwhse_max_qty : '') + '\t';
			sOutput += getWarehouseLocation(rInItemWarehouse, recordType, 'receipt') + '\t';
			sOutput += getWarehouseLocation(rInItemWarehouse, recordType, 'issue') + '\t';
			sOutput += '\t'; // Can not retrieve the project description because there is no link between in_item_warehouse and sa_customer_project;
		}
		else if (rInItemDocument && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments) {
			itemCode = getItemCode(rInItemDocument, recordType);
			if (!itemCode || itemCode == '') {
				sOutput = null;
				return;
			}
			sOutput += itemCode + '\t';
			var oItemDocumentInfo = getItemDocumentInfo(rInItemDocument, recordType);
			sOutput += (oItemDocumentInfo.Title != null ? oItemDocumentInfo.Title : '') + '\t';
			sOutput += (oItemDocumentInfo.FilePath != null ? oItemDocumentInfo.FilePath : '') + '\t';
			sOutput += getBOMItemCode(rInItemDocument, recordType) + '\t';
			sOutput += getWorkTypeSectionDesc(rInItemDocument, recordType) + '\t';
			sOutput += getDocumentMgmtCategory(rInItemDocument, recordType) + '\t';
			sOutput += (getYesNo(rInItemDocument.item_doc_is_proof) != null ? getYesNo(rInItemDocument.item_doc_is_proof) : '') + '\t';
			sOutput += (getYesNo(rInItemDocument.item_doc_is_for_order) != null ? getYesNo(rInItemDocument.item_doc_is_for_order) : '') + '\t';
			sOutput += (getYesNo(rInItemDocument.item_doc_is_approved) != null ? getYesNo(rInItemDocument.item_doc_is_approved) : '') + '\t';
			sOutput += getEmployeeCode(rInItemDocument, recordType) + '\t';
			sOutput += (rInItemDocument.item_doc_approved_date != null ? scopes.avDate.formatDate(rInItemDocument.item_doc_approved_date,'YYYYMMDD') : '') + '\t';
			sOutput += (getYesNo(rInItemDocument.item_doc_show_jobticket) != null ? getYesNo(rInItemDocument.item_doc_show_jobticket) : '') + '\t';
			sOutput += (getYesNo(rInItemDocument.item_doc_show_quote) != null ? getYesNo(rInItemDocument.item_doc_show_quote) : '') + '\t';
		}
		else if (rInItemBOM && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemBOM) {
			if (utils.hasRecords(rInItemBOM.in_item_bill_of_material_to_in_item) && utils.hasRecords(rInItemBOM.in_item_bill_of_material_raw_material_item_id_to_in_item)) {
				sOutput += rInItemBOM.in_item_bill_of_material_to_in_item.item_code + '\t';
				sOutput += rInItemBOM.in_item_bill_of_material_raw_material_item_id_to_in_item.item_code + '\t';
				sOutput += rInItemBOM.itembom_qty_per_unit;
			}
		}
	}
	
	function getPapergradeWeightBy(lagUnit) {
		var oLagUnit = {
			'b': 'Basis Weight',
			'g': 'GSM'
		}
		if(oLagUnit[lagUnit]) {
			return oLagUnit[lagUnit];
		}
		return '';
	}
	
	function getPapergradeName(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.SubstrateFinishes && utils.hasRecords(rRecord.in_paper_brand_to_in_paper_grade)) {
			return rRecord.in_paper_brand_to_in_paper_grade.papergrade_name;
		}
		return '';
	}

	function getItemClassCode(rRecord, recordType) {
		var itemClassId = rRecord.itemclass_id ? rRecord.itemclass_id.toString() : null;
		if (itemClassCache.hasOwnProperty(itemClassId)) {
			return itemClassCache[itemClassId] ? itemClassCache[itemClassId].itemclass_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_class)) {
			itemClassCache[itemClassId] = rRecord.in_item_to_in_item_class;
			return rRecord.in_item_to_in_item_class.itemclass_code;
		}
		return '';
	}
	
	/**
	 * @param {JSRecord<db:/avanti/in_item>} rItem
	 * 
	 * @return {String}
	 */
	function getLanguage(rItem) {
		if (utils.hasRecords(rItem.in_item_to_sys_org_language)) {
			return rItem.in_item_to_sys_org_language.sys_org_language;
		}
		else {
			return '';
		}
	}
	
	function getItemTypeCode(itemType) {
		var oItemTypes = {
			'A': 'Assembled Kit',
			'AC': 'Account Item',
			'B': 'Build-to-Order Kit',
			'F': 'Finished Goods',
			'N': 'Non Stock',
			'P': 'Product',
			'S': 'Stock Item',
			'SE': 'Service Item'
		}
		if(oItemTypes[itemType]) {
			return oItemTypes[itemType];
		}
		return '';
	}
	
	function getItemStatus(itemStatus) {
		var oItemStatus = {
			'A': 'Active',
			'I': 'Inactive',
			'O': 'Obsolete',
			'D': 'To be Deleted'
		}
		if(oItemStatus[itemStatus]) {
			return oItemStatus[itemStatus];
		}
		return '';
	}
	
	function getSalesTaxOption(salesTaxOption) {
		var oSalesTaxOptions = {
			'C': 'Based on Customer',
			'T': 'Taxable',
			'N': 'Non-Taxable'
		}
		if(oSalesTaxOptions[salesTaxOption]) {
			return oSalesTaxOptions[salesTaxOption];
		}
		return '';
	}
	
	function getTaxGroupCode(rRecord, recordType) {
		var taxGroupId = rRecord.taxgroup_id ? rRecord.taxgroup_id.toString() : null;
		if (taxGroupCache.hasOwnProperty(taxGroupId)) {
			return taxGroupCache[taxGroupId] ? taxGroupCache[taxGroupId].taxgroup_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_sys_sales_tax_group)) {
			taxGroupCache[taxGroupId] = rRecord.in_item_to_sys_sales_tax_group;
			return rRecord.in_item_to_sys_sales_tax_group.taxgroup_code;
		}
		return '';
	}

	function getUOMCode(rRecord, recordType) {
		var uomId = rRecord.item_standard_uom_id ? rRecord.item_standard_uom_id.toString() : null;		
		if (sysUnitOfMeasureCache.hasOwnProperty(uomId)) {
			return sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '';
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && uomId) {
			sysUnitOfMeasureCache[uomId] = getUOMRecord(uomId);
			return (sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '');
		}
		return '';
	}

	function getCustomerCode(rRecord, recordType) {
		var custId = rRecord.cust_id ? rRecord.cust_id.toString() : null;		
		if (customerCache.hasOwnProperty(custId)) {
			return customerCache[custId] ? customerCache[custId].cust_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_sa_customer)) {
			customerCache[custId] = rRecord.in_item_to_sa_customer;
			return rRecord.in_item_to_sa_customer.cust_code;
		}
		return '';
	}
	
	function getWorkTypeCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_sa_task_worktype)) {
			return rRecord.in_item_to_sa_task_worktype.worktype_code;
		}
		return '';
	}

	function getInventoryGroupCode(rRecord, recordType) {
		var inGroupId = rRecord.ingroup_id ? rRecord.ingroup_id.toString() : null;	
		if (inventoryGroupCache.hasOwnProperty(inGroupId)) {
			return inventoryGroupCache[inGroupId] ? inventoryGroupCache[inGroupId].ingroup_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_group)) {
			inventoryGroupCache[inGroupId] = rRecord.in_item_to_in_group;
			return rRecord.in_item_to_in_group.ingroup_code;
		}
		return '';
	}
	
	function getGLAccountCode(rRecord, recordType, accountType) {
		var glAccountId = null;	
		if (accountType == 'inventory_adj') {
			glAccountId = rRecord.item_glacct_id_inventory_adj ? rRecord.item_glacct_id_inventory_adj.toString() : null;
		}
		else if (accountType == 'cost_of_sales') {
			glAccountId = rRecord.item_glacct_id_cost_of_sales ? rRecord.item_glacct_id_cost_of_sales.toString() : null;
		}
		else if (accountType == 'inventory') {
			glAccountId = rRecord.item_glacct_id_inventory ? rRecord.item_glacct_id_inventory.toString() : null;
		}
		else if (accountType == 'sales_returns') {
			glAccountId = rRecord.item_glacct_id_sales_returns ? rRecord.item_glacct_id_sales_returns.toString() : null;
		}
		else if (accountType == 'sales') {
			glAccountId = rRecord.item_glacct_id_sales ? rRecord.item_glacct_id_sales.toString() : null;
		}

		if (glAccountCache.hasOwnProperty(glAccountId)) {
			return glAccountCache[glAccountId] ? glAccountCache[glAccountId].glacct_number : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && accountType == 'inventory_adj' && utils.hasRecords(rRecord.in_item_to_gl_account$item_glacct_id_inventory_adj)) {
			glAccountCache[glAccountId] = rRecord.in_item_to_gl_account$item_glacct_id_inventory_adj;
			return rRecord.in_item_to_gl_account$item_glacct_id_inventory_adj.glacct_number;
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && accountType == 'cost_of_sales' && utils.hasRecords(rRecord.in_item_to_gl_account$item_glacct_id_cost_of_sales)) {
			glAccountCache[glAccountId] = rRecord.in_item_to_gl_account$item_glacct_id_cost_of_sales;
			return rRecord.in_item_to_gl_account$item_glacct_id_cost_of_sales.glacct_number;
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && accountType == 'inventory' && utils.hasRecords(rRecord.in_item_to_gl_account$inventory)) {
			glAccountCache[glAccountId] = rRecord.in_item_to_gl_account$inventory;
			return rRecord.in_item_to_gl_account$inventory.glacct_number;
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && accountType == 'sales_returns' && utils.hasRecords(rRecord.in_item_to_gl_account$item_glacct_id_sales_returns)) {
			glAccountCache[glAccountId] = rRecord.in_item_to_gl_account$item_glacct_id_sales_returns;
			return rRecord.in_item_to_gl_account$item_glacct_id_sales_returns.glacct_number;
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && accountType == 'sales' && utils.hasRecords(rRecord.in_item_to_gl_account$item_glacct_id_sales)) {
			glAccountCache[glAccountId] = rRecord.in_item_to_gl_account$item_glacct_id_sales;
			return rRecord.in_item_to_gl_account$item_glacct_id_sales.glacct_number;
		}
		return '';
	}
	
	function getInkTypeCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && 
				utils.hasRecords(rRecord.in_item_to_in_item_ink) && 
				utils.hasRecords(rRecord.in_item_to_in_item_ink.in_item_ink_to_in_ink_type)) {
			return rRecord.in_item_to_in_item_ink.in_item_ink_to_in_ink_type.inktype_code;
		}
		return '';
	}
	
	function getCylinderTeeth(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_cylinder)) {
			return rRecord.in_item_to_in_item_cylinder.itemcyl_teeth;
		}
		return '';
	}
	
	function getCylinderDiameter(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_cylinder)) {
			return rRecord.in_item_to_in_item_cylinder.itemcyl_diameter;
		}
		return '';
	}
	
	function getPlateRunLength(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_plate)) {
			return rRecord.in_item_to_in_item_plate.itemplate_run_length;
		}
		return '';
	}

	function getPaperInfo(rRecord, recordType) {
		var returnObj = {
			PaperGradeName: null,
			PaperBrandName: null,
			PaperFirstDim: null,
			PaperSecondDim: null,
			PaperCaliper: null,
			PaperWeight: null
		}
		var itemId = null;
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems) {
			itemId = rRecord.item_id ? rRecord.item_id.toString() : null;
		}
		
		if (itemPaperCache.hasOwnProperty(itemId)) {
			return itemPaperCache[itemId];
		} 
		else {
			if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_paper)) {
				returnObj = {
					PaperGradeName: utils.hasRecords(rRecord.in_item_to_in_item_paper.in_item_paper_to_in_paper_grade) ? rRecord.in_item_to_in_item_paper.in_item_paper_to_in_paper_grade.papergrade_name : null,
					PaperBrandName: utils.hasRecords(rRecord.in_item_to_in_item_paper.in_item_paper_to_in_paper_brand) ? rRecord.in_item_to_in_item_paper.in_item_paper_to_in_paper_brand.paperbrand_name : null,
					SubstrateBrandName: rRecord.in_item_to_in_item_paper.paper_brand_name,
					PaperFirstDim: rRecord.in_item_to_in_item_paper.paper_first_dim,
					PaperSecondDim: rRecord.in_item_to_in_item_paper.paper_second_dim,
					PaperCaliper: rRecord.in_item_to_in_item_paper.paper_caliper,
					PaperWeight: rRecord.in_item_to_in_item_paper.paper_weight
				}
				itemPaperCache[itemId] = returnObj;
			}
		}
		return returnObj;
	}

	function getPrimaryWarehouseInfo(rRecord, recordType) {
		var whseId = null;
		var whseEstCostUOMId = null;
		var whseIssueBinId = null;
		var whseReceiptBinId = null;
		var returnObj = {
			WarehouseCode: null,
			WarehouseEstUnitsUOMCode: null,
			WarehouseAverageCost: null,
			WarehouseReceiptBinLocation: null,
			WarehouseIssueBinLocation: null
		};
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && 
				utils.hasRecords(rRecord.in_item_to_in_item_warehouse$primary)) {
			whseId = rRecord.in_item_to_in_item_warehouse$primary.whse_id ? rRecord.in_item_to_in_item_warehouse$primary.whse_id.toString() : null;
			whseEstCostUOMId = rRecord.in_item_to_in_item_warehouse$primary.itemwhse_estimate_cost_uom_id ? rRecord.in_item_to_in_item_warehouse$primary.itemwhse_estimate_cost_uom_id : null;
			
			if (warehouseCache.hasOwnProperty(whseId)) {
				returnObj.WarehouseCode = warehouseCache[whseId] ? warehouseCache[whseId].whse_code : null;
			}
			else if (utils.hasRecords(rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse)) {
				warehouseCache[whseId] = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse;
				returnObj.WarehouseCode = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse.whse_code;
			}
			whseIssueBinId = rRecord.in_item_to_in_item_warehouse$primary.itemwhse_default_issue_bin;
			whseReceiptBinId = rRecord.in_item_to_in_item_warehouse$primary.itemwhse_default_receipt_bin;
			
			if (returnObj.WarehouseCode) {
				returnObj.WarehouseAverageCost = rRecord.in_item_to_in_item_warehouse$primary.itemwhse_avg_cost;
				
				if (sysUnitOfMeasureCache.hasOwnProperty(whseEstCostUOMId)) {
					returnObj.WarehouseEstUnitsUOMCode = sysUnitOfMeasureCache[whseEstCostUOMId] ? sysUnitOfMeasureCache[whseEstCostUOMId].uom_code : null;
				} 
				else {
					/** @type {JSRecord<db:/avanti/sys_unit_of_measure>} **/
					var res = getUOMRecord(whseEstCostUOMId);
					if (res) {
						sysUnitOfMeasureCache[whseEstCostUOMId] = res;
						returnObj.WarehouseEstUnitsUOMCode = res ? res.uom_code : null;
					}
				}
				
				if (warehouseLocationCache.hasOwnProperty(whseIssueBinId)) {
					returnObj.WarehouseIssueBinLocation = warehouseLocationCache[whseIssueBinId] ? warehouseLocationCache[whseIssueBinId].whseloc_bin_location : null;
				}
				else if (whseIssueBinId && utils.hasRecords(rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$issue_bin)) {
					warehouseLocationCache[whseIssueBinId] = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$issue_bin;
					returnObj.WarehouseIssueBinLocation = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$issue_bin.whseloc_bin_location;
				}
				
				if (warehouseLocationCache.hasOwnProperty(whseReceiptBinId)) {
					returnObj.WarehouseReceiptBinLocation = warehouseLocationCache[whseReceiptBinId] ? warehouseLocationCache[whseReceiptBinId].whseloc_bin_location : null;
				}
				else if (whseReceiptBinId && utils.hasRecords(rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$receipt_bin)) {
					warehouseLocationCache[whseReceiptBinId] = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$receipt_bin;
					returnObj.WarehouseReceiptBinLocation = rRecord.in_item_to_in_item_warehouse$primary.in_item_warehouse_to_in_warehouse_location$receipt_bin.whseloc_bin_location;
				}
			}

		}
		return returnObj;
	}

	function getSupplierInfo(rRecord, recordType) {
		var supplierId = null;
		/**@type {String} */
		var supplierUOMId = null;
		/**@type {String} */
		var landedCostUOMId = null;
		var returnObj = {
			SupplierCode: null,
			SupplierUOMCode: null,
			SupplierListPrice: null,
			SupplierLeadTimeFlag: null,
			SupplierUOMConvFactor: null,
			LandedCostUOMFactor: null,
			SupplierItemNumber: null,
			SupplierItemDesc: null,
			SupplierReorderMultipe: null,
			SupplierMinOrderQty: null,
			SupplierLandedCostUOMCode: null
		};
		
		var leadTimeFlag = {
			'A' : 'Automatic',
			'M' : 'Manual'
		};

		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_supplier$primarysupplier)) {
			/** @type {JSRecord<db:/avanti/in_item_supplier>} **/
			var rPrimarySupplier = rRecord.in_item_to_in_item_supplier$primarysupplier;

			supplierId = rPrimarySupplier.supplier_id ? rPrimarySupplier.supplier_id.toString() : null;

			if (supplierCache.hasOwnProperty(supplierId)) {
				returnObj.SupplierCode = supplierCache[supplierId] ? supplierCache[supplierId].supplier_code : null;
			}
			else if (utils.hasRecords(rRecord.in_item_to_in_item_supplier$primarysupplier.in_item_supplier_to_ap_supplier)) {
				supplierCache[supplierId] = rPrimarySupplier.in_item_supplier_to_ap_supplier;
				returnObj.SupplierCode = rPrimarySupplier.in_item_supplier_to_ap_supplier.supplier_code;
				
			}
			
			landedCostUOMId = rPrimarySupplier.itemsupp_cost_uom_id ? rPrimarySupplier.itemsupp_cost_uom_id.toString() : null;
			supplierUOMId = rPrimarySupplier.uom_id ? rPrimarySupplier.uom_id.toString() : null;
			returnObj.SupplierListPrice = rPrimarySupplier.itemsupp_list_price;
			returnObj.SupplierLeadTimeFlag = rPrimarySupplier.itemsupp_leadtime_flag ? leadTimeFlag[rPrimarySupplier.itemsupp_leadtime_flag] : null;
			returnObj.SupplierUOMConvFactor = rPrimarySupplier.itemsupp_uom_conv_factor;
			returnObj.LandedCostUOMFactor = rPrimarySupplier.itemsupp_cost_uom_factor;
			returnObj.SupplierItemNumber = rPrimarySupplier.itemsupp_part_number;
			returnObj.SupplierItemDesc = rPrimarySupplier.itemsupp_part_desc;
			returnObj.SupplierReorderMultipe = rPrimarySupplier.itemsupp_reorder_multiple;
			returnObj.SupplierMinOrderQty = rPrimarySupplier.itemsupp_min_order_qty;

			/** @type {JSRecord<db:/avanti/sys_unit_of_measure>} **/
			var res;
			if (sysUnitOfMeasureCache.hasOwnProperty(landedCostUOMId)) {
				returnObj.SupplierLandedCostUOMCode = sysUnitOfMeasureCache[landedCostUOMId] ? sysUnitOfMeasureCache[landedCostUOMId].uom_code : null;
			} 
			else if (landedCostUOMId) {
				res = getUOMRecord(landedCostUOMId);
				if (res) {
					sysUnitOfMeasureCache[landedCostUOMId] = res;
					returnObj.SupplierLandedCostUOMCode = res.uom_code;					
				}
			}
			
			if (sysUnitOfMeasureCache.hasOwnProperty(supplierUOMId)) {
				returnObj.SupplierUOMCode = sysUnitOfMeasureCache[supplierUOMId] ? sysUnitOfMeasureCache[supplierUOMId].uom_code : null;
			} 
			else if (supplierUOMId) {
				res = getUOMRecord(supplierUOMId);
				if (res) {
					sysUnitOfMeasureCache[supplierUOMId] = res;
					returnObj.SupplierUOMCode = res.uom_code;					
				}
			}
		}

		return returnObj;
	}

	function getItemSellingUOMInfo(rRecord, recordType) {
		var sUOMId = null;
		var pUOMId = null;
		/** @type {JSRecord<db:/avanti/sys_unit_of_measure>} **/
		var res = null;
		var returnObj = {
			SellingUOMCode: null,
			UOMListPrice: null,
			SellingUOMConvFactor: null,
			PricingUOMCode: null,
			PricingUOMConvFactor: null
		};
		
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItems && utils.hasRecords(rRecord.in_item_to_in_item_selling_uom)) {
			sUOMId = rRecord.in_item_to_in_item_selling_uom.itemselluom_sell_uom_id;
			pUOMId = rRecord.in_item_to_in_item_selling_uom.itemselluom_price_uom_id;
			
			returnObj.UOMListPrice = rRecord.in_item_to_in_item_selling_uom.itemselluom_list_price;
			returnObj.SellingUOMConvFactor = rRecord.in_item_to_in_item_selling_uom.itemselluom_sell_conv_factor;
			returnObj.PricingUOMConvFactor = rRecord.in_item_to_in_item_selling_uom.itemselluom_price_conv_factor;
			
			
			if (sysUnitOfMeasureCache.hasOwnProperty(sUOMId)) {
				returnObj.SellingUOMCode = sysUnitOfMeasureCache[sUOMId] ? sysUnitOfMeasureCache[sUOMId].uom_code : null;
			} 
			else if (sUOMId) {
				res = getUOMRecord(sUOMId);
				if (res) {
					sysUnitOfMeasureCache[sUOMId] = res;
					returnObj.SellingUOMCode = res.uom_code;				
				}
			}
			
			if (sysUnitOfMeasureCache.hasOwnProperty(pUOMId)) {
				returnObj.PricingUOMCode = sysUnitOfMeasureCache[pUOMId] ? sysUnitOfMeasureCache[pUOMId].uom_code : null;
			} 
			else if (pUOMId) {
				res = getUOMRecord(pUOMId);
				if (res) {
					sysUnitOfMeasureCache[pUOMId] = res;
					returnObj.PricingUOMCode = res.uom_code;				
				}
			}
		}
		return returnObj;
	}

	function getProjectDescByCustId(rRecord) {
		if (rRecord && rRecord.cust_id && rInItem.itemtype_code == scopes.avUtils.ITEM_TYPE.FinishedGood && scopes.avInv.isProjectInventoryOn()) {
			var custId = rRecord.cust_id.toString();
			if (projectDescByCustCache.hasOwnProperty(custId)) {
				if (projectDescByCustCache[custId] != null) {
					return projectDescByCustCache[custId].substring(0, 50);
				}
			}
			else {
				var sSQL = "SELECT custproj_id \
							  FROM sa_customer_project \
							 WHERE org_id = ? \
							   AND cust_id = ?"
				/**@type {JSRecord<db:/avanti/sa_customer_project>} */
				var rProject = scopes.avDB.getRecFromSQL(sSQL,'sa_customer_project',[globals.org_id.toString(), custId]);
				if (rProject && rProject.custproj_desc) {
					projectDescByCustCache[custId] = rProject.custproj_desc;
					return rProject.custproj_desc.substring(0, 50);
				}
				else {
					projectDescByCustCache[custId] = null;
				}
			}
		}
		return '';
	}

	function getWarehouseCode(rRecord, recordType) {
		var whseId = rRecord.whse_id ? rRecord.whse_id.toString() : null;
		
		if (warehouseCache.hasOwnProperty(whseId)) {
			return warehouseCache[whseId] ? warehouseCache[whseId].whse_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.BinLocations && utils.hasRecords(rRecord.in_warehouse_location_to_in_warehouse)) {
			warehouseCache[whseId] = rRecord.in_warehouse_location_to_in_warehouse;
			return rRecord.in_warehouse_location_to_in_warehouse.whse_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations && utils.hasRecords(rRecord.in_item_warehouse_to_in_warehouse)) {
			warehouseCache[whseId] = rRecord.in_item_warehouse_to_in_warehouse;
			return rRecord.in_item_warehouse_to_in_warehouse.whse_code;
		}

		return '';
	}
	
	function getWarehouseLevelCode(rRecord, recordType, level) {
		var whseLevelCodeId = null;
		switch (level) {
			case 1:
				whseLevelCodeId = rRecord.whselevelcode1_id ? rRecord.whselevelcode1_id.toString() : null;
				break;
			case 2:
				whseLevelCodeId = rRecord.whselevelcode2_id ? rRecord.whselevelcode2_id.toString() : null;
				break;
			case 3:
				whseLevelCodeId = rRecord.whselevelcode3_id ? rRecord.whselevelcode3_id.toString() : null;
				break;
			case 4:
				whseLevelCodeId = rRecord.whselevelcode4_id ? rRecord.whselevelcode4_id.toString() : null;
				break;
			case 5:
				whseLevelCodeId = rRecord.whselevelcode5_id ? rRecord.whselevelcode5_id.toString() : null;
				break;
		}

		if (warehouseLevelCodeCache.hasOwnProperty(whseLevelCodeId)) {
			return warehouseLevelCodeCache[whseLevelCodeId] ? warehouseLevelCodeCache[whseLevelCodeId].whselevelcode_code : '';
		} 
		else if (whseLevelCodeId) {
			var sSQL = "SELECT whselevelcode_id \
				  		  FROM in_warehouse_level_code \
				  		 WHERE org_id = ? \
				  		   AND whselevelcode_id = ?";
			/**@type {JSRecord<db:/avanti/in_warehouse_level_code>} */
			var rWhseLevelCode = scopes.avDB.getRecFromSQL(sSQL, 'in_warehouse_level_code', [globals.org_id.toString(), whseLevelCodeId]);
			if (rWhseLevelCode && rWhseLevelCode.whselevelcode_code) {
				warehouseLevelCodeCache[whseLevelCodeId] = rWhseLevelCode;
				return rWhseLevelCode.whselevelcode_code;
			}
		}
		return '';
	}

	function getItemCode(rRecord, recordType) {
		var itemId = rRecord.item_id ? rRecord.item_id.toString() : null;
		if (inventoryItemCache.hasOwnProperty(itemId) && inventoryItemCache[itemId] != scopes.avUtils.ITEM_TYPE.OutsourcedService) {
			return inventoryItemCache[itemId] ? inventoryItemCache[itemId].item_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations && 
				utils.hasRecords(rRecord.in_item_warehouse_to_in_item) && 
				rRecord.in_item_warehouse_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.OutsourcedService) {
			inventoryItemCache[itemId] = rRecord.in_item_warehouse_to_in_item;
			return rRecord.in_item_warehouse_to_in_item.item_code;
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && 
				utils.hasRecords(rRecord.in_item_doc_to_in_item) && 
				rRecord.in_item_doc_to_in_item.itemtype_code != scopes.avUtils.ITEM_TYPE.OutsourcedService) {
			inventoryItemCache[itemId] = rRecord.in_item_doc_to_in_item;
			return rRecord.in_item_doc_to_in_item.item_code;
		}
		return '';
	}

	function getWarehousePrimaryBinLocation(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations && utils.hasRecords(rRecord.in_item_warehouse_to_in_item_warehouse_location$primarylocation)) {
			return rRecord.in_item_warehouse_to_in_item_warehouse_location$primarylocation.whseloc_bin_location;
		}
		return '';
	}

	function getWarehouseEstUnitsUOMCode(rRecord, recordType) {
		var uomId = (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations && rRecord.itemwhse_estimate_cost_uom_id) ? rRecord.itemwhse_estimate_cost_uom_id.toString() : null;
		if (sysUnitOfMeasureCache.hasOwnProperty(uomId)) {
			return sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations && uomId) {
			sysUnitOfMeasureCache[uomId] = getUOMRecord(uomId);
			return (sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '');
		}
		return '';
	}
	
	function getReorderMethod(reOrderMethod) {
		var oReorderMethod = {
			'M': 'Min/Max',
			'E': 'EOQ/ROP'
		};
		
		if(oReorderMethod[reOrderMethod]) {
			return oReorderMethod[reOrderMethod];
		}
		
		return '';
	}

	function getWarehouseLocation(rRecord, recordType, locationType) {
		var whseIssueBinId = null;
		if (locationType == 'issue') {
			whseIssueBinId = rInItemWarehouse.itemwhse_default_issue_bin ? rInItemWarehouse.itemwhse_default_issue_bin.toString() : null;
		}
		else if (locationType == 'receipt') {
			whseIssueBinId = rInItemWarehouse.itemwhse_default_receipt_bin ? rInItemWarehouse.itemwhse_default_receipt_bin.toString() : null;
		}
		
		if (warehouseLocationCache.hasOwnProperty(whseIssueBinId)) {
			return warehouseLocationCache[whseIssueBinId] ? warehouseLocationCache[whseIssueBinId].whseloc_bin_location : '';
		}
		else if (whseIssueBinId && recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.InventoryItemsMultiLocations) {
			if (locationType == 'issue' && utils.hasRecords(rRecord.in_item_warehouse_to_in_warehouse_location$issue_bin)) {
				warehouseLocationCache[whseIssueBinId] = rRecord.in_item_warehouse_to_in_warehouse_location$issue_bin;
				return rRecord.in_item_warehouse_to_in_warehouse_location$issue_bin.whseloc_bin_location;
			} 
			else if (locationType == 'receipt' && utils.hasRecords(rRecord.in_item_warehouse_to_in_warehouse_location$receipt_bin)) {
				warehouseLocationCache[whseIssueBinId] = rRecord.in_item_warehouse_to_in_warehouse_location$receipt_bin;
				return rRecord.in_item_warehouse_to_in_warehouse_location$receipt_bin.whseloc_bin_location;
			}
		}
		
		return '';
	}

	function getItemDocumentInfo(rRecord, recordType) {
		var returnObj = {
			Title: null,
			FilePath: null
		};
		
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && utils.hasRecords(rRecord.in_item_doc_to_sys_document)) {
			returnObj.Title = rRecord.in_item_doc_to_sys_document.doc_title;
			returnObj.FilePath = rRecord.in_item_doc_to_sys_document.doc_path_storage;
		}
		return returnObj;
	}
	
	function getBOMItemCode(rRecord, recordType) {
		var itemId = null;
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && 
				utils.hasRecords(rRecord.in_item_doc_to_in_item_bill_of_material)) {
			itemId = rRecord.in_item_doc_to_in_item_bill_of_material.item_id ? rRecord.in_item_doc_to_in_item_bill_of_material.item_id.toString() : null;
		}

		if (inventoryItemCache.hasOwnProperty(itemId)) {
			return inventoryItemCache[itemId] ? inventoryItemCache[itemId].item_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && 
				utils.hasRecords(rRecord.in_item_doc_to_in_item_bill_of_material) && 
				utils.hasRecords(rRecord.in_item_bill_of_material_to_in_item)) {
			return rRecord.in_item_doc_to_in_item_bill_of_material.in_item_bill_of_material_to_in_item.item_code;
		}
		return '';
	}

	function getWorkTypeSectionDesc(rRecord, recordType) {
		var workTypeSectionId = (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && rRecord.worktypesection_id) ? rRecord.worktypesection_id.toString() : null;
		if (taskWorkTypeSectionCache.hasOwnProperty(workTypeSectionId)) {
			return taskWorkTypeSectionCache[workTypeSectionId] ? taskWorkTypeSectionCache[workTypeSectionId].worktypesection_desc : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && utils.hasRecords(rRecord.in_item_doc_to_sa_task_worktype_section)) {
			taskWorkTypeSectionCache[workTypeSectionId] = rRecord.in_item_doc_to_sa_task_worktype_section;
			return rRecord.in_item_doc_to_sa_task_worktype_section.worktypesection_desc;
		}
		return '';
	}
	
	function getDocumentMgmtCategory(rRecord, recordType) {
		var docsMgmtCatId = null;
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && utils.hasRecords(rRecord.in_item_doc_to_sys_document)) {
			docsMgmtCatId = (rRecord.in_item_doc_to_sys_document.docs_mgmt_category_id) ? rRecord.in_item_doc_to_sys_document.docs_mgmt_category_id.toString() : null;
		}
		
		if (docsManagementCategoryCache.hasOwnProperty(docsMgmtCatId)) {
			return docsManagementCategoryCache[docsMgmtCatId] ? docsManagementCategoryCache[docsMgmtCatId].category_name : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && 
				utils.hasRecords(rRecord.in_item_doc_to_sys_document) && 
				utils.hasRecords(rRecord.in_item_doc_to_sys_document.sys_document_to_docs_management_category)) {
			docsManagementCategoryCache[docsMgmtCatId] = rRecord.in_item_doc_to_sys_document.sys_document_to_docs_management_category;
			return rRecord.in_item_doc_to_sys_document.sys_document_to_docs_management_category.category_name;
		}
		return '';
	}

	function getEmployeeCode(rRecord, recordType) {
		var empId = null;
		if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments) {
			empId = rRecord.item_doc_approved_by ? rRecord.item_doc_approved_by.toString() : null;
		}
		
		if (sysEmployeeCache.hasOwnProperty(empId)) {
			return sysEmployeeCache[empId] ? sysEmployeeCache[empId].empl_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_INVENTORY_SUBTYPES.ItemDocuments && utils.hasRecords(rRecord.in_item_doc_to_sys_employee$item_doc_approved_by)) {
			sysEmployeeCache[empId] = rRecord.in_item_doc_to_sys_employee$item_doc_approved_by;
			return rRecord.in_item_doc_to_sys_employee$item_doc_approved_by.empl_code;
		}
		return '';
	}
}

/**
 * Export employee related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"DA8B8833-C568-43B9-AF7E-1FEBE1241E83"}
 */
function EmployeeExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sa_sales_person>} */
	var rSalesPerson;
	
	/**@type {JSRecord<db:/avanti/sys_employee>} */
	var rSysEmployee;
	

	if (rFSRecord && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps) {
		rSalesPerson = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees) {
		rSysEmployee = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		if (rSalesPerson && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps) {
			sOutput += (rSalesPerson.salesper_code != null ? rSalesPerson.salesper_code : '') + '\t';
			sOutput += getTerritoryCode(rSalesPerson, recordType) + '\t';
			sOutput += (rSalesPerson.salesper_name != null ? rSalesPerson.salesper_name : '') + '\t';
			sOutput += (getYesNo(rSalesPerson.salesper_active) != null ? getYesNo(rSalesPerson.salesper_active) : '') + '\t';
			sOutput += (rSalesPerson.salesper_comm != null ? rSalesPerson.salesper_comm : '') + '\t';
			sOutput += getCommissionType(rSalesPerson.salesper_comm_type) + '\t';
		}
		else if (rSysEmployee && subType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees) {
			sOutput += (rSysEmployee.empl_code != null ? rSysEmployee.empl_code : '') + '\t';
			sOutput += (rSysEmployee.empl_first_name != null ? rSysEmployee.empl_first_name : '') + '\t';
			sOutput += (rSysEmployee.empl_last_name != null ? rSysEmployee.empl_last_name : '') + '\t';
			sOutput += getEmployeeClassCode(rSysEmployee, recordType) + '\t';
			sOutput += (getYesNo(rSysEmployee.empl_active) != null ? getYesNo(rSysEmployee.empl_active) : '') + '\t';
			sOutput += getEmployeeDateFormat(rSysEmployee.empl_date_format) + '\t';
			sOutput += getSalesPersonCode(rSysEmployee, recordType) + '\t';
			sOutput += getAssignmentTypeDesc(rSysEmployee, recordType) + '\t';
		}
	}

	function getTerritoryCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.SalesReps && utils.hasRecords(rRecord.sa_sales_person_to_sa_territory)) {
			return rRecord.sa_sales_person_to_sa_territory.terr_code;
		}
		return '';
	}
	
	function getCommissionType(commissionType) {
		var oCommissionType = {
			1: 'Added Value',
			2: 'Sales Price'
		}
		if(oCommissionType[commissionType]) {
			return oCommissionType[commissionType];
		}
		return '';
	}
	
	function getEmployeeDateFormat(dateFormat) {
		var oDateFormat = {
			1: 'MMDDYYYY',
			2: 'DDMMYYYY'
		}
		if(oDateFormat[dateFormat]) {
			return oDateFormat[dateFormat];
		}
		return '';
	}

	function getEmployeeClassCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees && utils.hasRecords(rRecord.sys_employee_to_sys_employee_class)) {
			return rRecord.sys_employee_to_sys_employee_class.emplclass_code;
		}
		return '';
	}

	function getSalesPersonCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees && utils.hasRecords(rRecord.sys_employee_to_sa_sales_person)) {
			return rRecord.sys_employee_to_sa_sales_person.salesper_code;
		}
		return '';
	}
	
	function getAssignmentTypeDesc(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_EMPLOYEE_SUBTYPES.Employees && utils.hasRecords(rRecord.sys_employee_to_app_assignment_type)) {
			return rRecord.sys_employee_to_app_assignment_type.assignment_desc;
		}
		return '';
	}
}

/**
 * Export Note related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"636F87D9-CC0A-4436-9A1F-010490F4631E"}
 */
function NoteExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sys_note>} */
	var rSysNote;

	if (rFSRecord) {
		rSysNote = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		if (rSysNote && (rSysNote.note_object_source_type == 'Customer' || rSysNote.note_object_source_type == 'Supplier')) {
			sOutput += (rSysNote.note_creation_date != null ? scopes.avDate.formatDate(rSysNote.note_creation_date,'YYYYMMDD') : '') + '\t';
			sOutput += getEmployeeCode(rSysNote, 'created') + '\t';
			sOutput += (rSysNote.note_last_modified_date != null ? scopes.avDate.formatDate(rSysNote.note_last_modified_date,'YYYYMMDD') : '') + '\t';
			sOutput += getEmployeeCode(rSysNote, 'modified') + '\t';
			//First replace removes tabs and new line characters from the string
			//Second replace removes quotations from beginning and end of the string
			sOutput += (rSysNote.note_text != null ? rSysNote.note_text.replace(/[\t\n\r]/gm,' ').trim().replace(/(^"*|"*$)/g, '') : '') + '\t';
			sOutput += getNoteTypeDesc(rSysNote) + '\t';
			sOutput += (rSysNote.note_title != null ? rSysNote.note_title.replace(/[\t\n\r]/gm,' ') : '') + '\t';
			sOutput += (getYesNo(rSysNote.note_internal_only) != null ? getYesNo(rSysNote.note_internal_only) : '') + '\t';
			if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact) {
				sOutput += getCustomerCode(rSysNote, recordType) + '\t';
				sOutput += getContactFirstAndLastName(rSysNote, recordType) + '\t';
			}
			else if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate || recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder) {
				sOutput += getCustomerCode(rSysNote, recordType) + '\t';
				sOutput += getOrderDocumentNumber(rSysNote, recordType) + '\t';
			}
			else if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead) {
				sOutput += getCustomerCode(rSysNote, recordType) + '\t';
				sOutput += getCRMLeadNumber(rSysNote, recordType) + '\t';
			}
			else if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier) {
				sOutput += getSupplierCode(rSysNote, recordType) + '\t';
			}
			else {
				sOutput += getCustomerCode(rSysNote, recordType) + '\t';
			}
		}
	}

	function getEmployeeCode(rRecord, fieldType) {
		var empId = null;
		if (fieldType == 'created') {
			empId = rRecord.note_creation_empl_id ? rRecord.note_creation_empl_id.toString() : null;
		}
		else if (fieldType == 'modified') {
			empId = rRecord.note_last_modified_empl_id ? rRecord.note_last_modified_empl_id.toString() : null;
		}
		
		if (sysEmployeeCache.hasOwnProperty(empId)) {
			return sysEmployeeCache[empId] ? sysEmployeeCache[empId].empl_code : '';
		} 
		else if (fieldType == 'created' && utils.hasRecords(rRecord.sys_note_to_sys_employee)) {
			sysEmployeeCache[empId] = rRecord.sys_note_to_sys_employee;
			return rRecord.sys_note_to_sys_employee.empl_code;
		}
		else if (fieldType == 'modified' && utils.hasRecords(rRecord.sys_note_to_sys_employee$note_last_modified_empl_id)) {
			sysEmployeeCache[empId] = rRecord.sys_note_to_sys_employee$note_last_modified_empl_id;
			return rRecord.sys_note_to_sys_employee$note_last_modified_empl_id.empl_code;
		}
		return '';
	}

	function getNoteTypeDesc(rRecord) {
		if (rRecord.sys_note_to_sys_note_type) {
			return rRecord.sys_note_to_sys_note_type.notetype_desc
		}
		return '';
	}

	function getCustomerCode(rRecord, recordType) {
		if (utils.hasRecords(rRecord.sys_note_to_sa_customer)) {
			return rRecord.sys_note_to_sa_customer.cust_code;
		}
		return '';
	}

	function getContactFirstAndLastName(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.CustomerContact && utils.hasRecords(rRecord.sys_note_to_sys_contact)) {
			return rRecord.sys_note_to_sys_contact.contact_first_and_last;
		}
		return '';
	}

	function getOrderDocumentNumber(rRecord, recordType) {
		if ( (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Estimate || recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.SalesOrder) && 
				utils.hasRecords(rRecord.sys_note_to_sa_order_revision_header) && 
				utils.hasRecords(rRecord.sys_note_to_sa_order_revision_header.sa_order_revision_header_to_sa_order)) {
			return rRecord.sys_note_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_document_num;
		} 
		return '';
	}

	function getCRMLeadNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Lead && utils.hasRecords(rRecord.sys_note_to_crm_lead)) {
			return rRecord.sys_note_to_crm_lead.lead_number;
		} 
		return '';
	}

	function getSupplierCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_NOTES_SUBTYPES.Supplier && utils.hasRecords(rRecord.sys_note_to_ap_supplier)) {
			return rRecord.sys_note_to_ap_supplier.supplier_code;
		} 
		return '';
	}
}

/**
 * Export CRM related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * @properties={typeid:24,uuid:"DFFB771F-B990-4817-A5E2-3486A26E7DC7"}
 */
function CRMExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/crm_lead>} */
	var rCRMLead;
	
	/**@type {JSRecord<db:/avanti/crm_activity>} */
	var rCRMActivity;

	if (rFSRecord && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads) {
		rCRMLead = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities) {
		rCRMActivity = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		if (rCRMLead && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads) {
			sOutput += (rCRMLead.lead_number != null ? rCRMLead.lead_number : '') + '\t';
			sOutput += getCustomerCode(rCRMLead, recordType) + '\t';
			sOutput += (rCRMLead.lead_date != null ? scopes.avDate.formatDate(rCRMLead.lead_date,'YYYYMMDD', 24).replace(/:/g,'') : '') + '\t';
			sOutput += getContactFirstAndLastName(rCRMLead, recordType) + '\t';
			sOutput += (rCRMLead.lead_date_to_rep != null ? scopes.avDate.formatDate(rCRMLead.lead_date_to_rep,'YYYYMMDD',24).replace(/:/g,'') : '') + '\t';
			sOutput += getSalesRepCode(rCRMLead, recordType, 'sales_person') + '\t';
			sOutput += '\t';
			sOutput += (getYesNo(rCRMLead.is_active) != null ? getYesNo(rCRMLead.is_active) : '') + '\t';
			sOutput += getOrderDocumentNumber(rCRMLead, recordType, 'estimate') + '\t';
			sOutput += getCRMLeadStageDesc(rCRMLead, recordType) + '\t';
			sOutput += (rCRMLead.created_date != null ? scopes.avDate.formatDate(rCRMLead.created_date,'YYYYMMDD',24).replace(/:/g,'') : '') + '\t';
			sOutput += getEmployeeCode(rCRMLead, recordType, 'created') + '\t';
			sOutput += (rCRMLead.modified_date != null ? scopes.avDate.formatDate(rCRMLead.modified_date,'YYYYMMDD',24).replace(/:/g,'') : '') + '\t';
			sOutput += getEmployeeCode(rCRMLead, recordType, 'modified') + '\t';
			sOutput += getCRMLeadRatingDesc(rCRMLead, recordType, 'lead_rating') + '\t';
			sOutput += getCRMLeadRatingDesc(rCRMLead, recordType, 'lead_rating_after') + '\t';
			sOutput += getSalesRepCode(rCRMLead, recordType, 'lead_owner') + '\t';
			sOutput += getCRMLeadSourceDesc(rCRMLead, recordType) + '\t';
		}
		else if (rCRMActivity && subType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities) {
			sOutput += (rCRMActivity.activity_type != null ? rCRMActivity.activity_type : '') + '\t';
			sOutput += (rCRMActivity.activity_subject != null ? rCRMActivity.activity_subject : '') + '\t';
			sOutput += (rCRMActivity.activity_regarding != null ? rCRMActivity.activity_regarding : '') + '\t';
			sOutput += getEmployeeCode(rCRMActivity, recordType, 'assignment_employee') + '\t';
			sOutput += getCustomerCode(rCRMActivity, recordType) + '\t';
			sOutput += getContactFirstAndLastName(rCRMActivity, recordType) + '\t';
			sOutput += (rCRMActivity.activity_priority != null ? rCRMActivity.activity_priority : '') + '\t';
			sOutput += (rCRMActivity.activity_end_datetime != null ? scopes.avDate.formatDate(rCRMActivity.activity_end_datetime,'YYYYMMDD',24).replace(/:/g,'') : '') + '\t';
			sOutput += getOrderDocumentNumber(rCRMActivity, recordType, 'estimate') + '\t';
			sOutput += getOrderDocumentNumber(rCRMActivity, recordType, 'order') + '\t';
			sOutput += getProdJobNumber(rCRMActivity, recordType) + '\t';
			sOutput += getPackingSlipNumber(rCRMActivity, recordType) + '\t';
			sOutput += (rCRMActivity.activity_status != null ? rCRMActivity.activity_status : '') + '\t';
			sOutput += (rCRMActivity.activity_due_datetime != null ? scopes.avDate.formatDate(rCRMActivity.activity_due_datetime,'YYYYMMDD',24).replace(/:/g,'') : '') + '\t';
			sOutput += (getYesNo(rCRMActivity.activity_objective_complete) != null ? getYesNo(rCRMActivity.activity_objective_complete) : '') + '\t';
			sOutput += getActivityObjective(rCRMActivity, recordType) + '\t';
			sOutput += getSysNote(rCRMActivity, recordType) + '\t';
			sOutput += (rCRMActivity.created_date != null ? scopes.avDate.formatDate(rCRMActivity.created_date,'YYYYMMDD') : '') + '\t';
			sOutput += getEmployeeCode(rCRMActivity, recordType, 'created') + '\t';
			sOutput += getLeadNumber(rCRMActivity, recordType) + '\t';
		}
	}

	function getCustomerCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && utils.hasRecords(rRecord.crm_lead_to_sa_customer)) {
			return rRecord.crm_lead_to_sa_customer.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_sa_customer)) {
			return rRecord.crm_activity_to_sa_customer.cust_code;
		}
		return '';
	}
	
	function getContactFirstAndLastName(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && 
				utils.hasRecords(rRecord.crm_lead_to_sa_customer_contact) && 
				utils.hasRecords(rRecord.crm_lead_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
			return rRecord.crm_lead_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && 
				utils.hasRecords(rRecord.crm_activity_to_sa_customer_contact) && 
				utils.hasRecords(rRecord.crm_activity_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
			return rRecord.crm_activity_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
		} 
		return '';
	}
	
	function getSalesRepCode(rRecord, recordType, field) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && field == 'sales_person' && utils.hasRecords(rRecord.crm_lead_to_sa_sales_person)) {
			return rRecord.crm_lead_to_sa_sales_person.salesper_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && field == 'lead_owner' && utils.hasRecords(rRecord.crm_lead_to_sa_sales_person$lead_owner_id)) {
			return rRecord.crm_lead_to_sa_sales_person$lead_owner_id.salesper_code;
		}
		return '';
	}

	function getOrderDocumentNumber(rRecord, recordType, field) {
		if (field == 'estimate' && recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && utils.hasRecords(rRecord.crm_lead_to_sa_order)) {
			return rRecord.crm_lead_to_sa_order.ordh_document_num;
		}
		else if (field == 'estimate' && recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_sa_order$est)) {
			return rRecord.crm_activity_to_sa_order$est.ordh_document_num;
		}
		else if (field == 'order' && recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_sa_order)) {
			return rRecord.crm_activity_to_sa_order.ordh_document_num;
		}
		return '';
	}
	
	function getCRMLeadStageDesc(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && utils.hasRecords(rRecord.crm_lead_to_crm_lead_stage)) {
			return rRecord.crm_lead_to_crm_lead_stage.lead_stage_desc;
		} 
		return '';
	}

	function getEmployeeCode(rRecord, recordType, fieldType) {
		var empId = null;
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && fieldType == 'created') {
			empId = rRecord.created_by_id ? rRecord.created_by_id.toString() : null;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && fieldType == 'modified') {
			empId = rRecord.modified_by_id ? rRecord.modified_by_id.toString() : null;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && fieldType == 'assignment_employee') {
			empId = rRecord.activity_assignment_empl_id ? rRecord.activity_assignment_empl_id.toString() : null;
		}
		
		if (sysEmployeeCache.hasOwnProperty(empId)) {
			return sysEmployeeCache[empId] ? sysEmployeeCache[empId].empl_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && fieldType == 'created' && utils.hasRecords(rRecord.crm_lead_to_sys_employee$created_by_id)) {
			sysEmployeeCache[empId] = rRecord.crm_lead_to_sys_employee$created_by_id;
			return rRecord.crm_lead_to_sys_employee$created_by_id.empl_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && fieldType == 'modified' && utils.hasRecords(rRecord.crm_lead_to_sys_employee$modified_by_id)) {
			sysEmployeeCache[empId] = rRecord.crm_lead_to_sys_employee$modified_by_id;
			return rRecord.crm_lead_to_sys_employee$modified_by_id.empl_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && fieldType == 'assignment_employee' && utils.hasRecords(rRecord.crm_activity_to_sys_employee)) {
			sysEmployeeCache[empId] = rRecord.crm_activity_to_sys_employee;
			return rRecord.crm_activity_to_sys_employee.empl_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && fieldType == 'created' && utils.hasRecords(rRecord.crm_activity_to_sys_employee$created_by_id)) {
			sysEmployeeCache[empId] = rRecord.crm_activity_to_sys_employee$created_by_id;
			return rRecord.crm_activity_to_sys_employee$created_by_id.empl_code;
		}
		return '';
	}
	
	function getCRMLeadRatingDesc(rRecord, recordType, field) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && field == 'lead_rating' && utils.hasRecords(rRecord.crm_lead_to_crm_lead_rating)) {
			return rRecord.crm_lead_to_crm_lead_rating.lead_rating_desc;
		}
		else if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && field == 'lead_rating_after' && utils.hasRecords(rRecord.crm_lead_to_crm_lead_rating_after_followup)) {
			return rRecord.crm_lead_to_crm_lead_rating_after_followup.lead_rating_desc;
		}
		return '';
	}
	
	function getCRMLeadSourceDesc(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Leads && utils.hasRecords(rRecord.crm_lead_to_crm_lead_source)) {
			return rRecord.crm_lead_to_crm_lead_source.lead_source_desc;
		} 
		return '';
	}

	function getProdJobNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_prod_job)) {
			return rRecord.crm_activity_to_prod_job.job_number;
		} 
		return '';
	}
	
	function getPackingSlipNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_sa_pack)) {
			return rRecord.crm_activity_to_sa_pack.pack_doc_number;
		} 
		return '';
	}
	
	function getActivityObjective(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_crm_activity_objective)) {
			return rRecord.crm_activity_to_crm_activity_objective.objective;
		} 
		return '';
	}
	
	function getSysNote(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_sys_note)) {
			return rRecord.crm_activity_to_sys_note.note_text != null ? rRecord.crm_activity_to_sys_note.note_text.replace(/[\t\n\r]/gm,' ').trim().replace(/(^"*|"*$)/g, '') : null;
		} 
		return '';
	}
	
	function getLeadNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_CRM_SUBTYPES.Activities && utils.hasRecords(rRecord.crm_activity_to_crm_lead)) {
			return rRecord.crm_activity_to_crm_lead.lead_number;
		} 
		return '';
	}
}


/**
 * Export UDF Question related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"39445DDD-2066-471C-9AF8-8EE180B81543"}
 */
function UDFQuestionExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sys_udf_type>} */
	var rUDFType;
	
	/**@type {JSRecord<db:/avanti/sys_udf_type_table_values>} */
	var rUDFTypeTableValue;

	if (rFSRecord && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions) {
		rUDFType = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions) {
		rUDFTypeTableValue = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		if (rUDFType && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions) {
			sOutput += (rUDFType.udf_code != null ? rUDFType.udf_code : '') + '\t';
			sOutput += (rUDFType.udf_field != null ? rUDFType.udf_field : '') + '\t';
			sOutput += (rUDFType.udf_field_type != null ? rUDFType.udf_field_type : '') + '\t';
			sOutput += getParentUDFField(rUDFType, recordType) + '\t';
		}
		else if (rUDFTypeTableValue && subType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions) {
			sOutput += getUDFCategory(rUDFTypeTableValue, recordType) + '\t';
			sOutput += getUDFField(rUDFTypeTableValue, recordType) + '\t';
			sOutput += (rUDFTypeTableValue.udf_table_value != null ? rUDFTypeTableValue.udf_table_value : '') + '\t';
		}
	}

	function getParentUDFField(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestions && utils.hasRecords(rRecord.sys_udf_type_to_sys_udf_type$parent_sys_udf_type_id)) {
			return rRecord.sys_udf_type_to_sys_udf_type$parent_sys_udf_type_id.udf_field;
		} 
		return '';
	}

	function getUDFCategory(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions && utils.hasRecords(rRecord.sys_udf_type_table_values_to_sys_udf_type)) {
			return rRecord.sys_udf_type_table_values_to_sys_udf_type.udf_code;
		} 
		return '';
	}
	
	function getUDFField(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_UDF_QUESTIONS_SUBTYPES.UDFQuestionTblOptions && utils.hasRecords(rRecord.sys_udf_type_table_values_to_sys_udf_type)) {
			return rRecord.sys_udf_type_table_values_to_sys_udf_type.udf_field;
		} 
		return '';
	}
}


/**
 * Export UDF Answer related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"EC6066A4-1943-4594-8224-03797AAEA801"}
 */
function UDFAnswerExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sys_udf_values>} */
	var rUDFValue;

	if (rFSRecord && 
			(subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer || 
					subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact || 
					subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item || 
					subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers)) {
		rUDFValue = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		sOutput += getUDFField(rUDFValue) + '\t'; // UDF Descr
		sOutput += (rUDFValue.udf_answer != null ? rUDFValue.udf_answer : '') + '\t'; // UDF Answer
		sOutput += getUniqueIdCode(rUDFValue, recordType) + '\t'; // Customer Code or Item Code or Supplier Code
		if (rUDFValue && subType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact) {
			sOutput += getContactFirstAndLast(rUDFValue, recordType) + '\t'; // Contact First and Last name
		}
	}
	
	function getUDFField(rRecord) {
		if (utils.hasRecords(rRecord.sys_udf_values_to_sys_udf_type)) {
			return rRecord.sys_udf_values_to_sys_udf_type.udf_field;
		} 
		return '';
	}

	function getUniqueIdCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Customer && utils.hasRecords(rRecord.sys_udf_values_to_sa_customer$unique_id)) {
			return rRecord.sys_udf_values_to_sa_customer$unique_id.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact && 
				utils.hasRecords(rRecord.sys_udf_values_to_sys_contact$unique_id) && 
				utils.hasRecords(rRecord.sys_udf_values_to_sys_contact$unique_id.sys_contact_to_sa_customer_contact) && 
				utils.hasRecords(rRecord.sys_udf_values_to_sys_contact$unique_id.sys_contact_to_sa_customer_contact.sa_customer_contact_to_sa_customer)) {
			return rRecord.sys_udf_values_to_sys_contact$unique_id.sys_contact_to_sa_customer_contact.sa_customer_contact_to_sa_customer.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Item && utils.hasRecords(rRecord.sys_udf_values_to_in_item$unique_id)) {
			return rRecord.sys_udf_values_to_in_item$unique_id.item_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.Suppliers && utils.hasRecords(rRecord.sys_udf_values_to_ap_supplier$unique_id)) {
			return rRecord.sys_udf_values_to_ap_supplier$unique_id.supplier_code;
		}
		return '';
	}
	
	function getContactFirstAndLast(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_UDF_ANSWERS_SUBTYPES.CustomerContact && utils.hasRecords(rRecord.sys_udf_values_to_sys_contact$unique_id)) {
			return rRecord.sys_udf_values_to_sys_contact$unique_id.contact_first_and_last;
		}
		return '';
	}
}


/**
 * Export UDF Answer related data
 * @param {JSRecord} rFSRecord 
 * @param {String} subType
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"83A8BAC6-5AFD-47DC-9ABF-C3534446B19E"}
 */
function TaxExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sys_sales_tax_item>} */
	var rSysTaxItem;
	
	/**@type {JSRecord<db:/avanti/sys_sales_tax_group>} */
	var rSysTaxGroup;
	
	/**@type {JSRecord<db:/avanti/sys_sales_tax_rate>} */
	var rSysTaxRate;

	if (rFSRecord && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems) {
		rSysTaxItem = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups) {
		rSysTaxGroup = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates) {
		rSysTaxRate = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	if (sOutput) {
		sOutput += '\n';
	}
	
	return sOutput;

	function buildLine(recordType) {
		if (rSysTaxItem && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems) {
			sOutput += (rSysTaxItem.taxitem_code != null ? rSysTaxItem.taxitem_code : '') + '\t';
			sOutput += getTaxItemType(rSysTaxItem.taxitem_type) + '\t';
			sOutput += (rSysTaxItem.taxitem_desc != null ? rSysTaxItem.taxitem_desc : '') + '\t';
			sOutput += (rSysTaxItem.taxitem_reg_number != null ? rSysTaxItem.taxitem_reg_number : '') + '\t';
			sOutput += getGLAccountNumber(rSysTaxItem, recordType) + '\t';
			sOutput += getTaxBasedOn(rSysTaxItem.taxitem_based_on) + '\t';
			sOutput += getTaxItemCode(rSysTaxItem.taxitem_based_on_taxitem) + '\t';
			sOutput += (rSysTaxItem.taxitem_rounding_rule == 'UP' ? 'Up to the Next Currency Decimal Digit' : '') + '\t';
			sOutput += (getYesNo(rSysTaxItem.taxitem_taxable_tax) != null ? getYesNo(rSysTaxItem.taxitem_taxable_tax) : '') + '\t';
			sOutput += (getYesNo(rSysTaxItem.taxitem_active) != null ? getYesNo(rSysTaxItem.taxitem_active) : '') + '\t';
			sOutput += (getYesNo(rSysTaxItem.taxitem_included_tax) != null ? getYesNo(rSysTaxItem.taxitem_included_tax) : '') + '\t';
			sOutput += (getYesNo(rSysTaxItem.taxitem_refundable_tax) != null ? getYesNo(rSysTaxItem.taxitem_refundable_tax) : '') + '\t';
		}
		else if (rSysTaxGroup && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups) {
			sOutput += (rSysTaxGroup.taxgroup_code != null ? rSysTaxGroup.taxgroup_code : '') + '\t';
			sOutput += (rSysTaxGroup.taxgroup_desc != null ? rSysTaxGroup.taxgroup_desc : '') + '\t';
			sOutput += (getYesNo(rSysTaxGroup.taxgroup_active) != null ? getYesNo(rSysTaxGroup.taxgroup_active) : '') + '\t';
			sOutput += (getYesNo(rSysTaxGroup.taxgroup_shipping_taxable) != null ? getYesNo(rSysTaxGroup.taxgroup_shipping_taxable) : '') + '\t';
			sOutput += getTaxGroupItemCodes(rSysTaxGroup, recordType) + '\t';
		}
		else if (rSysTaxRate && subType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItemRates) {
			sOutput += getTaxItemCode(rSysTaxRate.taxitem_id) + '\t';
			sOutput += (rSysTaxRate.taxrate_effective_date != null ? scopes.avDate.formatDate(rSysTaxRate.taxrate_effective_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rSysTaxRate.taxrate_percent != null ? (rSysTaxRate.taxrate_percent * 100).toFixed(5) : '') + '\t';
		}
	}
	
	function getTaxItemType(taxItemType) {
		var oTaxItemTypes = {
			'S': 'Sales',
			'P': 'Purchases'
		}
		if(oTaxItemTypes[taxItemType]) {
			return oTaxItemTypes[taxItemType];
		}
		return '';
	}

	function getGLAccountNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxItems && utils.hasRecords(rRecord.sys_sales_tax_item_to_gl_account_segment)) {
			return rRecord.sys_sales_tax_item_to_gl_account_segment.glacctseg_number;
		}
		return '';
	}
	
	function getTaxBasedOn(taxBasedOn) {
		var oTaxBasedOn = {
			'P': 'Percentage of Sales/Purchase',
			'PI': 'Percentage of Another Tax Item',
			'TI':'Tax Included with Item Price'
		}
		if(oTaxBasedOn[taxBasedOn]) {
			return oTaxBasedOn[taxBasedOn];
		}
		return '';
	}
	
	function getTaxItemCode(taxItemId) {
		if (!taxItemId) {
			return '';
		}
		var sSQL = "SELECT taxitem_id \
					  FROM sys_sales_tax_item \
					 WHERE org_id = ? \
					   AND taxitem_id = ?";
		/**@type {JSRecord<db:/avanti/sys_sales_tax_item>} */
		var rTaxItem = scopes.avDB.getRecFromSQL(sSQL,'sys_sales_tax_item',[globals.org_id.toString(), taxItemId.toString().trim()]);
		if (rTaxItem && rTaxItem.taxitem_code) {
			return rTaxItem.taxitem_code;
		}
		return '';
	}

	function getTaxGroupItemCodes(rRecord, recordType) {
		var str = '';
		if (recordType == scopes.avDataExport.ENUM_TAXES_SUBTYPES.TaxGroups && utils.hasRecords(rRecord.sys_sales_tax_group_to_sys_sales_tax_group_item)) {
			for(var i = 1; i <= rRecord.sys_sales_tax_group_to_sys_sales_tax_group_item.getSize(); i++) {
				/**@type {JSRecord<db:/avanti/sys_sales_tax_group_item>} */
				var record = rRecord.sys_sales_tax_group_to_sys_sales_tax_group_item.getRecord(i);
				if (i > 1) {
					str += '\t';
				}
				str += getTaxItemCode(record.taxitem_id);
			}
		}
		return str;
	}
}


/**
 * Export historical invoice related data
 * @param {JSRecord} rFSRecord 
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"2AE74920-9F17-4FFB-8AAC-2BCEF4003225"}
 */
function InvoiceExport(rFSRecord) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sa_invoice_register>} */
	var rInvoiceReg;

	if (rFSRecord) {
		rInvoiceReg = rFSRecord;
	}
	else {
		return null;
	}
	
	for (var i = 1; i <= rInvoiceReg.sa_invoice_register_to_sa_invoice$inv_historical.getSize(); i++) {
		if (i > 1) {
			sOutput += '\n';
		}
		buildLine(rInvoiceReg.sa_invoice_register_to_sa_invoice$inv_historical.getRecord(i));
	}
	
	if (sOutput) {
		sOutput += '\n';
	}

	return sOutput;
	
	function buildLine(rInvoice) {
		if (rInvoice) {
			//Loop through each detail line if there are more than one
			for (var c = 1; c <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); c++) {
				/**@type {JSRecord<db:/avanti/sa_invoice_det>} */
				var rInvDet = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(c);
				if (c > 1) {
					sOutput += '\n';
				}
				sOutput += getCustomerCode(rInvoice) + '\t';
				sOutput += (rInvoice.inv_date != null ? scopes.avDate.formatDate(rInvoice.inv_date, 'YYYYMMDD') : '') + '\t';
				sOutput += (rInvoice.inv_number != null ? rInvoice.inv_number : '') + '\t';
				sOutput += getItemCode(rInvDet) + '\t';
				sOutput += (rInvoice.inv_total_amt != null ? rInvoice.inv_total_amt : '') + '\t';
			}
		}
	}

	function getCustomerCode(rRecord) {
		if (rRecord && utils.hasRecords(rRecord.sa_invoice_to_sa_customer)) {
			return rRecord.sa_invoice_to_sa_customer.cust_code;
		}
		return '';
	}
	
	function getItemCode(rInvoiceDet) {
		if (rInvoiceDet && rInvoiceDet.getDataSource() == 'db:/avanti/sa_invoice_det' && utils.hasRecords(rInvoiceDet.sa_invoice_det_to_in_item)) {
			return rInvoiceDet.sa_invoice_det_to_in_item.item_code;
		}
		return '';
	}
}

/**
 * Export project related data
 * @param {JSRecord} rFSRecord 
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"BC0DCE5C-8371-4786-999E-EFBE3C2B4281"}
 */
function ProjectExport(rFSRecord) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sa_customer_project>} */
	var rCustProject;

	if (rFSRecord) {
		rCustProject = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine();
	
	if (sOutput) {
		sOutput += '\n';
	}

	return sOutput;
	
	function buildLine() {
		if (rCustProject) {
			sOutput += (rCustProject.custproj_desc != null ? rCustProject.custproj_desc : '') + '\t';
			sOutput += getCustomerCode(rCustProject) + '\t';
			sOutput += (getYesNo(rCustProject.project_controls_tax) != null ? getYesNo(rCustProject.project_controls_tax) : '') + '\t';
			sOutput += getSalesTaxOption(rCustProject.custproj_salestax_option) + '\t';
			sOutput += getTaxExemptReason(rCustProject) + '\t';
		}
	}

	function getCustomerCode(rRecord) {
		if (rRecord && utils.hasRecords(rRecord.sa_customer_project_to_sa_customer)) {
			return rRecord.sa_customer_project_to_sa_customer.cust_code;
		}
		return '';
	}
	
	function getSalesTaxOption(taxOption) {
		var oTaxOption = {
			'C': 'Based on Customer/Ship-To',
			'Y': 'Based on Tax-Type',
			'T': 'Taxable',
			'N': 'Non-Taxable'
		}
		if(oTaxOption[taxOption]) {
			return oTaxOption[taxOption];
		}
		return '';
	}
	
	function getTaxGroupCode(rRecord) {
		if (rRecord && utils.hasRecords(rRecord.sa_customer_project_to_sys_sales_tax_group)) {
			return rRecord.sa_customer_project_to_sys_sales_tax_group.taxgroup_code;
		}
		return '';
	}
	
	function getTaxExemptReason(rRecord) {
		if (rRecord && utils.hasRecords(rRecord.sa_customer_project_to_sys_tax_exemption_reason)) {
			return rRecord.sa_customer_project_to_sys_tax_exemption_reason.tax_exemption_code;
		}
		return '';
	}
}

/**
 * Export Sales Order related data
 * @param {JSRecord} rFSRecord
 * @param {String} subType
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"FDD148BE-DB3D-4B54-8223-A63FF65896E8"}
 */
function SalesOrderExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/sa_order>} */
	var rOrder;
	
	/**@type {JSRecord<db:/avanti/sa_order_revision_detail>} */
	var rOrdRevd;
	
	/**@type {JSRecord<db:/avanti/sa_order_revh_multi_ship>} */
	var rOrdRevhMultiShip;
	
	/**@type {JSRecord<db:/avanti/sa_order_revd_multi_ship_qty>} */
	var rOrdRevhMultiShipQty;

	if (rFSRecord && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders) {
		rOrder = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl) {
		rOrdRevd = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip) {
		rOrdRevhMultiShip = rFSRecord;
	}
	else if (rFSRecord && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty) {
		rOrdRevhMultiShipQty = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	
	if (sOutput) {
		sOutput += '\n';
	}

	return sOutput;
	
	function buildLine(recordType) {
		/**@type {JSRecord<db:/avanti/sa_order_revision_header>} */
		var rOrdRevh;
		/**@type {JSRecord<db:/avanti/sa_customer_address>} */
		var rCustAddr;
		/**@type {JSRecord<db:/avanti/sa_customer_address>} */
		var rCustAddrBillTo;
		/**@type {JSRecord<db:/avanti/sa_customer_address>} */
		var rCustAddrShipTo;
		/**@type {JSRecord<db:/avanti/sa_order_address>} */
		var rOrdAddrBillTo;
		/**@type {JSRecord<db:/avanti/sa_order_address>} */
		var rOrdAddrShipTo;
		/**@type {JSRecord<db:/avanti/sys_address>} */
		var rOrdAddrBillToSysAddress;
		/**@type {JSRecord<db:/avanti/sys_address>} */
		var rOrdAddrShipToSysAddress;
		
		if (rOrder && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders) {
			rOrdRevh = getOrderRevisionHeader(rOrder, recordType);
			rCustAddr = getCustomerAddress(rOrder.ordh_custaddr_id);
			rCustAddrBillTo = getCustomerAddress(rOrder.ordh_billto_custaddr_id);
			rCustAddrShipTo = getCustomerAddress(rOrder.ordh_shipto_custaddr_id);
			rOrdAddrBillTo = getOrderAddress(rOrder.ordh_id, 'billto');
			rOrdAddrShipTo = getOrderAddress(rOrder.ordh_id, 'shipto');
			rOrdAddrBillToSysAddress = getSysAddress((rOrdAddrBillTo ? rOrdAddrBillTo.addr_id : null));
			rOrdAddrShipToSysAddress = getSysAddress((rOrdAddrShipTo ? rOrdAddrShipTo.addr_id : null));
			
			sOutput += (rOrder.ordh_document_num != null ? rOrder.ordh_document_num : '') + '\t';
			sOutput += getCustomerCode(rOrder, recordType) + '\t';
			sOutput += (rCustAddrShipTo && rCustAddrShipTo.custaddr_code != null ? rCustAddrShipTo.custaddr_code : '') + '\t';
			sOutput += (rCustAddrBillTo && rCustAddrBillTo.custaddr_code != null ? rCustAddrBillTo.custaddr_code : '') + '\t';
			sOutput += (rOrder.ordh_description != null ? rOrder.ordh_description : '') + '\t';
			sOutput += getCurrencyCode(rOrder, recordType) + '\t';
			sOutput += getTerritoryCode(rOrder, recordType) + '\t';
			sOutput += (rOrder.ordh_order_date != null ? scopes.avDate.formatDate(rOrder.ordh_order_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrder.ordh_customer_po != null ? rOrder.ordh_customer_po : '') + '\t';
			sOutput += getCampaignCode(rOrder, recordType) + '\t';
			sOutput += getRushCode(rOrder, recordType) + '\t';
			sOutput += getCustProjectCode(rOrder, recordType) + '\t';
			sOutput += getOrderTypeCode(rOrder, recordType) + '\t';
			sOutput += getSalesPersonCode(rOrder, recordType) + '\t';
			sOutput += getEmployeeCode(rOrder, recordType) + '\t';
			sOutput += getPlantCode(rOrder, recordType) + '\t';
			sOutput += (rOrder.ordh_document_stream != null ? rOrder.ordh_document_stream : '') + '\t';
			sOutput += getShipMethodCode(rOrder, recordType) + '\t';
			sOutput += getContactFirstAndLastName(rOrder) + '\t';
			sOutput += (rOrder.ordh_priority != null ? rOrder.ordh_priority : '') + '\t';
			sOutput += (rOrder.ordh_staging_location != null ? rOrder.ordh_staging_location : '') + '\t';
			sOutput += getPayMethodCode(rOrder, recordType) + '\t';
			sOutput += (rOrder.ordh_chargeback_code != null ? rOrder.ordh_chargeback_code : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_order_status != null ? rOrdRevh.ordrevh_order_status : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_promise_date != null ? scopes.avDate.formatDate(rOrdRevh.ordrevh_promise_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_expected_date != null ? scopes.avDate.formatDate(rOrdRevh.ordrevh_expected_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_exchange_rate != null ? rOrdRevh.ordrevh_exchange_rate : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_order_qty != null ? rOrdRevh.ordrevh_total_order_qty : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_ship_qty != null ? rOrdRevh.ordrevh_total_ship_qty : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_backorder_qty != null ? rOrdRevh.ordrevh_total_backorder_qty : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_subtotal_amount != null ? rOrdRevh.ordrevh_subtotal_amount : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_markup_pct != null ? rOrdRevh.ordrevh_markup_pct : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_markup_amount != null ? rOrdRevh.ordrevh_markup_amount : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_shipping_amount != null ? rOrdRevh.ordrevh_shipping_amount : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_warehouse_amount != null ? rOrdRevh.ordrevh_warehouse_amount : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_rush_amount != null ? rOrdRevh.ordrevh_rush_amount : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_amount != null ? rOrdRevh.ordrevh_total_amount : '') + '\t';
			sOutput += (rOrdRevh && getYesNo(rOrdRevh.ordrevh_onhold) != null ? getYesNo(rOrdRevh.ordrevh_onhold) : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_taxes != null ? rOrdRevh.ordrevh_total_taxes : '') + '\t';
			sOutput += (rOrdRevh && getYesNo(rOrdRevh.ordrevh_is_active) != null ? getYesNo(rOrdRevh.ordrevh_is_active) : '') + '\t';
			sOutput += (rOrdRevh && rOrdRevh.ordrevh_total_commission != null ? rOrdRevh.ordrevh_total_commission : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_name != null ? rOrdAddrBillTo.ordaddr_name : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone1 != null ? rOrdAddrBillTo.ordaddr_phone1 : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone1_ext != null ? rOrdAddrBillTo.ordaddr_phone1_ext : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone2 != null ? rOrdAddrBillTo.ordaddr_phone2 : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone2_ext != null ? rOrdAddrBillTo.ordaddr_phone2_ext : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone3 != null ? rOrdAddrBillTo.ordaddr_phone3 : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_phone3_ext != null ? rOrdAddrBillTo.ordaddr_phone3_ext : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_fax != null ? rOrdAddrBillTo.ordaddr_fax : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_fax_ext != null ? rOrdAddrBillTo.ordaddr_fax_ext : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_fax_home != null ? rOrdAddrBillTo.ordaddr_fax_home : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_email_business != null ? rOrdAddrBillTo.ordaddr_email_business : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_email_home != null ? rOrdAddrBillTo.ordaddr_email_home : '') + '\t';
			sOutput += (rOrdAddrBillTo && rOrdAddrBillTo.ordaddr_email_other != null ? rOrdAddrBillTo.ordaddr_email_other : '') + '\t';
			sOutput += getContactFirstAndLastName(rOrdAddrBillTo) + '\t';
			sOutput += (rOrdAddrBillToSysAddress && rOrdAddrBillToSysAddress.addr_address1 != null ? rOrdAddrBillToSysAddress.addr_address1 : '') + '\t';
			sOutput += (rOrdAddrBillToSysAddress && rOrdAddrBillToSysAddress.addr_address2 != null ? rOrdAddrBillToSysAddress.addr_address2 : '') + '\t';
			sOutput += (rOrdAddrBillToSysAddress && rOrdAddrBillToSysAddress.addr_address3 != null ? rOrdAddrBillToSysAddress.addr_address3 : '') + '\t';
			sOutput += (rOrdAddrBillToSysAddress && rOrdAddrBillToSysAddress.addr_city != null ? rOrdAddrBillToSysAddress.addr_city : '') + '\t';
			sOutput += getStateProvinceCode(rOrdAddrBillToSysAddress.stateprov_id) + '\t';
			sOutput += (rOrdAddrBillToSysAddress && rOrdAddrBillToSysAddress.addr_postal != null ? rOrdAddrBillToSysAddress.addr_postal : '') + '\t';
			sOutput += getCountryCode(rOrdAddrBillToSysAddress.country_id) + '\t';
			
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_name != null ? rOrdAddrShipTo.ordaddr_name : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone1 != null ? rOrdAddrShipTo.ordaddr_phone1 : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone1_ext != null ? rOrdAddrShipTo.ordaddr_phone1_ext : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone2 != null ? rOrdAddrShipTo.ordaddr_phone2 : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone2_ext != null ? rOrdAddrShipTo.ordaddr_phone2_ext : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone3 != null ? rOrdAddrShipTo.ordaddr_phone3 : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_phone3_ext != null ? rOrdAddrShipTo.ordaddr_phone3_ext : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_fax != null ? rOrdAddrShipTo.ordaddr_fax : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_fax_ext != null ? rOrdAddrShipTo.ordaddr_fax_ext : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_fax_home != null ? rOrdAddrShipTo.ordaddr_fax_home : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_email_business != null ? rOrdAddrShipTo.ordaddr_email_business : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_email_home != null ? rOrdAddrShipTo.ordaddr_email_home : '') + '\t';
			sOutput += (rOrdAddrShipTo && rOrdAddrShipTo.ordaddr_email_other != null ? rOrdAddrShipTo.ordaddr_email_other : '') + '\t';
			sOutput += getContactFirstAndLastName(rOrdAddrShipTo) + '\t';
			sOutput += (rOrdAddrShipToSysAddress && rOrdAddrShipToSysAddress.addr_address1 != null ? rOrdAddrShipToSysAddress.addr_address1 : '') + '\t';
			sOutput += (rOrdAddrShipToSysAddress && rOrdAddrShipToSysAddress.addr_address2 != null ? rOrdAddrShipToSysAddress.addr_address2 : '') + '\t';
			sOutput += (rOrdAddrShipToSysAddress && rOrdAddrShipToSysAddress.addr_address3 != null ? rOrdAddrShipToSysAddress.addr_address3 : '') + '\t';
			sOutput += (rOrdAddrShipToSysAddress && rOrdAddrShipToSysAddress.addr_city != null ? rOrdAddrShipToSysAddress.addr_city : '') + '\t';
			sOutput += getStateProvinceCode(rOrdAddrShipToSysAddress.stateprov_id) + '\t';
			sOutput += (rOrdAddrShipToSysAddress && rOrdAddrShipToSysAddress.addr_postal != null ? rOrdAddrShipToSysAddress.addr_postal : '') + '\t';
			sOutput += getCountryCode(rOrdAddrShipToSysAddress.country_id) + '\t';
			sOutput += (rCustAddr && rCustAddr.custaddr_code != null ? rCustAddr.custaddr_code : '') + '\t';
		}
		else if (rOrdRevd && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl) {
			sOutput += getOrderDocumentNumber(rOrdRevd, recordType) + '\t';
			sOutput += (rOrdRevd.sequence_nr != null ? rOrdRevd.sequence_nr : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_prod_desc != null ? rOrdRevd.ordrevd_prod_desc : '') + '\t';
			sOutput += getWarehouseCode(rOrdRevd, recordType) + '\t';
			sOutput += getUOMCode(rOrdRevd, recordType) + '\t';
			sOutput += (rOrdRevd.ordrevd_qty_ordered != null ? rOrdRevd.ordrevd_qty_ordered : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_qty_shipped != null ? rOrdRevd.ordrevd_qty_shipped : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_qty_backord != null ? rOrdRevd.ordrevd_qty_backord : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_sell_price != null ? rOrdRevd.ordrevd_sell_price : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_disc_amt != null ? rOrdRevd.ordrevd_disc_amt : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_extended_price != null ? rOrdRevd.ordrevd_extended_price : '') + '\t';
			sOutput += getItemCode(rOrdRevd, recordType) + '\t';
			sOutput += (rOrdRevd.ordrevd_unit_cost != null ? rOrdRevd.ordrevd_unit_cost : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_extended_cost != null ? rOrdRevd.ordrevd_extended_cost : '') + '\t';
			sOutput += getSysWorkTypeCode(rOrdRevd, recordType) + '\t';
			sOutput += getWorkTypeCode(rOrdRevd, recordType) + '\t';
			sOutput += (rOrdRevd.ordrevd_unit_price != null ? rOrdRevd.ordrevd_unit_price : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_staging_location != null ? rOrdRevd.ordrevd_staging_location : '') + '\t';
			sOutput += (rOrdRevd.ordrevd_ext_price_over != null ? rOrdRevd.ordrevd_ext_price_over : '') + '\t';
		}
		else if (rOrdRevhMultiShip && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip) {
			rCustAddr = getCustomerAddress(rOrdRevhMultiShip.custaddr_id);
			
			sOutput += getOrderDocumentNumber(rOrdRevhMultiShip, recordType) + '\t';
			sOutput += (rOrdRevhMultiShip.sequence_nr != null ? rOrdRevhMultiShip.sequence_nr : '') + '\t';
			sOutput += getCustomerCode(rOrdRevhMultiShip, recordType) + '\t';
			sOutput += (rCustAddr && rCustAddr.custaddr_code != null ? rCustAddr.custaddr_code : '') + '\t';
			sOutput += getShipMethodCode(rOrdRevhMultiShip, recordType) + '\t';
			sOutput += (rOrdRevhMultiShip.ordrevhms_instructions != null ? rOrdRevhMultiShip.ordrevhms_instructions : '') + '\t';
			sOutput += (rOrdRevhMultiShip.ordrevhms_event_date != null ? scopes.avDate.formatDate(rOrdRevhMultiShip.ordrevhms_event_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrdRevhMultiShip.ordrevhms_must_arrive_date != null ? scopes.avDate.formatDate(rOrdRevhMultiShip.ordrevhms_must_arrive_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrdRevhMultiShip.ordrevhms_ship_date != null ? scopes.avDate.formatDate(rOrdRevhMultiShip.ordrevhms_ship_date,'YYYYMMDD') : '') + '\t';
			sOutput += (rOrdRevhMultiShip.ordrevhms_shipping_charges != null ? rOrdRevhMultiShip.ordrevhms_shipping_charges : '') + '\t';
			sOutput += getContactFirstAndLastName(rOrdRevhMultiShip) + '\t';
		}
		else if (rOrdRevhMultiShipQty && subType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty) {
			sOutput += getOrderDocumentNumber(rOrdRevhMultiShipQty, recordType) + '\t';
			sOutput += getOrderDetailLineNumber(rOrdRevhMultiShipQty, recordType) + '\t';
			sOutput += getMultiShipLineNumber(rOrdRevhMultiShipQty, recordType) + '\t';
			sOutput += (rOrdRevhMultiShipQty.ordrevdms_qty != null ? rOrdRevhMultiShipQty.ordrevdms_qty : '') + '\t';
			sOutput += (rOrdRevhMultiShipQty.ordrevdms_qty_backord != null ? rOrdRevhMultiShipQty.ordrevdms_qty_backord : '') + '\t';
			sOutput += (rOrdRevhMultiShipQty.ordrevdms_qty_shipped != null ? rOrdRevhMultiShipQty.ordrevdms_qty_shipped : '') + '\t';
			sOutput += (rOrdRevhMultiShipQty.ordrevdms_qty_shipped_actual != null ? rOrdRevhMultiShipQty.ordrevdms_qty_shipped_actual : '') + '\t';
		}
	}

	function getOrderDocumentNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && 
				utils.hasRecords(rRecord.sa_order_revision_detail_to_sa_order_revision_header$outer) && 
				utils.hasRecords(rRecord.sa_order_revision_detail_to_sa_order_revision_header$outer.sa_order_revision_header_to_sa_order$outer)) {
			return rRecord.sa_order_revision_detail_to_sa_order_revision_header$outer.sa_order_revision_header_to_sa_order$outer.ordh_document_num;
		}
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip && 
				utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sa_order_revision_header) && 
				utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sa_order_revision_header.sa_order_revision_header_to_sa_order$outer)) {
			return rRecord.sa_order_revh_multi_ship_to_sa_order_revision_header.sa_order_revision_header_to_sa_order$outer.ordh_document_num;
		}
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty && 
				utils.hasRecords(rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail) && 
				utils.hasRecords(rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header) && 
				utils.hasRecords(rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order$outer)) {
			return rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order$outer.ordh_document_num;
		}
		return '';
	}
	
	function getOrderDetailLineNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty && 
				utils.hasRecords(rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail)) {
			return rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revision_detail.sequence_nr;
		}
		return '';
	}
	
	function getMultiShipLineNumber(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShipQty && 
				utils.hasRecords(rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship)) {
			return rRecord.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship.sequence_nr;
		}
		return '';
	}
	
	function getSysWorkTypeCode(rRecord, recordType) {
		var workTypeId = rRecord.sysworktype_id ? rRecord.sysworktype_id.toString() : null;	
		if (workTypeCache.hasOwnProperty(workTypeId)) {
			return workTypeCache[workTypeId] ? workTypeCache[workTypeId].sysworktype_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && utils.hasRecords(rRecord.sa_order_revision_detail_to_sys_task_worktype)) {
				workTypeCache[workTypeId] = rRecord.sa_order_revision_detail_to_sys_task_worktype;
				return rRecord.sa_order_revision_detail_to_sys_task_worktype.sysworktype_code;
		}
		return '';
	}

	function getWorkTypeCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && 
				utils.hasRecords(rRecord.sa_order_revision_detail_to_sa_task_worktype)) {
			return rRecord.sa_order_revision_detail_to_sa_task_worktype.worktype_desc;
		}
		return '';
	}

	function getItemCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && 
				utils.hasRecords(rRecord.sa_order_revision_detail_to_in_item$outerjoin)) {
			return rRecord.sa_order_revision_detail_to_in_item$outerjoin.item_code;
		}
		return '';
	}

	function getWarehouseCode(rRecord, recordType) {
		var whseId = rRecord.whse_id ? rRecord.whse_id.toString() : null;
		
		if (warehouseCache.hasOwnProperty(whseId)) {
			return warehouseCache[whseId] ? warehouseCache[whseId].whse_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && utils.hasRecords(rRecord.sa_order_revision_detail_to_in_warehouse)) {
			warehouseCache[whseId] = rRecord.sa_order_revision_detail_to_in_warehouse;
			return rRecord.sa_order_revision_detail_to_in_warehouse.whse_code;
		}
		return '';
	}
	
	function getUOMCode(rRecord, recordType) {
		var uomId = rRecord.uom_id ? rRecord.uom_id.toString() : null;		
		if (sysUnitOfMeasureCache.hasOwnProperty(uomId)) {
			return sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderDtl && uomId) {
				sysUnitOfMeasureCache[uomId] = getUOMRecord(uomId);
				return (sysUnitOfMeasureCache[uomId] ? sysUnitOfMeasureCache[uomId].uom_code : '');
		}
		return '';
	}
	
	function getOrderRevisionHeader(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_order_revision_header$first_rev)) {
			return rRecord.sa_order_to_sa_order_revision_header$first_rev
		}
		return null;
	}
	
	function getPayMethodCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_payment_method)) {
			return rRecord.sa_order_to_sa_payment_method.paymethod_code;
		}
		return '';
	}

	function getCustomerCode(rRecord, recordType) {
		var custId = rRecord.cust_id ? rRecord.cust_id.toString() : null;		
		if (customerCache.hasOwnProperty(custId)) {
			return customerCache[custId] ? customerCache[custId].cust_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_customer)) {
			customerCache[custId] = rRecord.sa_order_to_sa_customer;
			return rRecord.sa_order_to_sa_customer.cust_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip && utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sa_customer)) {
			customerCache[custId] = rRecord.sa_order_revh_multi_ship_to_sa_customer;
			return rRecord.sa_order_revh_multi_ship_to_sa_customer.cust_code;
		}
		return '';
	}
	
	function getCustomerAddress(addrId) {
		if (!addrId) {
			return null;
		}
		
		var sSQL = "SELECT custaddr_id \
					  FROM sa_customer_address \
					 WHERE org_id = ? \
					   AND custaddr_id = ?";
		/**@type {JSRecord<db:/avanti/sa_customer_address>} */
		var rCustAddr = scopes.avDB.getRecFromSQL(sSQL,'sa_customer_address',[globals.org_id.toString(), addrId.toString()]);
		if (rCustAddr) {
			return rCustAddr;
		}
		return null;
	}
	
	function getSysAddress(addrId) {
		if (!addrId) {
			return null;
		}
		
		var sSQL = "SELECT addr_id \
					  FROM sys_address \
					 WHERE org_id = ? \
					   AND addr_id = ?";
		/**@type {JSRecord<db:/avanti/sys_address>} */
		var rSysAddr = scopes.avDB.getRecFromSQL(sSQL,'sys_address',[globals.org_id.toString(), addrId.toString()]);
		if (rSysAddr) {
			return rSysAddr;
		}
		return null;
	}
	
	function getStateProvinceCode(provId) {
		if (!provId) {
			return '';
		}
		
		var sSQL = "SELECT stateprov_id \
					  FROM sys_state_province \
					 WHERE org_id = ? \
					   AND stateprov_id = ?";
		/**@type {JSRecord<db:/avanti/sys_state_province>} */
		var rSysProv = scopes.avDB.getRecFromSQL(sSQL,'sys_state_province',[globals.org_id.toString(), provId.toString()]);
		if (rSysProv) {
			return rSysProv.stateprov_code;
		}
		return '';
	}
	
	function getCountryCode(countryId) {
		if (!countryId) {
			return '';
		}
		
		var sSQL = "SELECT country_id \
					  FROM sys_country \
					 WHERE org_id = ? \
					   AND country_id = ?";
		/**@type {JSRecord<db:/avanti/sys_country>} */
		var rSysCountry = scopes.avDB.getRecFromSQL(sSQL,'sys_country',[globals.org_id.toString(), countryId.toString()]);
		if (rSysCountry) {
			return rSysCountry.country_code;
		}
		return '';
	}

	function getOrderAddress(ordhId, addressType) {
		if (!ordhId || !addressType) {
			return null;
		}

		addressType = addressType.toLowerCase();
		var addrType;
		if (addressType == 'billto') {
			addrType = 'B';
		}
		else if (addressType == 'shipto') {
			addrType = 'S';
		}
		else if (addressType == 'customer') {
			addrType = 'C';
		}
		else {
			return null;
		}
		var sSQL = "SELECT ordaddr_id \
					  FROM sa_order_address \
					 WHERE org_id = ? \
					   AND ordh_id = ? \
					   AND ordaddr_type = ?";
		/**@type {JSRecord<db:/avanti/sa_order_address>} */
		var rOrdAddr = scopes.avDB.getRecFromSQL(sSQL,'sa_order_address',[globals.org_id.toString(), ordhId.toString(), addrType]);
		if (rOrdAddr) {
			return rOrdAddr;
		}
		return null;
	}

	function getCurrencyCode(rRecord, recordType) {
		var currId = rRecord.curr_id ? rRecord.curr_id.toString() : null;		
		if (sysCurrencyCache.hasOwnProperty(currId)) {
			return sysCurrencyCache[currId] ? sysCurrencyCache[currId].curr_iso_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sys_currency)) {
			sysCurrencyCache[currId] = rRecord.sa_order_to_sys_currency;
			return rRecord.sa_order_to_sys_currency.curr_iso_code;
		}
		return '';
	}
	
	function getTerritoryCode(rRecord, recordType) {
		var terrId = rRecord.salesterr_id ? rRecord.salesterr_id.toString() : null;		
		if (saTerritoryCache.hasOwnProperty(terrId)) {
			return saTerritoryCache[terrId] ? saTerritoryCache[terrId].terr_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_territory)) {
			saTerritoryCache[terrId] = rRecord.sa_order_to_sa_territory;
			return rRecord.sa_order_to_sa_territory.terr_code;
		}
		return '';
	}
	
	function getCampaignCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_crm_campaign)) {
			return rRecord.sa_order_to_crm_campaign.camp_desc;
		}
		return '';
	}
	
	function getRushCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_order_rush)) {
			return rRecord.sa_order_to_sa_order_rush.rush_code;
		}
		return '';
	}
	
	function getCustProjectCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_customer_project)) {
			return rRecord.sa_order_to_sa_customer_project.custproj_desc;
		}
		return '';
	}
	
	function getOrderTypeCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_order_type)) {
			return rRecord.sa_order_to_sa_order_type.ordtype_desc;
		}
		return '';
	}
	
	function getSalesPersonCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_sales_person)) {
			return rRecord.sa_order_to_sa_sales_person.salesper_code;
		}
		return '';
	}
	
	function getEmployeeCode(rRecord, recordType) {
		var emplId = rRecord.ordh_csr_empl_id ? rRecord.ordh_csr_empl_id.toString() : null;		
		if (sysEmployeeCache.hasOwnProperty(emplId)) {
			return sysEmployeeCache[emplId] ? sysEmployeeCache[emplId].empl_code : '';
		} 
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sa_territory)) {
			sysEmployeeCache[emplId] = rRecord.sa_order_to_sa_territory;
			return rRecord.sa_order_to_sa_territory.empl_code;
		}
		return '';
	}
	
	function getPlantCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sys_plant)) {
			return rRecord.sa_order_to_sys_plant.plant_code;
		}
		return '';
	}

	function getShipMethodCode(rRecord, recordType) {
		if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrders && utils.hasRecords(rRecord.sa_order_to_sys_shipping_method)) {
			return rRecord.sa_order_to_sys_shipping_method.shipmethod_code;
		}
		else if (recordType == scopes.avDataExport.ENUM_SALES_ORDER_SUBTYPES.SalesOrderMultiShip && utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sys_shipping_method)) {
			return rRecord.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_code;
		}
		return '';
	}

	function getContactFirstAndLastName(rRecord) {
		if (rRecord) {
			var dataSource = rRecord.getDataSource();

			if (dataSource == 'db:/avanti/sa_order' && 
					utils.hasRecords(rRecord.sa_order_to_sa_customer_contact) && 
					utils.hasRecords(rRecord.sa_order_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
				return rRecord.sa_order_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
			} 
			else if (dataSource == 'db:/avanti/sa_order_address' && 
					utils.hasRecords(rRecord.sa_order_address_to_sa_customer_contact) && 
					utils.hasRecords(rRecord.sa_order_address_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
				return rRecord.sa_order_address_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
			}
			else if (dataSource == 'db:/avanti/sa_order_revh_multi_ship' && 
					utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sa_customer_contact) && 
					utils.hasRecords(rRecord.sa_order_revh_multi_ship_to_sa_customer_contact.sa_customer_contact_to_sys_contact)) {
				return rRecord.sa_order_revh_multi_ship_to_sa_customer_contact.sa_customer_contact_to_sys_contact.contact_first_and_last;
			}
		}
		return '';
	}
}


/**
 * Export accounting related data
 * @param rFSRecord
 * @param subType
 *
 * @return
 * @properties={typeid:24,uuid:"6611896D-015D-4A85-A6C1-BB1120DC4D1A"}
 */
function AccountingExport(rFSRecord, subType) {
	var sOutput = null;
	
	/**@type {JSRecord<db:/avanti/gl_account>} */
	var rGLAccount;

	if (rFSRecord && subType == scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts) {
		rGLAccount = rFSRecord;
	}
	else {
		return null;
	}
	
	buildLine(subType);
	
	if (sOutput) {
		sOutput += '\n';
	}

	return sOutput;
	
	function buildLine(recordType) {
		if (rGLAccount && subType == scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts) {
			sOutput += rGLAccount.glacct_number + '\t';
			sOutput += rGLAccount.glacct_desc + '\t';
			sOutput += getGLAccountType(rGLAccount, subType) + '\t';
		}
	}

	function getGLAccountType(rRecord, recordType) {
		var oAccountType = {
			A: 'Asset',
			L: 'Liability',
			R: 'Revenue',
			E: 'Expense',
			Q: 'Equity',
			C: 'Cost Of Goods Sold'
		};
		if (recordType == scopes.avDataExport.ENUM_ACCOUNTING_SUBTYPES.ChartOfAccounts && utils.hasRecords(rRecord.gl_account_to_gl_account_segment)) {
			var accountTypeCode = rRecord.gl_account_to_gl_account_segment.glacctseg_type;
			if (oAccountType[accountTypeCode]) {
				return oAccountType[accountTypeCode];
			}
		}
		return '';
	}
}