customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
extendsID:"3E8F2F77-8657-4311-B8C6-28F1DA26507C",
items:[
{
cssPosition:"31,-1,-1,155,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"31",
width:"140"
},
dataProviderID:"_estimateNumber",
enabled:true,
onDataChangeMethodID:"7987DBFB-E634-46D1-9AE1-331D5BABE208",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7CE46F78-F5E8-48B8-91BC-8CA930589AC3",
visible:true
},
name:"_estimateNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"06790F07-415D-4861-9749-CC8FDB10EF01"
},
{
cssPosition:"58,-1,-1,155,140,20",
formIndex:4,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"155",
right:"-1",
top:"58",
width:"140"
},
dataProviderID:"_currentStatus",
enabled:true,
formIndex:4,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7232BA70-6D93-49D0-973C-5FA9F1D2FF82",
visible:true
},
name:"_currentStatus",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3C76E364-143D-4675-9B16-EA12B3EFD1A9"
},
{
cssPosition:"6,-1,-1,10,253,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"6",
width:"253"
},
enabled:true,
formIndex:6,
styleClass:"gray_bold label_bts",
tabSeq:-1,
text:"Estimate Selection",
visible:true
},
name:"component_49C92E39",
styleClass:"gray_bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"401B8451-639D-4ABD-A719-58A72B94E87F"
},
{
cssPosition:"122,6,-1,5,829,175",
json:{
containedForm:"886DD905-9ADB-40AB-AE02-88EB345FA17B",
cssPosition:{
bottom:"-1",
height:"175",
left:"5",
right:"6",
top:"122",
width:"829"
},
visible:true
},
name:"tabs_105",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"4A435D47-E253-4D51-A2F9-48160B6F9240"
},
{
cssPosition:"31,-1,-1,155,140,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"31",
width:"140"
},
dataProviderID:"_estimateNumber",
enabled:true,
formIndex:1,
onDataChangeMethodID:"7987DBFB-E634-46D1-9AE1-331D5BABE208",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7CE46F78-F5E8-48B8-91BC-8CA930589AC3",
visible:true
},
name:"_estimateNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7062B0AB-D52E-4AED-A9EB-D99D7F0A158F"
},
{
cssPosition:"298,6,39,5,829,225",
json:{
containedForm:"88B1CB55-D4A7-4DF4-9D1F-4909EEDBEB59",
cssPosition:{
bottom:"39",
height:"225",
left:"5",
right:"6",
top:"298",
width:"829"
},
visible:true
},
name:"tabs_219",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"788373F0-77FF-4AA1-8485-1A0FDDC8314D"
},
{
cssPosition:"31,-1,-1,441,140,24",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"441",
right:"-1",
top:"31",
width:"140"
},
dataProviderID:"_copyNotes",
enabled:true,
formIndex:1,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.copyNotes",
visible:true
},
name:"_copyNotes",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"7DE6D004-4A6A-4EB9-ABAF-AE7D7062511B"
},
{
cssPosition:"5,-1,-1,5,414,110",
json:{
cssPosition:{
bottom:"-1",
height:"110",
left:"5",
right:"-1",
top:"5",
width:"414"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_580CE94F",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9E128F36-0C1D-49FC-8CEB-ABF3A4C8E20F"
},
{
cssPosition:"31,-1,-1,300,24,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"300",
right:"-1",
top:"31",
width:"24"
},
enabled:true,
formIndex:2,
onActionMethodID:"B768E49A-DDE3-40DB-93A3-DEE3F82C7B88",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupEstimate",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AB7D06B6-2410-4162-B7FA-6535FE81887D"
},
{
height:562,
partType:5,
typeid:19,
uuid:"ABD90521-8242-46E6-8F58-8F7A930E06CA"
},
{
cssPosition:"58,-1,-1,441,140,24",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"441",
right:"-1",
top:"58",
width:"140"
},
dataProviderID:"_copyComments",
enabled:true,
formIndex:2,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.copyComments",
visible:true
},
name:"_copyComments",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"CB196A4B-DEF3-4DA2-9465-8C61721E482E"
},
{
cssPosition:"31,-1,-1,10,140,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"31",
width:"140"
},
enabled:true,
formIndex:5,
labelFor:"_estimateNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.estimate",
visible:true
},
name:"_estimateNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D02B116D-5F07-49A7-8319-3BE735041BA2"
},
{
cssPosition:"-1,-1,7,110,100,24",
json:{
cssPosition:{
bottom:"7",
height:"24",
left:"110",
right:"-1",
top:"-1",
width:"100"
},
enabled:true,
onActionMethodID:"97566A15-0C5E-4C55-B173-969925E4ED2E",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Close",
visible:true
},
name:"btnClose",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"D9CDB813-4E72-453F-9B74-6352C947EEF1"
},
{
cssPosition:"-1,-1,7,5,100,24",
json:{
cssPosition:{
bottom:"7",
height:"24",
left:"5",
right:"-1",
top:"-1",
width:"100"
},
enabled:true,
onActionMethodID:"29A753D4-B0B2-41D4-A43F-B77CE9F6B8DA",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Copy",
visible:true
},
name:"btnCopy",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"DA92E11C-39C0-4224-B75D-E99557DC57AC"
},
{
cssPosition:"5,5,-1,424,411,110",
json:{
cssPosition:{
bottom:"-1",
height:"110",
left:"424",
right:"5",
top:"5",
width:"411"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_008154AA",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E46FCA08-5C59-4CC2-92EC-3FD139488727"
},
{
cssPosition:"58,-1,-1,10,140,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"58",
width:"140"
},
enabled:true,
formIndex:3,
labelFor:"_currentStatus",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"ordh_estimate_status_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F8A2AC49-9646-44EC-B732-0BC0016131AE"
}
],
name:"sa_task_worktype_copy_estimate_dlg",
onShowMethodID:"7D9C64F5-B974-42BC-8E31-230DBDB02393",
scrollbars:33,
size:"840,562",
styleName:"Avanti",
titleText:"i18n:avanti.lbl.copyEstimateWorkTemplate",
typeid:3,
uuid:"962EC0A4-AAFC-4960-81B2-3F72CF6A57B2"