/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D3DCCEB0-1755-4906-8BEB-28F2492443CB",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"AA04B889-3B54-42C2-9A0C-CB871DBB3A49"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"4420B5E7-9EF5-4B25-8683-72C63EDA4E8C"}
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
 return _super.onShowForm(firstShow, event);
}
/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0666EE44-FEBE-4DD4-B725-88A529ED17D0"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	onRecordSelection(event);
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnSelect" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		btnSelect(event);
	}
	if (col.id == "btnSort" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		scopes.globals.avUtilities_shuffle(event);
	}
	if (col.id == "btnDelete" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		btnDelete(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"DD583838-0E75-481C-BEBF-8241EBE2D827"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldMenu" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChange_menuName(oldValue, newValue, event);
	}
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"********-84B7-46DF-A939-F9FE6E5C094E"}
 */
function onRecordSelection(event)
{
	globals.avRpt_selectedPopMenuID = nav_popmenu_id;
//	
//	if (owner_id == globals.systemOwnerUUID && globals.avBase_developmentMode != 1)
//	{
//		elements.grid.setReadOnly(true, ["fldMenu"]);
//	}
//	else
//	{
//		elements.grid.setReadOnly(false, ["fldMenu"]);
//	}
//	
//	// Load the sub menus
////	forms.rpt_report_dtl_tab_subMenus.foundset.loadRecords(forms.rpt_report_dtl_tab_subMenus.getSubMenus());
//	
////	globals.avUtilities_shuffleGrey("rpt_report_dtl_tab_subMenus");
//	
}

/**
 * @author: Gary Dotzlaw, 2011-04-30
 * @description: Add or Delete the menu information to the rptMenu record
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"17A99905-2983-441C-A0E2-84AD081B8250"}
 */
function btnSelect(event)
{
	if(globals.nav.mode == 'edit')	
	{
//		if (owner_id == globals.systemOwnerUUID && globals.avBase_developmentMode != 1)
		if (owner_id == null && globals.avBase_developmentMode != 1)
		{
			return false;
		}
		
		
		if (!forms.rpt_report_dtl_tab.checkAvailableReportPrograms(program_name)) return false;
		
		globals.avRpt_selectedPopMenuID = nav_popmenu_id;
		
		forms.rpt_report.rptSelectMenu(event);
	
	}
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"7003B879-2B9C-4281-9FA6-268C7BACDA5F"}
 */
function btnAdd(event)
{
	if(!scopes.avUtils.isNavModeReadOnly())	
	{
		// GD - 2013-12-11: Need to pass in the report record
		forms.rpt_report.rptAddPopMenu(null, forms.rpt_report_dtl.foundset.getSelectedRecord());
	}
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"D0370E73-C928-4E3E-A4D6-ED847590414B"}
 * @AllowToRunInFind
 */
function btnDelete(event)
{
	if(!scopes.avUtils.isNavModeReadOnly())	
	{
//		globals.avRpt_selectedReport = nav_popmenu_to_nav_function.nav_function_to_sec_security_key.rpt_id;
		forms.rpt_report.rptDeleteMenu(event);
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"00845A21-8CBD-4796-8B2B-0B0FAA729643"}
 */
function onDataChange_menuName(oldValue, newValue, event)
{
//	if (owner_id == globals.systemOwnerUUID && globals.avBase_developmentMode != 1)
	if (owner_id == null && globals.avBase_developmentMode != 1)
	{
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.deleteMenu_title"), 
			i18n.getI18NMessage("avanti.dialog.modifyMenu_msg"), 
			i18n.getI18NMessage("avanti.dialog.ok"));
		
		label = oldValue;
		
	}
	
	return true
}
