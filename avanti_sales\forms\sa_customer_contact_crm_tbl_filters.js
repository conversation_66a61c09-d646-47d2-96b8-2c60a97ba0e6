/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A5D3E633-3B63-434A-8496-CDF20D470F07"}
 */
var currentView = globals.avSales_CRM_MyCustomers;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"B4ADE62F-2881-4335-9E84-579E45416339"}
 */
var selectedRep = null;

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"95726E8B-47BC-436F-A1C4-9F2837ABBDFE"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	var result =  _super.onShowForm(firstShow, event);
	refreshUI(event);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * @param readonly
 *
 *
 * @properties={typeid:24,uuid:"73EC2E15-C39E-4BE7-B6FB-CCE90F93A549"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"CB2738E7-D356-4DD9-A109-C7304809FEC0"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	
	// Set globals and default view to not show managed sales reps.
	elements.managed_sales_reps.visible = false
	elements.managed_sales_reps_lbl.visible = false
	
	//var customer_contact_fs = forms.sa_customer_contact_crm_tbl.foundset.duplicateFoundSet()
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard")
	{
		
	}
	else
	{
		applyFilters(true);
	}
}

/**
 * @AllowToRunInFind
 * 
 * Apply foundset filters
 * @param bReApplyFilters
 *
 * @properties={typeid:24,uuid:"61DD69BA-C0EE-4FD5-85E5-C224CAEB8509"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		// Remove any filters.
		forms.sa_customer_contact_crm_tbl.foundset.removeFoundSetFilterParam('employeeFilter');
		
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
						// If a sales rep, filter by salesper_id
						case globals.avSales_CRM_SalesRep:
							if(utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "'",'employeeFilter')
							} else {
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						// If a sales manager, filter by salesper_id and add the 'Sales Manager' dropdown and make the managed sales rep drop down visible
						case globals.avSales_CRM_SalesManager:
							if(currentView == globals.avSales_CRM_AllCustomers){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if(currentView == globals.avSales_CRM_AllCustomers && selectedRep != null && selectedRep != '') {
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id = '" + selectedRep + "'",'employeeFilter')
							} else if (currentView == globals.avSales_CRM_AllCustomers && selectedRep == null) {
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id in (select salesper_id from sys_employee_managed_reps where empl_id = '" + globals.avBase_employeeUUID + "')",'employeeFilter')
							} else if(currentView == globals.avSales_CRM_MyCustomers && utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id = '" + employee_fs.sys_employee_to_sa_sales_person.salesper_id + "'",'employeeFilter')
							} else {
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						case globals.avSales_CRM_CustomerServiceRep:
							forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where cust_csr_empl_id = '" + globals.avBase_employeeUUID + "'",'employeeFilter')
							break;
						// If an admin
						case globals.avSales_CRM_Administrator:
							if(currentView == globals.avSales_CRM_AllCustomers){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							if(selectedRep != null && selectedRep != '') {
								forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id = '" + selectedRep + "'",'employeeFilter')
							}
							break;
						default:
							// Filter by invalid uuid.
							forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							break;
					}
				} else {
					// Filter by invalid uuid.
					forms.sa_customer_contact_crm_tbl.foundset.addFoundSetFilterParam('cust_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
				}
			}
		}
		forms.sa_customer_contact_crm_tbl.foundset.loadAllRecords();
		forms.sa_customer_contact_crm_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.sa_customer_contact_crm_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		
		// Reload the table and run initial sort on it.
		//forms.sa_customer_contact_crm_tbl.foundset.loadRecords(customer_contact_fs)
	}
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"415248F2-86D2-4927-B5AF-7BB77CF30DD7"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView != "Dashboard")
	{
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					if(employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_SalesManager || employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_Administrator) {
						currentView = globals.avSales_CRM_AllCustomers
					}
				}
			}
		}
	}
	
	return _super.onLoad(event)
}
