/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E8F2A3D7-B6C4-4F5A-9A1E-D7C3B5F8E9A2"}
 */
var version_build = '00';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"090CD2E7-9A5D-4056-93F4-76BE50651D37"}
 */
var version_revision = '00';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"40BFA4E0-C775-48B9-9AE6-252948CE291F"}
 */
var version_minor = '00';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"357C0C6D-A96A-4B2B-B952-597D11416048"}
 */
var version_major = '00';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F1A2A4E9-C992-410B-B383-1365C36FB1AF"}
 */
var avanti_closeForm = 'null';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7661B98A-EF50-4FDB-A2EB-DA97E8C7799C"}
 */
var nav_server_user = 'avanti';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"59012888-749A-4C1B-9E25-A1B1851E215D"}
 */
var nav_version = '6.884';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"1500F984-F06A-439C-9E56-7485FA749E71",variableType:4}
 */
var svy_nav_multi_tab_programs = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D28778A6-4594-42AD-AE68-D6FE62818C11"}
 */
var avanti_button_clicked = null;

/**
 * @public
 *
 * @type {String}
 *
 * @properties={typeid:35,uuid:"58A11A41-0FEF-4962-AA8C-4E0B71B66320"}
 */
var sURL = '';

/**
 * @properties={typeid:35,uuid:"B5C59897-E613-4198-8AF9-10D9D52CF087",variableType:-4}
 */
var invoicedet_SQL = null;

/**
 * @public
 *
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D8ECED21-4E60-477B-B027-5390A7B91E82"}
 */
var avSchedule_scripts = '';

/**
 * @public
 *
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1C16C821-D45E-4219-BFAC-97C6A23ADD0C"}
 */
var sProgressBarScript = '';

/**
 *	Opens the configurator screen in a dialog, if you are administrator
 *
 * <AUTHOR> Aleman
 * @since 2007-11-04
 *
 * @properties={showInMenu:true,typeid:24,uuid:"edeba2e0-4e6d-4654-8a7f-ba43c5acb034"}
 */
function _1_svy_nav_openNavigationForm() {
	if (_to_sec_user$user_id.flag_system_administrator) {
		var _modal = false
		if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) //non modal dialogs will not always open in WC
		{
			_modal = true
		}
		globals.DIALOGS.showFormInModalDialog(forms.svy_nav_c_main, -1, -1, -1, -1, null, true, false, 'Configurator', _modal)
	}
}

/**
 *	 Navigates to the main screen
 *
 * <AUTHOR> Aleman
 * @since 2007-11-04
 *
 * @properties={showInMenu:true,typeid:24,uuid:"aa42be89-94d5-40cc-9c12-5fa12f64cd35"}
 */
function _2_svy_nav_openMain() {
	forms.svy_nav_fr_main.controller.show()
}

/**
 *	Opens a shortcut dialog, so users can use shortcuts to go to a program
 *
 * <AUTHOR> Aleman
 * @since 2007-11-04
 *
 * @properties={showInMenu:true,typeid:24,uuid:"5d8d96db-9a9b-43f1-92f6-a0ad8441f8d4"}
 */
function _3_svy_nav_openShortcutDialog() {
	globals.DIALOGS.showFormInModalDialog(forms.svy_nav_fr_shortcut_dialog)
}

/**
 *	Navigate back from a related record
 *
 * <AUTHOR> Aleman
 * @since 2008-11-24
 *
 * @properties={typeid:24,uuid:"0b3dcd97-2049-41ba-aea8-30b4053e8cb8"}
 */
function svy_nav_backFormRelated(_event) {
	if (!scopes['avUtils'].isNavModeReadOnly())
	    return;
	if (globals.nav.related_text && globals.nav.stack_position > 0) {
		var _cur_related = globals.nav.stack[globals.nav.stack_position].foundset_related_text
		for (var j = globals.nav.stack_position - 1; j >= 0; j--) {
			if (_cur_related != globals.nav.stack[j].foundset_related_text) {
				globals.svy_nav_history_move(_event, j)
				break
			}
		}
	}
}

/**
 *	Create's  a tab for a program so the framework can show related data easily
 *
 * <AUTHOR> Aleman
 * @since 2008-11-04
 * @param {String} _program_name
 * @param {String} _server
 * @param {String} _table
 * @param {Number} _heigt_spliter
 *
 * @return  none
 *
 * @properties={typeid:24,uuid:"672fc79c-eb9c-426b-9483-c538f0963f1d"}
 */
function svy_nav_create_tab(_program_name, _server, _table, _heigt_spliter) {
	if (!_server || !_table) return;
	if (_program_name == undefined) return;
	var _tab_form = _program_name + '_tab';
	var _width = 50;
	var _height = 50;

	if (!forms[_tab_form]) {
		var jsForm = solutionModel.newForm(_tab_form, _server, _table, 'main_style', false, _width, _height);
		jsForm.newTabPanel('tabs', 0, 0, _width, _height);
		jsForm.navigator = SM_DEFAULTS.NONE;
		jsForm.styleName = 'sampleuse_navigation';
		jsForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER + SM_SCROLLBAR.VERTICAL_SCROLLBAR_NEVER;
		var jsTab = jsForm.getTabPanel('tabs');
		jsTab.anchors = 15;
		jsTab.tabOrientation = SM_ALIGNMENT.TOP;
		jsTab.transparent = true;
	}
}

/**
 *	Get info about the form where the users performs an action
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @param {JSEvent} _event
 * @param {String}	[_form_trigger] (svy_nav_fr_buttonbar_browser/svy_nav_fr_buttonbar_viewer)
 * @return {Array} information _form, _form_to,_program,_base,_form_trigger
 * @properties={typeid:24,uuid:"a88da286-ffef-46a7-9519-c05726728196"}
 */
function svy_nav_dc_getInfo(_event, _form_trigger) {

	if (!_form_trigger) {
		_form_trigger = _event.getFormName();
		if (_form_trigger == 'svy_nav_fr_main') {
			_form_trigger = globals.nav.browser_buttonbar
		}
	}

	if(_event)
	{
		if(_event.getFormName() == globals.nav.form_view_01)
		{
			globals.nav.activeView = 1
		}
		else if(_event.getFormName() == globals.nav.form_view_02)
		{
			globals.nav.activeView = 2
		}
	}

	if (_form_trigger != globals.nav.browser_buttonbar && _form_trigger != globals.nav.viewer_buttonbar) {
		if (globals.nav.activeView == 1) {
			_form_trigger = globals.nav.browser_buttonbar
		} else {
			_form_trigger = globals.nav.viewer_buttonbar
		}
	}
	var _form
	var _form_to
	var _base
	var _program
		// on the top form
	if (_form_trigger == globals.nav.browser_buttonbar || globals.nav_program_name == "System_Preferences") {
		_form = globals.nav.form_view_01
		_base = globals.nav.program[globals.nav_program_name].base_form_name
		_program = globals.nav_program_name
	} else if (_form_trigger == globals.nav.viewer_buttonbar) // on the bottom form
	{

		var _tab_form = globals.nav_program_name + '_tab';
		var iTabSelected = scopes.globals["avUtilities_tabGetSelectedIndex"](_tab_form, "tabs");
		globals.nav.program[globals.nav_program_name].tab.selected = iTabSelected;
		_form = scopes.globals["avUtilities_tabGetFormName"](_tab_form, "tabs", iTabSelected);
		_program = globals.nav.getTabProgram();
		_base = globals.nav.program[_program].base_form_name;
//		_base = globals.nav.program[globals.nav_program_name].base_form_name
//		_base = globals.nav.program[globals.nav_program_name].tab[scopes.globals.avUtilities_tabGetSelectedIndex(_tab_form, "tabs")].base_form_name
//		_program = globals.nav.program[globals.nav_program_name].program_name
//		_program = globals.nav.program[globals.nav_program_name].tab[scopes.globals.avUtilities_tabGetSelectedIndex(_tab_form, "tabs")].program_name
	}

	var _args = new Array()
	_args[0] = _form
	_args[1] = _form_to
	_args[2] = _program
	_args[3] = _base
	_args[4] = _form_trigger
	return _args
}

/**
 * Apply UI changes to a specific form's elements
 * @param {String} _form - Form name
 * @param {Array} fieldsToModify - Array of fields to modify
 * @param {String} _status - Current form status
 *
 * @properties={typeid:24,uuid:"7F3193EC-035F-42FD-A4FB-BA5556420AD0"}
 */
function applyFormRequiredFieldsUI(_form, fieldsToModify, _status) {
    if (!forms[_form]) return;

    var formObj = forms[_form];
    for (var j = 0; j < fieldsToModify.length; j++) {
        var field = fieldsToModify[j];
        var element = formObj.elements[field.labelElement];

        if (element && element.text !== undefined) {
            // Clean current text by removing any existing asterisks
            var currentText = element.text.replace(/^\*+\s/, "");

            if ((_status == 'edit' || _status == 'add' ||_status == 'required_fields') && field.isRequired) {
                var prefix = (_status == 'required_fields' && field.isProgrammerRequired) ? '** ' : '* ';
                element.text = prefix + currentText;
            } else {
                element.text = currentText;
            }
        }
    }
}

/**
 * Apply the required field UI changes to all collected forms and add click handlers in required_fields mode
 * @param {String} _status - Current form status
 *
 * @properties={typeid:24,uuid:"F1D85BB4-9A9D-41FE-8EC1-0D16BAFCC9C3"}
 */
function applyRequiredFieldsChanges(_status) {
    // Apply changes for each form in the collected data
    for (var formName in globals.nav.requiredFieldsData) {
        var formData = globals.nav.requiredFieldsData[formName];
        var formStatus = formData.status || _status;

        applyFormRequiredFieldsUI(formName, formData.fieldsToModify, formStatus);
    }

    // Clear the collected data after applying changes
    globals.nav.requiredFieldsData = {};
}

/**
 * Collects child containers from a form (unchanged from original)
 * @param {JSForm} jsForm - The form to analyze
 * @param {String} _form - Form name
 * @return {Array} Array of child container info
 *
 * @properties={typeid:24,uuid:"A6F35092-7D3E-4233-9203-30B67B20E7B1"}
 */
function collectChildContainers(jsForm, _form) {
    var prgcontainers = [];

    var allComponents = jsForm.getComponents();
    for (var i = 0; i < allComponents.length; i++) {
        var component = allComponents[i];
        if (component instanceof JSWebComponent) {
            var typeName = component.typeName;
            if (typeName === 'bootstrapcomponents-tabpanel' || typeName === 'servoyextra-splitpane' || typeName === 'servoycore-formcontainer') {
                prgcontainers.push({
                    name: component.name,
                    type: typeName
                });
            }
        }
    }

    return prgcontainers;
}

/**
 * Sets the status/mode of a form and updates the UI accordingly
 * @param {String} _status - The status to set ('browse', 'edit', 'add', 'find', 'required_fields')
 * @param {String} _form - The form name
 *
 * @properties={typeid:24,uuid:"F1ACA857-90D7-4F56-9FA9-8CFEFB0CC0C0"}
 */
function svy_nav_dc_setStatus(_status, _form) {
    if (!_form) return;

    globals.nav.mode = _status;

    // set _browse and fieldcolering
    var _browse;
    var _program = globals.nav_program_name;

    var sFormContainer = _form;
    if (application.getActiveWindow().controller.getName() == 'svy_nav_fr_main' || (application.getActiveWindow().getParent() !== null && application.getActiveWindow().getParent().controller.getName() == 'svy_nav_fr_main')) {
        // We need to use the name of the program container (which is the tab being shown in the main navigation tab panel)
        sFormContainer = globals.nav.programContainer;
    }

    /** @type {{required_fields:Object,noreadonly:Number,program_name:String,tab:Array}} */
    var _progObj = globals.nav.program[_program];

    if (_status == 'edit' || _status == 'add' || _status == 'find') {
        forms[_form].controller.readOnly = false;
        _browse = false;

        if (!globals.nav.related_text) {
            forms[_form].controller.focusFirstField();
        } else {
            globals.nav[_form + 'focusFirstField'] = true;
        }

        if (_status != 'find') {
            globals.svy_nav_setFieldsColor(sFormContainer, 'edit');
            globals.svy_nav_setRequiredFields(_progObj, sFormContainer, 'edit');
        }
    }
    else if (_status == 'required_fields') {
        forms[_form].controller.readOnly = true; // Keep form read-only in required_fields mode
        _browse = false; // This makes sure save/cancel buttons are enabled

        globals.svy_nav_setFieldsColor(sFormContainer, 'browse'); // Keep normal colors
        globals.svy_nav_setRequiredFields(_progObj, sFormContainer, 'required_fields');
    }
    else {

		if (globals.nav.program[globals.nav_program_name].form[1][2] == _form
			&& globals.nav.program[globals.nav_program_name].form[1][1] == 'table'
			&& forms[_form].elements.tabs_230
			&& forms[_form].elements.tabs_230.containedForm.toLowerCase().indexOf('utils_quicksearch'.toLowerCase()) > -1
			&& forms[_form].elements.grid) {

			forms[_form].controller.readOnly = false;
			forms[_form].elements.grid.setReadOnly(true);
		}
		else {
	        forms[_form].controller.readOnly = (_status == 'edit' || _status == 'add' || _status == 'find') ? false : true;
		}
        globals.svy_nav_setFieldsColor(sFormContainer, "browse");
        globals.svy_nav_setRequiredFields(_progObj, sFormContainer, "browse");
        _browse = true;
    }

	 // We do not want to change status if a dlg is being shown
	if (sFormContainer.indexOf('program_container_') > -1) {

	    // Set buttons enabled and disabled in the browser bar
	    globals.svy_nav_setBrowserbar(_status, sFormContainer);
	}

    // Set buttons enabled and disabled in viewer bar (if there are tabs)
    if (_progObj.tab.length > 0) {
        globals.svy_nav_setViewerBar(_status, _form);
    }

    /** @type {Array} */
    var _tabs = _progObj.tab;

    // Set tabs enabled/disabled so user can not get to other tab if not in browse mode
    if ((_tabs.length > 1)) {
        for (var i = 1; i < _tabs.length; i++) {
            var _tab_form = globals.nav_program_name + '_tab';
            if (scopes.globals["avUtilities_tabGetSelectedIndex"](_tab_form, 'tabs') == i || _browse) {
                scopes.globals["avUtilities_tabSetEnabled"](_tab_form, 'tabs', i, true);
            } else {
                scopes.globals["avUtilities_tabSetEnabled"](_tab_form, 'tabs', i, false);
            }
        }
    }

    // Set history buttons enabled/disabled
    globals.svy_nav_setNavigationBar(_status);

    if (_browse != false) {
        forms.svy_nav_base.onRecordSelection(null, _form);
    }

    if (_status == "browse") {
        if (forms[_form].gotoBrowse) {
            forms[_form].gotoBrowse();
        }
    } else if (_status == 'edit' || _status == 'add') {
        if (forms[_form].gotoEdit) {
            forms[_form].gotoEdit();
        }
    } else if (_status == 'required_fields') {
        // Call gotoRequiredFields if it exists (useful for custom behavior)
        if (forms[_form].gotoRequiredFields) {
            forms[_form].gotoRequiredFields();
        }

//        // Add clickable field handlers for required fields mode
//        addRequiredFieldsClickHandlers(_form);
    }
}

/**
 *	Set all buttons disabled
 *
 * <AUTHOR> Aleman
 * @since 2007-07-03
 *
 * @properties={typeid:24,uuid:"4a898c45-aa3b-4f5e-91b6-3ea37b1fe900"}
 */
function svy_nav_disable_form_funtions() {
	forms.svy_nav_fr_buttonbar_browser.elements.btn_next.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_prev.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_first.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_last.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_cancel.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_save.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_new.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_print.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_loadAllRecords.enabled = false
	forms.svy_nav_fr_buttonbar_browser.elements.btn_methods.enabled = false

	globals.nav_properties = 'template_images'
	for (var i = 0; i < _to_nav_properties$property_name.property_value.length; i++) {
		forms.svy_nav_fr_buttonbar_browser.elements['btn_template'+i].enabled = false
	}
}

/**
 *	Return the path of a menu, used to select the right menu in the tree.
 *
 * <AUTHOR> Aleman
 * @since 2007-07-03
 * @param {JSRecord<db:/svy_framework/nav_menu_items>} _rec record of the nav_menu table
 * @return {Array} path of the menu

 * @properties={typeid:24,uuid:"08068150-c45e-4e1f-9bf4-c5435c685f5c"}
 */
function svy_nav_getMenuPath(_rec) {
	if (!_rec.parent_id) {
		return [_rec.menu_item_id];
	} else {
		/** @type {JSRecord<db:/svy_framework/nav_menu_items>} */
		var _childRec = _rec.nav_menu_items_to_nav_menu_items$child_to_parent.getRecord(1)
		return globals.svy_nav_getMenuPath(_childRec).concat([_rec.menu_item_id]);
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"5b8e5403-1c9e-45a8-b911-243c40ab144f"}
 */
function svy_nav_getOwner_id() {

	return globals.owner_id

}

/**
 *	Queries the programs where the logged in user had rights for.
 *
 * <AUTHOR> Aleman
 * @since 2007-07-03
 * @return {JSDataSet} dataset with the programs and buttons
 *
 * @properties={typeid:24,uuid:"836ce0ff-e4c3-4016-821f-55d1108bb5e2"}
 */
function svy_nav_getPrograms() {
	var _query
	var i
	var _dataset
	var maxReturnedRows
	// array of all the buttons with security rights
	var _array_buttons = ['btn_new', 'btn_edit', 'btn_duplicate', 'btn_delete', 'btn_method', 'btn_print', 'btn_sort', 'btn_rec_nav', 'btn_search', 'btn_search_prop', 'btn_all_records', 'btn_export', 'btn_resettblheader', 'btn_help', 'btn_record_information', 'btn_required_fields']

	// administator doesn't have to have rights to the keys
	if (_to_sec_user$user_id.flag_system_administrator) {
		_query = 'SELECT 	p.program_name, '
		for (i = 0; i < _array_buttons.length; i++) {
			_query += "	p." + _array_buttons[i]
			if (i != (_array_buttons.length - 1)) {
				_query += ","
			}
		}
		_query += " FROM nav_program p \
					ORDER BY p.program_name";
		maxReturnedRows = 10000
		_dataset = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query, null, maxReturnedRows);
		return _dataset
	}
	// get the rights for normal users
	else {
		/**@type {Array<String>} */
		var aLicensedProgramNames = globals["getLicensedProgramNames"]();
		var sLicensedProgramNames = "'*** NO PROGRAMS AVAILABLE ***'";

		aLicensedProgramNames.push('_ItemSupplier'); // Load _ItemSupplier to all users

		if (aLicensedProgramNames && aLicensedProgramNames.length > 0) {
			sLicensedProgramNames = globals["converArrayToCommaSeparatedString"](aLicensedProgramNames);
		}

		_query = '	SELECT 		p.program_name, '
		for (i = 0; i < _array_buttons.length; i++) {
			_query += "			(	SELECT 	pi." + _array_buttons[i] + "\
									FROM 	nav_program pi\
									WHERE 	(not exists (\
									      	                         	SELECT 	nk.program_name \
									      	                         	FROM	nav_navigation_keys nk \
									      	                         	WHERE	nk.code = '" + _array_buttons[i] + "' and \
									      	                         			nk.program_name  = p.program_name) \
															OR \
											p.program_name IN (\
																		SELECT	nk.program_name\
																		FROM	nav_navigation_keys nk\
																		WHERE	nk.code = '" + _array_buttons[i] + "' and \
																				nk.program_name  = p.program_name and \
																				nk.security_key_id IN (" + globals.nav.keys + "))) and \
											p.program_name = pi.program_name)"
			if (i != (_array_buttons.length - 1)) {
				_query += "	,"
			}
		}

		_query += "				FROM nav_program p \
								WHERE	p.program_name IN ('_ItemSupplier', ( \
																		SELECT 	nk.program_name \
																		FROM	nav_navigation_keys nk \
																		WHERE	nk.code = 'program' and \
																				(p.program_name = nk.program_name and\
																				nk.security_key_id IN (" + globals.nav.keys + "))))  \
										AND p.program_name IN (" + sLicensedProgramNames + ") \
								ORDER BY p.program_name";

		maxReturnedRows = 10000
		_dataset = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query, null, maxReturnedRows);
		return _dataset
	}
}

/**
 *	To step forward, backward or to a position in history, wil reset your form + foundset
 *
 * <AUTHOR> Aleman
 * @since 2007-07-03
 * @param {JSEvent} _event
 * @param {Number} [_position] position you want to step to
 * @param {JSFoundSet} [_foundset] For back after edit, to load the right foundset
 * @return  none
 *
 * @properties={typeid:24,uuid:"77f34d88-84cb-4009-966a-93cae60edfaa"}
 */
function svy_nav_history_move(_event, _position, _foundset) {
	var _button
	if (_event && _event.getElementName) {
		_button = _event.getElementName();
	}
	if (! (_position == undefined)) {
		globals.nav.stack_position = _position
	} else if (_button == 'btn_his_back' && (globals.nav.stack_position > 0)) {
		globals.nav.stack_position--;
	} else if (_button == 'btn_his_forw' && (globals.nav.stack.length > globals.nav.stack_position + 1)) {
		globals.nav.stack_position++;
	} else {
		return
	}

	globals.nav.noHistory = 1

	/** @type {{view:Number,
	 * 			form:String,
	 * 			sql:String,
	 * 			sql_parameters:Array,
	 * 			rowId:Number,
	 * 			foundset_related_text:String,
	 * 			foundset_related_form:String,
	 * 			tab_form:String,
	 * 			program:String,
	 * 			foundsetfilter:Array,
	 * 			tab_rowId:Number,
	 * 			form:Array}}*/
	var _his = globals.nav.stack[globals.nav.stack_position]

	/** @type {{view:Number,form:Array}}*/
	var _progObj = globals.nav.program[_his.program]
		// set the right view
	_progObj.view = _his.view
	// show program of history
	/** @type {String} */
	var sForm = _his.form;
	globals.svy_nav_showForm(sForm, _his.program, true);

	if(!globals.nav.switchedForEdit || forms[_his.form].controller.getDataSource() != _foundset.getDataSource())//if there is a switch for edit right data should be there
	{
		// set foundset filter if related foundset.
		if (_his.foundsetfilter) {
			_foundset = databaseManager.getFoundSet(forms[_his.form].controller.getDataSource())

			for (var j = 0; j < _his.foundsetfilter.length; j++) {
				_foundset.addFoundSetFilterParam(_his.foundsetfilter[j][0], _his.foundsetfilter[j][1], _his.foundsetfilter[j][2])
			}

			_foundset.loadAllRecords()
			forms[_his.form].controller.loadRecords(_foundset)
			globals.nav.related_text = _his.foundset_related_text
			globals.nav.related_form = _his.foundset_related_form
			if (globals.nav.related_text) {
				forms.svy_nav_fr_status_bar.elements.form_name.text += ' -> ' + globals.nav.related_text
			}

		}
		// CH: Added validation for _his.sql and _his.sql_parameters to prevent loss of foundset filters on crm tables when switching between tab views. SL-1966
		// load records with the history foundset, if the sql is not already the same on the form
		if ( _his.sql != null &&  _his.sql_parameters != null && (databaseManager.getSQL(forms[_his.form].foundset) != _his.sql || databaseManager.getSQLParameters(forms[_his.form].foundset) != _his.sql_parameters)) {
			forms[_his.form].foundset.loadRecords(_his.sql, _his.sql_parameters)
		}
		// select the right record, if fails, go to the last record in foundset to select the right one.
		var _select = forms[_his.form].foundset.selectRecord(_his.rowId)
		if (_select == false) //the record is not in the current foundset
		{
			forms[_his.form].foundset.getRecord(databaseManager.getFoundSetCount(forms[_his.form].foundset))
			forms[_his.form].foundset.selectRecord(_his.rowId)
		}
		// if form has tabs, select the right record on the tab
		if (_his.tab_form) // form has tabform
		{
			var _tab_select = forms[_his.tab_form].foundset.selectRecord(_his.tab_rowId)
			if (_tab_select == false) {
				forms[_his.tab_form].foundset.getRecord(databaseManager.getFoundSetCount(forms[_his.tab_form].foundset))
				forms[_his.tab_form].foundset.selectRecord(_his.tab_rowId)
			}
		}

		// sinc the foundset
		var _index = forms[_his.form].foundset.getSelectedIndex()
		for (var i = 0; i < _progObj.form.length; i++) {
			if (forms[_progObj.form[i][2]]) {
				forms[_progObj.form[i][2]].controller.loadRecords(forms[_his.form].foundset)
				forms[_progObj.form[i][2]].controller.setSelectedIndex(_index)
			}

		}
		forms[globals.nav_program_name + '_tab'].controller.loadRecords(forms[_his.form].foundset)
		forms[globals.nav_program_name + '_tab'].controller.setSelectedIndex(_index)

		// load the right records on the tab form
		if (forms[globals.nav.template_types[_progObj.view]].hasTab()) {
			if (forms[globals.nav_program_name + '_tab']) {
				forms[globals.nav_program_name + '_tab'].controller.loadRecords(forms[_his.form].foundset)
			}
		}
	}
	else
	{
		for (var k = 0; k < _progObj.form.length; k++) {
			if (forms[_progObj.form[k][2]]) {
				forms[_progObj.form[k][2]].controller.loadRecords(_foundset)
			}

		}
		forms[globals.nav_program_name + '_tab'].controller.loadRecords(_foundset)
	}
}

/**
 *	To update the history
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @param {String}	_form name of the form
 * @return  none
 *
 * @properties={typeid:24,uuid:"91099c37-5707-47c8-ad2c-d0d5a6cbc8e9"}
 */
function svy_nav_history_update(_form) {
	// if globals.nav.noHistory is set to 1 there will be no stack update
	if (globals.nav.noHistory == 1) {
		globals.nav.noHistory = 0
		return
	}

	var _program = globals.nav_program_name
	var _view = globals.nav.program[_program].view
		// set al properties for history
	var _form_properties = new Object()
	_form_properties.program = _program
	_form_properties.form = _form
	_form_properties.view = _view
	// if there is a related foundset save it for history
	if (globals.nav.his) {

		/** @type {{foundset:String,foundset_related_text:String,foundset_related_form:String}}*/
		var _history = globals.nav.his
		_form_properties.foundsetfilter = _history.foundset
		_form_properties.foundset_related_text = _history.foundset_related_text
		_form_properties.foundset_related_form = _history.foundset_related_form
	}

	// clear the forward stack
	if (globals.nav.stack.length > globals.nav.stack_position + 1) {
		var _ar = new Array();
		for (var i = 0; i <= globals.nav.stack_position; i++) {

			_ar.push(globals.nav.stack[i]);
		}
		globals.nav.stack = _ar;
	}
	// push the properties into the stack and put the stackposition + 1
	globals.nav.stack_position++;
	globals.nav.stack.push(_form_properties);

	// set the history buttons
	forms.svy_nav_fr_tree_top.elements.btn_his_back.enabled = ( (globals.nav.stack_position != 0) && (scopes['avUtils'].isNavModeReadOnly()))
	forms.svy_nav_fr_tree_top.elements.btn_his_forw.enabled = (! (globals.nav.stack.length == globals.nav.stack_position + 1) && (scopes['avUtils'].isNavModeReadOnly()))

	//remove fist stackpos. when there are too much history-entries
	if (globals.nav.stack_position >= globals.nav.his_max_entries) {
//		var _first =
			globals.nav.stack.shift()
		globals.nav.stack_position--
	}

}

/**
 *	Build the navigation object, globals.nav in memory
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 *
 * @properties={typeid:24,uuid:"9ab87bce-b146-46a7-90a9-90034f9229cf"}
 */
function svy_nav_init() {

	//run onPreInit-method of when available
	if (globals['onPreInit']) {
//		var _methodReturn =
			globals['onPreInit']()
	}

	//remove tooltips if iphone/ipad
	globals.svy_nav_removeTooltips()

	//set template buttons
	// FIXME: Is this really needed? In any case, it should use the existing elements and not duplicates and set font-icon
//	globals.svy_nav_setTemplateButtons()

	//set the divider properties if user has no divider properties
	if (!application.getUserProperty(application.getSolutionName() + '.svy_nav_fr_main.tab_split.divLoc')) {
		application.setUserProperty(application.getSolutionName() + '.svy_nav_fr_main.tab_split.divLoc', '175')
	}
	if (!application.getUserProperty(application.getSolutionName() + '.svy_nav_fr_menu.tab_split.divLoc')) {
		application.setUserProperty(application.getSolutionName() + '.svy_nav_fr_menu.tab_split.divLoc', '400')
	}

	// the stack for the history and the position
	globals.nav.stack = new Array()
	globals.nav.stack_position = -1

	//set browser viewer forms
	globals.svy_nav_set_form_names();

	// load all the forms with properties into the object
	var _program_array = globals.svy_nav_init_programs();

	//user has not even the right to see one program
	if (!_program_array && !_to_sec_user$user_id.flag_system_administrator) {
		globals.DIALOGS.showWarningDialog('i18n:svy.fr.lbl.warning', 'i18n:svy.fr.lbl.warning_no_program', i18n.getI18NMessage('avanti.dialog.ok'))
		security.logout()
	}

	var _function_array = globals.svy_nav_get_functions()

		//load the nav properties
	globals.nav_properties = 'template_types'
	globals.nav.template_types = _to_nav_properties$property_name.property_value
	globals.nav_properties = 'template_types_notabs'
	globals.nav.template_types_notabs = _to_nav_properties$property_name.property_value
	globals.nav_properties = 'default_edit_template'
	globals.nav.default_edit_template = _to_nav_properties$property_name.property_value[0]
	globals.nav_properties = 'multiple_tabs_per_program'
	globals.svy_nav_multi_tab_programs = (_to_nav_properties$property_name.property_value[0] == 'true')

	// load the stucture of the menu into the object
	globals.svy_nav_init_menu_items(_program_array, _function_array);

	// load the functions into the nav object
	globals.svy_nav_init_functions()

	//set nav style form
	if(forms['svy_nav_style_custom'])
	{
		globals.nav_styleForm = 'svy_nav_style_custom'
	}

	//load the valuelists
	if (globals.svy_val_startup_setvaluelists) {
		globals.svy_val_startup_setvaluelists();
	}

	//set template images
	globals.svy_nav_init_setTemplateImages()

	// set date
	forms.svy_nav_fr_status_bar.elements.lbl_date.text = utils.dateFormat(new Date(), 'dd-MMM-yyyy') + ' - ' + i18n.getI18NMessage('svy.fr.lbl.week') + ': ' + utils.dateFormat(new Date(), 'w')

	globals.nav.mode = "browse"

	globals.nav.his = new Object()
	globals.nav.his.foundset = null
	globals.nav.childRelation = null
	globals.nav.his.foundset_related_text = null
	globals.nav.his_max_entries = 20
	globals.nav.related_text = null
	globals.nav.related_form = null
	globals.nav.switchedForEdit = false

	// Webclient and smartclient have different behaviour on clicks if there are layers:
	// in webclient the method has to be on the top layer
	// if the method is in the onAction of the toplayer, the onMouseOver image on the smartclient doesn't look right.
	if (application.getApplicationType() == 5)//webclient
	{
		var jsFormShortcuts = solutionModel.getForm('svy_nav_fr_shortcuts')
		for (var _i = 0; _i < 7; _i++) {
			var jsLabel = jsFormShortcuts.getLabel("shortcutLabel" + _i)
			jsLabel.onAction = solutionModel.getGlobalMethod("globals",'svy_nav_shortcut_goto')
		}
	}

	// load shortcuts/bookmarks
	forms.svy_nav_fr_shortcuts.loadShortcuts()
	forms.svy_nav_fr_bookmarks.loadBookmarks()
	globals.nav.recordHistory = new Array()
	forms.svy_nav_fr_tree_top.elements.btn_his_forw.enabled = false
	forms.svy_nav_fr_tree_top.elements.btn_his_back.enabled = false
	forms.svy_nav_fr_tree_top.elements.btn_shortcut.enabled = false
	forms.svy_nav_fr_tree_top.elements.btn_bookmark.enabled = false
	forms.svy_nav_fr_viewCompareTab.setViewButtonsEnabled(false)

	//init shortkeys
	globals.svy_nav_setShortKeys(_function_array)

	//set startup form
	globals.nav_properties = 'startup_program'
	if(_to_nav_properties$property_name.property_value && _to_nav_properties$property_name.property_value[0])
	{
		var _program = _to_nav_properties$property_name.property_value[0]
		var _template = globals.nav.template_types[globals.nav.program[_program].view]
		var _form = globals.nav.program[_program].form[forms[_template].has1()][2]
		globals.svy_nav_showForm(_form, _program);
	}

	//	application.putClientProperty("ComboBox.disabledBackground", new Packages.javax.swing.plaf.ColorUIResource(java.awt.Color.GREEN));
	application.putClientProperty("ComboBox.disabledForeground", new Packages.javax.swing.plaf.ColorUIResource(java.awt.Color.BLACK));

	//run onPostInit-method of when available
	if (globals['onPostInit']) {
//		_methodReturn =
			globals['onPostInit']()
	}


	//test sanneke
//	solutionModel.removeForm('svy_nav_fr_buttonbar_browser')
//	var _jsformbro = solutionModel.getForm('svy_nav_fr_buttonbar_browser_custom')
//	_jsformbro.name = 'svy_nav_fr_buttonbar_browser'
//	forms.svy_nav_fr_buttonbar_browser.controller.recreateUI()
}

/**
 *	Sort the tree menu and hide all the items where the user has no rights for.
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @param {Array} _program_array array with all the programs available for this user
 * @param {Array} _function_array
 * @properties={typeid:24,uuid:"540e7bfa-863b-41b1-be84-e8868faa6bb6"}
 * @AllowToRunInFind
 */
function svy_nav_init_menu_items(_program_array, _function_array) {


	var _query_menus = "SELECT menu_id\
						FROM nav_menu\
						WHERE security_key_id IN (" + globals.nav.keys + ") OR security_key_id is null\
						ORDER BY priority"
	var _answer, _open_config
	var _dataset = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query_menus, null, 1)

	if (!_dataset.getValue(1, 1)) {
		if(_to_sec_user$user_id.flag_system_administrator) //if user is admin also show option to open the configurator
		{
			_open_config = i18n.getI18NMessage('svy.fr.lbl.open_configurator')
			_answer = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.warning'), i18n.getI18NMessage('svy.fr.dlg.noMenuWarning'), i18n.getI18NMessage('svy.fr.lbl.logout'),_open_config );
			if(_answer == _open_config)
			{
				globals._1_svy_nav_openNavigationForm()
				return
			}
		}
		else
		{
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.warning'), i18n.getI18NMessage('svy.fr.dlg.noMenuWarning'), i18n.getI18NMessage('avanti.dialog.ok'));
			security.logout()
		}

		//throw a exeption to let the methods stop
		throw 'User has no menu';
	}

	if(_program_array.length < 1)
	{
		if(_to_sec_user$user_id.flag_system_administrator) //if user is admin also show option to open the configurator
		{
			_open_config = i18n.getI18NMessage('svy.fr.lbl.open_configurator')
			_answer = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.warning'), i18n.getI18NMessage('svy.fr.dlg.noMenuWarning'), i18n.getI18NMessage('svy.fr.lbl.logout'),_open_config );
			if(_answer == _open_config)
			{
				globals._1_svy_nav_openNavigationForm()
				return
			}
		}
		throw 'User has no right to programs';
	}

	var _program_string = _program_array.join("','")

	var _function_string = ''
	if(_function_array.length > 0)
	{
		_function_string = _function_array.join(",")
	}

		//some databases don't like in queries on empty strings
	if (_program_string == '') {
		_program_string = '-1'
	}
	if (_function_string == '') {
		_function_string = '-1'
	}

	//set a table filter
	var _query = "	SELECT 	menu_item_id \
					FROM 	nav_menu_items\
					WHERE	menu_id = " + _dataset.getValue(1, 1) + " AND\
							(menuitem_type = 'P' AND \
							program_name in ('" + _program_string + "'))\
					OR 	    description = 'i18n:avanti.program.home' \
					OR 		(menuitem_type = 'F' AND \
							function_id in (" + _function_string + "))\
					OR		(menuitem_type != 'P' AND\
							menu_item_id in (	SELECT 	 parent_id\
							            	FROM 	nav_menu_items\
											WHERE	menu_id = " + _dataset.getValue(1, 1) + " AND\
													(menuitem_type = 'P' AND \
													program_name in ('" + _program_string + "'))\
											OR 		(menuitem_type = 'F' AND \
													function_id in (" + _function_string + "))\
											OR		(menuitem_type != 'P' AND\
													menu_item_id in (	SELECT 	 parent_id\
									            	FROM 	nav_menu_items\
													WHERE	menu_id = " + _dataset.getValue(1, 1) + " AND\
															(menuitem_type = 'P' AND \
															program_name in ('" + _program_string + "'))\
															OR 		(menuitem_type = 'F' AND \
															function_id in (" + _function_string + "))))))"





	if (!_to_sec_user$user_id.flag_system_administrator) {
		var ds = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query, null, -1);
		if (ds){
			var aData = ds.getColumnAsArray(1);
			databaseManager.addTableFilterParam(globals.nav_db_framework, 'nav_menu_items', 'menu_item_id', 'sql:in', aData);
		}

//		databaseManager.addTableFilterParam(globals.nav_db_framework, 'nav_menu_items', 'menu_item_id', 'sql:in', _query)
	}

	var fs = forms.svy_nav_fr_tree.foundset;
	fs.sort('sort_order asc', true);

	if ( fs.find() || fs.find() ) {

		fs.parent_id = '^';
		fs.menu_id = _dataset.getValue(1, 1);
		fs.search();


	} else {
		application.output("Could not load nav_menu_items into treebean", LOGGINGLEVEL.ERROR);
	}

//	forms.svy_nav_fr_tree.controller.find()
//	forms.svy_nav_fr_tree.parent_id = '^'
//	forms.svy_nav_fr_tree.menu_id = _dataset.getValue(1, 1)
//	forms.svy_nav_fr_tree.controller.sort('sort_order asc')
//	forms.svy_nav_fr_tree.controller.search()

	// check if iPhone is used
//	if(!scopes.svySystem.isIOSPlatform())
	if(!scopes.svySystem.isIphoneIpad())
	{
		forms.svy_nav_fr_tree.init_tree();
		replaceCRMMenuItemWithJobManager(fs);
	}
	else
	{
		//mobile menu
		globals.svy_nav_m_init_menu()
	}

}


/**
 * @AllowToRunInFind
 *
 * Replace CRM root menu to "Job Manager" if there is only "Jobs" program under it
 * @param {JSFoundSet<db:/svy_framework/nav_menu_items>} fsNavMenuItem
 *
 * @properties={typeid:24,uuid:"372D1763-35DE-453C-81F3-A57930837EFA"}
 */
function replaceCRMMenuItemWithJobManager(fsNavMenuItem) {

	if (fsNavMenuItem && utils.hasRecords(fsNavMenuItem)) {
		for (var i = 1; i <= fsNavMenuItem.getSize(); i++) {
			var rCRMRootMenuItem = fsNavMenuItem.getRecord(i);

			if (rCRMRootMenuItem.menu_item_id === 67) { // CRM. See file 'nav_menu_items.csv'
				var fsCRMchildMenus = rCRMRootMenuItem.nav_menu_items_to_nav_menu_items$parent_id;
				// because data is saved in a database we need to update it each time
				if (fsCRMchildMenus &&
					utils.hasRecords(fsCRMchildMenus) &&
					fsCRMchildMenus.getSize() === 1) {
					var rCRMchildMenuItem = fsCRMchildMenus.getRecord(1);
					if (rCRMchildMenuItem.program_name.toLowerCase() === "crm_job_manager_view") {
						scopes['avSystem'].hideMenuItem(67); // hide 'CRM' root menu
						return;
					}
				}
				break;
			}
		}
		scopes['avSystem'].hideMenuItem(597); // hide 'Job Manager' root menu
	}

}

/**
 *	Init all the programs that are available for the login user, with tabs menu's'.
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @return {Array} programs that the user has rights for
 *
 *@SuppressWarnings(deprecated)
 *
 * @properties={typeid:24,uuid:"b55cf7ac-8092-4ecc-b5c9-84e1c2bfbbee"}
 */
function svy_nav_init_programs() {
	// make a program object in the nav object
	globals.nav.program = new Object()

	globals["override_i18n"]();

	var _rec;
//	_rec_tab, _rec_button
	var _data_rec = 1

		// get the programs where the user has rights for
	var _dataset = globals.svy_nav_getPrograms();

	//JMW - 2017-02-17: We need to verify that it's not in the dataset before adding otherwise and index used later on is out of synch.
	var iMax = _dataset.getMaxRowIndex();
	var bChartEditorAdded = false;
	var b_SA_Order_Revision_HeaderAdded = false;
	for(var idx = 1; idx <= iMax; idx++){
		var sProgramName = _dataset.getValue(idx,1);
		if(sProgramName == "Chart_Editor"){
			bChartEditorAdded = true;
		} else if(sProgramName == "_sa_order_revision_header"){
			b_SA_Order_Revision_HeaderAdded = true;
		}
	}

	if(!bChartEditorAdded){
		// GD - 2013-06-20: Add the Chart Editor here
		_dataset.addRow(_dataset.getMaxRowIndex() + 1, ["Chart_Editor", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ]);
	}

	if(!b_SA_Order_Revision_HeaderAdded){
		_dataset.addRow(_dataset.getMaxRowIndex() + 1, ["_sa_order_revision_header", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ]);
	}

	var _program_array = _dataset.getColumnAsArray(1)




	if (_dataset.getMaxRowIndex() == 0) {
		return null
	}

	forms.svy_nav_c_program_dtl.foundset.loadRecords(_dataset)

	//set progress bar properties
	// RG 2014-11-20 updated to finally work!
	var _progress = 0 ;
	var _begin_progress = 5 ;
	var _increment = ( 100 - _begin_progress ) / forms.svy_nav_c_program_dtl.controller.getMaxRecordIndex() ;
	var _iChunks = 10 ;

	for (var i = 1; i <= forms.svy_nav_c_program_dtl.controller.getMaxRecordIndex(); i++) {
		_rec = forms.svy_nav_c_program_dtl.foundset.getRecord(i)

		// setting progress bar.
		if ((i % _iChunks) == 0) {
			_progress = _begin_progress + ( _increment * i ) ;
			forms.svy_nav_fr_loading.setStatusBar( _progress ) ;
		}
		if (!security.canRead(_rec.getDataSource()) &&
		!((_rec.server_name == null && _rec.table_name == null)))// no security rights so remove from array
		{

			_program_array = _program_array.filter(function(x) {
				return x != _rec.program_name;
			});
		}

		if ( (_rec.program_name == _dataset.getValue(_data_rec, 1) ||
				_to_sec_user$user_id.flag_system_administrator) &&
				_rec.form_object ) {
			// in the dataset are
			// 1. program_name
			// 2. btn_new
			// 3. btn_edit
			// 4. btn_duplicate
			// 5. btn_delete
			// 6. btn_method
			// 7. btn_print
			// 8. btn_sort
			// 9. btn_rec_nav
			// 10. btn_search
			// 11. btn_search_prop
			// 12. btn_all_records
			// 13. btn_export
			// 14. btn_resettblheader
			// 15. btn_help
			// 16. btn_record_information
			// 17. btn_required_fields
			// put record information into the nav object

			var _programObject = globals.nav.program[_rec.program_name] = { };
			_programObject.description = _rec.display_description
			_programObject.program_image = _rec.program_image
			_programObject.program_name = _rec.program_name
			_programObject.delete_mode = _dataset.getValue(_data_rec, 5)
			_programObject.duplicate_mode = _dataset.getValue(_data_rec, 4)
			_programObject.update_mode = _dataset.getValue(_data_rec, 3)
			_programObject.add_mode = _dataset.getValue(_data_rec, 2)
			_programObject.divider_locked = _rec.divider_locked
			_programObject.divider_height = _rec.divider_height
			if (!_programObject.divider_height) _programObject.divider_height = 350
			_programObject.display_field_header = _rec.display_field_header
			_programObject.sort_value = _rec.sort_value
			_programObject.base_form_name = _rec.base_form_name
			_programObject.record_locking = _rec.record_locking
			_programObject.btn_all_records = false //_dataset.getValue(_data_rec, 12)
			_programObject.btn_method = _dataset.getValue(_data_rec, 6)
			_programObject.btn_print = _dataset.getValue(_data_rec, 7)
			_programObject.btn_search = false //_dataset.getValue(_data_rec, 10)
			_programObject.btn_search_prop = false //_dataset.getValue(_data_rec, 11)
			_programObject.btn_sort = _dataset.getValue(_data_rec, 8)
			_programObject.btn_rec_nav = _dataset.getValue(_data_rec, 9)
			_programObject.btn_export = _dataset.getValue(_data_rec, 13)
			_programObject.btn_resettblheader = false //_dataset.getValue(_data_rec, 14)
			_programObject.btn_help = _dataset.getValue(_data_rec, 15)
			_programObject.btn_record_information = _dataset.getValue(_data_rec, 16)
			_programObject.btn_required_fields = _dataset.getValue(_data_rec, 17)
			_programObject.btn_lookup_new = _rec.btn_lookup_new
			_programObject.btn_lookup_show = _rec.btn_lookup_show
			_programObject.server_name = _rec.server_name
			_programObject.table_name = _rec.table_name
			_programObject.form = _rec.form_object
			_programObject.template = _rec.template_object
			_programObject.view = _rec.startup_view
			_programObject.noreadonly = _rec.noreadonly
			_programObject.nobuttonbar = _rec.flag_no_buttonbar
			_programObject.empty_foundset = _rec.empty_foundset;

			//if program is in the menu, lookup the menu path
			if (utils.hasRecords(_rec.nav_program_to_nav_menu_items)) {
				  /** @type {JSRecord<db:/svy_framework/nav_menu_items>} */
				  var _childRec = _rec.nav_program_to_nav_menu_items.getRecord(1)
				_programObject.path = globals.svy_nav_getMenuPath(_childRec);
			} else {
				_programObject.path = null
			}

			//create the table form
			globals.svy_nav_create_tab(_rec.program_name, _rec.server_name, _rec.table_name, _rec.divider_height)

			// set all the TABS of a program in a array
			_programObject.tab = new Array()
			_programObject.tab.selected = 1
			//			_programObject.tab[0] = 1
			_programObject.table_tab_form = false

			// set all the METHODS of a program in a array
			_programObject.method = new Array()

			// set all the REPORT of a program in a array
			_programObject.report = new Array()

			// set filters
			_programObject.filter = new Array()
		}
		_data_rec++
	}

	//query the tab information
	var _stringPrograms = _program_array.join("','")
	var _queries_tab = "SELECT 	program_name,\
									relation,\
									form_type,\
									display_description,\
									edit_on_tab,\
									default_access,\
									delete_mode,\
									update_mode,\
									add_mode,\
									programtab_id,\
									target_program_name\
						FROM 		nav_programtabs\
						WHERE		program_name IN ('" + _stringPrograms + "')\
									and target_program_name IN ('"+_stringPrograms+"')\
						ORDER BY 	program_name asc, tab_sequence asc "
	var _dataset_tab = databaseManager.getDataSetByQuery(globals.nav_db_framework, _queries_tab, null, -1)

	var _tabIndObj = new Object()
	var _tabIds = _dataset_tab.getColumnAsArray(10)
	for (var j = 1; j <= _dataset_tab.getMaxRowIndex(); j++) {
		if (!globals.nav.program[_dataset_tab.getValue(j, 11)]) continue
		_programObject = globals.nav.program[_dataset_tab.getValue(j, 1)]
		var _tabnr = _programObject.tab.length
		if (_tabnr == 0)_tabnr = 1
		/** @type {{relation:String}} */
		var _tabObject = _programObject.tab[_tabnr] = new Object()
		_tabObject.program_name = _dataset_tab.getValue(j, 11)
		_tabObject.base_form_name = globals.nav.program[_dataset_tab.getValue(j, 11)].base_form_name
		_tabObject.relation = _dataset_tab.getValue(j, 2)
		_tabObject.form_type = _dataset_tab.getValue(j, 3)
		_tabObject.description = _dataset_tab.getValue(j, 4)
		_tabObject.edit_on_tab = _dataset_tab.getValue(j, 5)
		_tabIndObj[_dataset_tab.getValue(j, 10)] = _tabObject
		if (_dataset_tab.getValue(j, 6)) //default access
		{
			_tabObject.delete_mode = globals.nav.program[_dataset_tab.getValue(j, 11)].delete_mode
			_tabObject.update_mode = globals.nav.program[_dataset_tab.getValue(j, 11)].update_mode
			_tabObject.add_mode = globals.nav.program[_dataset_tab.getValue(j, 11)].add_mode
		} else {
			_tabObject.delete_mode = _dataset_tab.getValue(j, 7)
			_tabObject.update_mode = _dataset_tab.getValue(j, 8)
			_tabObject.add_mode = _dataset_tab.getValue(j, 9)
		}
		// set RELATION filters
		_tabObject.relation_filter = new Array()
		if (_tabObject.relation) {
			var _lastRelation = _tabObject.relation.replace(/.*\./, ""); //there could be multiple relations joined, in that case whe only need to know the last one for the filter fields.
			var _startRelation = utils.stringReplace(_tabObject.relation,_lastRelation,'')
			var jsRelation = solutionModel.getRelation(_lastRelation)
			if(jsRelation) //relation has to exist
			{
				var jsRelationtitems = jsRelation.getRelationItems()
				for (var q = 0; q < jsRelationtitems.length; q++) {
					_tabObject.relation_filter[q] = new Object()
					_tabObject.relation_filter[q].from_key = _startRelation + jsRelationtitems[q].primaryDataProviderID
					_tabObject.relation_filter[q].to_key = jsRelationtitems[q].foreignColumnName
					_tabObject.relation_filter[q].operator = jsRelationtitems[q].operator;
				}
			}
		}
	}

	if (_tabIds.join(","))//otherwise the are no tabs
	{

		var _stringTabIds = _tabIds.join(",")
		var _query_autofill = "SELECT 	child_field,\
										expression_type,\
										parent_expression,\
										programtab_id\
							FROM 		nav_programtabs_autofill\
							WHERE		programtab_id IN (" + _stringTabIds + ")\
							ORDER BY 	programtab_id  asc "
		var _dataset_autofill = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query_autofill, null, -1)

		var _Id
		var _autofill_array

		for (var k = 1; k <= _dataset_autofill.getMaxRowIndex(); k++) {

			if (_Id != _dataset_autofill.getValue(k, 4)) {
				_tabObject = _tabIndObj[_dataset_autofill.getValue(k, 4)]
				_autofill_array = _tabObject.autofill = new Array()
				_Id = _dataset_autofill.getValue(k, 4)
			}

			var _autofillObj = _autofill_array[_autofill_array.length] = new Object()
			_autofillObj.child_field = _dataset_autofill.getValue(k, 1)
			_autofillObj.expression_type = _dataset_autofill.getValue(k, 2)
			_autofillObj.parent_expression = _dataset_autofill.getValue(k, 3)
		}
	}

	var _query_methods_reports
		//query the REPORTS AND METHODS

	var aArgs = [];
    var bCustomerOn = _stringPrograms.indexOf("'Customers'") > -1;
    var bCRMCustomerOn = _stringPrograms.indexOf("'CRM Customer'") > -1;

    if (!bCustomerOn && bCRMCustomerOn) {
        var sProgramsBak = _stringPrograms;
        _stringPrograms += "','Customers";
    }

	if (_to_sec_user$user_id.flag_system_administrator) // administator doesn't have to have rights to the keys
	{

		_query_methods_reports = "\
			SELECT 	p.menu_type, \
					p.in_browse, \
					p.nav_popmenu_id, \
					p.in_edit, \
					p.label, \
					p.function_id, \
					p.parent_popmenu_id,\
					p.program_name \
			FROM	nav_popmenu p \
			WHERE	(p.owner_id is null OR p.owner_id = ?) \
			AND p.program_name  IN ('" + _stringPrograms + "')\
			ORDER BY p.program_name, p.sequence_nr"
		aArgs.push(globals.svy_sec_lgn_owner_id);

	}
	else {
		_query_methods_reports = "\
			SELECT p.menu_type, p.in_browse, p.nav_popmenu_id, p.in_edit, p.label, p.function_id, p.parent_popmenu_id, p.program_name \
			FROM nav_popmenu p \
			WHERE p.program_name IN ('" + _stringPrograms + "') \
			AND (\
			p.nav_popmenu_id IN ( \
				SELECT nk.nav_popmenu_id \
				FROM nav_navigation_keys nk \
				WHERE (p.owner_id is null OR p.owner_id = ?) \
				AND nk.code = p.menu_type and \
				nk.nav_popmenu_id  = p.nav_popmenu_id and \
				nk.security_key_id IN (" + globals.nav.keys + ") \
			)\
			OR p.nav_popmenu_id IN \
			( \
				SELECT p3.parent_popmenu_id \
				FROM nav_navigation_keys nk2 INNER JOIN nav_popmenu p3 ON nk2.nav_popmenu_id = p3.nav_popmenu_id \
				WHERE nk2.security_key_id IN (" + globals.nav.keys + ") \
			)\
			) \
			ORDER BY p.program_name, p.sequence_nr";
		aArgs.push(globals.svy_sec_lgn_owner_id);
	}

	var _dataset_popmenu = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query_methods_reports, aArgs, -1);

	var _menu_type
	for (var m = 1; m <= _dataset_popmenu.getMaxRowIndex(); m++) {
		if (_dataset_popmenu.getValue(m, 1) == 'F') // method
		{
			_menu_type = 'method'
		} else if (_dataset_popmenu.getValue(m, 1) == 'R') // report
		{
			_menu_type = 'report'
		}

	    // sl-17087 - we have to copy customer reports to crm customer object, so we can run cust reports from there
        if (_dataset_popmenu.getValue(m, 8) == "Customers") {
            if (bCustomerOn) {
                createMethodObject("Customers");
            }
            if (bCRMCustomerOn) {
                createMethodObject("CRM Customer");
            }
        }
        else {
            createMethodObject(_dataset_popmenu.getValue(m, 8));
        }
	}

    if (sProgramsBak) {
        _stringPrograms = sProgramsBak;
    }

	//query the filters
	var _query_filter = " SELECT 	p.program_name,\
									ff.filter_field_name,\
									ff.filter_operator,\
									ff.filter_value\
							FROM	nav_foundset_filter_parameters ff,\
									nav_program p\
							WHERE	ff.nav_foundset_filter_id = p.nav_foundset_filter_id\
							AND		p.program_name IN ('" + _stringPrograms + "')"

	var _dataset_filter = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query_filter, null, -1);
	for (var n = 1; n <= _dataset_filter.getMaxRowIndex(); n++) {
		var _filterObj = globals.nav.program[_dataset_filter.getValue(n, 1)].filter[globals.nav.program[_dataset_filter.getValue(n, 1)].filter.length] = new Object()
		_filterObj.filter_field_name = _dataset_filter.getValue(n, 2)
		_filterObj.filter_operator = _dataset_filter.getValue(n, 3)
		/** @type {String} */
		var _filter_value = _dataset_filter.getValue(n, 4)
		if (/^\[/.test(_filter_value)) {
			_filter_value = _filter_value.substring(1, _filter_value.length - 1).split(",");
		}
		_filterObj.filter_value = _filter_value

	}

	return _program_array;

	function createMethodObject(sProgrameName) {
        var _methodObj = globals.nav.program[sProgrameName][_menu_type][globals.nav.program[sProgrameName][_menu_type].length] = new Object();

        _methodObj.in_browse = _dataset_popmenu.getValue(m, 2)
        _methodObj.nav_popmenu_id = _dataset_popmenu.getValue(m, 3)
        _methodObj.in_edit = _dataset_popmenu.getValue(m, 4)
        _methodObj.label = _dataset_popmenu.getValue(m, 5)
        _methodObj.function_id = _dataset_popmenu.getValue(m, 6)
        _methodObj.parent_popmenu_id = _dataset_popmenu.getValue(m, 7)
	}
}

/**
 *	Load table view persistance, loads the position of the columns that the user defined
 *	This method works only if you use svy_nav_saveTableViewPersistance to
 *
 * <AUTHOR> Aleman/ Joas de Haan
 * @since 2008-11-01
 *
 * @param {String}	[_form] form you want to load the tableview persistance
 * @param {String} [_formSaveName] dynamic created form can work with a form save name
 *
 *
 * @properties={typeid:24,uuid:"5DE989D3-06ED-4CE7-A73A-D3291D46F43D"}
 */
function svy_nav_loadTableViewPersistance(_form, _formSaveName) {

	// GD - Apr 1, 2014: Writing this method for speed
	var _width = 0,
		_height = 0,
		_x = 0,
		_y = 0,
		iMax = 0,
		i = 0,
		fs,
		rRec,
		sElement = "";

	if(_formSaveName) {
		globals.nav_current_formname =_formSaveName;
	} else {
		globals.nav_current_formname = _form;
	}

	if (!_form) return

	if (forms[_form].elements.grid)  return;

	// loop true all the records for this user and this form
	fs = _to_sec_user_table_properties$formname$user_id$organization_id;
	fs.loadAllRecords();
	iMax = fs.getSize();
	for (i = 1; i <= iMax; i++) {

		rRec = fs.getRecord(i);
		sElement = rRec.element_name; //globals.nav_element_name
		_x = rRec.location_x;
		_y = rRec.location_y;
		_width = rRec.width;
		_height = rRec.height;

		if (forms[_form].elements[sElement]) {
			// set the size and the location
			if(_width != null && _height != null) {
				forms[_form].elements[sElement].setSize(_width, _height);
			}
			if (_y == undefined) _y = 0;
			if (_x != null) scopes.globals["avUtilities_setComponentLocation"](forms[_form].elements[sElement], _x, _y); //forms[_form].elements[sElement].setLocation(_x, _y);
		}
	}
}





/**
 *	Called by opening the module
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 *
 *
 * @properties={typeid:24,uuid:"3B064328-5331-4948-A2D7-4C8F49560CD6"}
 */
function svy_nav_onOpen() {
	forms.svy_nav_fr_postLogin.controller.show()

	// set owner id's
	globals.owner_id = globals.svy_sec_owner_id = globals.svy_sec_lgn_owner_id;

	globals.svy_nav_setGlobals();

	//check if we should switch the framework db
	if (globals["svy_nav_getFrameworkDBName"]) {
		globals.nav_db_framework = globals["svy_nav_getFrameworkDBName"]();
		if (forms.svy_sec_login.vFramework_db && (forms.svy_sec_login.vFramework_db != globals.nav_db_framework)) {
//		application.output("Switching framework db from " + globals.nav_db_framework + " to " + forms.svy_sec_login.vFramework_db);
		databaseManager.switchServer(globals.nav_db_framework, forms.svy_sec_login.vFramework_db);
	}
	}

	//check if we should switch the user db
	if (forms.svy_sec_login.vUser_db && globals["svy_nav_getUserDBName"] && (forms.svy_sec_login.vUser_db != globals["svy_nav_getUserDBName"]())) {
//		application.output("Switching user db from " + globals["svy_nav_getUserDBName"]() + " to " + forms.svy_sec_login.vUser_db);
		databaseManager.switchServer(globals["svy_nav_getUserDBName"](), forms.svy_sec_login.vUser_db);
	}

	/** @type {JSFoundSet<db:/svy_framework/sec_owner>}*/
	var _fs = databaseManager.getFoundSet(globals.nav_db_framework, "sec_owner");
	_fs.loadRecords(databaseManager.convertToDataSet([globals.svy_sec_owner_id]));
	var _newDB = _fs.database_name;
	if (_newDB != null && _newDB != "" && _newDB != globals.nav_server_user) {
		databaseManager.switchServer(globals.nav_server_user, _newDB);
	}

	//filter data
	globals.svy_nav_filterOrganization();

	databaseManager.setCreateEmptyFormFoundsets();

	//run onOpen-method of when available
	if (globals['onOpen']) {
		globals['onOpen']();
	}

	//onPost Login
	globals.svy_nav_postLogin();

	forms.svy_nav_fr_main.controller.show();
}

/**
 *	Popmenu for the general reports that are not attached to a specific program
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @param {JSEvent} [_event] the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"1170B4F0-6C21-4C7B-B47E-57BB6229D580"}
 */
function svy_nav_popmenu_main_reports(_event) {

	var _menu = plugins.window.createPopupMenu()
	var _submenu, _rec, _rec_child, _item

	if (!_to_nav_popmenu$main_report$roots.getSize()) {
		return
	}
	var _keys_ar = [];
	if (globals.nav.keys != -1) {
		/** @type {String} */
		var _keys = globals.nav.keys
		_keys_ar = _keys.split(",").map(function (x) {return x*1;});
	}

	// read the menu out the tables and create a popmenu
	for (var i = 1; i <= _to_nav_popmenu$main_report$roots.getSize(); i++) {
		_rec = _to_nav_popmenu$main_report$roots.getRecord(i)

		if (databaseManager.hasRecords(_rec.nav_popmenu_to_nav_navigation_keys$popmenu) &&
			_rec.nav_popmenu_to_nav_navigation_keys$popmenu.security_key_id &&
			_keys_ar.indexOf(_rec.nav_popmenu_to_nav_navigation_keys$popmenu.security_key_id) < 0)
		{
			continue; //user has no rights for this menu
		}

		if(utils.hasRecords(_rec.nav_popmenu_to_nav_popmenu$children))
		{
			_submenu = null;
			for (var j = 1; j <= _rec.nav_popmenu_to_nav_popmenu$children.getSize(); j++) {
				_rec_child = _rec.nav_popmenu_to_nav_popmenu$children.getRecord(j);

				if (databaseManager.hasRecords(_rec_child.nav_popmenu_to_nav_navigation_keys$popmenu) &&
					_rec_child.nav_popmenu_to_nav_navigation_keys$popmenu.security_key_id &&
					_keys_ar.indexOf(_rec_child.nav_popmenu_to_nav_navigation_keys$popmenu.security_key_id) < 0)
				{
					continue; //user has no rights for this menu
				}

				if (!_submenu) { //Add parent it it doesn't exist yet. Parent is not added if the user doesn't have rights to any of the children.
					_submenu = _menu.addMenu(_rec.label)
				}
				_item = _submenu.addMenuItem(_rec_child.label, globals.svy_nav_callFunctionFromPopmenu)
				_item.methodArguments = [_rec_child.function_id]

			}
		}
		else
		{
			_item  = _menu.addMenuItem(_rec.label, globals.svy_nav_callFunctionFromPopmenu);
			_item.methodArguments = [_rec.function_id]
		}
	}

	/** @type {RuntimeComponent} */
	var _source = _event.getSource()
	if (_source != null) {
		_menu.show(_source);
	}
}

/**
 * Save table view persistence for a form
 *
 * <AUTHOR> by Gary Dotzlaw
 * @since 2024-03-18
 *
 * @param {String} _form
 * @param {String} [_formSaveName] dynamic created form can work with a form save name
 *
 *
 * @properties={typeid:24,uuid:"AB971201-59D4-46B9-81CD-83CE38BEE0E2"}
 */
function svy_nav_saveTableViewPersistance(_form, _formSaveName) {
    if (!_form) return;

    // Skip if no grid component
    if (!forms[_form] || !forms[_form].elements || !forms[_form].elements.grid) {
        return;
    }

    // Ensure CURRENT_GRID_FORM is set correctly
    scopes['avGrid'].CURRENT_GRID_FORM = _form;

    // Check if form has tabs_230 (avGrid menus)
    if (forms[_form].elements.tabs_230) {
        // For forms with avGrid menus, implement the interactive behavior
        if (!hasGridChanged(_form)) {
            return; // No changes, nothing to save
        }

        // Check if user has a default grid
        var userDefaultGrid = scopes['avGrid'].getPreferenceBaseGrid();

        if (!userDefaultGrid) {
            // User has no default grid - automatically save current state as default
            scopes['avGrid'].onSaveGrid(scopes['avGrid'].DEFAULT_GRID, true, false);
        } else {
            // User already has a default grid and made changes - prompt to save
//            promptToSaveGridChanges(_form);
        }
    } else {
        // For forms without avGrid menus, always save as default without prompting
        scopes['avGrid'].onSaveGrid(scopes['avGrid'].DEFAULT_GRID, true, false);
    }
}

/**
 * Checks if the grid state has changed since it was loaded
 *
 * @private
 * @param {String} formName - The form name
 * @return {Boolean} - True if grid or filter state has changed
 *
 * @properties={typeid:24,uuid:"ADAEB1D4-0D10-4E28-A5CD-00FAF9DA0591"}
 */
function hasGridChanged(formName) {
    if (!forms[formName] || !forms[formName].elements || !forms[formName].elements.grid) {
        return false;
    }

    if (!globals.oInitialGridStates[formName]) {
        return true; // No initial state, assume changed
    }

    var quickSearchFormName = 'utils_quickSearch_' + formName;
    var currentGridState = JSON.stringify(forms[formName].elements.grid.getColumnState());
    var currentFilterState = null;

    if (forms[quickSearchFormName] && forms[quickSearchFormName].toolbarFilter) {
        currentFilterState = JSON.stringify(forms[quickSearchFormName].toolbarFilter.getToolbarFiltersState());
    }

    // Compare current state with initial state
    if (currentGridState !== globals.oInitialGridStates[formName].grid) {
        return true;
    }

    // Special handling for empty filters
    if (currentFilterState === "[]" && globals.oInitialGridStates[formName].filter === null) {
        return false; // Empty array and null are functionally the same (no filters)
    }

    if (globals.oInitialGridStates[formName].filter === "[]" && currentFilterState === null) {
        return false; // Empty array and null are functionally the same (no filters)
    }

    if (currentFilterState !== globals.oInitialGridStates[formName].filter) {
        return true;
    }

    return false;
}

/**
 * Prompts the user to save grid changes
 *
 * @private
 * @param {String} formName - The form name
 *
 * @properties={typeid:24,uuid:"A49D8A5C-4F75-4568-87C7-3AF2D97740E1"}
 */
function promptToSaveGridChanges(formName) {
    // Show input dialog to ask user for the grid name
    var result = scopes.globals.DIALOGS.showInputDialog(
        i18n.getI18NMessage('avanti.grid.saveCurrentGrid'),
        i18n.getI18NMessage('avanti.grid.enterGridNameOrDefault'),'default'
    );

    if (!result) {
        return; // User cancelled, don't save anything
    }

    var gridName = result.trim();

    if (!gridName) {
        return; // Empty input, don't save anything
    }

    if (gridName.toLowerCase() === 'default' || gridName.toLowerCase() === 'défaut') {
        // Save as default grid (replacing existing if it exists)
        scopes['avGrid'].onSaveGrid(scopes['avGrid'].DEFAULT_GRID, true, false);
    } else {
        // Save as new named grid
        scopes['avGrid'].onSaveGrid(gridName, false, false);
    }
}

/**
 *	Set the form names where the framework will work with
 *
 * <AUTHOR> Aleman
 * @since 2005-11-24
 * @return  none
 *
 *
 * @properties={typeid:24,uuid:"8CB20C34-866E-48D8-9C27-2EB18844B4E1"}
 */
function svy_nav_set_form_names() {
	nav.viewer_form = 'svy_nav_fr_viewer'
	nav.browser_form = 'svy_nav_fr_browser'
	nav.viewer_buttonbar = 'svy_nav_fr_buttonbar_viewer'
	nav.browser_buttonbar = 'svy_nav_fr_buttonbar_browser'
}


/**
 *	Set the color of the field, used to give fields a different color in edit
 *
 * <AUTHOR> Dotzlaww
 * @since 2024-11-29
 * @param {String} _form name of the form
 * @param {String} _status you want to set it to (edit/browse)
 * @return  none
 *
 *
 * @properties={typeid:24,uuid:"22A0865E-3748-4759-BB90-20B888617FE6"}
 */
function svy_nav_setFieldsColor(_form, _status) {
	if (!forms[_form].elements) {
		return;
	}

	var _elements = forms[_form].elements.allnames;
    var STYLE_COMPONENTS = [
//        globals.BOOTSTRAP_COMPONENT_MAPPING.BUTTON,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.IMAGE_MEDIA,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.LABEL,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.CHECK,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.CALENDAR,
        globals.BOOTSTRAP_COMPONENT_MAPPING.COMBOBOX,
        globals.BOOTSTRAP_COMPONENT_MAPPING.FIELD,
        globals.BOOTSTRAP_COMPONENT_MAPPING.HTML_AREA,
        globals.BOOTSTRAP_COMPONENT_MAPPING.DATA_LABEL,
        globals.BOOTSTRAP_COMPONENT_MAPPING.LISTBOX,
        globals.BOOTSTRAP_COMPONENT_MAPPING.MULTISELECT_LISTBOX,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.RADIOS,
        globals.BOOTSTRAP_COMPONENT_MAPPING.RTF_AREA,
//        globals.BOOTSTRAP_COMPONENT_MAPPING.SPINNER,
        globals.BOOTSTRAP_COMPONENT_MAPPING.TEXT_AREA,
        globals.BOOTSTRAP_COMPONENT_MAPPING.TYPE_AHEAD,
		globals.BOOTSTRAP_COMPONENT_MAPPING.CALENDAR
    ];


    for (var i = 0; i < _elements.length; i++) {
        var _element = _elements[i];
        var element = forms[_form].elements[_element];
        if (!element) continue;

        var _type;
        try {
            _type = element.getElementType();
        } catch (e) {
            _type = null;
        }

        if (!_type) continue;

        if (STYLE_COMPONENTS.indexOf(_type) !== -1) {
            if (element.getDesignTimeProperty("byPassStyle")) continue;

            var _color = null;
            if (element.enabled && _status === 'edit') {
            	_color = globals.SVY_ENUM_COLOR_MODE.enabled;

            	if(element.enabled == false ){
            		_color = globals.SVY_ENUM_COLOR_MODE.disabled;
            	}
//            	if (_type === globals.BOOTSTRAP_COMPONENT_MAPPING.COMBOBOX) {
//                   _color += ' bts-combobox-edit-mode';
//                }
            }
            else if (!element.enabled) {
	            _color = globals.SVY_ENUM_COLOR_MODE.disabled;
            }

            scopes.globals["avUtilities_setStyleClass"](element, _color);

        } else if (_type === globals.BOOTSTRAP_COMPONENT_MAPPING.TABPANEL) {
            for (var k = 1; k <= scopes.globals["avUtilities_tabGetMaxTabIndex"](_form, _element); k++) {
                var _formTab = scopes.globals["avUtilities_tabGetFormName"](_form, _element, k);
                if (_formTab && forms[_formTab]) {
                    globals.svy_nav_setFieldsColor(_formTab, _status);
                }
            }
        } else if (_type === globals.BOOTSTRAP_COMPONENT_MAPPING.SPLIT_PANE) {
            try {
                var rightForm = element.getRightForm();
                var leftForm = element.getLeftForm();

                if (rightForm) {
                    var rightFormTab = rightForm.controller.getName();
                    if (rightFormTab && forms[rightFormTab]) {
                        globals.svy_nav_setFieldsColor(rightFormTab, _status);
                    }
                }
                if (leftForm) {
                    var leftFormTab = leftForm.controller.getName();
                    if (leftFormTab && forms[leftFormTab]) {
                        globals.svy_nav_setFieldsColor(leftFormTab, _status);
                    }
                }
            } catch (e) {
                // unhandled exception
            }
        } else if (_type === globals.BOOTSTRAP_COMPONENT_MAPPING.TABLESS && element.containedForm) {
            globals.svy_nav_setFieldsColor(element.containedForm, _status);
        }
    }
}

/**
 * Creates a program container form with split pane layout
 *
 * @private
 * @param {String} containerFormName The name for the new container form
 * @param {String} buttonBarFormName The name of the button bar form
 * @param {String} contentFormName The name of the content form
 * @return {JSForm} The created container form
 *
 *
 * @properties={typeid:24,uuid:"44512A10-7D8E-469A-8D14-4D237BAD7503"}
 */
function createProgramContainerForm(containerFormName, buttonBarFormName, contentFormName) {
    // Create the container form
    var jsForm = solutionModel.newForm(containerFormName, null, null, false, 800, 600);
    jsForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER | SM_SCROLLBAR.VERTICAL_SCROLLBAR_NEVER;
    jsForm.useCssPosition = true;

    var splitPane = jsForm.newWebComponent('splitpane', 'servoyextra-splitpane');
    splitPane.setJSONProperty('splitType', 1); // Vertical
    splitPane.setJSONProperty('divLocation', 33);
    splitPane.setJSONProperty('divSize', 0);
    splitPane.setJSONProperty('pane1MinSize', 30);
    splitPane.setJSONProperty('pane2MinSize', 30);
    splitPane.setJSONProperty('enabled', true);
    splitPane.setJSONProperty('visible', true);
    splitPane.cssPosition.t('0px').l('0px').r('0px').b('0px').w('920px').h('600px');

    // Create onLoad method to set the forms
    var loadCode = 'function onLoad(event) {\n' +
        ' elements.splitpane.setLeftForm(forms.' + buttonBarFormName + ');\n' +
        ' elements.splitpane.setRightForm(forms.' + contentFormName + ');\n' +
        ' return;\n' +
        '}';

    var onLoadMethod = jsForm.newMethod(loadCode);
    jsForm.onLoad = onLoadMethod;

    return jsForm;
}

/**
 * @param {String} programName
 * @param {Object} _progObj
 *
 * @properties={typeid:24,uuid:"A62ACD23-FA35-4DEA-9FA1-BECCCC3C7BF5"}
 */
function createTabsForm(programName, _progObj) {
    // Create form for the tab panel
    var tabFormName = programName + '_tab';
    /**@type {JSForm} */
    var tabsForm = solutionModel.getForm(tabFormName);
    if (!tabsForm) {
	    tabsForm = solutionModel.newForm(tabFormName, null, null, false, 800, 500);
    }

    var tabPanel = tabsForm.getWebComponent('tabs');
    if (!tabPanel) {
	    tabsForm.useCssPosition = true;
	    tabsForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER | SM_SCROLLBAR.VERTICAL_SCROLLBAR_NEVER;
	    tabPanel = tabsForm.newWebComponent('tabs', 'bootstrapcomponents-tabpanel');
	    tabPanel.cssPosition.t('5px').l('0px').r('0px').b('0px');

	    // Add tabs from _progObj.tab
	    if ( _progObj.tab.length > 0) {

		    for (var i = 1; i < _progObj.tab.length; i++) {
		    	/** @type {{add_mode: number,
		        base_form_name: string,
		        delete_mode: number,
		        description: string,
		        edit_on_tab: number,
		        form_type: number,
		        program_name: string,
		        relation: string,
		        relation_filter: Array<{
		            from_key: string,
		            operator: string,
		            to_key: string
		        }>,
		        update_mode: number}} */
		        var tabConfig = _progObj.tab[i];
		        var formAtTab = globals.nav.program[tabConfig.program_name].form[tabConfig.form_type][2];

		// 					avUtilities_tabAdd(sForm, 			sTab, 		sTabForm, 		sTabLabel, 				sTabRelation, 		aTabMap, sTabText, 				iTabIndex, sTabToolTip, tabPanel, bHideCloseIcon, sIconStyleClass, sTabStyleClass)
		        scopes.globals["avUtilities_tabAdd"](tabFormName, 'tabs', forms[formAtTab], tabConfig.description, tabConfig.relation, null, 	tabConfig.program_name, -1, 		null, 		null, 		false);
	        }
	    }
    }

    return tabsForm;
}

/**
 * Creates a program container form with split pane layout
 *
 * @private
 * @param {String} containerFormName The name for the new container form
 * @param {String} template The name of the content form
 * @param {String} template3 The name of the content form
 * @param {String} _form The name of the content form
 * @return {JSForm} The created container form
 *
 *
 *
 * @properties={typeid:24,uuid:"656943BD-BCE8-476F-BE0B-76EB1C136E3E"}
 */
function createComplexProgramContainerForm(containerFormName, template, template3, _form) {
	/**@type {JSForm} */
	var jsForm = solutionModel.getForm(containerFormName);
    if (jsForm) return jsForm;

	jsForm = solutionModel.newForm(containerFormName, null, null, false, 800, 500);
    jsForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER | SM_SCROLLBAR.VERTICAL_SCROLLBAR_NEVER;
    jsForm.useCssPosition = true;
    jsForm.extendsForm = solutionModel.getForm('svy_nav_base');

    var splitPane = jsForm.newWebComponent('splitpane', 'servoyextra-splitpane');
    splitPane.cssPosition.t('5px').l('0px').r('0px').b('0px').w('800px').h('500px');
    splitPane.setJSONProperty('divLocation', 33);
    splitPane.setJSONProperty('divSize', 0);
    splitPane.setJSONProperty('splitType', 1);

    // Create the tabs form for template3
    /**@type {JSForm} */
    var jsTabsForm = createTabsForm(globals.nav_program_name, globals.nav.program[globals.nav_program_name]);

    if (jsTabsForm) {
    	var sTabsForm = jsTabsForm.name;
	    if (forms[template3]) {
	    	forms[template3].elements.tab_split.setLeftForm(forms.svy_nav_fr_buttonbar_viewer);
	    	forms[template3].elements.tab_split.setRightForm(sTabsForm);
	    }
	    // Then set template splits
	    if (forms[template]) {
	    	forms[template].elements.tab_split.setLeftForm(forms[ _form]);
	    	forms[template].elements.tab_split.setRightForm(forms[template3]);
	    }
	    // Finally set main split
	    forms[containerFormName].elements.splitpane.setLeftForm(forms.svy_nav_fr_buttonbar_browser);
	    forms[containerFormName].elements.splitpane.setRightForm(forms[template]);

//	    application.output("Completed splitpane complex view build...Left Form: " + forms[template].elements.tab_split.getLeftForm()._formname_);
//	    application.output("Completed splitpane complex view build...Right Form: " + forms[template].elements.tab_split.getRightForm()._formname_);
//	    application.output("Completed splitpane complex view build...svy_nav_fr_template3 Left Form: " + forms[template3].elements.tab_split.getLeftForm()._formname_);
//	    application.output("Completed splitpane complex view build...svy_nav_fr_template3 Right Form: " + forms[template3].elements.tab_split.getRightForm()._formname_);
    }

	return jsForm;
}

/**
 *	To show a form in the framework
 *
 * <AUTHOR> Aleman
 * @since 2005-11-24
 * @param {String} _form the name of the form
 * @param {String} [_program] name of the program
 * @param {Boolean} [_showAll] Load all records
 * @param {Boolean} [_orgTableView] don't go the the default table view
 * @param {Number} [_forcedTabNr] the number of the tab the form is on
 *
 * @return  {Array} array with the selected form and program
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"B7A9C5D8-E3F2-4A16-9D8C-F6E5A4B3C2D1"}
 */
function svy_nav_showForm(_form, _program, _showAll, _orgTableView, _forcedTabNr) {

 	globals.nav_program_name = _program;
 	var splitPos = 0;
	var sPrefType = '';
	var savedLocation = 0;

 	var template = globals.nav.getTemplate();
    var template3 = 'svy_nav_fr_template3';
    var templateForm = forms[template];
    var hasComplexTemplate = templateForm && templateForm.hasSplitter();


    globals.nav.programCurrent = _program;
    var bCRM = (_program.toLowerCase().indexOf('crm_') > -1) ? true : false;
    var sProgramDisplayName = (bCRM) ? 'CRM_' : '';
    sProgramDisplayName += globals.nav.program[_program].description.replace(/_/g, ' ');

    /** @type {{	view:Number,
	 * 				foundset:JSFoundSet,
	 * 				divider_height:Number,
	 * 				base_form_name:String,
	 * 				path:Array,
	 * 				filter:Array,
	 * 				nobuttonbar:Number,
	 * 				form:Array,
	 * 				divider_locked:Number,
	 * 				tab:Array,
	 * 				empty_foundset:Number,
	 * 				table_name: String}}*/
	var _progObj = globals.nav.program[_program]
	var containerFormName = 'program_container_' + _program;
	globals.nav.programContainer = containerFormName;
	globals.nav.programTabForm = (hasComplexTemplate) ? _program + '_tab': null;

    // Initialize the programs tab panel if needed
    if (!forms.svy_nav_fr_programs) {
        var tabPanelForm = solutionModel.newForm('svy_nav_fr_programs', null, null, false, 800, 600);
        tabPanelForm.useCssPosition = true;
        tabPanelForm.scrollbars = SM_SCROLLBAR.HORIZONTAL_SCROLLBAR_NEVER | SM_SCROLLBAR.VERTICAL_SCROLLBAR_NEVER;
        var onChangeCode = 'function onChange(previousIndex, event) {\n' +
        ' var index = 0;\n' +
        ' if (globals.nav.mode != "browse") {\n' +
        '   index = previousIndex;\n' +
        '   // application.output("Previous Index: " + previousIndex);\n' +
        ' }\n' +
        ' else {\n' +
		'   index = scopes.globals.avUtilities_tabGetSelectedIndex("svy_nav_fr_programs", "tabpanel");\n' +
        '   // application.output("New Index: " + previousIndex);\n' +
        ' }\n' +
	    ' var currentTab = scopes.globals.avUtilities_tabGetName("svy_nav_fr_programs", "tabpanel", index);\n' +
        ' if (currentTab) {\n' +
        '   var containerForm = "program_container_" + currentTab;\n' +
		'   // index = scopes.globals.avUtilities_tabGetSelectedIndex("svy_nav_fr_programs", "tabpanel");\n' +
        '   var currentForm = forms[containerForm];\n'+
		'	var currentProgObj = globals.nav.program[currentTab];\n' +
        '   if (currentForm && currentForm.elements.splitpane) {\n' +
        '     globals.nav_program_name = currentTab;\n' +
        '     globals.nav.programContainer = containerForm;\n' +
        '     globals.nav.lastProgram = currentTab;\n' +
        '     globals.nav.form_view_01 = currentForm.elements.splitpane.getRightForm().controller.getName();\n' +
		'	  globals.svy_nav_form_name = currentForm.elements.splitpane.getRightForm().controller.getName();\n' +
		'	  globals.nav.related_text = globals.nav.related_form = "";\n' +
		'	  globals.nav.lastView = currentProgObj.view;\n' +
		'	  globals.nav_base_form_name = currentProgObj.base_form_name;\n' +
		'  }\n' +
		'   globals.svy_nav_dc_setStatus(globals.nav.mode, globals.svy_nav_form_name)' +
		' }\n' +
       '}';

	    /** @type {RuntimeWebComponent<bootstrapcomponents-tabpanel>} */
        var tabPanel = tabPanelForm.newWebComponent('tabpanel', 'bootstrapcomponents-tabpanel');
		var onChangeMethod = tabPanelForm.newMethod(onChangeCode);
		tabPanel.setHandler('onChange', onChangeMethod);

		var onTabClickedCode = 'function onTabClicked(event, clickedTabIndex, dataTarget) {\n' +
		 ' if (globals.nav.mode != "browse") {\n' +
		 '   //application.output("onTabClicked Event fired: " + globals.nav.mode);\n' +
		 '   return false;\n' +
		 ' }\n' +
		 ' return true;\n' +
		 ' //application.output("nav mode: " + globals.nav.mode);\n' +
		 '}';

		 var onTabCloseCode = 'function onTabClose(event, clickedTabIndex) {\n' +
		    '    if (globals.nav.mode != "browse") {\n' +
		    '        //application.output("onTabClosed Event fired: " + globals.nav.mode);\n' +
		    '        return false;\n' +
		    '    }\n' +
		    '    return true;\n' +
		    '}';


		var onTabClickedMethod = tabPanelForm.newMethod(onTabClickedCode);
		var onTabCloseMethod = tabPanelForm.newMethod(onTabCloseCode);
		tabPanel.setHandler('onTabClicked', onTabClickedMethod);
		tabPanel.setHandler('onTabClose', onTabCloseMethod);

        tabPanel.setJSONProperty('closeIconStyleClass', 'glyphicon glyphicon-remove close-icon');
        tabPanel.setJSONProperty('showTabCloseIcon', true);
        tabPanel.setJSONProperty('styleClass', 'nav-tabs-container');
        tabPanel.cssPosition.t('0px').l('2px').r('0px').b('0px');
        tabPanel.cssPosition.w('800px').h('600px');


        // Add the tab panel form to the main split pane
        forms.svy_nav_fr_main.elements.tab_split.setRightForm(forms['svy_nav_fr_programs']);
    }
    else {
    	var sForm = forms.svy_nav_fr_main.elements.tab_split.getRightForm();
    	if (sForm && sForm.controller.getName() == 'dbrd_home') {
    		// switch back to normal navigation
	    	forms.svy_nav_fr_main.elements.tab_split.setRightForm(forms['svy_nav_fr_programs']);
    	}
    }

    // Load foundset if needed
    if (_showAll !== false && globals.nav.program[_program].table_name) {
        var progFoundset = databaseManager.getFoundSet(forms[_form].controller.getDataSource());

        if (globals.nav.program[_program].foundset) {
            forms[_form].controller.loadRecords(globals.nav.program[_program].foundset);
        }
        else {
            // Apply filters
        	for (var j = 0; j < _progObj.filter.length; j++) {
				/** @type {String} */
				var _value = _progObj.filter[j].filter_value
					//global value is used
				if (utils.stringPatternCount(_value, 'globals.')) {
					_value = eval(_value);
				}
				progFoundset.addFoundSetFilterParam(_progObj.filter[j].filter_field_name, _progObj.filter[j].filter_operator, _value, 'svynav_' + _progObj.filter[j].filter_field_name + '_filter');
			}

            progFoundset.loadAllRecords();
            forms[_form].controller.loadRecords(progFoundset);
            globals.nav.program[_program].foundset = progFoundset;
        }
    }

    /** @type {RuntimeWebComponent<bootstrapcomponents-tabpanel>} */
    var tabPanelComponent = forms.svy_nav_fr_programs.elements.tabpanel;
    var existingTabIndex = scopes.globals["avUtilities_tabGetIndexByLabel"]('svy_nav_fr_programs', 'tabpanel', sProgramDisplayName);

    if (nav_program_name == "Dashboard") {

        forms.svy_nav_fr_main.elements.tab_split.setRightForm('svy_nav_fr_programs');
    }
    else if (!hasComplexTemplate) {

    	var sBrowserForm = 'svy_nav_fr_buttonbar_browser';
		if (nav_program_name == "Triggers_&_Alerts") {
			sBrowserForm = 'svy_nav_fr_browser'
		}

	    if (existingTabIndex !== -1) {

	    	containerFormName = scopes.globals["avUtilities_tabGetFormName"]('svy_nav_fr_programs', 'tabpanel', existingTabIndex);

            // Update the right form in the existing split pane
            forms[containerFormName].elements.splitpane.setRightForm(forms[_form]);

	        tabPanelComponent.tabIndex = existingTabIndex;
	    }
	    else {

	        // New tab panel
	        if (!forms[containerFormName]) {

	            createProgramContainerForm(containerFormName, sBrowserForm, _form);
	        }

	        // Add new tab with the container form
	        var tabAdded = scopes.globals["avUtilities_tabAdd"]('svy_nav_fr_programs', 'tabpanel', forms[containerFormName], sProgramDisplayName.replace('_',' '), null, null, _program, -1, null, null, false);
	        if (!tabAdded) {
	            application.output('Failed to add tab for program: ' + _program, LOGGINGLEVEL.ERROR);
	            return [];
	        }

	        // Sort tabs alphabetically 
	        //SL-30012 Commenting it out for now, will revisit in future 
//	        tabPanelComponent.tabs.sort(function(a, b) {
//	            return a.text.localeCompare(b.text);
//	        });
	    }
    }
    else {

		var sCloneTemplate =  template + '_' + application.getUUID().toString().slice(1,8);
		var sCloneTemplate3 = template3 + '_' + application.getUUID().toString().slice(1,8);
       
        var jsTemplate = solutionModel.getForm(template);
        var jsCloneTemplate = solutionModel.cloneForm(sCloneTemplate, jsTemplate);
        var jsTemplate3 = solutionModel.getForm(template3);
        var jsCloneTemplate3 = solutionModel.cloneForm(sCloneTemplate3, jsTemplate3);
        
    	if (existingTabIndex !== -1) {   		

	    	containerFormName = scopes.globals["avUtilities_tabGetFormName"]('svy_nav_fr_programs', 'tabpanel', existingTabIndex);

		    if (forms[containerFormName].elements.splitpane.getRightForm()._formname_ != jsCloneTemplate) {

		    	var sTabsForm = globals.nav_program_name + '_tab';

			    if (forms[jsCloneTemplate3]) {
			    	forms[jsCloneTemplate3].elements.tab_split.setLeftForm(forms.svy_nav_fr_buttonbar_viewer);
			    	forms[jsCloneTemplate3].elements.tab_split.setRightForm(sTabsForm);
			    }
			    // Then set template splits
			    if (forms[jsCloneTemplate]) {
			    	forms[jsCloneTemplate].elements.tab_split.setLeftForm(forms[ _form]);
			    	forms[jsCloneTemplate].elements.tab_split.setRightForm(forms[jsCloneTemplate3]);
			    }
			    // Finally set main split
			    forms[containerFormName].elements.splitpane.setLeftForm(forms.svy_nav_fr_buttonbar_browser);
			    forms[containerFormName].elements.splitpane.setRightForm(forms[jsCloneTemplate]);

//			    application.output("Completed splitpane complex view build...Left Form: " + forms[template].elements.tab_split.getLeftForm()._formname_);
//			    application.output("Completed splitpane complex view build...Right Form: " + forms[template].elements.tab_split.getRightForm()._formname_);
//			    application.output("Completed splitpane complex view build...svy_nav_fr_template3 Left Form: " + forms[template3].elements.tab_split.getLeftForm()._formname_);
//			    application.output("Completed splitpane complex view build...svy_nav_fr_template3 Right Form: " + forms[template3].elements.tab_split.getRightForm()._formname_);

			    setComplexTabpanelDividers();

			    scopes.globals["avUtilities_tabAdd"]('svy_nav_fr_programs', 'tabpanel', forms[containerFormName], sProgramDisplayName.replace('_',' '), null, null, _program, -1, null, null, false);
		    }
		    else {

		    	// Update the right form in the existing split pane
	            forms[containerFormName].elements.splitpane.setRightForm(forms[_form]);
		    }
	    }
	    else {

	    	if (!forms[containerFormName]) {

	    		createComplexProgramContainerForm(containerFormName, sCloneTemplate, sCloneTemplate3, _form);

	    		setComplexTabpanelDividers();
	    	}

	        scopes.globals["avUtilities_tabAdd"]('svy_nav_fr_programs', 'tabpanel', forms[containerFormName], sProgramDisplayName.replace('_',' '), null, null, _program, -1, null, null, false);

	        // Sort tabs alphabetically
	        //SL-30012 Commenting it out for now, will revisit in future 
//	        tabPanelComponent.tabs.sort(function(a, b) {
//	            return a.text.localeCompare(b.text);
//	        });
	    }
    }

    function setComplexTabpanelDividers() {

    	if (_progObj.divider_locked) {
            forms[sCloneTemplate].elements.tab_split.divSize = 0;
            forms[sCloneTemplate3].elements.tab_split.divSize = 0;
        } else {
            forms[sCloneTemplate].elements.tab_split.divSize = 1;
            forms[sCloneTemplate3].elements.tab_split.divSize = 0;
        }

        if (_progObj.divider_height) {
            splitPos = _progObj.divider_height;
            if (globals.nav_show_open_tabs) splitPos += 25;

            // Get saved divider location from user preferences
//            sPrefType = _program.replace(/\s/g,"_") + '_template' + _progObj.view + '_divider';
//            savedLocation = scopes.avUtils.getUserPref(sPrefType);
//            if (savedLocation) {
//                splitPos = parseInt(savedLocation);
//            }
//            forms[template].elements.tab_split.divLocation = splitPos;
            forms[sCloneTemplate3].elements.tab_split.divLocation = 30;
        }
    }

    globals.nav_program_name = _program;
	globals.nav.lastProgram = _program;
	globals.nav.lastView = _progObj.view;
	globals.nav_base_form_name = _progObj.base_form_name;
	globals.nav.related_text = globals.nav.related_form = '';
	globals.svy_nav_form_name = _form;
    globals.nav.form_view_01 = _form;
    if (hasComplexTemplate) {
		globals.nav.form_view_02 = globals.nav_program_name + '_tab';
	}
	else {
		globals.nav.form_view_02 = null;
	}

	//set the right view
	if(globals.svy_nav_multi_tab_programs && forms['svy_nav_fr_openTabs']['setView'])
	{
		forms['svy_nav_fr_openTabs']['setView']()
	}

	//run onPreShow-method of program when available
	if (globals[_program + '_onPreShow']) {
		var _methodReturn = globals[_program + '_onPreShow']()

		if (_methodReturn == -1) {
			return [];
		}
	}

	// sync the foundset, if there is a tableL4741
	if (_progObj.table_name) {
		var _index = forms[_form].foundset.getSelectedIndex()
		for (var i = 0; i < _progObj.form.length; i++) {
			if (forms[_progObj.form[i][2]]) {
				forms[_progObj.form[i][2]].controller.loadRecords(forms[_form].foundset)
				forms[_progObj.form[i][2]].controller.setSelectedIndex(_index)
			}

		}
        if (forms[globals.nav_program_name + '_tab'].foundset.getDataSource() == forms[_form].foundset.getDataSource()) {
            forms[globals.nav_program_name + '_tab'].controller.loadRecords(forms[_form].foundset);
            forms[globals.nav_program_name + '_tab'].controller.setSelectedIndex(_index);
        }
	}

	//set image to show with template is selected
	var _elementsButtonbar = forms.svy_nav_fr_buttonbar_browser.elements
	globals.nav_properties = 'template_images'
	for (i = 0; i < _to_nav_properties$property_name.property_value.length; i++) {
		_elementsButtonbar['template_selected'+i].visible = false
	}
	_elementsButtonbar['template_selected' + _progObj.view].visible = true

	// write form to history stack
	globals.svy_nav_history_update(_form);

	var sForm = null;
	if (globals.nav.switchedForEdit
			&& globals.nav.stack[1]
			&& globals.nav.stack[1].form) {
	    sForm = globals.nav.stack[1].form;
	}
	else {
	    sForm = _form;
	}

	tabPanelComponent = forms.svy_nav_fr_programs.elements.tabpanel;
	var iIndex = scopes.globals["avUtilities_tabGetIndexByLabel"](null, null, sProgramDisplayName.replace(/_/g, ' '), tabPanelComponent);
	scopes.globals["avUtilities_tabSetSelectedIndex"](null, null, iIndex, tabPanelComponent);
	application.executeLater(selectNewTab, 500, [tabPanelComponent, iIndex, sForm]);

	return [];
}

/**
 * @param {RuntimeWebComponent<bootstrapcomponents-tabpanel>} tabPanelComponent
 * @param {Number} iIndex
 * @param {String} sForm
 *
 * @properties={typeid:24,uuid:"7981E4E6-C180-41D4-8B27-407C376B625F"}
 */
function selectNewTab(tabPanelComponent, iIndex, sForm) {
	scopes.globals["avUtilities_tabSetSelectedIndex"](null, null, iIndex, tabPanelComponent);
	globals.svy_nav_dc_setStatus(globals.nav.mode, sForm);
	if(globals.nav.mode === 'add') {
		globals.svy_nav_setFieldsColor(sForm, 'edit');
	} else {
		globals.svy_nav_setFieldsColor(sForm, globals.nav.mode);
	}

}

/**
*
* @param {JSEvent} event
* @param {String} sFormName - name of form to be removed from history/solutionModel
* @public
*
* @return
* @properties={typeid:24,uuid:"CBA42586-4538-4F14-84C8-DE00E89753A9"}
*/
function removeFormJistory (event, sFormName) {
	var success = history.removeForm(sFormName);
	if ( success ) {
		solutionModel.removeForm(sFormName);
	}
	return;
}
/**
 *	To toggle view between table/form or top/bottom
 *
 * <AUTHOR> Aleman
 * @since 2005-11-24
 * @param {JSEvent} [_event] the event that triggered the action
 * @param {String}	[_formToArg] optional form you want to show
 * @param {String} [sButton] optional button you want to emulate got clicked
 * @param {Boolean} [bNoFoundsetLoad] optional to not load the foundset
 * @return  none
 *
 * @properties={typeid:24,uuid:"a57e0b12-f008-409b-9a7f-5da6cdc8643f"}
 * @AllowToRunInFind
 */
function svy_nav_toggleView(_event, _formToArg, sButton, bNoFoundsetLoad) {
	var _button = _event.getElementName();
	var _form_trigger = _event.getFormName()

	if (sButton) {
		_button = sButton;
	} else if (forms[_form_trigger].elements[_button].getElementType() == 'aggrid-groupingtable') {
		// If this method is called from the onClick event of a grid it's most likely this button
		// Use the sButton parameter for special cases
		_button = 'btn_template0';
	}

	var _form
	var _form_to
//	var _base
	var _program
	var _program_name = globals.nav.program[globals.nav_program_name].description
	var _tab_nr
	var _tab_form
	var _lenght

		// fill variables depending on if top form is calling the method or bottom form
	if ( (_form_trigger == globals.nav.browser_buttonbar)
		|| (_event.getFormName() == globals.nav.form_view_01)
		|| _event.getFormName() == "in_trans_entry_header_tbl"
		|| _event.getFormName() == "sch_schedule_tbl_by_job"
		|| _event.getFormName() == "sch_schedule_tbl_scheduled_jobs"
		|| _event.getFormName() == "in_trans_inquiry_tbl_details"
		|| _event.getFormName() == "svy_nav_fr_status_bar" ) {
		_form = globals.nav.form_view_01;
//		_base = globals.nav.program[globals.nav_program_name].base_form_name
		_program = globals.nav_program_name;
	} else if ( (_form_trigger == globals.nav.viewer_buttonbar)
		|| (_formToArg)
		|| (_event.getFormName() == globals.nav.form_view_02)) {
		_tab_form = globals.nav.lastProgram + '_tab';
		_tab_nr = scopes.globals["avUtilities_tabGetSelectedIndex"](_tab_form, "tabs");
		_form = scopes.globals["avUtilities_tabGetFormName"](_tab_form, "tabs", _tab_nr);
//		_base = globals.nav.program[globals.nav_program_name].tab[_tab_nr].base_form_name
		_program = globals.nav.program[globals.nav_program_name].tab[_tab_nr].program_name;
		_form_trigger = globals.nav.viewer_buttonbar;
		globals.nav.toggle = 1;
		globals.nav.activeView = 1;
	}
	else {
        var tabIndex = scopes.globals["avUtilities_tabGetSelectedIndex"]('svy_nav_fr_programs', 'tabpanel');
        _program = scopes.globals["avUtilities_tabGetName"]('svy_nav_fr_programs', 'tabpanel', tabIndex);
    }

	/** @type {{	view:Number,
	 * 				foundset:JSFoundSet,
	 * 				divider_height:Number,
	 * 				base_form_name:String,
	 * 				filter:Array,
	 * 				form:Array,
	 * 				divider_locked:Number}}*/
	var _progObj = globals.nav.program[_program]


	// define the form where the method will navigate to
	if (utils.stringPatternCount(_button, 'btn_template') > 0) // method is called by one of the template buttons
	{
		var _buttonnr = utils.stringReplace(_button, 'btn_template', '')
		_progObj.view = _buttonnr
		var _template = globals.nav.template_types[_buttonnr]
		_form_to = _progObj.form[forms[_template].has1()][2]
	} else {
		_form_to = _formToArg;
	}

	// if the toggle is from the bottom form to the top form filters have to be set.
	if ( !bNoFoundsetLoad && _form_trigger == globals.nav.viewer_buttonbar) {
		var _foundset = databaseManager.getFoundSet(forms[_form].controller.getDataSource())
		/** @type {{foundset:Array,parentProgram:String,tabIndex:Number,parentRec:JSRecord,foundset_related_form:Sting,foundset_related_text:String}} */
		globals.nav.his = new Object()
		globals.nav.his.foundset = new Array()
		globals.nav.his.parentProgram = globals.nav_program_name
		globals.nav.his.tabIndex = globals.nav.program[globals.nav_program_name]['tab']['selected']
		globals.nav.his.parentRec = forms[globals.nav.form_view_01].foundset.getRecord(forms[globals.nav.form_view_01].foundset.getSelectedIndex())

		// set program specific filters
		for (var j = 0; j < _progObj.filter.length; j++) {

			/** @type {String} */
			var _value = _progObj.filter[j].filter_value
				//global value is used
			if (utils.stringPatternCount(_value, 'globals.')) {
				_value = eval(_value)
			}
			_foundset.addFoundSetFilterParam(_progObj.filter[j].filter_field_name, _progObj.filter[j].filter_operator, _value, 'svynav_' + _progObj.filter[j].filter_field_name + '_filter')
			_lenght = globals.nav.his.foundset.length
			globals.nav.his.foundset[_lenght] = new Array()
			globals.nav.his.foundset[_lenght][0] = _progObj.filter[j].filter_field_name
			globals.nav.his.foundset[_lenght][1] = _progObj.filter[j].filter_operator
			globals.nav.his.foundset[_lenght][2] = _value
			globals.nav.his.foundset[_lenght][3] =_from_key
		}

		//copy the sort
		_foundset.sort(forms[_form].foundset.getCurrentSort(),true)

		// set relation specific filters
		for (var k = 0; k < nav.program[globals.nav_program_name].tab[_tab_nr]['relation_filter'].length; k++) {
			/** @type {String} */
			var _from_key = nav.program[globals.nav_program_name].tab[_tab_nr].relation_filter[k].from_key
			var bIsGlobal = false;
			//If the from key is a global and not a db column we need to eval it.
			if(_from_key.search("scopes.") > -1 || _from_key.search("globals.") > -1){
				_from_key = eval(_from_key);
				bIsGlobal = true;
			}

			var _operator =  nav.program[globals.nav_program_name].tab[_tab_nr].relation_filter[k].operator;
			var _to_key = nav.program[globals.nav_program_name].tab[_tab_nr].relation_filter[k].to_key

			if(bIsGlobal){
				_foundset.addFoundSetFilterParam(_to_key, _operator, _from_key)
			} else {
				_foundset.addFoundSetFilterParam(_to_key, _operator, forms[globals.nav.form_view_01][_from_key])
			}

			_lenght = globals.nav.his.foundset.length
			globals.nav.his.foundset[_lenght] = new Array()
			globals.nav.his.foundset[_lenght][0] = _to_key
			globals.nav.his.foundset[_lenght][1] = _operator
			globals.nav.his.foundset[_lenght][2] = forms[globals.nav.form_view_01][_from_key]
			globals.nav.his.foundset[_lenght][3] =_from_key
		}
		// load all records only if filters set
		if (nav.program[globals.nav_program_name].tab[_tab_nr].relation_filter.length > 0 || _progObj.filter.length > 0) {
			_foundset.loadAllRecords();

			//Get the PK field name and the PK value of the selected record so that we can select it
			var sPKName = databaseManager.getTable(forms[_form].controller.getDataSource()).getRowIdentifierColumnNames()[0];
			var rSelectedRecord = forms[_form].foundset.getSelectedRecord();
			if (rSelectedRecord) {
			    var sPKValue = rSelectedRecord.getPKs()[0];

			    //If we can't select the record it might not have been loaded and we need to search
			    if (!_foundset.selectRecord(sPKValue)) {
			        _foundset.find();
			        _foundset[sPKName] = sPKValue;
			        _foundset.search(false,false);
			    }
			}
		}

		nav.new_record_filter = globals.nav.his.foundset
		// load the foundset to the form
		if (_form != _form_to) {
			forms[_form_to].controller.loadRecords(_foundset)
		}
		// load the foundset to the tab form
		forms[_program + '_tab'].controller.loadRecords(_foundset)
		var _field_value
		if (globals.nav.program[globals.nav_program_name].display_field_header) {
			_field_value = forms[globals.nav.form_view_01][globals.nav.program[globals.nav_program_name].display_field_header]
		}
		globals.nav.his.foundset_related_text = _program_name + ' - ' + _field_value
		globals.nav.his.foundset_related_form = globals.nav.form_view_01

	}
	// show the form

	globals.svy_nav_showForm(_form_to, _program, false)
	globals.nav.related_form = globals.nav.his.foundset_related_form
	globals.nav.related_text = globals.nav.his.foundset_related_text
	// set the right text in the status bar
	if (globals.nav.related_text) {
		forms.svy_nav_fr_status_bar.elements.form_name.text += ' -> ' + globals.nav.related_text
	}
	return _form_to
}

/**
 *	Called when the user select a tree node, will navigate to a program
 *
 * <AUTHOR> Aleman
 * @since 2005-11-24
 * @param {Number} _menu_item_id Menu item id of the item you want to open
 * @return  none
 *
 * @properties={typeid:24,uuid:"5e91d6ea-92ae-45a6-aa45-d077f5530398"}
 */
function svy_nav_tree_node_selected(_menu_item_id) {
	if (!scopes['avUtils'].isNavModeReadOnly())
	    return; // no navigation in find and edit

	if (_menu_item_id == 60)
	{
		globals.nav_program_name = "Dashboard";
		var sHome = forms.svy_nav_fr_main._homeForm;
		if (sHome != "dbrd_home") sHome = "dbrd_home_blank";

		forms.svy_nav_fr_main.elements.tab_split.setLeftForm("svy_nav_fr_menu");
		forms.svy_nav_fr_main.elements.tab_split.setRightForm(sHome);
		forms.svy_nav_fr_status_bar.elements.form_name.visible = false;
		return;
	}

	forms.svy_nav_fr_status_bar.elements.form_name.visible = true;

	globals.nav.fromTree = 1

	// TD - Servoy Case SVY-6965: Commented the code below to solve a problem with having to double-click a menu option after closing the active program tab.
	// It does cause an extra execution if re-selected but solved the issue.

//	if (globals.nav.setMenuTree == 1) // if the tree is called because showForm set the right node, don't navigate
//	{
//		globals.nav.setMenuTree = 0
//		return
//	}

	globals.nav_menu_item_id = _menu_item_id

	if (_to_nav_menu_items.menuitem_type == 'F') {
		var _rec = _to_nav_menu_items.nav_menu_items_to_nav_function.getRecord(1)

		var _methodcall = _rec.method + '('
		var _rec_arg
		for (var i = 1; i <= _rec.nav_function_to_nav_function_arguments.getSize(); i++) {
			_rec_arg = _rec.nav_function_to_nav_function_arguments.getRecord(i)
			if (i != 1) {
				_methodcall += ', '
			}

			if (_rec_arg.arg_type == 1) {
				_methodcall += '"' + _rec_arg.argument + '"'
			} else if (_rec_arg.arg_type == 2) {
				_methodcall += '"' + globals[_rec_arg.argument] + '"'
			}
		}
		_methodcall += ')'

		eval(_methodcall)
	} else if (_to_nav_menu_items.menuitem_type == 'P') {
		var _program_name = _to_nav_menu_items.program_name
		if (_program_name) {
			var _program = _program_name
			if (!globals.nav.program[_program] || globals.nav.program[_program] == undefined) return

			globals.nav.his = new Object()

			var _template = globals.nav.template_types[globals.nav.program[_program].view]
			var _form = globals.nav.program[_program].form[forms[_template].has1()][2]

			// show the program
			var _return = globals.svy_nav_showForm(_form, _program);
			// sort the tables
//			if (_return == -1) {
				return
//			}

//			if (_return[0])// only if there are tabs
//			{
//					/** @type {String} */
//				var sRet0 = _return[0],
//					/** @type {String} */
//					sRet1 = _return[1];
////				forms.svy_nav_base.dc_sort(_return[0], _return[1]);
//				forms.svy_nav_base.dc_sort(sRet0, sRet1);
//			}

		}
	}
}

/**
 * Will show the right click menus in the navigation tree
 * <AUTHOR> Aleman
 * @since 2011-06-15
 * @param {Object} _menu_item_id
 * @param {String} _tableName
 * @param {Number} _mouseX
 * @param {Number} _mouseY
 *
 * @properties={typeid:24,uuid:"05938BC5-EF99-464E-9D07-B6E5AA8EC0DD"}
 */
function svy_nav_tree_node_selected_rightClick(_menu_item_id, _tableName, _mouseX, _mouseY) {

	globals.nav_menu_item_id = _menu_item_id
	if (utils.hasRecords(_to_nav_menu_items) && utils.hasRecords(_to_nav_menu_items.nav_menu_items_to_nav_menu_items_context_menu))
	{
		//get the foundset
		var _fs = _to_nav_menu_items.nav_menu_items_to_nav_menu_items_context_menu
		var _rec, _menuitem
		//sort the foundset
		_fs.sort('sequence asc')
		//create menu
		var _popupmenu = plugins.window.createPopupMenu()

		for (var i = 1; i <= _fs.getSize(); i++) {
			_rec = _fs.getRecord(i)
			_menuitem = _popupmenu.addMenuItem(_rec.display_description, globals.svy_nav_callFunctionFromPopmenu)
			_menuitem.methodArguments = [_rec.function_id]
		}
		_popupmenu.show(_mouseX, _mouseY);
	}

}

/**
 *	Key shortcut 4 to open the security window
 *
 * <AUTHOR> Aleman
 * @since 2008-05-24
 * @return  none
 *
 * @properties={showInMenu:true,typeid:24,uuid:"4d3ae3f6-4e6c-4a3b-80bf-34f24ddfa113"}
 */
function _4_svy_nav_openSecurityDialog() {
	if (_to_sec_user$user_id.flag_system_administrator) {
		globals.DIALOGS.showFormInModalDialog(forms.svy_sec_main, -1, -1, -1, -1, null, true, false, 'SecurityScreenForDev', false)
	}
}

/**
 * Callback method for when solution is closed, force boolean argument tells if this is a force (not stopable) close or not.
 *
 * @param {Boolean} force if false then solution close can be stopped by returning false
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"7A5BA424-0891-4A07-A72C-8D7E0C390C1E"}
 * @AllowToRunInFind
 */
function svy_nav_onClose(force) {
	// rollback any open transactions
	if (databaseManager.hasTransaction()) {
		databaseManager.rollbackTransaction();
	}

	// register logout
	/** @type {JSFoundSet<db:/svy_framework/sec_user_login_attempt>} */
	var _fs_loginAttempt = databaseManager.getFoundSet(globals.nav_db_framework, 'sec_user_login_attempt');
	if (_fs_loginAttempt.find()) {
		_fs_loginAttempt.user_id = globals.svy_sec_lgn_user_id;
		_fs_loginAttempt.is_successful = 1;
		if (_fs_loginAttempt.search()) {
			_fs_loginAttempt.sort('attempt_datetime desc');
			_fs_loginAttempt.setSelectedIndex(1);
			_fs_loginAttempt.logout_datetime = new Date();
			databaseManager.saveData(_fs_loginAttempt);
		}
	}

	application.removeClientInfo(globals.owner_id)

	//run onClose-method of when available
	if (globals['onClose']) {
		var _methodReturn = globals['onClose']()
	}
	if (_methodReturn == false) {
		return false
	} else {
		return true
	}
}

/**
 *	Shows a menu of the history stack
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 * @param {JSEvent} _event
 * @return  none
 *
 * @properties={typeid:24,uuid:"03fedae0-b665-4f9c-b7cd-d67ba4290d91"}
 */
function svy_nav_history_menu(_event) {
	if (!scopes['avUtils'].isNavModeReadOnly())
	    return;

	var _popupmenu = plugins.window.createPopupMenu()
	var _item
	for (var i = 0; i < globals.nav.stack.length; i++) {
		_item = _popupmenu.addCheckBox(globals.nav.stack[i].program, globals.svy_nav_history_moveFromPopmenu, 'media:///' + globals.nav.program[globals.nav.stack[i].program].program_image)
		_item.methodArguments = [null, i, 'sec_arg'];
		if (i == globals.nav.stack_position) {
			_item.selected = true

		}
	}

	/** @type {RuntimeComponent} */
	var _source = _event.getSource()
	if (_source != null) {
		_popupmenu.show(_source);
	}

}

/**
 *	To show an array as a popup where you can edit
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 * @param {Array}	_array you want to edit
 * @param {String}	_form you want to save youre edited array to
 * @param {String}	_field you want to save youre edited array to
 * @return  none
 *
 * @properties={typeid:24,uuid:"99af8973-092c-4fe4-96ed-f5a6bd6b48c6"}
 */
function svy_nav_arrayEdit(_array, _form, _field) {
	globals.nav.arrayForm = _form
	globals.nav.arrayField = _field
	var _dataset = databaseManager.createEmptyDataSet(0,0)
	_dataset.addColumn('data_field')
	_dataset.addColumn('sort_field')
	for (var i = 0; i < _array.length; i++) {
		_dataset.addRow([_array[i], i])
	}

	var _datasource = _dataset.createDataSource('array_table', [JSColumn.TEXT, JSColumn.INTEGER]);
	var jsForm = solutionModel.getForm('svy_nav_c_array_editor')
	jsForm.dataSource = _datasource
	var jsField = jsForm.getField('sort_field')
	jsField.dataProviderID = 'sort_field'
	jsField = jsForm.getField('data_field')
	jsField.dataProviderID = 'data_field'
	forms.svy_nav_c_array_editor.controller.recreateUI()
	globals.DIALOGS.showFormInModalDialog(forms.svy_nav_c_array_editor, -1, -1, 318, 318)

}

/**
 *	To set the images of the templates
 *
 * <AUTHOR> Aleman
 * @since 2007-05-24
 *
 *
 * @properties={typeid:24,uuid:"53142689-ef03-478e-bf73-a8593151236a"}
 */
function svy_nav_init_setTemplateImages() {
	globals.nav_properties = 'template_images'
}

/**
 *	Get the row identifiers
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 * @param {String}	[_form] Formname you want the row id from
 * @return {Array} Rowidents
 *
 * @properties={typeid:24,uuid:"2c1a70a4-b8a6-4d51-87d6-78491b12f8b3"}
 */
function svy_nav_getRowIdentifierValues(_form) {
	var jsTable = databaseManager.getTable(forms[_form].controller.getDataSource())

	if (!jsTable) return null //form without table

	var _record_names = jsTable.getRowIdentifierColumnNames()
	var _record_ids = new Array()
	for (var i = 0; i < _record_names.length; i++) {
		// get the record id values
		_record_ids[i] = forms[_form][_record_names[i]]
	}
	return _record_ids
}

/**
 *	Update the history with the record information
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 *
 * @properties={typeid:24,uuid:"c1491815-ecd4-4559-9351-c8d8035ccc21"}
 */
function svy_nav_history_update_record() {
	if (globals.nav.noHistory == 1) {
		return
	}

	//update record history
	if (globals.nav.stack_position > 0) {
		/**@type {{form:String,program:String, foundsetfilter:Number}}  */
		var _stackObj = globals.nav.stack[globals.nav.stack_position]
		var _form = _stackObj.form
		var _program = _stackObj.program
		if ( (globals.nav.program[_program].tab.length > 1) && globals.nav.form_view_02) {
			_stackObj.tab_form = globals.nav.form_view_02
			_stackObj.tab_rowId = globals.svy_nav_getRowIdentifierValues(globals.nav.form_view_02);
		}

		if (_stackObj.foundsetfilter) //related foundset
		{
			_stackObj.sql = databaseManager.getSQL(forms[_stackObj.form].foundset)
			_stackObj.sql_parameters = databaseManager.getSQLParameters(forms[_stackObj.form].foundset)
		} else {
			_stackObj.sql = databaseManager.getSQL(globals.nav.program[_stackObj.program].foundset)
			_stackObj.sql_parameters = databaseManager.getSQLParameters(globals.nav.program[_stackObj.program].foundset)
		}
		_stackObj.rowId = globals.svy_nav_getRowIdentifierValues(_form)

	}
}

/**
 *	To open the valuelist editor with the shortcut ctrl+5
 *
 * <AUTHOR> Kramer
 * @since 2009-03-12
 *
 * @properties={showInMenu:true,typeid:24,uuid:"41176068-33a9-4787-81aa-42a1d8904c2c"}
 */
function _5_svy_nav_openValuelistEditor() {
	if (_to_sec_user$user_id.flag_system_administrator) {
		globals.DIALOGS.showFormInModalDialog(forms.svy_utl_valuelists_dtl, -1, -1, -1, -1, 'Valuelists', true, false, 'Valuelists', false)
	}
}

/**
 *	Is used to get form out of find when user uses ESC, or to load all Records
 *
 * <AUTHOR> Aleman
 * @since 2009-03-26
 * @param {JSEvent} _event
 *
 *
 * @properties={typeid:24,uuid:"2f31774a-5143-4281-a403-15b3217622aa"}
 */
function svy_nav_showAllRecordsCmd(_event) {
	var _form = _event.getFormName();
	if (forms[_form].foundset.isInFind()) {
		_form = globals.svy_sea_searchForm
		globals.svy_sea_cancelSearch()
		globals.svy_nav_dc_setStatus("browse", _form)
		return
	} else if (forms.svy_nav_fr_buttonbar_browser.elements.btn_loadAllRecords.enabled) {
		forms.svy_nav_base.dc_loadAll()
	}
}

/**
 * This function replaces 1 form name with another that we want to use for export.
 * EG. the main form has no data to export, but an inner form does and we want to export data from it
 * We cant use the getExportFormName() override method used in svy_navy_base because this globals.js is not extended.
 * This is used in svy_nav_setBrowserbar() below to enable the export btn
 *
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"27DA2E0E-8C52-478B-8145-E25907DC1705"}
 */
function getExportFormName(_form){
	switch (_form) {
		case "sch_schedule_board_dtl": return "sch_schedule_board_dtl_split_schedule_tbl";
		case "sch_dept_dtl": return "sch_dept_dtl_split_milestones";
		default: return _form;
	}

}

/**
 *	To the status of the browserbar
 *
 * @param {String}	_status the status you want to set the bar in (browse edit add delete)
 * @param {String}	_form	the formname
 * @return  none
 *
 * @properties={typeid:24,uuid:"67a57a72-0f49-4823-b860-775582468fba"}
 */
function svy_nav_setBrowserbar(_status, _form) {
    // Handle container forms in tabbed UI
    var displayForm = _form;
    if (_form && _form.indexOf('program_container_') > -1) {
        var rightForm = forms[_form].elements.splitpane.getRightForm();
        if (rightForm) {
            displayForm = rightForm.controller.getName();
        }
    }

    _form = getExportFormName(_form);

    var _browse = (_status == 'edit' || _status == 'add' || _status == 'find' || _status == 'required_fields') ? false : true;
    var _foundset = (forms[getExportFormName(globals.nav.form_view_01)].foundset.getSize() > 0);

    /**@type {{server_name:String, table_name:String, template:Array, btn_rec_nav: String, btn_print:Number, btn_method:Number, btn_sort:Number, btn_search:Number, btn_search_prop:Number, btn_all_records:Number, btn_export:Number, btn_record_information:Number, btn_help:Number, btn_required_fields:Number, btn_resettblheader:Number, add_mode:Number, update_mode:Number, delete_mode:Number, duplicate_mode:Number  }} */
    var _progObj = globals.nav.program[globals.nav_program_name];

    var _buttonBarElements = forms.svy_nav_fr_buttonbar_browser.elements;

    var _datasource = forms[globals.nav.form_view_01].controller.getDataSource();

    // The Reset Table Headers btn is only enabled for _tbl forms. If you want to enable it for other forms add them to this array
    var _reset_tableheader_list = ['in_trans_entry_header_main', 'in_item_inquiry_dtl', 'po_planned_dtl', 'sch_schedule_board_dtl',
        'sch_dept_dtl', 'sa_invoice_batch_dtl', 'sa_cash_receipt_dtl'];

    //SL-6687 Disable Division & Plant filters when not in browse mode
    forms.svy_nav_fr_status_bar.elements.fldDivision.enabled = _browse;
    forms.svy_nav_fr_status_bar.elements.fldPlant.enabled = _browse;

    _buttonBarElements.btn_new.enabled = _browse && (_progObj.add_mode == 1) && security.canInsert(_datasource);
    _buttonBarElements.btn_edit.enabled = _browse && (_progObj.update_mode == 1) && security.canUpdate(_datasource) && _foundset;
    _buttonBarElements.btn_delete.enabled = _browse && (_progObj.delete_mode == 1) && security.canDelete(_datasource) && _foundset;
    _buttonBarElements.btn_duplicate.enabled = _browse && (_progObj.duplicate_mode == 1) && security.canInsert(_datasource) && _foundset;

    _buttonBarElements.btn_print.enabled = _status != 'find' && (_progObj.btn_print == 1);
    _buttonBarElements.btn_methods.enabled = _status != 'find' && (_progObj.btn_method == 1);
    _buttonBarElements.btn_sort.enabled = _browse && (/(_tbl)$|(_tblt)$/.test(globals.nav.form_view_01)) && (_progObj.btn_sort == 1) && _foundset;
    //_buttonBarElements.btn_search.enabled = _browse && (_progObj.btn_search == 1);
    _buttonBarElements.btn_search.enabled = false;
    //_buttonBarElements.btn_search_menu.enabled = _browse && (_progObj.btn_search_prop == 1);
    _buttonBarElements.btn_search_menu.enabled = false;
    //_buttonBarElements.btn_loadAllRecords.enabled = _browse && (_progObj.btn_all_records == 1);
    _buttonBarElements.btn_loadAllRecords.enabled = false;
    _buttonBarElements.btn_export.enabled = _browse && (_progObj.btn_export == 1) && _foundset;

    //the reset table headers is only available in tbl
//    _buttonBarElements.btn_resettblheader.enabled =
//        _browse
//        && ((/(_tbl)$|(_tblt)$/.test(globals.nav.form_view_01)) || _reset_tableheader_list.indexOf(globals.nav.form_view_01) >-1)
//        && (_progObj.btn_resettblheader == 1);
    _buttonBarElements.btn_resettblheader.enabled = false;

    globals.nav_properties = 'template_images';
    for (var i = 0; i < _to_nav_properties$property_name.property_value.length; i++) {
        _buttonBarElements['btn_template'+i].enabled = (_progObj.template[i][2] == 1) && _browse;
    }

    var _browse_nav = (utils.stringPatternCount(_form, '_tbl') && (_form == globals.nav.form_view_01) && (_status == 'edit' || _status == 'add')) ? true : _browse;

    _buttonBarElements.btn_next.enabled = _browse_nav && (_progObj.btn_rec_nav == 1) && _foundset;
    _buttonBarElements.btn_prev.enabled = _browse_nav && (_progObj.btn_rec_nav == 1) && _foundset;
    _buttonBarElements.btn_record_information.enabled = _browse_nav && (_progObj.btn_record_information == 1) && _foundset;
    _buttonBarElements.btn_first.enabled = _browse_nav && (_progObj.btn_rec_nav == 1) && _foundset;
    _buttonBarElements.btn_last.enabled = _browse_nav && (_progObj.btn_rec_nav == 1) && _foundset;
    _buttonBarElements.btn_tabCurrent.enabled = _browse;
    _buttonBarElements.btn_tabCloseAll.enabled = _browse;

    // Modified to work with tabbed UI and required_fields mode
    _buttonBarElements.btn_cancel.enabled = !_browse && (displayForm == globals.nav.form_view_01 || _status == 'required_fields');
    _buttonBarElements.btn_save.enabled = !_browse && (displayForm == globals.nav.form_view_01 || _status == 'required_fields');

    _buttonBarElements.btn_help.enabled = _browse && (_progObj.btn_help == 1);

    //button for required fields will disappear if users don't have rights to it.
    if(_progObj.btn_required_fields && scopes['avUtils'].userHasPermissionToSetMandatoryFields()) { // SL-17184 limit access to specific users with permission
        _buttonBarElements.btn_required_fields.visible = true;
        _buttonBarElements.btn_required_fields.enabled = _browse_nav || _status == 'required_fields';
    }
    else {
        _buttonBarElements.btn_required_fields.visible = false;
    }

    //Hook to do custom things when setting the browserbar
    if (globals['onSetBrowserBar']) {
        globals['onSetBrowserBar']();
    }
}

/**
 *	To the status of the viewerbar
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 * @param {String}	_status the status you want to set the bar in (browse edit add delete)
 * @param {String}	_form	the formname
 * @return  none
 *
 * @properties={typeid:24,uuid:"2a2345ca-1e0c-405f-8ea2-c330309591c3"}
 */
function svy_nav_setViewerBar(_status, _form) {
    if (!globals.nav.form_view_02) return

	var _browse = (_status == 'edit' || _status == 'add' || _status == 'find' || _status == 'required_fields') ? false : true
	//no child records when there is no main record
	var _foundset_main = (forms[globals.nav.form_view_01].controller.getMaxRecordIndex() > 0)
	var _foundset = (forms[globals.nav.form_view_02].controller.getMaxRecordIndex() > 0)
	var _program = globals.nav_program_name
	var _buttonbarviewerElements = forms.svy_nav_fr_buttonbar_viewer.elements
	if (globals.nav.program[_program].tab.length <= 1)return //program with no tabs

//	var _tab_form = globals.nav_program_name + '_tab'
	var _index = scopes.globals["avUtilities_tabGetSelectedIndex"](globals.nav.form_view_02, "tabs");
	globals.nav.program[_program]['tab']['selected'] = _index;
	if (globals.nav.program[_program].tab.selected == -1) globals.nav.program[_program].tab.selected = 1;
//	if(_index < 0)
//	{
//		globals.nav.form_view_02 = globals.nav.getTabForm()
//	}
//	else
//	{
//		globals.nav.form_view_02 = scopes.globals.avUtilities_tabGetFormName(_tab_form, "tabs", _index)
//	}
	if (globals.nav.getTabRelation()) // if tab is related
	{
		_foundset = (forms[globals.nav.form_view_01][globals.nav.getTabRelation()] != null && forms[globals.nav.form_view_01][globals.nav.getTabRelation()].getSize() > 0)
	}

	var _tab_program = globals.nav.getTabProgram()
	/** @type {{server_name:String,table_name:String, btn_print:Number, btn_method:Number, btn_sort:Number, btn_rec_nav:Number, btn_resettblHeader:Number, btn_record_information:Number, btn_search:Number,
	 * btn_all_records:Number, btn_export:Number, btn_resettblheader:Number, template:Array, form:String}}*/
	var _tabProgObj = globals.nav.program[_tab_program]
	var _datasource = forms[globals.nav.form_view_02].controller.getDataSource();
	/** @type {{add_mode:Number, update_mode:Number, delete_mode:Number, btn_rec_nav:Number, btn_resettblHeader:Number, btn_record_information:Number, btn_search:Number, btn_all_records:Number,
	 * btn_export:Number }}*/
	var _tabProp = globals.nav.program[_program].tab[globals.nav.program[_program].tab.selected]
	_buttonbarviewerElements.btn_new.enabled = (_tabProp.add_mode == 1) && _browse && security.canInsert(_datasource) && _foundset_main
	_buttonbarviewerElements.btn_edit.enabled = (_tabProp.update_mode == 1) && _browse && security.canUpdate(_datasource) && _foundset
	_buttonbarviewerElements.btn_delete.enabled = (_tabProp.delete_mode == 1) && _browse && security.canDelete(_datasource) && _foundset
	_buttonbarviewerElements.btn_duplicate.enabled = (_tabProp.add_mode == 1) && _browse && security.canInsert(_datasource) && _foundset

	_buttonbarviewerElements.btn_print.enabled = _status != 'find' && (_tabProgObj.btn_print == 1)
	_buttonbarviewerElements.btn_methods.enabled = _status != 'find' && (_tabProgObj.btn_method == 1)
	_buttonbarviewerElements.btn_sort.enabled = _browse && (/(_tbl)$|(_tblt)$/.test(globals.nav.form_view_02)) && (_tabProgObj.btn_sort == 1) && _foundset
	//_buttonbarviewerElements.btn_search.enabled = _browse && (_tabProgObj.btn_search == 1)
	_buttonbarviewerElements.btn_search.enabled = false
	//_buttonbarviewerElements.btn_loadAllRecords.enabled = _browse && (_tabProgObj.btn_all_records == 1)
	_buttonbarviewerElements.btn_loadAllRecords.enabled = false
	_buttonbarviewerElements.btn_export.enabled = _browse && (_tabProgObj.btn_export == 1) && _foundset
	//the reset table headers is only available in tbl
	//_buttonbarviewerElements.btn_resettblheader.enabled = _browse && (/(_tbl)$|(_tblt)$/.test(globals.nav.form_view_02)) && (_tabProgObj.btn_resettblheader == 1)
	_buttonbarviewerElements.btn_resettblheader.enabled = false

	globals.nav_properties = 'template_images'
	for (var i = 0; i < _to_nav_properties$property_name.property_value.length; i++) {
		_buttonbarviewerElements['btn_template'+i].enabled = (_tabProgObj.template[i][2] == 1) && _browse && _foundset
	}

	_buttonbarviewerElements.btn_save.enabled = !_browse && (_form == globals.nav.form_view_02)  && (!globals.nav.activeView || globals.nav.activeView == 2)
	_buttonbarviewerElements.btn_cancel.enabled = !_browse && (_form == globals.nav.form_view_02)  && (!globals.nav.activeView || globals.nav.activeView == 2)

	var _browse_nav = (utils.stringPatternCount(_form, '_tbl') && (_form == globals.nav.form_view_02) && (_status == 'edit' || _status == 'add')) ? true : _browse

	_buttonbarviewerElements.btn_next.enabled = _browse_nav && (_tabProgObj.btn_rec_nav == 1) && _foundset
	_buttonbarviewerElements.btn_prev.enabled = _browse_nav && (_tabProgObj.btn_rec_nav == 1) && _foundset
	_buttonbarviewerElements.btn_record_information.enabled = _browse_nav && (_tabProgObj.btn_record_information == 1) && _foundset
	_buttonbarviewerElements.btn_first.enabled = _browse_nav && (_tabProgObj.btn_rec_nav == 1) && _foundset
	_buttonbarviewerElements.btn_last.enabled = _browse_nav && (_tabProgObj.btn_rec_nav == 1) && _foundset

}

/**
 *	To the status of the navigationbar
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 * @param {String}	_status the status you want to set the bar in (browse edit add delete)
 * @return  none
 *
 * @properties={typeid:24,uuid:"d92fb9e4-5e4f-4927-bfb0-8c2b7036e336"}
 */
function svy_nav_setNavigationBar(_status) {
	var _browse = (_status == 'edit' || _status == 'add' || _status == 'find' || _status == 'required_fields') ? false : true

	forms.svy_nav_fr_tree_top.elements.btn_his_back.enabled = _browse && (globals.nav.stack_position != 0)
	forms.svy_nav_fr_tree_top.elements.btn_his_forw.enabled = _browse && (! (globals.nav.stack.length == globals.nav.stack_position + 1))
	forms.svy_nav_fr_tree_top.elements.btn_shortcut.enabled = _browse
	forms.svy_nav_fr_tree_top.elements.btn_bookmark.enabled = _browse
	forms.svy_nav_fr_viewCompareTab.setViewButtonsEnabled(_browse)
	forms.svy_nav_fr_tree_top.elements.btn_print.enabled = _browse
//	forms.svy_nav_fr_tree_top.elements.tree.enabled = _browse
	forms.svy_nav_fr_bottom_bkmk_short.controller.enabled = _browse
	if(globals.nav_show_open_tabs)
	{
		forms.svy_nav_fr_openTabs.controller.enabled = _browse
	}
	if (forms.svy_nav_fr_bottom_bkmk_short.elements.tab_bookmark.visible && _browse)// bookmarks are visibele
	{
		if (globals.nav.bookmarks.length > 0) {
			forms.svy_nav_fr_bookmarks.elements.btnshortcutMoveUp0.enabled = false
			forms.svy_nav_fr_bookmarks.elements['btnshortcutMoveDown' + (globals.nav.bookmarks.length - 1)].enabled = false
		}
	} else if (forms.svy_nav_fr_bottom_bkmk_short.elements.tab_favorites.visible && _browse)// shortcuts are visibele
	{
		if (globals.nav.shortcuts.length > 0) {
			forms.svy_nav_fr_shortcuts.elements.btnshortcutMoveUp0.enabled = false
			forms.svy_nav_fr_shortcuts.elements['btnshortcutMoveDown' + (globals.nav.shortcuts.length - 1)].enabled = false
		}
	}
}

/**
 *	Create shortcut last open record
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 *
 * @properties={typeid:24,uuid:"7c4b7ec6-fff4-4c7a-a8ee-5b0bbf539c58"}
 */
function svy_nav_lastOpenRecordCreate() {

	//Returns an array containing the names of the identifier (PK) column(s)
	var jsTable = databaseManager.getTable(globals.nav.program[globals.nav_program_name].server_name, globals.nav.program[globals.nav_program_name].table_name)

	if (!jsTable) return // there is no table attached to the form

	var i
		// save the recordids
	var _record_names = jsTable.getRowIdentifierColumnNames()
	var _record_ids = new Array() //saving array directly to database field doesn't work
	var _record_types = new Array()
	var _program_info = globals.nav.program[globals.nav_program_name].description
	var _record_info = forms[globals.nav.form_view_01][globals.nav.program[globals.nav_program_name].display_field_header]

	for (i = 0; i < _record_names.length; i++) {
		// get the record id values
		_record_ids[i] = forms[globals.nav.form_view_01][_record_names[i]]

		// get the column types
		var _jsColumn = jsTable.getColumn(_record_names[i])
		_record_types[i] = _jsColumn.getTypeAsString()

	}

	//look if already in array_ if delete from array
	for (i = 0; i < globals.nav.recordHistory.length; i++) {
		if (scopes.svyJSUtils.areObjectsEqual(globals.nav.recordHistory[i].record_ids, _record_ids) &&
		globals.nav.recordHistory[i].program == globals.nav_program_name
		) {

			if ( (i + 1) == globals.nav.recordHistory.length) {
				return
			}

			//object already in Array so delete it
			globals.nav.recordHistory = globals.nav.recordHistory.slice(0, i).concat(globals.nav.recordHistory.slice(i + 1));
			i--;
		}
	}

	if (globals.nav.recordHistory.length >= 7) {
		//remove first entry array can only be 7 long
		globals.nav.recordHistory.shift()

	}

	//put in the array at the end
	var _l = globals.nav.recordHistory.length
	globals.nav.recordHistory[_l] = new Object()
	globals.nav.recordHistory[_l].record_names = _record_names
	globals.nav.recordHistory[_l].record_ids = _record_ids
	globals.nav.recordHistory[_l].record_types = _record_types
	globals.nav.recordHistory[_l].program = globals.nav_program_name
	globals.nav.recordHistory[_l].program_info = _program_info
	globals.nav.recordHistory[_l].record_info = _record_info
	globals.nav.recordHistory[_l].view = globals.nav.program[globals.nav_program_name].view

	// rePaint record History if visible
	if (forms.svy_nav_fr_bottom_bkmk_short.elements.tab_record_history.visible) {
		// load the record History to see the new recordHistory
		forms.svy_nav_fr_recordHistory.loadRecordHistory()
	}

}

/**
 *	To use by deeplinking and setting the owner_id
 *
 * <AUTHOR> Aleman
 * @since 2009-05-24
 *
 * @properties={typeid:24,uuid:"79d17d46-535b-492e-bbfe-3bf8ccd0c74a"}
 */
function svy_nav_deeplink() {
	//	If you now open a client with an url like this: http://<server_ip:port>/servoy-client/sampleuse_navigation.jnlp?method=svy_nav_deeplink&argument=<uuid>
	//then the <uuid> that is passed will be the owner_id for that session. (for webclient the url will look like this:
	//http://<server_ip:port>/servoy-webclient/ss/s/sampleuse_navigation/m/svy_nav_deeplink/a/<uuid>)

}

/**
 *	To init the functions of the nav object.
 *
 * <AUTHOR> Aleman
 * @since 2009-09-24
 *
 * @properties={typeid:24,uuid:"2be9468c-4a5b-4bd3-b57b-45095e81f7cc"}
 */
function svy_nav_init_functions() {
	globals.nav.getTabProgram = function() {
		var _program = globals.nav_program_name
		return globals.nav.program[_program].tab[globals.nav.program[_program].tab.selected].program_name
	}

	globals.nav.getTabRelation = function() {
		return globals.nav.program[globals.nav_program_name].tab[globals.nav.program[globals.nav_program_name].tab.selected].relation
	}

	globals.nav.getTabSelected = function() {
		return globals.nav.program[globals.nav_program_name].tab.selected
	}

	globals.nav.getTabForm = function() {
		return globals.nav.program[globals.nav.program[globals.nav_program_name].tab[globals.nav.program[globals.nav_program_name].tab.selected].program_name].form[globals.nav.program[globals.nav_program_name].tab[globals.nav.program[globals.nav_program_name].tab.selected].form_type][2]
	}

	globals.nav.getTemplate = function() {
		var _template
		if (globals.nav.program[globals.nav_program_name].tab.length > 0) {
			_template = globals.nav.template_types[globals.nav.program[globals.nav_program_name].view]
		} else {
			_template = globals.nav.template_types_notabs[globals.nav.program[globals.nav_program_name].view]
		}
		return _template
	}

	globals.nav.viewHasTable = function() {
		var _template = globals.nav.getTemplate()
		return forms[_template].hasTable()

	}

	globals.nav.getCurrentFormName = function() {
		return globals.nav.form_view_01
	}

	globals.nav.getCurrentTabFormName = function() {
		return globals.nav.form_view_02
	}

	globals.nav.viewHasDetail = function() {
		var _template = globals.nav.getTemplate()
		return forms[_template].hasDetail()
	}

}

/**
 * @properties={typeid:24,uuid:"63beb2bf-4fe3-45ac-a3f6-8b95aaac647a"}
 */
function svy_nav_documentation() {
	/*
	 *
	 *
	 * The Navigation object.

	 globals.nav
	 bookmarks
	 Array containing an object for each bookmark.
	 The object contains Bookmark_id, program and records_ids.
	 For example: globals.nav.bookmarks.1.program
	 browser_buttonbar
	 Name of the browserbuttonbar.
	 browser_form
	 Name of the browser form.
	 default_edit_template
	 The template where the navigation will go for edits.
	 form_view_01
	 The form that is currently showing in the top view, view 01
	 form_view_02
	 The form that is currently showing in the bottom view, view 02
	 history (Object)
	 foundset (array)		=	The filter for the related foundset
	 foundset_related_form 	=	The related foundset form
	 foundset_related_text	=	The related text for in the toolbar
	 history_max_entries
	 Max entries for the history so the history doesn't become to big.
	 keys
	 Security keys for all the rights that the logged in user has.
	 lastProgram
	 Last program that the user has visited.
	 lastView
	 Last view that the user has visited.
	 mode
	 Mode that the navigation is in, possible: edit, browse, find, add.
	 new_record_filter
	 Fields that have to be entered if a related record is created.
	 noHistory
	 Boolean, can be set to 1 if no history entry is needed while navigating, for example if you 	navigate in the history because otherwise you would get double entry's

	 program
	 Object containing an object for each program.
	 In each of those program-objects the following:
	 add_mode				= 	Has user right for adding records in this program.
	 base_form_name			= 	Has form name of the program.
	 btn_all_records			= 	Has user right for the all records button.
	 btn_export				=	Has user right for the export button.
	 btn_help				= 	Has user right for the help button.
	 btn_method				= 	Has user right for the methods button.
	 btn_print				= 	Has user right for the print button.
	 btn_rec_nav 			= 	Has user right for the buttons of the record navigation.
	 btn_record_information 	= 	Has user right for the record information button.
	 btn_resettblheader		= 	Has user right for the reset table headers button.
	 btn_search				= 	Has user right for the search button.
	 btn_search_prop			= 	Has user right for the search properties button.
	 btn_sort				= 	Has user right for the sort button.
	 delete_mode  			= 	Has user right to delete records in this program.
	 description				=	The description of the program for the end user.
	 display_field_header	=	The field that will be displayed in the header.
	 divider_height			= 	Height of the divider.
	 divider_locked			= 	Divider locked, user can not change it's position.
	 duplicate_mode			= 	Has user right to duplicate records in this program.
	 filter (array)			= 	The filter for the data of the program.
	 filter_field_name	=	The field to filter on.
	 filter_field_operator =	Operator to filter with.
	 filter_value		=	Value to filter with.
	 form					= 	Object with information which forms are available.
	 method (array)			= 	Method menu of the program.
	 in_browse			=	Show method in menu if user is in browse.
	 in_edit				=	Show method in menu if user is in edit.
	 label				= 	Label shown to the user in menu.
	 method				= 	Method that the menu will call.
	 nav_popmenu_id		= 	Id of the popmenu in database.
	 parent_popmenu_id   = 	Id of the parentpopmenu in database.
	 noreadonly				=	program doesn't go in read only.
	 path					=	Path of the program in the tree.
	 program_image			= 	Image of the program.
	 record_locking			=	When record is edited record should be locked.
	 report	Array			=	Report menu for program.
	 in_browse			=	Show report in menu if user is in browse.
	 in_edit				=	Show report in menu if user is in edit.
	 label				= 	Label shown to the user in menu.
	 method				= 	Method that the menu will call.
	 nav_popmenu_id		= 	Id of the popmenu in database.
	 parent_popmenu_id   = 	Id of the parentpopmenu in database.
	 server_name				= 	Servername of the program forms.
	 sort_value				= 	Value where the program should sort on by default.
	 tab.selected			= 	Tab that is selected.
	 tab (array)				=	Array with the tabs of a program.
	 add_mode			=	Addmode for the tab.
	 base_form_name		= 	Base form name of the tab.
	 delete_mode			=	Delete mode for the tab.
	 description			=	Description of the program on the tab.
	 edit_on_tab			= 	Edit on tab, no navigation.
	 form_type			=	Type of the form on tab.
	 program_name		= 	Name of the program on tab.
	 relation			=	The relation between the program and the tab program.
	 relation_filter (array)	=	Relation filter.
	 form_key		=	From key column from the program.
	 to_key			= 	To key column to the tab program.
	 update_mode			= 	Update mode of the tab.
	 table_name				= 	Table name of the forms of the program.
	 template				=	Object with information witch templates are available.
	 update_mode				=	Has user right to update records in this program.
	 view					=	Current view of the program.
	 recordHistory (array)		= 	Object for the tab record history.
	 program					= 	Program name.
	 program_info			= 	Info about the selected record, program description.
	 record_ids				= 	Record column ids.
	 record_info				= 	Record info.
	 record_names			=	Record column names.
	 record_types			=	Record column types.
	 related_form				=	Only used if the framework is in a related view.
	 related_text				=	Header text of the related view.
	 shortcuts (array)			=	Array with the information of the shortcuts.
	 program					=	Program.
	 shortcut_id				=	Id of the shortcut.
	 stack (array)				= 	Stack of the history of the program.
	 form					=	Form that the user was on in history.
	 foundset_related_form	=	Foundset of the related form.
	 foundset_related_text	=	Related text.
	 foundsetfilter			=	Foundset filter.
	 program					=	Program.
	 view					=	View that the program was in.
	 stack_position				= 	Position of the framework in the history stack.
	 switchedForEdit				=	Has the navigation switched for edit.
	 template_types				= 	The template type.
	 template_types_notabs		=	The template types used when there are no tabs.
	 viewer_buttonbar			=	Formname of the viewer buttonbar.
	 viewer_form					=	Formname of the viewer.

	 */

}

/**
 *	To set the tabs
 *
 * <AUTHOR> Aleman
 * @since 2009-09-14
 * @param {String}	_template name of the template
 * @param {{tab:Array}} _progObj Object of the program
 * @return {Array} array with the selected form and program
 *
 * @properties={typeid:24,uuid:"f7b0b4ea-a693-4b69-9926-9be49745c5ce"}
 */
function svy_nav_setTabs(_template, _progObj) {

	var _return = [];

	if (!forms[_template]) {
		return _return;
	}

	if (!forms[_template].elements.tabpanel) {
		return _return;
	}

	var selectedIndex = scopes.globals["avUtilities_tabGetSelectedIndex"](_template, 'tabpanel');
	if (selectedIndex >= 0) {
		var selectedTabName = scopes.globals["avUtilities_tabGetName"](_template, 'tabpanel', selectedIndex);

		if (selectedIndex == _progObj['tab']["selected"]) {
			var _tabObj = _progObj.tab[selectedIndex];

			forms[globals.nav.viewer_buttonbar].elements['btn_duplicate']['enabled'] = (_tabObj.add_mode == 1) && (globals.nav.mode != 'edit' && globals.nav.mode != 'add' && globals.nav.mode != 'find');
			forms[globals.nav.viewer_buttonbar].elements['btn_new']['enabled'] = (_tabObj.add_mode == 1) && (globals.nav.mode != 'edit' && globals.nav.mode != 'add' && globals.nav.mode != 'find');

			globals.nav.form_view_02 = selectedTabName;
			_return[0] = selectedTabName;
			_return[1] = _tabObj.program_name;
		}
	}

	return _return;
}

/**
 * Method that calls the function on the svy_nav_base form
 * <AUTHOR> Aleman
 * @since 2009-11-18
 * @param {JSEvent} _event the event that triggered the action
 * @param {String}	_function name of the function that should be called on the svy_nav_base form
 * @param {String} [sForm] - optional form to use
 * @properties={typeid:24,uuid:"0BF1BA7A-91E6-40D9-BC7D-9021D389EA47"}
 * @AllowToRunInFind
 */
function svy_nav_dc_onClick(_event, _function, sForm) {
	var _triggerForm;
	avanti_button_clicked = _function;
	if (sForm)
	{
		_triggerForm = sForm;
	}
	else
	{
		_triggerForm = _event.getFormName();
	}
		//controller.search()
	if (_triggerForm == 'svy_nav_fr_buttonbar_browser') {
		globals.nav.activeView = 1
		forms[globals.nav.form_view_01][_function](_event, _triggerForm)
	} else {
		globals.nav.activeView = 2
		forms[_triggerForm][_function](_event, _triggerForm);
//		forms[globals.nav.form_view_02][_function](_event, _triggerForm)
	}

}

/**
 * @AllowToRunInFind
 *
 * <AUTHOR> Aleman
 * @since 2009-11-18
 * @param {JSEvent} _event the event that triggered the action
 * @param {String}	_function name of the function that should be called on the svy_nav_base form
 *
 * @properties={typeid:24,uuid:"DDA341D7-34B2-4B06-8099-97EF52A5C30A"}
 */
function svy_nav_dc_onRightClick(_event, _function) {
	var _triggerForm = _event.getFormName()
		//controller.search()
	if (_triggerForm == 'svy_nav_fr_buttonbar_browser') {
		globals.nav.activeView = 1
		forms[globals.nav.form_view_01][_function](_event, _triggerForm)
	} else {
		globals.nav.activeView = 2
		forms[globals.nav.form_view_02][_function](_event, _triggerForm)
	}

}

/**
 * Method that selects the right record in the menu configurator
 * <AUTHOR> Aleman
 * @since 2010-02-18
 * @param {Number} _menu_item_id the id of the menu item
 * @properties={typeid:24,uuid:"76228E33-44DE-457E-9633-4B8747D08126"}
 * @AllowToRunInFind
 */
function svy_nav_c_nodeSelected(_menu_item_id) {

	/** @type {JSFoundSet<db:/svy_framework/nav_menu_items>} */
	var _foundset = databaseManager.getFoundSet(forms.svy_nav_c_menu_item_dtl.controller.getDataSource())

	if (_foundset.find()) {
		_foundset.menu_item_id = _menu_item_id
		_foundset.search()
	}
	forms.svy_nav_c_menu_item_dtl.controller.loadRecords(_foundset)
}

/**
 * Error handling method
 * <AUTHOR> Aleman
 * @since 2010-02-18
 * @throws {Object}
 *
 * @return
 * @properties={typeid:24,uuid:"29E39484-4E1A-4B58-80C3-21886DD6B42D"}
 * @AllowToRunInFind
 */
function svy_nav_onError(e) {

	if (application.isInDeveloper()) {
		// Show the full error in developer to hel debug
	    application.output(e, LOGGINGLEVEL.ERROR);
	}

    var sMsg = "";

    sMsg += "Exception Object: " + e, LOGGINGLEVEL.ERROR + "\n";
    sMsg += "MSG: " + e.message + "\n";

    if (/^User has no menu/.test(e.message)) {
        application.output(sMsg, LOGGINGLEVEL.ERROR);
        return false;
    }
    
    // Cancel any changes and reset the UI to browse mode
    if (globals.nav.mode != 'browse') {
    	// Fire the dc_cancel to rollback and get back in to browse mode.
    	if (application.getActiveWindow().controller.getName() == 'svy_nav_fr_main') {
            // We need to use the name of the program container (which is the tab being shown in the main navigation tab panel)
            /**@type {String} */
            var _triggerForm = null;
            var sFormContainer = globals.nav.programContainer;
            if (sFormContainer && sFormContainer.indexOf('program_container_') > -1) {
            	var sSimpleContainerForm = forms[sFormContainer].elements.splitpane.getRightForm();
                if (sSimpleContainerForm && sSimpleContainerForm['_formname_']) {
                	_triggerForm = sSimpleContainerForm['_formname_'];
                }
                else {
        	        var sComplexContainerForm = forms[sFormContainer].elements.tab_split.getRightForm();
        	        if (sComplexContainerForm && sComplexContainerForm['_formname_']) {
        	        	_triggerForm = sComplexContainerForm['_formname_'];
        	        }               	
                }
                if (_triggerForm) {
                	databaseManager.revertEditedRecords();
                    databaseManager.rollbackTransaction(true);
                    databaseManager.releaseAllLocks()
			        scopes['avDB'].avUnLockRecord(globals.nav_program_name);
			        globals.svy_nav_dc_setStatus("browse", _triggerForm)
                }
            }
        }
    }

    if (e instanceof ServoyException) {
        /** @type {ServoyException} */
        var servoyException = e;

        sMsg += "ServoyException" + "\n";
        sMsg += "Errorcode: " + servoyException.getErrorCode() + "\n";

        if (servoyException.getErrorCode() == ServoyException.SAVE_FAILED) {

            //Get the failed records after a save
            var array = databaseManager.getFailedRecords();

            for (var i = 0; i < array.length; i++) {

                var record = array[i];

                if (record.exception instanceof DataException) {
                    /** @type {DataException} */
                    var dataException = record.exception;
                    sMsg += 'SQL: ' + dataException.getSQL() + "\n";
                    sMsg += 'SQLState: ' + dataException.getSQLState() + "\n";
                    sMsg += 'VendorErrorCode: ' + dataException.getVendorErrorCode() + "\n";
                }
            }
            application.output(sMsg, LOGGINGLEVEL.ERROR);
            return false;
        }

        if (servoyException.getErrorCode() == ServoyException.NO_PARENT_DELETE_WITH_RELATED_RECORDS) {
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('svy.fr.dlg.error'), i18n.getI18NMessage('svy.fr.dlg.noParentDeleteWithRelatedRecords'), i18n.getI18NMessage('avanti.dialog.ok'));
            if (databaseManager.hasTransaction()) databaseManager.rollbackTransaction();
            if (databaseManager.hasLocks()) databaseManager.releaseAllLocks();
            application.output(sMsg, LOGGINGLEVEL.ERROR);
            return false;
        }

        if (servoyException.getErrorCode() == ServoyException.NO_RELATED_CREATE_ACCESS) {
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('svy.fr.dlg.error'), i18n.getI18NMessage('svy.fr.dlg.noRelatedCreateAccess'), i18n.getI18NMessage('avanti.dialog.ok'));
            if (databaseManager.hasTransaction()) databaseManager.rollbackTransaction();
            if (databaseManager.hasLocks()) databaseManager.releaseAllLocks();
            application.output(sMsg, LOGGINGLEVEL.ERROR);
            return false;
        }
    }

    if (globals["avBase_getSystemPreference_Number"](149) == 1) {
        var sDlgMsg = i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + '\n\n' + e + '\n\n' + i18n.getI18NMessage('avanti.dialog.contactSupport');

        globals.DIALOGS.setDialogWidth(750);
        globals.DIALOGS.setDialogHeight(250);
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('servoy.general.error'), sDlgMsg, i18n.getI18NMessage('avanti.dialog.ok'));
        globals.DIALOGS.resetDialogSize();
    }
    try {
        // get an error on this output in the production server sometimes
        application.output(sMsg, LOGGINGLEVEL.ERROR);
    }
    catch (e) {
        // ignore
    }
    return false;
}

/**
 * Method that sets the shortkeys that are available for a user by functions
 * <AUTHOR> Dotzlaw
 * @since 2024-12-05
 * @param {Array} _function_array array with the functions that a user has rights to
 * @properties={typeid:24,uuid:"300E8D16-8E32-4E1A-BE65-B18F95C3EE9F"}
 */
function svy_nav_setShortKeys(_function_array) {
	var isMac = application.getOSName().toLowerCase().indexOf('mac') >= 0;
	/** @type {JSFoundSet<db:/svy_framework/nav_shortkey>} */
	var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_shortkey')
	var _rec
	var _shortcut

	//security check
	var _function_string = _function_array.join(",")
	if(_function_string == '')_function_string = '-1'
	var _query = "SELECT s.shortkey_id\
					FROM nav_shortkey s\
					WHERE s.function_id IN (" + _function_string + ") \
					AND (s.organization_id = ? OR s.organization_id IS NULL)";

	_foundset.loadRecords(_query, [globals.org_id]);
    globals.nav.shortkeys = {};

    for (var i = 1; i <= _foundset.getSize(); i++) {
        _rec = _foundset.getRecord(i);
        _shortcut = '';

        if (databaseManager.hasRecords(_rec.nav_shortkey_to_nav_function)) {
        	if (_rec.flag_control) {
                _shortcut = _shortcut ? _shortcut + (isMac ? ' meta' : ' control') : (isMac ? 'meta' : 'control');
            }
            if (_rec.flag_shift) {
                _shortcut = _shortcut ? _shortcut + ' shift' : 'shift';
            }
            if (_rec.flag_alt) {
                _shortcut = _shortcut ? _shortcut + (isMac ? ' option' : ' alt') : (isMac ? 'option' : 'alt');
            }
            _shortcut = _shortcut ? _shortcut + " " + _rec.shortkey : _rec.shortkey;

            globals.nav.shortkeys[_shortcut] = {
                method: _rec.nav_shortkey_to_nav_function.method,
                args: [],
                argTypes: [],
				menutype: 'F'
            };

            if (utils.hasRecords(_rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments)) {
                _rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.sort('sort_order asc');

                for (var j = 1; j <= _rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.getSize(); j++) {
                    var argRec = _rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.getRecord(j);
                    globals.nav.shortkeys[_shortcut].args[j - 1] = argRec.argument;
                    globals.nav.shortkeys[_shortcut].argTypes[j - 1] = argRec.arg_type;
                }
            }

            plugins.window.createShortcut(_shortcut, globals.svy_nav_callBackShortkeys);
        }
    }
    svy_nav_loadUserShortcuts(globals.avBase_employeeUserID, globals.org_id);
}

/**
 * Gets parent menu item ID
 * @private
 * @param {Number} nMenuItemId Menu item ID
 * @return {Number} Parent menu item ID
 *
 * @properties={typeid:24,uuid:"DED861D5-A2E8-4302-9078-030EEB79A47D"}
 */
function getParentMenuItemId(nMenuItemId) {
    var sSQL = "SELECT parent_id FROM nav_menu_items WHERE menu_item_id = ?";
    var dsParent = scopes.avDB.getDataset(sSQL, [nMenuItemId],
                  globals.avBase_dbase_framework);
    return dsParent && dsParent.getMaxRowIndex() > 0 ?
           dsParent.getValue(1, 1) : null;
}

/**
 * Gets menu item ID for a program
 *
 * @private
 * @param {String} sProgramName Program name
 * @return {Number} Menu item ID
 *
 * @properties={typeid:24,uuid:"838737F7-0CC5-4FE0-81A7-36CD4AF9E9B9"}
 */
function getMenuItemId(sProgramName) {
    var sSQL = "SELECT menu_item_id \
                FROM nav_menu_items \
                WHERE program_name = ?";
    var dsMenuItem = scopes.avDB.getDataset(sSQL, [sProgramName],
                    globals.avBase_dbase_framework);
    return dsMenuItem && dsMenuItem.getMaxRowIndex() > 0 ?
           dsMenuItem.getValue(1, 1) : null;
}

/**
 * Method that is called by all the shortkeys to handle arguments
 * <AUTHOR> Dotzlaw
 * @since 2024-12-05
 * @param {JSEvent} _event
 * @properties={typeid:24,uuid:"9637A11A-13A8-49CB-ACF6-86D80FA5E88D"}
 */
function svy_nav_callBackShortkeys(_event) {

	var sShortkey = _event.getType();
    var oShortcutObj = globals.nav.shortkeys[sShortkey];

    if (!oShortcutObj || !oShortcutObj.program_name) {
        return;
    }

    var nMenuItemId = getMenuItemId(oShortcutObj.program_name);
    if (!nMenuItemId) {
        return;
    }

    var nParentId = getParentMenuItemId(nMenuItemId);
    if (nParentId) {
        // Expand parent node first
        forms.svy_nav_fr_tree.elements.sidenav.setMenuItemExpanded(nParentId.toString(), true, false);
        // Brief delay to allow expansion animation
//        application.executeLater(forms.svy_nav_fr_tree.onMenuItemSelected(nMenuItemId.toString(), _event), 100);
    }
    forms.svy_nav_fr_tree.onMenuItemSelected(nMenuItemId.toString(), _event);




//    var sShortkey = _event.getType();
//    var oShortcutObj = globals.nav.shortkeys[sShortkey];
//
//    if (!oShortcutObj) {
//        return;
//    }
//    application.output("Program: " + oShortcutObj.program_name + " shortkey: " + sShortkey)
//
//    var sProgramName = oShortcutObj.program_name;    if (!sProgramName || !globals.nav.program[sProgramName]) {
//        return;
//    }
//
//    /** @type {String} */
//    var sTemplate = globals.nav.template_types[globals.nav.program[sProgramName].view];
//    if (!sTemplate || !forms[sTemplate]) {
//        return;
//    }
//
//    /** @type {String} */
//    var sFormName = globals.nav.program[sProgramName].form[forms[sTemplate].has1()][2];
//    if (!sFormName) {
//        return;
//    }
//
//
//    // Set history object for navigation tracking
//    globals.nav.his = new Object();
//    globals.nav.fromTree = 1;
//
//    // Clear foundset if needed
//    if (globals.nav.program[sProgramName].empty_foundset) {
//        /** @type {JSFoundSet} */
//        var fs = databaseManager.getFoundSet(sFormName);
//        fs.loadAllRecords();
//    }
//
//    // Reset tab selection
//    if (globals.nav.program[sProgramName].tab) {
//        globals.nav.program[sProgramName].tab.selected = 1;
//    }
//
//    // Navigate to form
//    globals.svy_nav_showForm(sFormName, sProgramName);
}


/**
 * Loads default shortcuts for new user from app_shortcut table
 * Call this when:
 * Creating new users
 * Resetting user shortcuts
 * Adding new default shortcuts to existing users
 *
 * <AUTHOR> Dotzlaw
 * @since 2024-12-05
  * @param {Number} iUserId User ID
 * @param {String} sOrgId Organization ID
 *
 * @properties={typeid:24,uuid:"4F5A7733-79EE-4C6D-BF81-0D1BA0D083B0"}
 */
function svy_nav_loadDefaultShortcuts(iUserId, sOrgId) {
    var sExistingSQL = "SELECT program_name FROM nav_shortcut \
                        WHERE user_id = ? AND organization_id = ?";
    var aExistingArgs = [iUserId, sOrgId];
    var dsExisting = scopes.avDB.getDataset(sExistingSQL, aExistingArgs,
        globals.avBase_dbase_framework);

    var sAppShortcutSQL = "SELECT app_shortcut_id, program_name, program_label, form_name, sort_order \
                          FROM app_shortcut";
    var dsAppShortcuts = scopes.avDB.getDataset(sAppShortcutSQL, null,
        globals.avBase_dbase_avanti);

    var aExistingPrograms = [];
    if (dsExisting) {
	    for (var i = 1; i <= dsExisting.getMaxRowIndex(); i++) {
	        aExistingPrograms.push(dsExisting.getValue(i, 1));
	    }
    }

    for (var j = 1; j <= dsAppShortcuts.getMaxRowIndex(); j++) {
        var sProgramName = dsAppShortcuts.getValue(j, 2);
        if (aExistingPrograms.indexOf(sProgramName) === -1) {
        	var sInsertSQL = "INSERT INTO nav_shortcut \
        						(shortcut_id, program_name, program_label, \
    							form_name, user_id, organization_id, sort_order, app_shortcut_id) \
    							VALUES (NEWID(), ?, ?, ?, ?, ?, ?, ?)";
			var aInsertArgs = [sProgramName,
								dsAppShortcuts.getValue(j, 3),
								dsAppShortcuts.getValue(j, 4),
								iUserId,
								sOrgId,
								dsAppShortcuts.getValue(j, 5),
								dsAppShortcuts.getValue(j, 1)];

            scopes.avDB.RunSQL(sInsertSQL, false, aInsertArgs,
                globals.avBase_dbase_framework);
        }
    }

    var sExistingKeySQL = "SELECT program_name FROM nav_shortkey";
    var dsExistingKeys = scopes.avDB.getDataset(sExistingKeySQL, null,
        globals.avBase_dbase_framework);

    var sAppKeySQL = "SELECT program_name, shortkey, flag_control, flag_shift, flag_alt \
                      FROM app_shortkey";
    var dsAppKeys = scopes.avDB.getDataset(sAppKeySQL, null,
        globals.avBase_dbase_avanti);

    var aExistingKeys = [];
    if (dsExistingKeys) {
	    for (var k = 1; k <= dsExistingKeys.getMaxRowIndex(); k++) {
	        aExistingKeys.push(dsExistingKeys.getValue(k, 1));
	    }
    }

    for (var m = 1; m <= dsAppKeys.getMaxRowIndex(); m++) {
        sProgramName = dsAppKeys.getValue(m, 1);
        if (aExistingKeys.indexOf(sProgramName) === -1) {
            var sInsertKeySQL = "INSERT INTO nav_shortkey \
                               (shortkey_id, program_name, shortkey, \
                                flag_control, flag_shift, flag_alt, user_id, organization_id) \
                               SELECT ISNULL(MAX(shortkey_id), 0) + 1, ?, ?, ?, ?, ?, ?, ? \
                               FROM nav_shortkey";
            var aInsertKeyArgs = [sProgramName,
                dsAppKeys.getValue(m, 2),
                dsAppKeys.getValue(m, 3),
                dsAppKeys.getValue(m, 4),
                dsAppKeys.getValue(m, 5),
                iUserId, sOrgId];

            scopes.avDB.RunSQL(sInsertKeySQL, false, aInsertKeyArgs,
                globals.avBase_dbase_framework);
        }
    }
}

/**
 * Loads all shortcuts and creates window shortcuts for Windows/Mac
 *
 * @param {Number} iUserId User ID
 * @param {String} sOrgId Organization ID
 *
 * @properties={typeid:24,uuid:"8DE1511C-8711-45D4-95A8-35D2B6D9E7F2"}
 */
function svy_nav_loadUserShortcuts(iUserId, sOrgId) {
    globals.nav.shortkeys = {};
    var isMac = application.getOSName().toLowerCase().indexOf('mac') >= 0;

    /** @type {JSFoundSet<db:/svy_framework/nav_shortkey>} */
    var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_shortkey');

    var sFuncSQL = "SELECT s.shortkey_id \
                    FROM nav_shortkey s \
                    WHERE user_id = ? AND (organization_id = ? OR organization_id IS NULL)";

    _foundset.loadRecords(sFuncSQL, [iUserId, sOrgId]);

    for (var i = 1; i <= _foundset.getSize(); i++) {
        var _rec = _foundset.getRecord(i);
        var _shortcut = '';

        if (_rec) {
            if (_rec.flag_control) {
                _shortcut = _shortcut ? _shortcut + (isMac ? ' meta' : ' control') : (isMac ? 'meta' : 'control');
            }
            if (_rec.flag_shift) {
                _shortcut = _shortcut ? _shortcut + ' shift' : 'shift';
            }
            if (_rec.flag_alt) {
                _shortcut = _shortcut ? _shortcut + (isMac ? ' option' : ' alt') : (isMac ? 'option' : 'alt');
            }
            _shortcut = _shortcut ? _shortcut + ' ' + _rec.shortkey : _rec.shortkey;

            globals.nav.shortkeys[_shortcut] = {
                program_name: _rec.program_name,
                menutype: 'P'  // Default to program type
            };

            plugins.window.createShortcut(_shortcut.trim(), globals.svy_nav_callBackShortkeys);
        }
    }
}

/**
 * Removes all window shortcuts based on nav_shortkey table
 *
 * @param {Number} sUserId User ID
 * @param {String} sOrgId Organization ID
 *
 * @properties={typeid:24,uuid:"CE0A910B-7936-4148-B9A5-7BDD5C9DFD77"}
 */
function svy_nav_removeAllWindowShortcuts(iUserId, sOrgId) {
    var isMac = application.getOSName().toLowerCase().indexOf('mac') >= 0;
    var dsShortcuts = svy_nav_getShortkeys(iUserId, sOrgId);

    globals.nav.shortkeys = {}; // Reset the shortkeys object

    for (var i = 1; i <= dsShortcuts.getMaxRowIndex(); i++) {
        var sCombo = '';
        if (dsShortcuts.getValue(i, 2)) {
            sCombo += isMac ? 'meta ' : 'control ';
        }
        if (dsShortcuts.getValue(i, 3)) {
            sCombo += 'shift ';
        }
        if (dsShortcuts.getValue(i, 4)) {
            sCombo += isMac ? 'option ' : 'alt ';
        }
        sCombo += dsShortcuts.getValue(i, 1);

        plugins.window.removeShortcut(sCombo.trim());
    }
}

/**
 * Get all shortkey combinations for user's shortcuts
 *
 * @param {Number} iUserId User ID
 * @param {String} sOrgId Organization ID
 *
 * @return
 * @properties={typeid:24,uuid:"9ACF3595-C15B-4F59-B9EC-503094B709B6"}
 */
function svy_nav_getShortkeys(iUserId, sOrgId) {
    var sSQL = "SELECT DISTINCT k.shortkey, k.flag_control, k.flag_shift, k.flag_alt \
                FROM nav_shortcut s \
                INNER JOIN nav_shortkey k ON s.program_name = k.program_name \
                WHERE s.user_id = ? AND s.organization_id = ?";

    return scopes.avDB.getDataset(sSQL, [iUserId, sOrgId],
           globals.avBase_dbase_framework);
}

/**
* Resets user's navigation shortcuts to match app defaults
*
* <AUTHOR> Dotzlaw
* @since 2024-12-05
* @param {String} sUserId User ID
* @param {String} sOrgId Organization ID
* @return {Boolean} Success flag
*
 * @properties={typeid:24,uuid:"6080C4DC-BC0C-4624-BDFC-02ADB64B303D"}
 */
function svy_nav_resetUserShortcuts(sUserId, sOrgId) {
   var sDeleteSQL = "DELETE FROM nav_shortcut \
                     WHERE user_id = ? AND organization_id = ?";
   var aDeleteArgs = [sUserId, sOrgId];

   if (!scopes.avDB.RunSQL(sDeleteSQL, false, aDeleteArgs,
       globals.avBase_dbase_framework)) {
       return false;
   }

   var sAppShortcutSQL = "SELECT program_name, program_label, form_name, sort_order \
                         FROM app_shortcut";
   /**@type {JSDataSet} */
   var dsAppShortcuts = scopes.avDB.getDataset(sAppShortcutSQL, null,
                        globals.avBase_dbase_avanti);

   for (var i = 1; i <= dsAppShortcuts.getMaxRowIndex(); i++) {
       var sInsertSQL = "INSERT INTO nav_shortcut \
                       (shortcut_id, program_name, program_label, \
                        form_name, user_id, organization_id, sort_order) \
                       VALUES (NEWID(), ?, ?, ?, ?, ?, ?)";
       var aInsertArgs = [dsAppShortcuts.getValue(i, 1),
							dsAppShortcuts.getValue(i, 2),
							dsAppShortcuts.getValue(i, 3),
							sUserId,
							sOrgId,
							dsAppShortcuts.getValue(i, 4)];

       if (!scopes.avDB.RunSQL(sInsertSQL, false, aInsertArgs,
           globals.avBase_dbase_framework)) {
           return false;
       }
   }

   return true;
}

/**
 * Shows popup menu with program shortcuts
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"1F6633F8-86F3-47D8-BC94-6B885A9D3BF5"}
 */
function svy_nav_showShortcutMenu(event) {
    var menu = plugins.window.createPopupMenu();
    var sSQL = "SELECT a.program_label, \
                CASE \
                    WHEN flag_alt = 1 AND flag_shift = 0 THEN 'Alt+' + shortkey \
                    WHEN flag_alt = 1 AND flag_shift = 1 THEN 'Alt+Shift+' + shortkey \
                    WHEN flag_alt = 0 AND flag_shift = 1 THEN 'Shift+' + shortkey \
                    ELSE shortkey \
                END as shortcut_combo \
                FROM app_shortcut a \
                LEFT JOIN app_shortkey k ON a.program_name = k.program_name \
                ORDER BY a.program_label";

    /**@type {JSDataSet} */
    var dsMenu = scopes.avDB.getDataset(sSQL, null, globals.avBase_dbase_avanti);

    for (var i = 1; i <= dsMenu.getMaxRowIndex(); i++) {
        var sLabel = dsMenu.getValue(i, 1);
        var sShortcut = dsMenu.getValue(i, 2) || '';
        var sPadding = new Array(Math.max(1, 40 - sLabel.length)).join(' ');
        menu.addMenuItem(sLabel + sPadding + sShortcut);
    }

    menu.show(event.getSource());
}


///**
// * Method that sets the shortkeys that are available for a user by functions
// * <AUTHOR> Aleman
// * @since 2010-03-03
// * @param {Array} _function_array array with the functions that a user has rights to.
// * @properties={typeid:24,uuid:"300E8D16-8E32-4E1A-BE65-B18F95C3EE9F"}
// */
//function svy_nav_setShortKeys(_function_array) {
//
//	/** @type {JSFoundSet<db:/svy_framework/nav_shortkey>} */
//	var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_shortkey')
//	var _rec
//	var _shortcut
//
//	//security check
//	var _function_string = _function_array.join(",")
//	if(_function_string == '')_function_string = '-1'
//	var _query = "SELECT s.shortkey_id\
//					FROM nav_shortkey s\
//					WHERE s.function_id IN (" + _function_string + ")";
////	var maxReturnedRows = 10000
////	var _dataset = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query, null, maxReturnedRows);
//	_foundset.loadRecords(_query)
//
//	globals.nav.shortkeys = new Object()
//
//	for (var i = 1; i <= _foundset.getSize(); i++) {
//		_rec = _foundset.getRecord(i)
//		_shortcut = ''
//		if (databaseManager.hasRecords(_rec.nav_shortkey_to_nav_function)) {
//			if (_rec.flag_control) {
//				_shortcut = _shortcut ? _shortcut + " control" : "control";
//			}
//			if (_rec.flag_shift) {
//				_shortcut = _shortcut ? _shortcut + " shift" : "shift";
//			}
//			if (_rec.flag_alt) {
//				_shortcut = _shortcut ? _shortcut + " alt" : "alt";
//			}
//			_shortcut = _shortcut ? _shortcut + " " + _rec.shortkey : _rec.shortkey;
//
//			//set method and arguments in a array so that the callback method nows with method to call with which argument
//			globals.nav.shortkeys[_shortcut] = new Object()
//			globals.nav.shortkeys[_shortcut].method = _rec.nav_shortkey_to_nav_function.method
//			globals.nav.shortkeys[_shortcut].arg = new Array()
//			globals.nav.shortkeys[_shortcut].argtypes = new Array()
//			if(utils.hasRecords(_foundset.nav_shortkey_to_nav_function) && utils.hasRecords(_foundset.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments)) {
//				_foundset.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.sort('sort_order asc')
//				for (var j = 1; j <= _rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.getSize(); j++) {
//					_rec = _rec.nav_shortkey_to_nav_function.nav_function_to_nav_function_arguments.getRecord(j)
//					globals.nav.shortkeys[_shortcut].arg[j - 1] = _rec.argument
//					globals.nav.shortkeys[_shortcut].argtypes[j - 1] = _rec.arg_type
//				}
//			}
//
//			plugins.window.createShortcut(_shortcut, globals.svy_nav_callBackShortkeys)
//		}
//	}
//}
//
///**
// * Method that is called by all the shortkeys to handle arguments
// * <AUTHOR> Aleman
// * @since 2010-03-03
// * @param {JSEvent} _event
// * @properties={typeid:24,uuid:"9637A11A-13A8-49CB-ACF6-86D80FA5E88D"}
// */
//function svy_nav_callBackShortkeys(_event) {
//	var _shortkey = _event.getType()
//	/** @type {{arg:Array, method:String, argtypes:Array}} */
//	var _shk_obj = globals.nav.shortkeys[_shortkey]
//	if (!_shk_obj) return
//
//	var _methodcall = _shk_obj.method + '('
//
//	for (var i = 0; i < _shk_obj.arg.length; i++) {
//
//		if (i != 0) {
//			_methodcall += ', '
//		}
//
//		if (_shk_obj.argtypes[i] == 1) {
//			_methodcall += '"' + _shk_obj.arg[i] + '"'
//		} else if (_shk_obj.argtypes[i] == 2) {
//			_methodcall += '"' + globals[_shk_obj.arg[i]] + '"'
//		}
//	}
//	_methodcall += ')'
//
//	eval(_methodcall)
//}

/**
 *
 * Method that looks up all the functions that are available for the logged in user
 * <AUTHOR> Aleman
 * @since 2010-03-03
 * @return return the function id, so they can be filtered in the menu
 * @properties={typeid:24,uuid:"46418AB3-DA49-4106-87A2-B08B3C8AD65C"}
 */
function svy_nav_get_functions() {
	var _query = 'SELECT f.function_id\
					FROM  nav_function f\
					WHERE ( 	f.security_key_id is null \
						OR	f.security_key_id IN (' + globals.nav.keys + '))'
	var maxReturnedRows = 10000
	var _dataset = databaseManager.getDataSetByQuery(globals.nav_db_framework, _query, null, maxReturnedRows);
	var _function_array = _dataset.getColumnAsArray(1)
	return _function_array
}

/**
 * Method that places elements on a form
 * <AUTHOR> Aleman
 * @since 2010-03-03
 * @param {JSForm} _jsForm the form where you want to place elements on
 * @param {JSFoundSet<db:/svy_framework/sec_user_table_properties>} _foundset the foundset of elements you want to place on the form
 * @param {Array} [_fixedElements] fixed elements of the design-time form which should be generated
 *
 * @properties={typeid:24,uuid:"D7CF0A2F-D6A5-4E00-8F14-CFD8EA50337F"}
 */
function svy_nav_placeElementsOnForm(_jsForm, _foundset, _fixedElements) {
	var _start_pos_x = 0
	var _start_pos_y = 0
	if(_jsForm.getHeaderPart()) _start_pos_y += _jsForm.getHeaderPart().height
	if(_jsForm.getTitleHeaderPart()) _start_pos_y += _jsForm.getTitleHeaderPart().height

	// check for fixed elements at the beginning and place them on the form
	if (_fixedElements) {
		for (var j = 0; j < _fixedElements.length; j++) {
			if (_fixedElements[j][2] == false) {
				// element should be fixed at the beginning
				var _fixedElementStart = solutionModel.cloneComponent(_fixedElements[j][0].name, _fixedElements[j][0], _jsForm);
				_fixedElementStart.x = _start_pos_x;
				_fixedElementStart.y = _start_pos_y;

				if (_fixedElements[j][1]) {
					// place the header of the element
					var _fixedHeaderStart = solutionModel.cloneComponent(_fixedElements[j][1].name, _fixedElements[j][1], _jsForm);
					_fixedHeaderStart.x = _start_pos_x;
					_fixedHeaderStart.y = _start_pos_y;
				}

				_start_pos_x += _fixedElementStart.width;
			}
		}
	}

	var _endPositionX = 0
	for (var i = 1; i <= _foundset.getSize(); i++) {
		var _rec = _foundset.getRecord(i)
		if (_jsForm.getComponent(_rec.element_name)) continue // element already exist on form
		if (databaseManager.hasRecords(_rec.sec_user_table_properties_to_nav_program_fields)) {
			var _rec_pf = _rec.sec_user_table_properties_to_nav_program_fields.getRecord(1)

			var _name = _rec.element_name

			if ( (!_rec.location_x) && (!_rec.location_y)) {
				_rec.location_x = 10 + 200
				_rec.location_y = 20 * i + _start_pos_y
				_rec.height = 20
				_rec.width = 200
				if(_rec_pf.field_width)
				{
					_rec.width = _rec_pf.field_width
				}

			}

			var _field = _jsForm.newField(_rec_pf.dataprovider,
				JSField[_rec_pf.display_type],
				_rec.location_x + _start_pos_x,
				_rec.location_y,
				_rec.width,
				_rec.height)
			_field.name = _name
			_field.styleClass = 'table'
			_field.anchors = SM_ANCHOR.ALL

			if (_rec_pf.valuelistname) {
				_field.valuelist = solutionModel.getValueList(_rec_pf.valuelistname)
			}
			var _label = _jsForm.newLabel(_rec_pf.label, _rec.location_x + _start_pos_x, _rec.location_y, _rec.width, _rec.height)
			_label.name = 'lbl_' + _rec.element_name
			_label.labelFor = _name
			_label.styleClass = 'table'
		}

		_endPositionX = _rec.location_x + _start_pos_x + _rec.width;
	}

	// check for fixed elements at the end and place them on the form
	if (_fixedElements) {
		for (var k = 0; k < _fixedElements.length; k++) {
			if (_fixedElements[k][2] == true) {
				// element should be fixed at the end
				var _fixedElementEnd = solutionModel.cloneComponent(_fixedElements[k][0].name, _fixedElements[k][0], _jsForm);
				_fixedElementEnd.x = _endPositionX;
				_fixedElementEnd.y = _start_pos_y;

				if (_fixedElements[k][1]) {
					// place the header of the element
					var _fixedHeaderEnd = solutionModel.cloneComponent(_fixedElements[k][1].name, _fixedElements[k][1], _jsForm);
					_fixedHeaderEnd.x = _endPositionX;
					_fixedHeaderEnd.y = _start_pos_y;
				}

				_endPositionX += _fixedElementEnd.width;
			}
		}
	}

	forms[_jsForm.name].controller.recreateUI()
}

/**
 * Method that creates a table view
 * <AUTHOR> Aleman
 * @since 2010-03-03
 * @param {JSEvent} [_event]
 * @param {Number} _tableViewID user_table_view_id
 * @param {Boolean} _applyNewFoundset whether or not a new foundset should be applied
 * @properties={typeid:24,uuid:"AF5763CF-2B76-4BFB-A464-16ABC8B594E2"}
 * @AllowToRunInFind
 */
function svy_nav_createTableView(_event, _tableViewID, _applyNewFoundset) {
	/** @type {JSFoundSet<db:/svy_framework/sec_user_table_properties>} */
	var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'sec_user_table_properties')
	_foundset.addFoundSetFilterParam('user_table_view_id', '=', _tableViewID)
	_foundset.addFoundSetFilterParam('user_id', '=', globals.svy_sec_lgn_user_id);
	_foundset.addFoundSetFilterParam('organization_id', '=', globals.svy_sec_lgn_organization_id);
	_foundset.loadAllRecords();

	if (_foundset.getSize() == 0) {
		application.output('Could not create view, no data found');
		return;
	}

	var _base = globals.nav.program[globals.nav_program_name].base_form_name;
	var _form = _base + '_tbl';

	// get all the fixed elements which should be placed on the generated table view
	var _fixedElementsTable = new Array();
	_fixedElementsTable = globals.svy_nav_getFixedElementsTable(_form);

	var _jsForm = solutionModel.getForm(_form);
	var _components = _jsForm.getComponents();
	for (var i = 0; i < _components.length; i++) {
		_jsForm.removeComponent(_components[i].name);
	}

	globals.svy_nav_placeElementsOnForm(_jsForm, _foundset, _fixedElementsTable);

	forms[_form].controller.recreateUI()

	/** @type {JSFoundSet<db:/svy_framework/nav_user_table_view>} */
	var _foundsetTableView = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_user_table_view');
	_foundsetTableView.addFoundSetFilterParam('user_table_view_id', '=', _tableViewID);
	_foundsetTableView.addFoundSetFilterParam('user_id', '=', globals.svy_sec_lgn_user_id);
	_foundsetTableView.addFoundSetFilterParam('organization_id', '=', globals.svy_sec_lgn_organization_id);
	_foundsetTableView.loadAllRecords();


	if (_applyNewFoundset) {
		if (_foundsetTableView.search_type == 'R') {
			globals.svy_sea_restoreSearch(_foundsetTableView.search_id);
		} else if (_foundsetTableView.search_type == 'A') {
			globals.svy_sea_restoreAdvancedSearch(_foundsetTableView.search_id);
		} else {
			globals.nav.activeView = 1
			forms[globals.nav.form_view_01].dc_loadAll();
		}
	}

	if(_event) {
		globals.DIALOGS.closeForm(_event);
	}
}

/**
 * Method that creates a default table view
 * <AUTHOR> Aleman
 * @since 2010-03-03
 * @param {String} _program program name
 * @param {String} _form form name
 * @properties={typeid:24,uuid:"E3C1AABD-CF67-4EF7-89F7-E8DC475D7237"}
 */
function svy_nav_createDefaultTableView(_program, _form) {
	globals.nav.program[_program].table_created = 1

	//get an id
	/** @type {JSFoundSet<db:/svy_framework/nav_user_table_view>} */
	var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'nav_user_table_view')
	_foundset.addFoundSetFilterParam('program_name', '=', _program)
	_foundset.addFoundSetFilterParam('flag_system', '=', 1)
	_foundset.addFoundSetFilterParam('user_id', '=', globals.svy_sec_lgn_user_id);
	_foundset.addFoundSetFilterParam('organization_id', '=', globals.svy_sec_lgn_organization_id);

	_foundset.loadAllRecords()

	if (_foundset.getSize() == 0) {//user doesn't have a view yet
		//create the view
		_foundset.newRecord()
		_foundset.program_name = _program
		_foundset.table_name = globals.nav.program[_program].table_name
		_foundset.server_name = globals.nav.program[_program].server_name
		_foundset.flag_system = 1
		_foundset.user_id = globals.svy_sec_lgn_user_id;
		_foundset.organization_id = globals.svy_sec_lgn_organization_id;
		_foundset.name = 'System'

		/** @type {JSFoundSet<db:/svy_framework/sec_user_table_properties>} */
		var _foundset_prop = databaseManager.getFoundSet(globals.nav_db_framework, 'sec_user_table_properties')

//		var _rec_field

		for (var i = 1; i <= _to_nav_program.nav_program_to_nav_program_fields$default.getSize(); i++) {
			var _rec = _to_nav_program.nav_program_to_nav_program_fields$default.getRecord(i)
			_foundset_prop.newRecord()
			_foundset_prop.program_field_id = _rec.program_field_id
			_foundset_prop.user_table_view_id = _foundset.user_table_view_id
			_foundset_prop.user_id = globals.svy_sec_lgn_user_id;
			_foundset_prop.organization_id = globals.svy_sec_lgn_organization_id;
			_foundset_prop.element_name = _rec.elementname
		}

	}

	var _user_table_view_id = _foundset.user_table_view_id
//	var _save =
		databaseManager.saveData()

		//find the foundset with the properties of the elements
	_foundset_prop = databaseManager.getFoundSet(globals.nav_db_framework, 'sec_user_table_properties')
	_foundset_prop.addFoundSetFilterParam('user_table_view_id', '=', _user_table_view_id)
	_foundset_prop.addFoundSetFilterParam('user_id', '=', globals.svy_sec_lgn_user_id);
	_foundset_prop.addFoundSetFilterParam('organization_id', '=', globals.svy_sec_lgn_organization_id);
	_foundset_prop.loadAllRecords()

	//create the form
	var _jsForm = solutionModel.newForm(_form, 'db:/'+globals.nav.program[_program].server_name+'/'+ globals.nav.program[_program].table_name, 'sampleuse_navigation', false, 800, 500)
	var _extend = 'svy_nav_base'
	var _base = globals.nav.program[_program].base_form_name
	if(forms[_base]) _extend = _base
	_jsForm.extendsForm = solutionModel.getForm(_extend)
	_jsForm.view = JSForm.LOCKED_TABLE_VIEW

	//place the fields on the form.
	globals.svy_nav_placeElementsOnForm(_jsForm, _foundset_prop)

}

/**
 * @properties={typeid:24,uuid:"959C3499-A965-468D-9934-B7367CCA778E"}
 * @AllowToRunInFind
 */
function svy_nav_postLogin() {

	forms.svy_nav_fr_loading.setStatusBar(5);
	globals.user_id = globals.svy_sec_lgn_user_id;
	globals.svy_sec_username = security.getUserName();


	//show open tabs in the framework if the property is set.
//	globals.nav_properties = 'show_open_tabs';
//	if(databaseManager.hasRecords(_to_nav_properties$property_name) && _to_nav_properties$property_name.property_value[0] == 'true') {
//		forms.svy_nav_fr_openTabs.showFormInFramework();
//		globals.nav_show_open_tabs = 1;
//	}
	globals.nav_show_open_tabs = 0;

	// get the record so it is editable
	/** @type {JSFoundSet<db:/svy_framework/sec_user>} */
	var _foundset = databaseManager.getFoundSet(globals.nav_db_framework, 'sec_user');
	var _rec;
	if(_foundset.find()) {
		_foundset.user_id = globals.user_id;
		_foundset.search();
		_rec = _foundset.getRecord(1);
	}

	globals.owner_id = globals.svy_sec_owner_id = _rec.owner_id;

	//set valuelistOwner id
	if(globals.vlt_owner_id) {
		globals.vlt_owner_id = globals.svy_sec_owner_id;
	}

	globals.svy_sec_currentdate = new Date();

	var bResetPassword = false;

	if (!globals.avLogin_isSSO) {
		/**@type {JSRecord<db:/svy_framework/sec_user_password>} */
		var rUserPassword = getUserLoginPasswordRecord(globals.user_id);

		// user has clicked the 'reset password'
		if (globals.bResettingPassword) {
			globals.bResettingPassword = false;
			bResetPassword = true;
		}
		// password is expired
		if (!databaseManager.hasRecords(_rec.sec_user_to_sec_user_password$current_date)) {
			bResetPassword = true;
		}
		// must change pwd on fst login
		else if (rUserPassword && rUserPassword.must_change_pwd_on_1st_login == 1) {
			bResetPassword = true;
		}
	}

	_rec.times_wrong_login = null;

	if (rUserPassword) {
		rUserPassword.must_change_pwd_on_1st_login = null;
		databaseManager.saveData(rUserPassword);
	}

	// password must be reset
	if (bResetPassword) {
		globals.DIALOGS.showFormInModalDialog(forms.svy_sec_password_new_login, null, null, null, null, null, true, false, "svy_sec_password_new", true);
	}
	else {
		//run onPostLogin-method when available
		if(globals['onPostLogin']) {
			globals['onPostLogin']();
		}
		globals.nav = new Object();

		// set the i18n based on the owner/organization
		if (globals.svy_nav_setI18N) {
			globals.svy_nav_setI18N(globals.svy_sec_lgn_organization_id);
		}

		//set the security for the elements
		globals.svy_sec_getSecurityKeys();
		//globals.svy_sec_setElementRightsWithKeys() -- CH: Wasting 350 ms and we don't use this.
		globals.svy_sec_setTableFilters();

		globals.svy_nav_init();
		forms.svy_nav_fr_loading.setStatusBar(100);
	}
}

/**
 * @param {Number} nUserID
 *
 * @return {JSRecord<db:/svy_framework/sec_user_password>}
 *
 * @properties={typeid:24,uuid:"48E9F859-7DDD-40FF-AE39-3222F89C2472"}
 */
function getUserLoginPasswordRecord(nUserID) {
	var sSQL = "SELECT user_password_id \
				FROM sec_user_password \
				WHERE \
					user_id = ? \
					AND ? BETWEEN start_date AND end_date";
	var aArgs = [nUserID, application.getTimeStamp()];
	/**@type {JSFoundSet<db:/svy_framework/sec_user_password>} */
	var fsUserPassword = databaseManager.getFoundSet("svy_framework", "sec_user_password");
	/**@type {JSRecord<db:/svy_framework/sec_user_password>} */
	var rUserPassword = null;

	fsUserPassword.loadRecords(sSQL, aArgs);

	if (utils.hasRecords(fsUserPassword)) {
		fsUserPassword.sort("user_password_id asc");
		rUserPassword = fsUserPassword.getRecord(1) ;
	}

	return rUserPassword;
}


/**
 * @properties={typeid:24,uuid:"E93BB43F-4814-4248-9B7F-BEFD34BBC4CE"}
 * @AllowToRunInFind
 */
function svy_nav_postPostLogin () {

	//run onPostLogin-method when available
	if(globals['onPostLogin']) {
		globals['onPostLogin']();
	}
	globals.nav = new Object();

	// set the i18n based on the owner/organization
	if (globals.svy_nav_setI18N) {
		globals.svy_nav_setI18N(globals.svy_sec_lgn_organization_id);
	}

	//set the security for the elements
	globals.svy_sec_getSecurityKeys();
	globals.svy_sec_setTableFilters();

	globals.svy_nav_init();
	forms.svy_nav_fr_loading.setStatusBar(100);

	forms.svy_nav_fr_main.controller.show();
}

/**
 * @return
 * @properties={typeid:24,uuid:"7D0B5E3F-4AA6-4D91-87DC-80A52FC496EC"}
 */
function svy_nav_showTabDetail() {

	var _tab_form = globals.nav_program_name + '_tab';
    if (!forms[_tab_form]) {
        application.output('Tab form does not exist: ' + _tab_form);
        return null;
    }

    var selectedIndex = scopes.globals["avUtilities_tabGetSelectedIndex"](_tab_form, 'tabpanel');
    if (selectedIndex >= 0) {
        var selectedTabName = scopes.globals["avUtilities_tabGetName"](_tab_form, 'tabpanel', selectedIndex);
        globals.nav.form_view_02 = selectedTabName;
        return true;
    }

    return false;
}

/**
 * @return
 * @properties={typeid:24,uuid:"FD0A6E8D-007F-4C9C-8910-38638A435377"}
 */
function svy_nav_showTabTable() {
	//set view 2 with the form
	var _tab_form = globals.nav_program_name + '_tab' // depending on the table the right tab form schould be set
	if (!forms[_tab_form]) // if the form doesn't exist print a warning to output and exit the method.
	{
		return null;
	}

	/** @type {{tab:Array}} */
	var _progObj = globals.nav.program[globals.nav_program_name]

	//set the tabs on view 2
	var _tab_relation = null
	var _form_at_tab

	var _tabIndex = scopes.globals["avUtilities_tabGetSelectedIndex"]("_tab_form", "tabs")

scopes.globals["avUtilities_tabRemoveAt"](null, null, _tabIndex, 	forms[_tab_form].elements['tabs'])
	_form_at_tab = globals.nav.program[_progObj.tab[_tabIndex].program_name].form[1][2]

	//if form doesn't exist create it, it can happen that table views don't exist but are created dynamicly
	if (!forms[_form_at_tab]) {
		globals.svy_nav_createDefaultTableView(_progObj.tab[_tabIndex].program_name, _form_at_tab)
	}

	/** @type {{program_image:String}} */
	var _progObjTab = globals.nav.program[_progObj.tab[_tabIndex].program_name]
	/** @type {{description:String,relation:String}} */
	var _tabObj = _progObj.tab[_tabIndex]
	var _imageUrl
	if (_progObjTab.program_image) {
		_imageUrl = 'media:///' + _progObjTab.program_image
	} else {
		_imageUrl = null
	}
	// set the form on the tab
	if (_tabObj.relation) {
		_tab_relation = _tabObj.relation
		scopes.globals["avUtilities_tabAdd"](_tab_form, "tabs", forms[_form_at_tab], _tabObj.description, _tab_relation, null, '', (_tabIndex-1), null)
	} else {
		scopes.globals["avUtilities_tabAdd"](_tab_form, "tabs", forms[_form_at_tab], _tabObj.description, null, null, '', (_tabIndex-1), null)
	}
	scopes.globals["avUtilities_tabSetSelectedIndex"](_tab_form, "tabs", _tabIndex)
	return true
}

/**
 * @properties={typeid:24,uuid:"82FE7A09-2056-4460-B544-813F69E73B8F"}
 */
function svy_nav_rec_prev(_event) {
	//function is used by the shortkeys
	forms[globals.nav.form_view_01].controller.setSelectedIndex(forms[globals.nav.form_view_01].controller.getSelectedIndex()-1)
}

/**
 * @properties={typeid:24,uuid:"A1D32F7C-E58C-451D-8C23-4D0395A47598"}
 */
function svy_nav_rec_next(_event) {
	//function is used by the shortkeys
	forms[globals.nav.form_view_01].controller.setSelectedIndex(forms[globals.nav.form_view_01].controller.getSelectedIndex()+1)
}

/**
 * Method that navigates to the form and program and selects the record
 * <AUTHOR> Aleman
 * @since 2010-10-11
 * @param {String} _program program name
 * @param {String} _form form name
 * @param {Number} _record_id record id
 *
 * @properties={typeid:24,uuid:"553CF49E-35AE-400B-9DA9-B95CF09BD2BB"}
 */
function svy_nav_showProgramRecord(_form, _program, _record_id) {
	globals.nav.program[_program].view = 0
	globals.svy_nav_showForm(_form,_program,false)
	forms[_form].foundset.loadRecords(_record_id)
}

/**
 * Will create a different menu not using the tree menu for easy use on mobile divices
 * <AUTHOR> Aleman
 * @since 2011-02-03
 * @properties={typeid:24,uuid:"4AB46F90-1A5E-4E91-A2DB-9067C4DBA83A"}
 */
function svy_nav_m_init_menu() {

	var _jsFormMenu = solutionModel.getForm('svy_nav_fr_m_menu')

	var _rec, _formname; //_jsTabP
	for (var i = 1; i <= forms.svy_nav_fr_tree.foundset.getSize(); i++) {

		/** @type {JSRecord<db:/svy_framework/nav_menu_items>} */
		_rec =  forms.svy_nav_fr_tree.foundset.getRecord(i)
		//build a menu

		_formname = 'svy_nav_fr_m_menu_parts'+i
		//make new form instance
		application.createNewFormInstance('svy_nav_fr_m_menu_parts',_formname)
		forms[_formname].elements['lbl_menu']['text'] = _rec.display_description

		if(utils.hasRecords(_rec.nav_menu_items_to_nav_menu_items$parent_id)) {
			_rec.nav_menu_items_to_nav_menu_items$parent_id.sort('sort_order asc')
			forms[_formname].controller.loadRecords(_rec.nav_menu_items_to_nav_menu_items$parent_id)
		} else {
			forms[_formname].controller.loadRecords(_rec.nav_menu_items_to_nav_menu_items$self_id)
		}

		if(i==1)
		{
			forms[_formname]['vMenuExpanded'] = 1
		}

		//Add the menu part to the menu form as a tabpannel,
		//other method (forms.svy_nav_fr_m_menu.drawForm()) will give it the right size and pos
//		_jsTabP =
			_jsFormMenu.newTabPanel('tab_'+i,0,0,50,50)

	}
	forms.svy_nav_fr_m_menu.controller.recreateUI()
	for (var j = 1; j <= forms.svy_nav_fr_tree.foundset.getSize(); j++) {
//		forms.svy_nav_fr_m_menu.elements['tab_'+j].addTab(forms['svy_nav_fr_m_menu_parts'+j])
		scopes.globals["avUtilities_tabAdd"]('svy_nav_fr_m_menu', 'tab_'+j, forms['svy_nav_fr_m_menu_parts'+j]);
	}
	forms.svy_nav_fr_m_menu.drawForm()

	//put the form in menu
	scopes.globals["avUtilities_tabRemoveAll"]("svy_nav_fr_tree_top", "tree")
//	forms.svy_nav_fr_tree_top.elements.tree.addTab(forms.svy_nav_fr_m_menu)
	scopes.globals["avUtilities_tabAdd"]('svy_nav_fr_tree_top', 'tree', forms.svy_nav_fr_m_menu);
	forms.svy_nav_fr_main.elements.tab_split.setDividerLocation(200);
}

/**
 * Will remove tooltips from forms so they can be easily used in ipad/iphone
 * <AUTHOR> Aleman
 * @since 2011-02-03
 * @properties={typeid:24,uuid:"F3E56CD6-6344-4EA1-A73F-FFEF1EB77357"}
 */
function svy_nav_removeTooltips() {

	// check if iPhone is used
	if(!scopes.svySystem.isIphoneIpad()) return // <- my attempt at fix
//	if(!scopes.svySystem.isIOSPlatform())return  <- after merge into Ti project
//	if(!globals.svy_utl_OsIsIphoneIpad())return  <- original code in Slingshot Web client


	var _forms = ['svy_nav_fr_bottom_bkmk_short',
	              'svy_nav_fr_tree_top',
	              'svy_nav_fr_buttonbar_browser',
	              'svy_nav_fr_buttonbar_viewer']
	var _jsForm, _labels, _label
	for (var i = 0; i <  _forms.length; i++) {
		_jsForm = solutionModel.getForm(_forms[i])
		_labels = _jsForm.getLabels()
		for (var j = 0; j < _labels.length; j++) {
			_label = _labels[j]
			_label.toolTipText = null
		}
	}
}

/**
 *  Will add table filters to filter all the organization records
 * <AUTHOR> Aleman
 * @since 2011-02-05
 *
 * @properties={typeid:24,uuid:"9BE76907-D275-45DE-B54A-7D654502F7D0"}
 */
function svy_nav_filterOrganization() {
	var _database = globals.nav_server_user
	var _organization_id = globals.svy_sec_lgn_organization_id


	databaseManager.removeTableFilterParam(_nav_database,'organization_filter')

	if(_to_sec_owner$owner_id.filter_field_organization)
	{
		databaseManager.removeTableFilterParam(_database,'organization_filter')
		databaseManager.addTableFilterParam(_database,null,_to_sec_owner$owner_id.filter_field_organization,'=',_organization_id,'organization_filter')

//		databaseManager.addTableFilterParam(_database,null,_to_sec_owner$owner_id.filter_field_organization,'sql:in',[_organization_id, 'CCA7F91A-0F62-4BE8-BD0E-0AD9F61CECC8'],'organization_filter')

//		// Filter the nav_popmenus for the org
//		databaseManager.addTableFilterParam(globals.nav_db_framework,'nav_popmenu', 'org_id', 'sql:in', [_organization_id, 'CCA7F91A-0F62-4BE8-BD0E-0AD9F61CECC8'], 'nav_popmenu_filter_1');


//		databaseManager.addTableFilterParam(_database,null,_to_sec_owner$owner_id.filter_field_organization,'=',_organization_id,'organization_filter')
	}
	//filter navigation tables
	var _nav_database = globals.nav_db_framework
	databaseManager.addTableFilterParam(_nav_database,'nav_user_required_field','organization_id','=',_organization_id,'organization_filter')

	// filter i18n
//	i18n.setI18NMessagesFilter('i18n_organization_id', _organization_id);
}

/**
 * Gets the required fields for a program, the ones defined by the programmer and the ones defined by the user
 * @param {{required_fields:Object,program_name:String}} _progObj the object of the current program
 * @return {Object} _progObj.required_fields the object with the required fields
 *
 * @properties={typeid:24,uuid:"AEA84C67-4E8C-4E7E-A1F2-60C9EDD973C5"}
 */
function svy_nav_getRequiredFields(_progObj) {

	//if the required fields are already known just return them
	if(_progObj.required_fields)
	{
		return _progObj.required_fields
	}

	_progObj.required_fields = new Object()

	var _fs_fields, _fs_fields_size
	/** @type{JSRecord<db:/svy_framework/nav_program_fields>} */
	var _rec_fields

	//get the required fields for a program
	/** @type {JSFoundSet<db:/svy_framework/nav_program_fields>} */
	_fs_fields = databaseManager.getFoundSet(globals.nav_db_framework,'nav_program_fields')
	_fs_fields.addFoundSetFilterParam('program_name','=',_progObj.program_name)
	_fs_fields.addFoundSetFilterParam('flag_required','=',1)
	_fs_fields.loadAllRecords()
	_fs_fields_size = _fs_fields.getSize()
	for (var i = 1; i <= _fs_fields_size; i++) {
		_rec_fields = _fs_fields.getRecord(i)
		_progObj.required_fields[_rec_fields.dataprovider] = new Object()
		_progObj.required_fields[_rec_fields.dataprovider].db_status = 'R'
		_progObj.required_fields[_rec_fields.dataprovider].req_by_prog = true
		_progObj.required_fields[_rec_fields.dataprovider].form_status = 'R'
		_progObj.required_fields[_rec_fields.dataprovider].on_form = false

	}

	//get the required fields for a program set by user - organization
	_fs_fields = databaseManager.getFoundSet(globals.nav_db_framework,'nav_user_required_field')
	_fs_fields.addFoundSetFilterParam('program_name','=',_progObj.program_name)
	_fs_fields.loadAllRecords()
	_fs_fields_size = _fs_fields.getSize()
	for (var j = 1; j <= _fs_fields_size; j++) {
		_rec_fields = _fs_fields.getRecord(j)
		_progObj.required_fields[_rec_fields.dataprovider] = new Object()
		_progObj.required_fields[_rec_fields.dataprovider].db_status = 'R'
		_progObj.required_fields[_rec_fields.dataprovider].req_by_prog = false
		_progObj.required_fields[_rec_fields.dataprovider].form_status = 'R'
		_progObj.required_fields[_rec_fields.dataprovider].on_form = false

	}

	return _progObj.required_fields
}

/**
 * Sets the required fields on the form, will add * to the label of the required fields
 * @param {{required_fields:Object,program_name:String}} _progObj the object of the current program
 * @param {String} _form the name of the form
 * @param {String} _status the status of the framework
 *
 * @properties={typeid:24,uuid:"B7F52A9D-63E1-47C8-9A3F-D215EC08B76C"}
 */
function svy_nav_setRequiredFields(_progObj, _form, _status) {
    // Initialize globals.nav.requiredFieldsData if it doesn't exist
    if (!globals.nav.requiredFieldsData) {
        globals.nav.requiredFieldsData = {};
    }

    // Initialize the field label map if it doesn't exist
    if (!globals.nav.requiredFieldsLabelMap) {
        globals.nav.requiredFieldsLabelMap = {};
    }

    if (_form.indexOf('program_container_') > -1) {
        var sSimpleContainerForm = forms[_form].elements.splitpane.getRightForm();
        if (sSimpleContainerForm && sSimpleContainerForm['_formname_']) {
        	_form = sSimpleContainerForm['_formname_'];
        }
        else if (forms[_form].elements.tab_split) {
	        var sComplexContainerForm = forms[_form].elements.tab_split.getRightForm();
	        if (sComplexContainerForm && sComplexContainerForm['_formname_']) {
	        	_form = sComplexContainerForm['_formname_'];
	        }
        	
        }
        if (!sSimpleContainerForm && !sComplexContainerForm) {
        	return;
        }
    }

    // Initialize arrays to track processed forms
    var processedForms = [];

    // Make sure required fields are loaded
    _progObj.required_fields = svy_nav_getRequiredFields(_progObj);

    // Step 1: Collect data about required fields across all forms
    collectRequiredFieldsData(_progObj, _form, _status, processedForms);

    // Step 2: Apply UI changes using only runtime references
    applyRequiredFieldsChanges(_status);
}

/**
 * Stores field labels from all forms for validation purposes
 * @param {{required_fields:Object,program_name:String,requiredLabels:Object}} _progObj
 *
 * @properties={typeid:24,uuid:"AA3E9B5D-6F4E-4B3D-A876-BCD5C6C9D31B"}
 */
function storeFieldLabelsForValidation(_progObj) {
    // Initialize requiredLabels if it doesn't exist
    if (!_progObj.requiredLabels) {
        _progObj.requiredLabels = {};
    }

    // Process all collected labels in requiredFieldsData
    for (var formName in globals.nav.requiredFieldsData) {
        var formData = globals.nav.requiredFieldsData[formName];

        for (var i = 0; i < formData.fieldsToModify.length; i++) {
            var field = formData.fieldsToModify[i];
            var element = forms[formName].elements[field.labelElement];

            if (element && element.text !== undefined) {
                // Store clean label text without any asterisks
                var cleanLabel = element.text.replace(/^\*+\s/, "");
                _progObj.requiredLabels[field.requiredField] = cleanLabel;

                // Also store by base field name for easier lookup
                var lastDotIndex = field.requiredField.lastIndexOf('.');
                if (lastDotIndex !== -1) {
                    var baseFieldName = field.requiredField.substring(lastDotIndex + 1);
                    _progObj.requiredLabels[baseFieldName] = cleanLabel;
                }
            }
        }
    }
}

/**
 * Collects data about required fields for a form and its child forms
 * @param {{required_fields:Object,program_name:String}} _progObj the object of the current program
 * @param {String} _form the name of the form
 * @param {String} _status the status of the framework
 * @param {Array} processedForms Array to track processed forms
 * @return {Boolean} Success indicator
 *
 * @properties={typeid:24,uuid:"C4D8F719-14FE-43B0-A29F-0AB12DBAF277"}
 */
function collectRequiredFieldsData(_progObj, _form, _status, processedForms) {
    // Skip if form doesn't exist or has already been processed
    if (!forms[_form] || processedForms.indexOf(_form) !== -1) return false;

    var jsForm = solutionModel.getForm(_form);
    if (!jsForm) return false;

    // Add to processed forms list
//    application.output("Processing form: " + _form + " - Processed forms: " + processedForms.join(','));
    processedForms.push(_form);

    // Initialize form data in our global map
    globals.nav.requiredFieldsData[_form] = {
        fieldsToModify: [],
        status: _status
    };
    
    
    var fieldName = null;
    var webComponents = jsForm.getWebComponents();
	var childContainers = [];
	for (var i = 0; i < webComponents.length; i++) {
		var oWebComponent = webComponents[i];
		if (oWebComponent.typeName === 'servoycore-formcontainer') {

			childContainers = [];
            try {
                var containedFormName = oWebComponent.getJSONProperty('containedForm').name;
                if (containedFormName) {
                    var containedJSForm = solutionModel.getForm(containedFormName);
                    if (containedJSForm) {
                    	
                    	// Load all the required fields in case some are present on this form
					    if (_progObj.required_fields
								&& globals.nav.requiredFieldsData[_form].fieldsToModify.length == 0) {
					    	for (fieldName in _progObj.required_fields) {
					    		globals.nav.requiredFieldsData[_form].fieldsToModify.push(fieldName);
					    	}
					    }
					    
                        // Recursively process the contained form
                        collectFormRequiredFields(_progObj, containedJSForm, containedFormName, _status);
			        	childContainers = collectChildContainers(jsForm, _form);
			        	if (childContainers && childContainers.length > 0) {
			        		collectChildFormsData(_form, _status, childContainers, processedForms);
			        	}
                    }
                }
            } catch (e) {
            	// ignore error; form container
//                application.output('Warning: Error processing formcontainer: ' + e, LOGGINGLEVEL.WARNING);
            }
        }
	}

	if (_progObj.required_fields
			&& globals.nav.requiredFieldsData[_form].fieldsToModify.length == 0) {
		for (fieldName in _progObj.required_fields) {
    		globals.nav.requiredFieldsData[_form].fieldsToModify.push(fieldName);
    	}
	}

    // Analyze form for required fields
    collectFormRequiredFields(_progObj, jsForm, _form, _status);

    // Collect child forms data
    childContainers = collectChildContainers(jsForm, _form);
    collectChildFormsData(_form, _status, childContainers, processedForms);

    return true;
}

/**
 * Collects information about required fields on a form
 * @param {{required_fields:Object,program_name:String}} _progObj the object of the current program
 * @param {JSForm} jsForm - The solution model form
 * @param {String} _form - Form name
 * @param {String} _status - Current form status
 *
 * @properties={typeid:24,uuid:"87579B53-C069-48EA-9137-B0C64B71415B"}
 */
function collectFormRequiredFields(_progObj, jsForm, _form, _status) {
    // Debug output for City and Contact fields
//    application.output('DEBUG: collectFormRequiredFields for form: ' + _form);

    var bReturnParentFields = false;

    if (jsForm.getVariable('bGetParentFields') &&
        jsForm.getVariable('bGetParentFields').defaultValue === 'true') {
        bReturnParentFields = true;
    }

    var components = jsForm.getComponents(bReturnParentFields);

    // First pass: collect all dataproviders
    var formDataProviders = {};
    for (var i = 0; i < components.length; i++) {
        var component = components[i];
        if (!(component instanceof JSWebComponent)) continue;

        try {
            var typeName = component.typeName;

            // We're looking for labels that point to fields
            if (typeName === 'bootstrapcomponents-label') {
                var labelFor = null;
                try {
                    labelFor = component.getJSONProperty('labelFor');
                } catch (e) {

                }

                if (!labelFor) {
                    continue;
                }

                // Find the field this label points to
                var fieldComponent = null;
                for (var j = 0; j < components.length; j++) {
                    if (components[j].name === labelFor) {
                        fieldComponent = components[j];
                        break;
                    }
                }

                if (!fieldComponent || !(fieldComponent instanceof JSWebComponent)) continue;

                var dataProvider = null;
                try {
                    dataProvider = fieldComponent.getJSONProperty('dataProvider');
                } catch (e) {

                }

                if (!dataProvider) continue;

                // Store the mapping of dataprovider to label element and label text
                var labelText = component.getJSONProperty('text');
                // Clean the label text (remove asterisks if present)
                var cleanLabelText = labelText ? labelText.replace(/^\*+\s*/, "").trim() : null;

                formDataProviders[dataProvider] = {
                    labelElement: component.name,
                    dataProvider: dataProvider,
                    labelText: cleanLabelText
                };
            }
        } catch (e) {
            application.output('Warning: Error collecting dataprovider: ' + e, LOGGINGLEVEL.WARNING);
        }
    }

    // Second pass: check all required fields against form dataproviders
    for (var reqField in _progObj.required_fields) {
        var requiredFieldInfo = _progObj.required_fields[reqField];
        var isMatched = false;
        var matchedLabel = null;
        var matchedProvider = null;
        var matchedLabelText = null;

        // Case 1: Direct match
        if (formDataProviders[reqField]) {
            isMatched = true;
            matchedLabel = formDataProviders[reqField].labelElement;
            matchedProvider = reqField;
            matchedLabelText = formDataProviders[reqField].labelText;
        } else {
            // Case 2: Field is a relation field (contains dots)
            var lastDotIndex = reqField.lastIndexOf('.');
            if (lastDotIndex !== -1) {
                var baseFieldName = reqField.substring(lastDotIndex + 1);

                // Check if base field name matches any dataprovider
                if (formDataProviders[baseFieldName]) {
                    isMatched = true;
                    matchedLabel = formDataProviders[baseFieldName].labelElement;
                    matchedProvider = baseFieldName;
                    matchedLabelText = formDataProviders[baseFieldName].labelText;
                }
            } else {
                // Case 3: Simple field name, check if it's part of a relation field in the form
                for (var formDP in formDataProviders) {
                    var formDPLastDotIndex = formDP.lastIndexOf('.');
                    if (formDPLastDotIndex !== -1) {
                        var formBaseField = formDP.substring(formDPLastDotIndex + 1);
                        if (formBaseField === reqField) {
                            isMatched = true;
                            matchedLabel = formDataProviders[formDP].labelElement;
                            matchedProvider = formDP;
                            matchedLabelText = formDataProviders[formDP].labelText;
                            break;
                        }
                    }
                }
            }
        }

        // If we found a match, add it to the required fields for this form
        if (isMatched && matchedLabel) {
            // Initialize the form data if needed
            if (!globals.nav.requiredFieldsData[_form]) {
                globals.nav.requiredFieldsData[_form] = {
                    fieldsToModify: [],
                    status: _status
                };
            }

            globals.nav.requiredFieldsData[_form].fieldsToModify.push({
                labelElement: matchedLabel,
                isRequired: (requiredFieldInfo.form_status == 'R'),
                isProgrammerRequired: requiredFieldInfo.req_by_prog,
                dataProvider: matchedProvider,
                requiredField: reqField
            });

            // Update the on_form flag
            if ((_status == 'edit' || _status == 'add' || _status == 'required_fields') &&
                requiredFieldInfo.form_status == 'R') {
                requiredFieldInfo.on_form = true;

                // Store the label text in our global map - KEY OPTIMIZATION HERE
                if (matchedLabelText) {
                	if (!globals.nav.requiredFieldsLabelMap[_form]) {
                	    globals.nav.requiredFieldsLabelMap[_form] = {};
                	}
                    // Store using both the full field name and the base field name (if it's a relation field)
                    // Also store the form name for each field to know where to look for values
                    globals.nav.requiredFieldsLabelMap[_form][reqField] = {
                        label: matchedLabelText,
                        formName: _form
                    };

                    if (lastDotIndex !== -1) {
                        var baseField = reqField.substring(lastDotIndex + 1);
                        globals.nav.requiredFieldsLabelMap[_form][baseField] = {
                            label: matchedLabelText,
                            formName: _form
                        };
                    }
                }
            }
        }
    }
}

/**
 * Collects data from child forms within containers
 * @param {String} _form - Form name
 * @param {String} _status - Current form status
 * @param {Array} childContainers - Array of child container info
 * @param {Array} processedForms - Array tracking processed forms
 *
 * @properties={typeid:24,uuid:"FACCB42D-1E0D-4B66-A913-F034C5944A81"}
 */
function collectChildFormsData(_form, _status, childContainers, processedForms) {
    var formObj = forms[_form];

    for (var i = 0; i < childContainers.length; i++) {
        var container = childContainers[i];

        try {
            if (container.type === 'bootstrapcomponents-tabpanel') {
                var selectedIndex = scopes.globals["avUtilities_tabGetSelectedIndex"](formObj, container.name);
                if (selectedIndex >= 0) {
                    var childForm = scopes.globals["avUtilities_tabGetFormName"](formObj, container.name, selectedIndex);
                    if (childForm && forms[childForm]) {
                        // Get the correct program object for this child form
                        /** @type {{required_fields:Object,program_name:String}} */
                        var childProgObj = getFormProgramObject(childForm);

                        // Make sure required fields are loaded - critical for correct operation
                        childProgObj.required_fields = svy_nav_getRequiredFields(childProgObj);

                        // Process this child form using the correct program object
                        collectRequiredFieldsData(childProgObj, childForm, _status, processedForms);
                    }
                }
            } else if (container.type === 'servoyextra-splitpane') {
                // Process right and left forms with the same pattern
                var rightForm = formObj.elements[container.name].getRightForm();
                if (rightForm) {
                    var rightFormName = rightForm.controller.getName();
                    /** @type {{required_fields:Object,program_name:String}} */
                    var rightProgObj = getFormProgramObject(rightFormName);
                    rightProgObj.required_fields = svy_nav_getRequiredFields(rightProgObj);
                    collectRequiredFieldsData(rightProgObj, rightFormName, _status, processedForms);
                }

                var leftForm = formObj.elements[container.name].getLeftForm();
                if (leftForm) {
                    var leftFormName = leftForm.controller.getName();
                    /** @type {{required_fields:Object,program_name:String}} */
                    var leftProgObj = getFormProgramObject(leftFormName);
                    leftProgObj.required_fields = svy_nav_getRequiredFields(leftProgObj);
                    collectRequiredFieldsData(leftProgObj, leftFormName, _status, processedForms);
                }
	        } else if (container.type === 'servoycore-formcontainer') {
	            // Get the contained form
	            var containedForm = formObj.elements[container.name].getContainedForm();
	            if (containedForm) {
	                /** @type {{required_fields:Object,program_name:String}} */
	                var containedProgObj = getFormProgramObject(containedForm);
	
	                // Make sure required fields are loaded
	                containedProgObj.required_fields = svy_nav_getRequiredFields(containedProgObj);
	
	                // Validate contained form's fields
	                var containedResult = validateFormRequiredFields(containedProgObj, containedForm);
	                if (containedResult === -1) return -1;
	
	                // Check if contained form has child forms
	                containedResult = validateChildForms(containedForm);
	                if (containedResult === -1) return -1;
	            }
	        }
        } catch (e) {
            application.output('Error collecting data from container ' + container.name + ': ' + e);
        }
    }
}

/**
 * Checks if all required fields on a form have a value
 * @param {{required_fields:Object,program_name:String}} _progObj the object of the current program
 * @param {String} _form the name of the form
 * @return {Number} 1 if all required fields have a value, -1 if there are empty required fields
 *
 * @properties={typeid:24,uuid:"F93C7CB8-A5D7-4FF3-8DBA-E71030FE3BBC"}
 */
function svy_nav_checkRequiredFields(_progObj, _form) {
    // Skip validation if we're in required_fields mode
    if (globals.nav.mode == "required_fields") {
        return null;
    }

    // Always create a new empty array for failed field labels
    globals.nav.failedFieldLabels = [];

    // Make sure required fields are loaded
    _progObj.required_fields = svy_nav_getRequiredFields(_progObj);

    // First validate the current form
    var result = validateFormRequiredFields(_progObj, _form);

    // Continue validating child forms, even if current form has issues
    var childResult = validateChildForms(_form);

    // Remove duplicate entries in failedFieldLabels
    if (globals.nav.failedFieldLabels && globals.nav.failedFieldLabels.length > 0) {
        globals.nav.failedFieldLabels = globals.nav.failedFieldLabels.filter(function(item, pos) {
            return globals.nav.failedFieldLabels.indexOf(item) === pos;
        });
    }

    // If either validation failed, return -1
    return (result === -1 || childResult === -1) ? -1 : 1;
}

/**
 * Validates required fields for a specific form
 * @param {{required_fields:Object}} _progObj - Program object with required fields info
 * @param {String} _form - Form name
 * @return {Number} 1 if all valid, -1 if validation failed
 *
 * @properties={typeid:24,uuid:"H5B3B16-749B-4A3D-BDD0-A5978DCECC04"}
 */
function validateFormRequiredFields(_progObj, _form) {
    if (!forms[_form]) return 1;

    // Debug output for form validation
//    application.output('DEBUG: Validating form: ' + _form + ' with program: ' + _progObj.program_name);

    var firstInvalidField = null;
    var foundInvalidField = false;

    // Initialize array to collect failed field labels if it doesn't exist
    if (!globals.nav.failedFieldLabels) {
        globals.nav.failedFieldLabels = [];
    }

    // Track processed fields to avoid duplicates
    var processedFields = {};

    // Check each data provider in the required fields object
    for (var dataProvider in _progObj.required_fields) {
        var requiredField = _progObj.required_fields[dataProvider];

        // Debug output for City and Contact fields
        if (dataProvider.indexOf('addr_city') !== -1 || dataProvider.indexOf('custcontact_id') !== -1) {
            application.output('DEBUG: Checking required field: ' + dataProvider);
            application.output('DEBUG: Required field info: ' + JSON.stringify(requiredField));
        }

        // Only validate fields that are required and actually on this form
        if (requiredField.form_status === 'R' && requiredField.on_form === true) {
            // Try to get the value
            var value = null;
            var bError = false;
            var formToCheck = _form;

            // Check if this field has a specific form in the label map
            var baseFieldName = dataProvider;
            var lastDot = dataProvider.lastIndexOf('.');
            if (lastDot !== -1) {
                baseFieldName = dataProvider.substring(lastDot + 1);
            }

            // Check if we have form info for this field
            var fieldInfo = null;
            if (globals.nav.requiredFieldsLabelMap && globals.nav.requiredFieldsLabelMap[_form] && globals.nav.requiredFieldsLabelMap[_form][dataProvider]) {
                fieldInfo = globals.nav.requiredFieldsLabelMap[_form][dataProvider];
            } else if (globals.nav.requiredFieldsLabelMap && globals.nav.requiredFieldsLabelMap[_form] && globals.nav.requiredFieldsLabelMap[_form][baseFieldName]) {
                fieldInfo = globals.nav.requiredFieldsLabelMap[_form][baseFieldName];
            }

            // If we have form info, use that form instead
            if (fieldInfo && fieldInfo.formName) {
                formToCheck = fieldInfo.formName;
                if (dataProvider.indexOf('addr_city') !== -1 || dataProvider.indexOf('custcontact_id') !== -1) {
                    application.output('DEBUG: Using form from label map: ' + formToCheck);
                }
            }

            // If the field is NOT on this form then skip it
            if (!globals.nav.requiredFieldsLabelMap[formToCheck]
            	|| !globals.nav.requiredFieldsLabelMap[formToCheck][dataProvider]) {
            	continue;
            }
            try {
                // First try to get the value directly from the dataprovider
                if (typeof forms[formToCheck][dataProvider] !== 'undefined') {
                    value = forms[formToCheck][dataProvider];
                } else {
                    // Otherwise, try to find a form element with this dataprovider
                    value = findElementValue(formToCheck, dataProvider);
                }

                // Debug output for City and Contact fields
                if (dataProvider.indexOf('addr_city') !== -1 || dataProvider.indexOf('custcontact_id') !== -1) {
                    application.output('DEBUG: Value found in form ' + formToCheck + ': ' + value);
                }
            } catch (e) {
                application.output('Error getting value for ' + dataProvider + ' in form ' + formToCheck + ': ' + e);
                bError = true;
            }
            if (bError) continue;

            // Check if value is empty/null/undefined/empty string
            if (value === null || value === undefined || value === '') {
                foundInvalidField = true;

                // Remember the first invalid field
                if (!firstInvalidField) {
                    // Use the correct form when finding the element
                    firstInvalidField = findElementByDataProvider(formToCheck, dataProvider);

                    // Debug output for City and Contact fields
                    if (baseFieldName === 'addr_city' || baseFieldName === 'custcontact_id' ||
                        dataProvider.indexOf('addr_city') !== -1 || dataProvider.indexOf('custcontact_id') !== -1) {
                        application.output('DEBUG: First invalid field: ' + firstInvalidField + ' in form: ' + formToCheck);
                    }
                }

                // baseFieldName was already extracted earlier in the function

                // We already have fieldInfo from earlier in the function
                var fieldLabel = null;

                // Get the field label
                if (fieldInfo) {
                    fieldLabel = fieldInfo.label;
                } else {
                    // If still not found, fall back to the original method
                    fieldLabel = getFieldLabelByDataProvider(formToCheck, dataProvider);
                }

                // Debug output for City and Contact fields
                if (baseFieldName === 'addr_city' || baseFieldName === 'custcontact_id' ||
                    dataProvider.indexOf('addr_city') !== -1 || dataProvider.indexOf('custcontact_id') !== -1) {
                    application.output('DEBUG: Field validation failed for: ' + dataProvider);
                    application.output('DEBUG: Field label: ' + fieldLabel);
                }

                // Always use the field label if we have one
                if (!processedFields[baseFieldName]) {
                    // Our getFieldLabelByDataProvider function now always returns a value
                    globals.nav.failedFieldLabels.push(fieldLabel);
                    processedFields[baseFieldName] = true;
                }
            }
        }
    }

    // If we found an invalid field, show a message and set focus to it
    if (foundInvalidField && firstInvalidField) {
        // Focus the first invalid field
        forms[_form].controller.focusField(firstInvalidField, true);
        return -1;
    }

    return 1;
}

/**
 * Helper function to find the program object for a form
 * @param {String} formName - The form name to find a program for
 * @return {Object} Program object or null if not found
 *
 * @properties={typeid:24,uuid:"D726FE8B-90C3-4E17-AA94-17CD538A9E0D"}
 */
function findProgramObjectForForm(formName) {
    // Try to find in globals any object that has a required_fields property
    // and check if this form is mentioned in any of the required fields
    for (var key in globals) {
        if (globals[key] && typeof globals[key] === 'object' && globals[key].required_fields) {
            for (var field in globals[key].required_fields) {
                var reqField = globals[key].required_fields[field];
                if (reqField.form === formName) {
                    return globals[key];
                }
            }
        }
    }
    return null;
}

/**
 * Finds the program object for a specific form
 * @param {String} formName - Form name
 * @return {Object} Program object for this form
 *
 * @properties={typeid:24,uuid:"A5D21F93-7EBC-48D6-B0E7-C9F835D27A06"}
 */
function getFormProgramObject(formName) {
    // If we already have a mapping from form to program, use it
    if (globals.nav.formToProgramMap && globals.nav.formToProgramMap[formName]) {
        var mappedProgram = globals.nav.formToProgramMap[formName];
        return globals.nav.program[mappedProgram];
    }

    // Try to determine program from form name (various patterns)
    // Pattern 1: program_formname
    var underscoreIndex = formName.indexOf('_');
    if (underscoreIndex > 0) {
        var possibleProgram = formName.substring(0, underscoreIndex);
        if (globals.nav.program[possibleProgram]) {
            return globals.nav.program[possibleProgram];
        }
    }

    // Pattern 2: form is directly in a program's form array
    for (var progName in globals.nav.program) {
        var program = globals.nav.program[progName];
        if (program.form) {
            for (var i = 0; i < program.form.length; i++) {
                if (program.form[i] && program.form[i][2] === formName) {
                    return program;
                }
            }
        }
    }

    // Default to current program as fallback
    return globals.nav.program[globals.nav_program_name];
}

/**
 * Finds and validates a form element with the given dataprovider
 * @param {String} formName - Form name
 * @param {String} dataProvider - Data provider name
 * @return {Boolean} true if valid, false if invalid
 *
 * @properties={typeid:24,uuid:"6BED7C4A-F89D-4782-A513-E20DF3458C19"}
 */
function findAndValidateElement(formName, dataProvider) {
    // Get all elements in the form
	/**@type {Array} **/
    var aElements = forms[formName].elements;
    for (var elementName in aElements) {
    	/** @type {RuntimeTextField} */
        var element = aElements[elementName];

        // Check if this element has a dataProvider property
        if (element && element.getDataProviderID() && element.getDataProviderID() === dataProvider) {
        	var sDataProvider = element.getDataProviderID();
            var value = element[sDataProvider];
            // Return false if the value is empty
            if (value === null || value === undefined || value === '') {
                return false;
            }
            return true;
        }
    }
    return true; // If we can't find the element, assume it's valid
}

/**
 * Find element by data provider on a form
 * @param {String} formName - Form name
 * @param {String} dataProvider - Data provider name
 * @return {String} Element name or null
 *
 * @private
 * @properties={typeid:24,uuid:"729B3D51-F80A-42E6-9C7D-183B5F92A0D4"}
 */
function findElementByDataProvider(formName, dataProvider) {
    var form = forms[formName];
    if (!form) return null;

    // Extract base field name if this is a relation field
    var baseFieldName = dataProvider;
    var lastDot = dataProvider.lastIndexOf('.');
    if (lastDot !== -1) {
        baseFieldName = dataProvider.substring(lastDot + 1);
    }

    // Try to find an element with matching dataProvider
    for (var elementName in form.elements) {
        var element = form.elements[elementName];

        try {
            // Skip non-data elements
            if (!element.getDataProviderID) continue;

            var elementDataProvider = element.getDataProviderID();

            // Check for direct match or base field name match
            if (elementDataProvider === dataProvider || elementDataProvider === baseFieldName) {
                return elementName;
            }
        } catch (e) {
            // Element might not support these operations
            continue;
        }
    }

    return null;
}

/**
 * Find and get the value of an element by its data provider
 * @param {String} formName - Form name
 * @param {String} dataProvider - Data provider name
 * @return {Object} Element value or null
 *
 * @private
 * @properties={typeid:24,uuid:"38A9F462-D103-4C3B-A7E5-F2B8D91C60E4"}
 */
function findElementValue(formName, dataProvider) {
    var form = forms[formName];
    if (!form) return null;

    // First try to get the value directly from the form's foundset
    try {
        // For relation fields (containing dots), try to traverse the relation path
        if (dataProvider.indexOf('.') !== -1) {
            var record = form.foundset.getSelectedRecord();
            if (record) {
                // Try to access the value through the record
                var value = record[dataProvider];
                if (value !== undefined) {
                    return value;
                }

                // If direct access failed, try to navigate through the relation chain
                var parts = dataProvider.split('.');
                var currentObj = record;

                for (var i = 0; i < parts.length; i++) {
                    if (currentObj && currentObj[parts[i]] !== undefined) {
                        currentObj = currentObj[parts[i]];
                    } else {
                        currentObj = null;
                        break;
                    }
                }

                if (currentObj !== null) {
                    return currentObj;
                }
            }
        }
    } catch (e) {
        // Silently continue if this approach fails
    }

    // Extract base field name if this is a relation field
    var baseFieldName = dataProvider;
    var lastDot = dataProvider.lastIndexOf('.');
    if (lastDot !== -1) {
        baseFieldName = dataProvider.substring(lastDot + 1);
    }

    // Try to find an element with matching dataProvider
    for (var elementName in form.elements) {
    	/**@type {RuntimeTextField} */
        var element = form.elements[elementName];

        try {
            // Skip non-data elements
            if (!element.getDataProviderID) continue;

            var elementDataProvider = element.getDataProviderID();

            // Check for direct match or base field name match
            if (elementDataProvider === dataProvider || elementDataProvider === baseFieldName) {
                return element[elementDataProvider];
            }
        } catch (e) {
            // Element might not support these operations
            continue;
        }
    }

    return null;
}

/**
 * Gets the label text for a field by its data provider
 * @param {String} formName - Form name
 * @param {String} dataProvider - Data provider name
 * @return {String} Label text or null if not found
 *
 * @private
 * @properties={typeid:24,uuid:"A7F3B16-749B-4A3D-BDD0-A5978DCECC05"}
 */
function getFieldLabelByDataProvider(formName, dataProvider) {
    var form = forms[formName];
    
    
    if (!form) return formatFieldName(dataProvider);

    // Extract base field name if this is a relation field
    var baseFieldName = dataProvider;
    var lastDot = dataProvider.lastIndexOf('.');
    if (lastDot !== -1) {
        baseFieldName = dataProvider.substring(lastDot + 1);
    }

    // Find the element with this data provider
    var elementName = findElementByDataProvider(formName, dataProvider);
    if (!elementName) return formatFieldName(baseFieldName);

    // Look for a label that points to this element
    for (var labelName in form.elements) {
        var element = form.elements[labelName];

        // Check if this is a label element with text
        if (element && element.text !== undefined) {
            try {
                // Check if this label points to our element
                if (element.labelFor === elementName) {
                    // Return the label text without asterisks
                    return element.text.replace(/^\*+\s*/, "").trim();
                }
            } catch (e) {
                // Element might not have labelFor property
                continue;
            }
        }
    }

    // If no label found, format the field name to be more readable
    return formatFieldName(baseFieldName);
    
    function formatFieldName(fieldName) {
        // Remove common prefixes like "addr_", "cust_", etc.
        var cleanName = fieldName.replace(/^(addr_|cust_|sa_|sys_)/, '');

        // Replace underscores with spaces
        cleanName = cleanName.replace(/_/g, ' ');

        // Capitalize first letter of each word
        cleanName = cleanName.replace(/\b\w/g, function(l) { return l.toUpperCase(); });

        return cleanName;
    }
}

/**
 * Validates required fields in child forms
 * @param {String} _form - Parent form name
 * @return {Number} 1 if all valid, -1 if validation failed
 *
 * @properties={typeid:24,uuid:"C9C48F72-0F93-4823-AB8D-7A9E3FD2D5BA"}
 */
function validateChildForms(_form) {
    if (!forms[_form]) return 1;

    var result = 1;

    // Look for tab panels and split panes in the form
    var aElements = forms[_form].elements;
    for (var elementName in aElements) {
        var element = aElements[elementName];

        try {
            // Get the element type name
            var elementType = "";
            if (element.getElementType) {
                elementType = element.getElementType();
            } else if (element.getName) {
                // Alternative way to get type information
                elementType = element.getName();
            }

            // Check if this is a bootstrapcomponents-tabpanel
            if (elementType.indexOf('tabpanel') !== -1 || elementType === 'bootstrapcomponents-tabpanel') {
                var selectedIndex = scopes.globals["avUtilities_tabGetSelectedIndex"](_form, elementName);
                if (selectedIndex >= 0) {
                    var childForm = scopes.globals["avUtilities_tabGetFormName"](_form, elementName, selectedIndex);
                    if (childForm && forms[childForm]) {
                        // Get the correct program object for this child form
                    	/** @type {{required_fields:Object,program_name:String}} */
                        var childProgObj = getFormProgramObject(childForm);

                        // Make sure required fields are loaded
                        childProgObj.required_fields = svy_nav_getRequiredFields(childProgObj);

                        // First validate this child form's fields
                        var childResult = validateFormRequiredFields(childProgObj, childForm);
                        if (childResult === -1) return -1;

                        // Then check if this child form has its own child forms (grandchildren)
                        childResult = validateChildForms(childForm);
                        if (childResult === -1) return -1;
                    }
                }
            }
            // Similarly update the splitpane section...
            else if (elementType.indexOf('splitpane') !== -1 || elementType === 'servoyextra-splitpane') {
                // Check right form
                var rightForm = element.getRightForm();
                if (rightForm) {
                    var rightFormName = rightForm.controller.getName();
                    /** @type {{required_fields:Object,program_name:String}} */
                    var rightProgObj = getFormProgramObject(rightFormName);

                    // Make sure required fields are loaded
                    rightProgObj.required_fields = svy_nav_getRequiredFields(rightProgObj);

                    // Validate right form's fields
                    var rightResult = validateFormRequiredFields(rightProgObj, rightFormName);
                    if (rightResult === -1) return -1;

                    // Check if right form has child forms
                    rightResult = validateChildForms(rightFormName);
                    if (rightResult === -1) return -1;
                }


                // Check left form - similar update needed here
                var leftForm = element.getLeftForm();
                if (leftForm) {
                    var leftFormName = leftForm.controller.getName();
                    /** @type {{required_fields:Object,program_name:String}} */
                    var leftProgObj = getFormProgramObject(leftFormName);

                    // Make sure required fields are loaded
                    leftProgObj.required_fields = svy_nav_getRequiredFields(leftProgObj);

                    // Validate left form's fields
                    var leftResult = validateFormRequiredFields(leftProgObj, leftFormName);
                    if (leftResult === -1) return -1;

                    // Check if left form has child forms
                    leftResult = validateChildForms(leftFormName);
                    if (leftResult === -1) return -1;
                }

            }
            else if (elementType.indexOf('formcontainer') !== -1 || elementType === 'servoycore-formcontainer') {
                // Get the contained form
                var containedForm = element.getContainedForm();
                if (containedForm) {
                    /** @type {{required_fields:Object,program_name:String}} */
                    var containedProgObj = getFormProgramObject(containedForm);

                    // Make sure required fields are loaded
                    containedProgObj.required_fields = svy_nav_getRequiredFields(containedProgObj);

                    // Validate contained form's fields
                    var containedResult = validateFormRequiredFields(containedProgObj, containedForm);
                    if (containedResult === -1) return -1;

                    // Check if contained form has child forms
                    containedResult = validateChildForms(containedForm);
                    if (containedResult === -1) return -1;
                }
            }
        } catch (e) {
            application.output('Error validating child form for element ' + elementName + ': ' + e);
        }
    }

    return result;
}


/**
 * To start a transaction
 * <AUTHOR> Aleman
 * @since 2011-02-11
 * @properties={typeid:24,uuid:"BE637AA7-B523-43E2-8D56-6E30BE0981EA"}
 */
function svy_nav_startTransaction() {
	databaseManager.startTransaction()
}

/**
 * To save the open edits and commit the transaction, works best in combination with svy_nav_startTransaction
 * <AUTHOR> Aleman
 * @since 2011-02-11
 * @return
 * @properties={typeid:24,uuid:"468E84CB-FCE9-42E8-A5B8-B7A5B6239667"}
 */
function svy_nav_saveCommitTransaction() {
	var _record, _failedArray
	if (!databaseManager.saveData()) {
		_failedArray = databaseManager.getFailedRecords()
		for (var i = 0; i < _failedArray.length; i++) {
			_record = _failedArray[i];
		}
		application.output('Error in save:' + _record.exception, LOGGINGLEVEL.ERROR)
		databaseManager.revertEditedRecords()
		databaseManager.rollbackTransaction(true);
		databaseManager.rollbackTransaction()
		return false;
	}

	if (!databaseManager.commitTransaction()) {
		_failedArray = databaseManager.getFailedRecords()
		for (i = 0; i < _failedArray.length; i++) {
			_record = _failedArray[i];
		}
		databaseManager.saveData()
		application.output('Error in commit:' + _record.exception, LOGGINGLEVEL.ERROR)
		databaseManager.revertEditedRecords()
		databaseManager.rollbackTransaction(true);
		databaseManager.rollbackTransaction()
		databaseManager.releaseAllLocks()
		return false;

	}
	databaseManager.setAutoSave(true)
	return true

}

/**
 * To lose the focus, this method will set the focus on a small element that the user will not see
 * <AUTHOR> Aleman
 * @since 2011-02-14
 * @properties={typeid:24,uuid:"E3830529-9AC3-4ABD-848B-A6BC843FBEFD"}
 */
function svy_nav_loseFocus() {
// 	forms.svy_nav_fr_status_bar.elements.loseFocus.requestFocus()
}

/**
 *  Check the validation rules for all the fields
 * @param {{program_name:String,table_name:String}} _progObj the object of the current program
 * @param {String} _form the name of the form
 * <AUTHOR> Aleman
 * @since 2011-02-14
 * @return
 * @properties={typeid:24,uuid:"875274EA-22AC-40A4-8EB1-84789B40A2F9"}
 */
function svy_nav_checkValidationRules(_progObj,_form) {

	//look what the validation rules are
	/** @type {JSFoundSet<db:/svy_framework/nav_field_validation_rule>} */
	var _fs_rules = databaseManager.getFoundSet(globals.nav_db_framework,'nav_field_validation_rule')
	_fs_rules.addFoundSetFilterParam('program_name','=',_progObj.program_name)
	_fs_rules.sort('dataprovider asc, sequence asc', true)
	_fs_rules.loadRecords()

	var _fs_rules_size = _fs_rules.getSize()
	var _fs_rec, _method, _dataprovider, _newValue, _record,  _return; //_label,

	var _table = _progObj.table_name
	_record = forms[_form].foundset.getSelectedRecord()
	var _success = 0

	for (var i = 1; i <= _fs_rules_size; i++) {
		_fs_rec = _fs_rules.getRecord(i)
		_dataprovider = _fs_rec.dataprovider
		_newValue = forms[_form][_dataprovider]

		if( utils.stringPatternCount(_fs_rec.method,'globals.')>0)
		{
			_method = utils.stringReplace(_fs_rec.method,'globals.','')
			_return = globals[_method](_table,_dataprovider, _newValue, _record)
		}
		else if( utils.stringPatternCount(_fs_rec.method,'forms.')>0)
		{
			_method = _fs_rec.method.split('.', 5)
			_return = forms[_method[1]][_method[2]](_table,_dataprovider, _newValue, _record)
		}

		if(_return == -1)
		{
			_success = -1
		}
	}
	return _success
}

/**
 * to call a function from a popmenu you don't need the first 5 arguments
 * @param {Object} _arg1
 * @param {Object} _arg2
 * @param {Object} _arg3
 * @param {Object} _arg4
 * @param {Object} _arg5
 * @param {Number} _function_id
 *
 * @properties={typeid:24,uuid:"4F35026E-E49D-472C-A110-C1B4AD5E70F9"}
 */
function svy_nav_callFunctionFromPopmenu(_arg1, _arg2, _arg3, _arg4, _arg5, _function_id) {
	 svy_nav_callFunction(_function_id)
}

/**
* to call a function from a popmenu you don't need the first 5 arguments
* @param {Object} _arg1
* @param {Object} _arg2
* @param {Object} _arg3
* @param {Object} _arg4
* @param {Object} _arg5
* @param {JSEvent} [_event]
* @param {Number} _position
*
 * @properties={typeid:24,uuid:"950C39C5-6CF1-45CE-A301-6DEF4DEBFE79"}
 */
function svy_nav_history_moveFromPopmenu(_arg1, _arg2, _arg3, _arg4, _arg5, _event, _position) {
	globals.svy_nav_history_move(_event, _position)
}

/**
 * @param {Number} _function_id
 * @properties={typeid:24,uuid:"CCD4815D-3A52-4BCA-A159-9AB30D3D241E"}
 */
function svy_nav_callFunction(_function_id) {

	/** @type {JSFoundSet<db:/svy_framework/nav_function>} */
	var _fs_function = databaseManager.getFoundSet(globals.nav_db_framework,'nav_function')
	_fs_function.addFoundSetFilterParam('function_id','=',_function_id)
	_fs_function.loadAllRecords()

	if(_fs_function.getSize() != 1) return

	var _rec = _fs_function.getRecord(1)

	var _methodcall = _rec.method + '('
	var _rec_arg
	for (var i = 1; i <= _rec.nav_function_to_nav_function_arguments.getSize(); i++) {
		_rec_arg = _rec.nav_function_to_nav_function_arguments.getRecord(i)
		if (i != 1) {
			_methodcall += ', '
		}

		if (_rec_arg.arg_type == 1) {
			_methodcall += '"' + _rec_arg.argument + '"'
		} else if (_rec_arg.arg_type == 2) {
			_methodcall += '"' + globals[_rec_arg.argument] + '"'
		}
	}
	_methodcall += ')'

	eval(_methodcall)
}

/**
 * @properties={typeid:24,uuid:"04F454D0-EAF0-4A08-AEE4-AE5A1E6F73EC"}
 */
function svy_nav_setTemplateButtons() {

	var _b_y = 4
	var _b_size = 30
	var _b_w = 24
	var _b_h = 24
	var _b_x = 592
	var _b_offset_arrow = 8
	var _b_y_arrow = 27
	var _b_arrow_h = 6
	var _b_arrow_w = 9
	var _b_offset_line = 7

	var _v_y = 4
	var _v_size = 30
	var _v_w = 18
	var _v_h = 18
	var _v_x = 655
	var _v_offset_line = 7

	var _bForm = solutionModel.getForm('svy_nav_fr_buttonbar_browser')
	var _vForm = solutionModel.getForm('svy_nav_fr_buttonbar_viewer')

	globals.nav_properties = 'template_images'
	/** @type{String} */
	var _template_images = _to_nav_properties$property_name.property_value
	globals.nav_properties = 'template_i18n_labels'
	var _template_tooltips = _to_nav_properties$property_name.property_value
	var _method = solutionModel.getGlobalMethod("globals",'svy_nav_toggleView')
	globals.nav_properties = 'template_types'
	var _template_types = _to_nav_properties$property_name.property_value


	//calculate_start
	_b_x = _b_x - ((_template_images.length)*_b_size)
	_v_x = _v_x - ((_template_images.length)*_v_size)

	//set the line on the right location
	var _lineElement = _bForm.getComponent('btn_begin_views')
	_lineElement.x = _b_x - _b_offset_line

	_lineElement = _vForm.getComponent('btn_begin_views')
	_lineElement.x = _v_x - _v_offset_line


	globals.nav_properties = 'create_table_by_user'
	var _create_table = (_to_nav_properties$property_name.property_value[0] == 'true')

	for (var i = 0; i < _template_images.length; i++) {

		var _bLabel = _bForm.newLabel('', _b_x, _b_y, _b_w, _b_h, _method)
		if(!scopes.svySystem.isIOSPlatform()) _bLabel.toolTipText = i18n.getI18NMessage(_template_tooltips[i])
		_bLabel.name = 'btn_template'+i
	//	_bLabel.imageMedia = solutionModel.getMedia(_template_images[i])
		_bLabel.styleClass = utils.stringReplace(utils.stringReplace(_template_images[i],'bar','btn'),'.png','')
		_bLabel.anchors = SM_ANCHOR.NORTH | SM_ANCHOR.EAST;
		_bLabel.formIndex = 999999
		_bLabel.transparent = true
		_bLabel.showClick = false
		//set 1pixel images for webclient to
		if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {
			_bLabel.imageMedia = solutionModel.getMedia('1pixel_trans.png')
		}

		//set right click for table views
		if(forms[_template_types[i]].hasTable() && _create_table)
		{
			_bLabel.onRightClick = _bForm.getMethod('listMenu')

		}

		var _arrowLabel = _bForm.newLabel('', _b_x + _b_offset_arrow, _b_y_arrow, _b_arrow_w, _b_arrow_h)
		_arrowLabel.styleClass = 'btn_selected_template'
		_arrowLabel.name = 'template_selected'+i
		_arrowLabel.anchors = SM_ANCHOR.NORTH | SM_ANCHOR.EAST;
		_arrowLabel.formIndex = 999999
		_arrowLabel.transparent = true
		_arrowLabel.transparent = true
		_arrowLabel.showClick = false

		_b_x += _b_size

		var _vLabel = _vForm.newLabel('', _v_x, _v_y, _v_w, _v_h, _method)
		if(!scopes.svySystem.isIOSPlatform()) _vLabel.toolTipText = _template_tooltips[i]
		_vLabel.name = 'btn_template'+i
		//_vLabel.imageMedia = solutionModel.getMedia(_template_images[i])
		_vLabel.styleClass = utils.stringReplace(utils.stringReplace(_template_images[i],'bar','btn'),'.png','_viewer')
		_vLabel.anchors = SM_ANCHOR.NORTH | SM_ANCHOR.EAST;
		_vLabel.formIndex = 999999
		_vLabel.transparent = true
		_vLabel.transparent = true
		_vLabel.showClick = false
		//set 1pixel images for webclient to
		if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {
			_vLabel.imageMedia = solutionModel.getMedia('1pixel_trans.png')
		}

		_v_x += _v_size
	}
}

/**
 * Retrieves the elements of the form that should be shown at generated table views
 *
 * @param {String} _form the name of the form
 *
 * <AUTHOR> Schuurhof
 * @since 2011-07-07
 *
 * @return
 * @properties={typeid:24,uuid:"475A4C3A-047D-495E-A71E-8689E54E4364"}
 */
function svy_nav_getFixedElementsTable(_form) {
	if (forms[_form].vFixedElementsTable.length) {
		// first revert the form to get its design-time fixed elements
		solutionModel.revertForm(_form);
		forms[_form].controller.recreateUI();
	} else {
		return null;
	}

	var _jsForm = solutionModel.getForm(_form)
	var _components = _jsForm.getComponents()

	/** @type {Array} */
	var _fixedElementsTable = forms[_form].vFixedElementsTable;
	var _fixedElements = new Array();
	var _fixedElementsCounter = 0;
	for (var i = 0; i < _components.length; i++) {
		for (var j = 0; j < _fixedElementsTable.length; j++) {
			if (_components[i].name == _fixedElementsTable[j][0]) {
				_fixedElements[_fixedElementsCounter] = new Array();
				// define the record type element
				_fixedElements[_fixedElementsCounter][0] = _components[i];
				for (var k = 0; k < _components.length; k++) {
					if (_components[k]["labelFor"] == _fixedElementsTable[j][0]) {
						// define the header element
						_fixedElements[_fixedElementsCounter][1] = _components[k];
						break;
					}
				}

				// define whether the element should be fixed at the front or the back
				_fixedElements[_fixedElementsCounter][2] = _fixedElementsTable[j][1];

				_fixedElementsCounter++;
			}
		}
	}

	return _fixedElements;
}

/**
 * @properties={typeid:24,uuid:"8EFC33FF-C133-4E0C-B78D-8B31B2B49981"}
 */
function svy_nav_setGlobals( ) {
	globals.sURL = scopes["avUtils"].getServerURL();

	globals.avSchedule_scripts = '<script src="' + sURL + '/avanti_resources/dhtml/codebase/dhtmlxscheduler.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_all_timed.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_limit.js"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_collision.js"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_tooltip.js"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_readonly.js"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_units.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_timeline.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_minical.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_serialize.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/codebase/ext/dhtmlxscheduler_active_links.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/samples/common/dhtmlxCommon/dhtmlxcommon.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/samples/common/dhtmlxMenu/dhtmlxmenu.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/samples/common/dhtmlxMenu/ext/dhtmlxmenu_ext.js" type="text/javascript" charset="utf-8"></script> '
		+ '<script src="' + sURL + '/avanti_resources/dhtml/sch_agenda_dtl.js" type="text/javascript" charset="utf-8"></script> ';

	globals.sProgressBarScript = '';
}
/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"04610D22-6DBD-485D-B998-52F57DAE09DA"}
 */
function getDataSetForValueListSecurityKeys(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	/** @type QBSelect<db:/svy_framework/sec_security_key> */
	var query = databaseManager.createSelect('db:/svy_framework/sec_security_key');
	/** @type  {JSDataSet} */
	var result = null;
	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		query.result.add(query.columns.name).add(query.columns.security_key_id);
		query.sort.add(query.columns.name.asc)
		result = databaseManager.getDataSetByQuery(query, 5000);
	} else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [displayValue + "%", displayValue + "%"];
		query.result.add(query.columns.name).add(query.columns.security_key_id).root.where.add(query.or.add(query.columns.name.lower.like(args[0] + '%')));
		result = databaseManager.getDataSetByQuery(query, 5000);
	} else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		args = [realValue];
		query.result.add(query.columns.name).add(query.columns.security_key_id).root.where.add(query.columns.security_key_id.eq(args[0]));
		result = databaseManager.getDataSetByQuery(query, 1);
	}
	return result;

}

/**
 * This function returns a different column name for a calculated column that cant otherwise be exported.
 * EG. Return progress bar pct rather than progress bar html for progress bar columns
 * This can used with export/quickSearch/columnSort functionality.
 *
 * @public
 *
 * @param sColName
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"22D42C21-4364-488E-9230-0220415DF9D1"}
 */
function translateExportColumn(sColName) {
	var sNewColumnName = sColName;

	// replace clc_progress_qty_html with get_prod_qty_done_pct to just return progress bar pct rather than progress bar html
	if (sColName.includes("clc_progress_qty_html")) {
		sNewColumnName = sColName.replace("clc_progress_qty_html", "get_prod_qty_done_pct");
	}
	// replace clc_progress_qty_html with get_prod_qty_done_pct to just return progress bar pct rather than progress bar html
	else if (sColName.includes("clc_progress_html")) {
		sNewColumnName = sColName.replace("clc_progress_html", "clc_job_progress");
	}

	return sNewColumnName;
}

/**
 *	Makes a array from a object
 *
 * <AUTHOR> Bakker
 * @since 2006-11-05
 * @param {Object} ObjectArray
 * @return  {Array} JSArray
 *
 * @properties={typeid:24,uuid:"ff264069-247f-488d-81d4-1b39487018e4"}
 */
function svy_utl_objectArray2JSArray(ObjectArray) {
	if (!ObjectArray) return null;

	var JSArray = new Array(),
		count = 0,
		i = 0;

	for (i in ObjectArray) {
		JSArray[count] = i;
		count += 1;
	}

	return JSArray;
}

/**
 *	 Selects a record
 *
 * <AUTHOR>
 * @since Unknown
 *
 * @param {String} vWhichRecord (next|prev|first|last)
 * @param {String} [vFormName] name of the form
 * @param {Boolean} [bJustReturnRecord] - if this is true then we just return the next/etc record, we dont select it in the form foundset
 *
 * @return {JSRecord} - returns record found if bJustReturnRecord is true, otherwise returns null
 *
 * @properties={typeid:24,uuid:"c05b3cc9-8b27-4ffb-b749-271eba39002f"}
 */
function svy_utl_setSelectedIndex(vWhichRecord, vFormName, bJustReturnRecord) {
	if (vWhichRecord != undefined) {

		// get current record index
		var vNewIndex = forms[vFormName].controller.getSelectedIndex();

		// get new index of selected record
		switch (vWhichRecord) {
		case 'next':
			vNewIndex = vNewIndex + 1;
			break;

		case 'prev':
			vNewIndex = vNewIndex - 1;
			break;

		case 'first':
			vNewIndex = 1;
			break;

		case 'last':
			vNewIndex = databaseManager.getFoundSetCount(forms[vFormName].foundset)

			break;
		}

		if (bJustReturnRecord) {
			return forms[vFormName].foundset.getRecord(vNewIndex)
		}
		else {
			// go to record
			forms[vFormName].foundset.getRecord(vNewIndex);
			forms[vFormName].controller.setSelectedIndex(vNewIndex);
		}
	} else {
		application.output('[scCore_setSelectedRecord] invalid index choice:' + vWhichRecord, LOGGINGLEVEL.ERROR);
	}

	return null;
}

/**
 *	 Goes to first record in foundset on a form
 *
 * <AUTHOR>
 * @since Unknown
 *
 * @param {String} [vFormName] name of the form
 * @param {Boolean} [bJustReturnRecord] - if this is true then we just return the first record, we dont select it in the form foundset
 *
 * @return {JSRecord} - returns record found if bJustReturnRecord is true, otherwise returns null
 *
 * @properties={typeid:24,uuid:"11e1cd63-66d9-44aa-bd17-7851ff41aeb0"}
 */
function svy_utl_setSelectedIndexFirst(vFormName, bJustReturnRecord) {
	return globals.svy_utl_setSelectedIndex("first", vFormName, bJustReturnRecord);
}

/**
 *	 Goes to last record in foundset on a form, the actual last record, not 200
 *
 * <AUTHOR>
 * @since Unknown
 *
 * @param {String} [vFormName] name of the form
 * @param {Boolean} [bJustReturnRecord] - if this is true then we just return the last record, we dont select it in the form foundset
 *
 * @return {JSRecord} - returns record found if bJustReturnRecord is true, otherwise returns null
 *
 * @properties={typeid:24,uuid:"363ff333-c067-495e-9816-4d2852ca2774"}
 */
function svy_utl_setSelectedIndexLast(vFormName, bJustReturnRecord) {

	return globals.svy_utl_setSelectedIndex("last", vFormName, bJustReturnRecord);
}

/**
 *	 Goes to next record in foundset on a form
 *
 * <AUTHOR>
 * @since Unknown
 *
 * @param {String} [vFormName] name of the form
 * @param {Boolean} [bJustReturnRecord] - if this is true then we just return the next record, we dont select it in the form foundset
 *
 * @return {JSRecord} - returns record found if bJustReturnRecord is true, otherwise returns null
 *
 * @properties={typeid:24,uuid:"3b7e8788-65af-4de2-ad52-4ede18dd5ce2"}
 */
function svy_utl_setSelectedIndexNext(vFormName, bJustReturnRecord) {
	return globals.svy_utl_setSelectedIndex("next", vFormName, bJustReturnRecord);
}

/**
 *	 Goes to previous record in foundset on a form
 *
 * <AUTHOR>
 * @since Unknown
 *
 * @param {String} [vFormName] name of the form
 * @param {Boolean} [bJustReturnRecord] - if this is true then we just return the previous record, we dont select it in the form foundset
 *
 * @return {JSRecord} - returns record found if bJustReturnRecord is true, otherwise returns null
 *
 * @properties={typeid:24,uuid:"321b31ab-c897-4ef2-a9bf-6146b7e80b12"}
 */
function svy_utl_setSelectedIndexPrevious(vFormName, bJustReturnRecord) {

	return globals.svy_utl_setSelectedIndex("prev", vFormName, bJustReturnRecord);
}

/**
 * @properties={typeid:24,uuid:"CE4B2C04-AA28-4BCE-8CA0-E5F2365A9FC5"}
 */
function svy_utl_setButtonsDisabled(_formName) {
	var _elements = forms[_formName].elements.allnames;
	var _element;

	for (var i = 0; i < _elements.length; i++) {
		_element = forms[_formName].elements[_elements[i]]
		if (_element.getElementType() == "LABEL") {
			_element.enabled = false;
		} else if (_element.getElementType() == "TABPANEL") {
			/** @type {RuntimeTabPanel} */
			var _tab = _element
			for (var j = 1; j <= scopes.globals["vUtilities_tabGetMaxTabIndex"](null, null, _tab); j++) {
				svy_utl_setButtonsDisabled(scopes.globals["avUtilities_tabGetFormName"](null, null, j, _tab));
			}
		}
	}
}

/**
 * Method to duplicate a record including its related records
 *
 * <AUTHOR> Schuurhof
 * @since 2011-06-14
 * @param {JSFoundSet} _fs foundset
 * @param {Array} _relatedFsArray all relations for which copies of records should be created
 *
 * @SuppressWarnings(deprecated)
 *
 * @properties={typeid:24,uuid:"1803F2E5-B6CE-4AAA-9663-C4A5D288304D"}
 */
function svy_utl_duplicateRelatedRecords(_fs, _relatedFsArray) {
	var _dup = _fs.getRecord(_fs.duplicateRecord(false, false));

	for (var k = 0; k < _relatedFsArray.length; k++) {

		/** @type {JSFoundSet} */
		var _related = _fs[_relatedFsArray[k]];
		for (var i = 1; i <= _related.getSize(); i++) {
			var _relatedOriginal = _related.getRecord(i);
			var _relatedDub = _dup[_relatedFsArray[k]].getRecord(_dup[_relatedFsArray[k]].newRecord(false, false));
			databaseManager.copyMatchingFields(_relatedOriginal, _relatedDub);
		}
	}
}

/**
 * Convenient method to set multiple properties of a SplitPane in one go
 * Copied from deprecated function scopes.svyUI.initSplitPane and adapted to servoyextra-splitpane component
 *
 * @public
 *
 * @param {String} formName
 * @param {String} elementName
 * @param {Number} resizeWeight
 * @param {Number} dividerLocation
 * @param {Number} dividerSize
 * @param {Boolean} continuousLayout
 * @param {String} [bgColor] If omitted, the SplitPane will be made transparent
 * @param {Number} [leftFormMinSize] Minimum size of the left/top form
 * @param {Number} [rightFormMinSize] Minimum size of the right/bottom form
 *
 * @properties={typeid:24,uuid:"E540DBE4-9828-465E-A2E2-41B54A50ECDC"}
 */
function initSplitPane(formName, elementName, resizeWeight, dividerLocation, dividerSize, continuousLayout, bgColor, leftFormMinSize, rightFormMinSize) {
	if (!forms[formName].elements[elementName]) {
		return;
	}

	if (forms[formName].elements[elementName] instanceof RuntimeSplitPane) {
		/** @type {RuntimeSplitPane} */
		var splitPane = forms[formName].elements[elementName];

		if (resizeWeight instanceof Number) {
			splitPane.resizeWeight = resizeWeight;
		}
		if (dividerLocation instanceof Number) {
			splitPane.dividerLocation = dividerLocation;
		} else {
			restoreSplitPaneDividerPosition(formName, elementName, dividerLocation);
		}
		if (dividerSize instanceof Number) {
			splitPane.dividerSize = dividerSize;
		}
		if (continuousLayout) {
			splitPane.continuousLayout = continuousLayout;
		}

		if (bgColor && bgColor != 'transparent') {
			splitPane.transparent = false;
			splitPane.bgcolor = bgColor;
		} else {
			splitPane.transparent = true;
		}
		if (leftFormMinSize instanceof Number) {
			splitPane.leftFormMinSize = leftFormMinSize
		}
		if (rightFormMinSize instanceof Number) {
			splitPane.rightFormMinSize = rightFormMinSize;
		}
	} else if (forms[formName].elements[elementName].getElementType() == 'servoyextra-splitpane') {
		/** @type {RuntimeWebComponent<servoyextra-splitpane>} */
		var svySplitPane = forms[formName].elements[elementName];

		if (resizeWeight instanceof Number) {
			svySplitPane.resizeWeight = resizeWeight;
		}
		if (dividerLocation instanceof Number) {
			svySplitPane.divLocation = dividerLocation;
		} else{
			restoreSplitPaneDividerPosition(formName, elementName, dividerLocation);
		}
		if (dividerSize instanceof Number) {
			svySplitPane.divSize = dividerSize;
		}
		if (leftFormMinSize instanceof Number) {
			svySplitPane.setLeftFormMinSize(leftFormMinSize);
		}
		if (rightFormMinSize instanceof Number) {
			svySplitPane.setRightFormMinSize(rightFormMinSize);
		}
	}
}

/**
 * @public
 *
 * Persists the position of the splitpane divider to be used by restoreSplitPaneDividerPosition() in a next user session
 * Copied from deprecated function scopes.svyUI.persistSplitPaneDividerPosition() and adapted to servoyextra-splitpane component
 *
 * @param {String} formName
 * @param {String} elementName
 *
 * @properties={typeid:24,uuid:"A8E01E03-7245-47F7-8D81-FD79F5D406CB"}
 */
function persistSplitPaneDividerPosition(formName, elementName) {
	if (!formName || !elementName) {
		application.output('persistSplitPaneDividerPosition called without mandatory params', LOGGINGLEVEL.ERROR);
		return;
	}

	var pos;

	if (forms[formName].elements[elementName] instanceof RuntimeSplitPane) {
		/** @type {RuntimeSplitPane} */
		var splitPane = forms[formName].elements[elementName];
		pos = splitPane.dividerLocation;
	} else if (forms[formName].elements[elementName].getElementType() == 'servoyextra-splitpane') {
		/** @type {RuntimeWebComponent<servoyextra-splitpane>} */
		var svySplitPane = forms[formName].elements[elementName];
		pos = svySplitPane.divLocation;
	}

	application.setUserProperty(application.getSolutionName() + '.' + formName + '.' + elementName + '.divLoc', pos);
}

/**
 * Restores the position of the splitpane divider persisted by persistSplitPaneDividerPosition() between user sessions
 * Copied from deprecated function scopes.svyUI.restoreSplitPaneDividerPosition() and adapted to servoyextra-splitpane component
 *
 * @public
 *
 * @param {String} formName
 * @param {String} elementName
 * @param {Number} position
 *
 * @properties={typeid:24,uuid:"3CCECECE-8423-472C-97EF-F2194B7483E2"}
 */
function restoreSplitPaneDividerPosition(formName, elementName, position) {
	if (!formName || !elementName) {
		application.output('restoreSplitPaneDividerPosition called without mandatory params', LOGGINGLEVEL.ERROR);
		return;
	}
	/** @type {String} */
	var pos = application.getUserProperty(application.getSolutionName() + '.' + formName + '.' + elementName + '.divLoc');
	if (!pos) {
		return;
	}

	pos = utils.stringToNumber(pos);

	if (forms[formName].elements[elementName] instanceof RuntimeSplitPane) {
		/** @type {RuntimeSplitPane} */
		var splitPane = forms[formName].elements[elementName];
		splitPane.dividerLocation = pos;
	} else if (forms[formName].elements[elementName].getElementType() == 'servoyextra-splitpane') {
		/** @type {RuntimeWebComponent<servoyextra-splitpane>} */
		var svySplitPane = forms[formName].elements[elementName];
		svySplitPane.divLocation = pos;
	}
}

/**
 * To save a shortcut for the current program
 * Called when the shortcut button is clicked in the UI
 *
 * <AUTHOR> Dotzlaw
 * @since 2025-02-26
 * @return none
 *
 * @properties={typeid:24,uuid:"c427d4d8-ab90-46cb-8840-f5c61ecfacef"}
 */
function svy_nav_shortcut_save() {
	if (!globals.nav_program_name) return;
	if (databaseManager.hasRecords(_to_nav_shortcut$user_id$organization_id$program_name)) return;// shortcut already exists

	if (databaseManager.hasRecords(_to_nav_shortcut$user_id$organization_id) && _to_nav_shortcut$user_id$organization_id.getSize() == 7) {
		// user has 7 shortcuts, maximum
		globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('svy.fr.lbl.maximum_shortcuts'), i18n.getI18NMessage('avanti.dialog.ok'));
		return;
	}

	// create the shortcut
	forms.svy_nav_fr_shortcuts.foundset.newRecord();
	forms.svy_nav_fr_shortcuts.program_name = globals.nav_program_name;
	forms.svy_nav_fr_shortcuts.user_id = globals.svy_sec_lgn_user_id;
	forms.svy_nav_fr_shortcuts.organization_id = globals.svy_sec_lgn_organization_id;
	forms.svy_nav_fr_shortcuts.is_user_created = 1;

	// set the right sort order
	var maxReturnedRows = 1;
	var query = 'SELECT MAX(sort_order) FROM nav_shortcut WHERE user_id = ? AND organization_id = ?';
	var args = new Array();
	args[0] = globals.svy_sec_lgn_user_id;
	args[1] = globals.svy_sec_lgn_organization_id;
	var dataset = databaseManager.getDataSetByQuery(databaseManager.getDataSourceServerName(forms.svy_nav_fr_shortcuts.controller.getDataSource()), query, args, maxReturnedRows);
	if (dataset.getValue(1, 1)) {
		forms.svy_nav_fr_shortcuts.sort_order = dataset.getValue(1, 1) + 1;
	} else {
		forms.svy_nav_fr_shortcuts.sort_order = 1;
	}

	// Save the new shortcut
	databaseManager.saveData(forms.svy_nav_fr_shortcuts.foundset);

	// load the shortcuts to see the new one
	forms.svy_nav_fr_shortcuts.loadShortcuts();

	// show the tab of the shortcuts
	forms.svy_nav_fr_bottom_bkmk_short.onClickShortcut();
}

/**
 * To delete a shortcut
 * @param {JSEvent} _event
 * <AUTHOR> Dotzlaw
 * @since 2025-02-26
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"2EF5D141-3E2D-494C-BAE2-FE6DA8D7BFE1"}
 */
function svy_nav_shortcutDelete(_event) {
    var _button = _event.getElementName();
    // delete the record, if record exists
    if (forms.svy_nav_fr_shortcuts.foundset.find()) {
        forms.svy_nav_fr_shortcuts.shortcut_id = globals.nav.shortcuts[_button.replace(/[^0-9]/g, "")].shortcut_id;
        forms.svy_nav_fr_shortcuts.foundset.search();
        forms.svy_nav_fr_shortcuts.foundset.deleteRecord();
        forms.svy_nav_fr_shortcuts.loadShortcuts();
    }
}

/**
 * On click open the program from the shortcut, by using information in the object built in loadShortcuts()
 * Calls svy_nav_showForm to utilize tabpanel navigation
 *
 * <AUTHOR> Aleman
 * @since 2008-05-24
 * @param {JSEvent} _event
 * @param {String} [_program] - optional program (gd)
 * @param {String} [_form] - optional form to navigate to (gd)
 *
 * @properties={typeid:24,uuid:"20d4344d-a086-40b7-a1cb-db0c64afef01"}
 */
function svy_nav_shortcut_goto(_event, _program, _form) {
	if (!scopes['avUtils'].isNavModeReadOnly())
	    return;

	globals.nav.fromTree = 1;

	if (!_program) {
		var _button = _event.getElementName();
		// get the programname
		if(utils.stringPatternCount(_button,'shortcutLabel') > 0) { // webclient is used
			_program = globals.nav.shortcuts[utils.stringReplace(_button, 'shortcutLabel', '')].program;
		} else {
			_program = globals.nav.shortcuts[utils.stringReplace(_button, 'shortcut', '')].program;
		}

		var _template = globals.nav.template_types[globals.nav.program[_program].view];
		_form = globals.nav.program[_program].form[forms[_template].has1()][2];
	}

	// show the program - this will create or select the appropriate tab in the bootstrap tabpanel
	globals.svy_nav_showForm(_form, _program);
}

/**
 * To create a bookmark for the current record
 *
 * <AUTHOR> Dotzlaw
 * @since 2025-02-26
 *
 * @properties={typeid:24,uuid:"96e2b1d9-235e-4930-9dc6-99154505cfbd"}
 */
function svy_nav_bookmarkCreate() {
	// if user has 7 BOOKMARKS, maximum will give a warning and return
	if (databaseManager.hasRecords(_to_nav_bookmarks$user_id$organization_id) && _to_nav_bookmarks$user_id$organization_id.getSize() == 7) {
		globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('svy.fr.lbl.maximum_bookmarks'), i18n.getI18NMessage('avanti.dialog.ok'));
		return;
	}

	// Returns an array containing the names of the identifier (PK) column(s)
	var jsTable = databaseManager.getTable(globals.nav.program[globals.nav_program_name].server_name, globals.nav.program[globals.nav_program_name].table_name);
	if (!jsTable) return; // no table for this form

	// save the recordids
	var _record_names = jsTable.getRowIdentifierColumnNames();
	var _record_ids = new Array(); // saving array directly to database field doesn't work
	var _record_types = new Array();

	for (var i = 0; i < _record_names.length; i++) {
		// get the record id values
		_record_ids[i] = forms[globals.nav.form_view_01][_record_names[i]];

		// get the column types
		var _jsColumn = jsTable.getColumn(_record_names[i]);
		_record_types[i] = _jsColumn.getTypeAsString();
	}

	// check if bookmark is unique
	for (var j = 1; j <= _to_nav_bookmarks$user_id$organization_id.getSize(); j++) {
		/** @type {Array} */
		var _dbRecord_ids = _to_nav_bookmarks$user_id$organization_id.record_ids;
		/** @type {Array} */
		var _dbRecord_names = _to_nav_bookmarks$user_id$organization_id.record_ids_names;
		if (scopes.svyJSUtils.areObjectsEqual(_dbRecord_ids, _record_ids) && scopes.svyJSUtils.areObjectsEqual(_dbRecord_names, _record_names)) {
			return; // bookmark already exists
		}
	}

	// create the bookmark record
	_to_nav_bookmarks$user_id$organization_id.newRecord();

	// save the recordids_names
	_to_nav_bookmarks$user_id$organization_id.record_ids_names = _record_names;

	// save the record ids
	_to_nav_bookmarks$user_id$organization_id.record_ids = _record_ids;

	// save the column types
	_to_nav_bookmarks$user_id$organization_id.record_ids_types = _record_types;

	// save the program
	_to_nav_bookmarks$user_id$organization_id.program = globals.nav_program_name;

	// save the view
	_to_nav_bookmarks$user_id$organization_id.program_view = globals.nav.program[globals.nav_program_name].view;

	// NEW: Store the display value directly
	var displayField = globals.nav.program[globals.nav_program_name].display_field_header;
	if (displayField == 'contact_full_name') {
		// Map to sys_contact table
		displayField = "sa_customer_contact_to_sys_contact.contact_full_name";
	}
	if (displayField && forms[globals.nav.form_view_01][displayField]) {
		_to_nav_bookmarks$user_id$organization_id.display_text = forms[globals.nav.form_view_01][displayField];
	} else {
		// Try to find a reasonable display field if display_field_header isn't set
		var possibleDisplayFields = ['full_name', 'name', 'description', 'title'];
		for (var k = 0; k < possibleDisplayFields.length; k++) {
			if (forms[globals.nav.form_view_01][possibleDisplayFields[k]]) {
				_to_nav_bookmarks$user_id$organization_id.display_text = forms[globals.nav.form_view_01][possibleDisplayFields[k]];
				break;
			}
		}

		// If still no display text, use the first record ID as fallback
		if (!_to_nav_bookmarks$user_id$organization_id.display_text && _record_ids.length > 0) {
			_to_nav_bookmarks$user_id$organization_id.display_text = "ID: " + _record_ids[0];
		}
	}

	// set the right sort order
	var maxReturnedRows = 1;
	var query = 'SELECT MAX(sort_order) FROM nav_bookmarks WHERE user_id = ? AND organization_id = ?';
	var args = new Array();
	args[0] = globals.svy_sec_lgn_user_id;
	args[1] = globals.svy_sec_lgn_organization_id;
	var dataset = databaseManager.getDataSetByQuery(databaseManager.getDataSourceServerName(forms.svy_nav_fr_bookmarks.controller.getDataSource()), query, args, maxReturnedRows);
	if (dataset.getValue(1, 1)) {
		_to_nav_bookmarks$user_id$organization_id.sort_order = dataset.getValue(1, 1) + 1;
	} else {
		_to_nav_bookmarks$user_id$organization_id.sort_order = 1;
	}

	// Save the bookmark
	databaseManager.saveData(_to_nav_bookmarks$user_id$organization_id.getRecord(1));

	// load the bookmarks to see the new bookmark
	forms.svy_nav_fr_bookmarks.loadBookmarks();

	// show the tab with the bookmarks
	forms.svy_nav_fr_bottom_bkmk_short.onClickBookmark();
}


/**
 * To delete a bookmark
 * @param {JSEvent} _event
 * <AUTHOR> Dotzlaw
 * @since 2025-02-26
 *
 * @properties={typeid:24,uuid:"43d7406f-e55a-4b27-bc0a-f366fdca87e3"}
 * @AllowToRunInFind
 */
function svy_nav_bookmarkDelete(_event) {
	var _button = _event.getElementName();
	// delete the record, if record exists
	if (forms.svy_nav_fr_bookmarks.foundset.find()) {
		forms.svy_nav_fr_bookmarks.bookmark_id = globals.nav.bookmarks[_button.replace(/[^0-9]/g, "")].bookmark_id;
		forms.svy_nav_fr_bookmarks.foundset.search();
		forms.svy_nav_fr_bookmarks.foundset.deleteRecord();
		forms.svy_nav_fr_bookmarks.loadBookmarks();
	}
}

/**
 * To go to the program and record of a bookmark
 * Uses svy_nav_showForm for tabpanel navigation and then selects the specific record
 *
 * <AUTHOR> Dotzlaw
 * @since 2025-02-26
 *
 * @param {JSEvent} [_event] that triggered the action
 * @param {String} _program
 * @param {Number|String} _pk
 * @param {Number} _view
 * @return none
 *
 * @properties={typeid:24,uuid:"54af5404-cb70-4519-b7cf-837f8b6c12e0"}
 * @AllowToRunInFind
 */
function svy_nav_bookmarkGoto(_event, _program, _pk, _view) {
	if (!scopes['avUtils'].isNavModeReadOnly())
	    return;

	globals.nav.program[_program].view = _view;
	var _template = globals.nav.template_types[_view];
	var _form = globals.nav.program[_program].form[forms[_template].has1()][2];

	// AVANTI CHANGE
	if (_form == "sa_invoice_dtl") { // Prevents a bookmarked invoice from loading if the foundset is not filtered correctly
		forms[_form].foundset.removeFoundSetFilterParam('InvoiceStatusFilter');
		forms[_form].foundset.removeFoundSetFilterParam('InvoicePaidFilter');
	}

	// show the form - this will create or select the appropriate tab in the bootstrap tabpanel
	globals.svy_nav_showForm(_form, _program);

	// navigate to the right record
	var success = false;
	success = forms[_form].foundset.selectRecord(_pk);

	if (success == false) { // record is not in the first 200 records
		forms[_form].foundset.getRecord(databaseManager.getFoundSetCount(forms[_form].foundset));
		success = forms[_form].foundset.selectRecord(_pk);

		// GD - 2013-09-24: SL-1356 - AMMS reporting that it still sometimes will go to a blank record
		if (success == false) {
			var sTemp = globals.nav_program_name;
			globals.nav_program_name = _program;
			var sTable = _to_nav_program.table_name;
			var sPKName = globals['avUtilities_tableGetPKName'](sTable, globals['avBase_dbase_avanti']);
			globals.nav_program_name = sTemp;

			if (forms[_form].foundset.find()) {
				forms[_form].foundset[sPKName] = _pk;
				forms[_form].foundset.search();

				if (forms[_form].foundset.getSize() > 0 && forms[_form].foundset[sPKName] == _pk) {
					success = true;
				}
				else {
					success = false;
				}
			}
		}
	}
}
