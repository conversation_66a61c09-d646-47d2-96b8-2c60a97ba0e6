/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2618A974-EEF1-4EDD-A86A-2C869AAAA27B"}
 */
var jobID = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E4D3C06E-CFBD-4DD0-8EEA-B34A1314168C"}
 */
var sRemainingPathInArray = '';

/**
 * Callback method when form is (re)loaded.  This registers the plugins when the headless client
 * is started on a web service call.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A25F9F4A-397A-4609-90AD-7C408114D2DD"}
 */
function onLoad(event) {
}

/**
 * Process GET Request
 *
 * @return
 * @properties={typeid:24,uuid:"28C5812A-45BC-48E9-B0D9-F2B8ED6BE1D2"}
 */
function ws_read() {

	/* Loop through the arguments passed in the URL, looking to populate the RequestType

	 /*
	 * requestType: The type of request it is which can be:
	 * 			'OpenSalesOrdersByMailer': Return all open sales order by Mailer
	 */
	var requestType = ''
	var mailerID = ''
	var customerID = ''
	var salesOrderNum, lineItemNum, lineItemQty, jobIDs
	var itemIDs = new Array()
	var nResetOnHand = 0;
	var nResetOpeningBalance = 0;
	var nForceOnHandReset = 0;
	var bFixOnly = false;
	var oTestingDetails = new Object();
	var sWebCode = "";
	scopes.avWebToPrint.bFromWebToPrint = true;
	
    for (var i = 0; i < arguments.length; i++) {
        if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
        }
        else {
            for (var key in arguments[i]) {
                if (key == 'RequestType') {
                    requestType = arguments[i][key][0]
                }
                else if (key == 'MailerID') {
                    mailerID = arguments[i][key][0]
                }
                else if (key == 'CustomerID') {
                    customerID = arguments[i][key][0]
                }
                else if (key == 'OrderNumber') {
                    salesOrderNum = arguments[i][key][0]
                }
                else if (key == 'LineItemNumber') {
                    lineItemNum = arguments[i][key][0]
                }
                else if (key == 'LineItemQty') {
                    lineItemQty = arguments[i][key][0]
                }
                else if (key == 'JobIDs') {
                    jobIDs = arguments[i][key][0]
                }
                else if (key == 'TestCode') {
                    oTestingDetails.sTestCode = arguments[i][key][0];
                }
                else if (key == 'MappingFilePath') {
                    oTestingDetails.sPathToMappingFile = arguments[i][key][0];
                }
                else if (key == 'InputFileName') {
                    oTestingDetails.sInputFileName = arguments[i][key][0];
                }
                else if (key == 'UseOriginalLogic') {
                    oTestingDetails.bUseOriginalLogic = arguments[i][key][0];
                }
                else if (key == "WebCode") {
                    sWebCode = arguments[i][key][0];
                }
                else if (key == 'ItemIDs') {
                    itemIDs = arguments[i][key][0].split(',');
                }
                else if (key == 'ResetOnHandValues') {
                    nResetOnHand = Number(arguments[i][key][0]);
                    if (isNaN(nResetOnHand)) {
                        nResetOnHand = 0;
                    }
                }
                else if (key == 'ResetOpeningBalance') {
                    nResetOpeningBalance = Number(arguments[i][key][0]);
                    if (isNaN(nResetOpeningBalance)) {
                        nResetOpeningBalance = 0;
                    }
                }
                else if (key == 'ForceOnHandReset') {
                	nForceOnHandReset = Number(arguments[i][key][0]);
                    if (isNaN(nForceOnHandReset)) {
                    	nForceOnHandReset = 0;
                    }
                }
                else if (key == 'FixOnly') {
                    bFixOnly = Boolean(arguments[i][key][0]);
                }
            }
        }
    }

	if(requestType != 'TestWebToPrint' && requestType != 'FifoRebuild') {
		var response = scopes.avLogin.authenticateForAPI(arguments);

		if (response != null) {
			return response;
		}
		response = new Object();
	}
	
	if (requestType == "FifoRebuild") {
		if (sWebCode) {
			
			/** @type{JSFoundSet<db:/avanti/sys_organization>} */
			var fsOrg = scopes.avDB.getFS("sys_organization",["org_web_service_code"],[sWebCode], null, null, true);
			if (fsOrg && fsOrg.getSize() == 1) {
				globals.org_id = fsOrg.org_id;
				scopes.avUtils.getPrefs();
			}
			else {
				throw [plugins.http.HTTP_STATUS.SC_UNAUTHORIZED, 'Failed Authentication: Invalid web service code token.'];
			}
		}
		else {
			response = new Object();
			response.message = 'Web Code is required.';
			return response;
		}
	}
	
	switch (requestType) {
		case 'OpenSalesOrdersByMailer':
			application.output('Return list of open sales orders by mailer ID')
			return getOpenSalesOrdersByMailer(mailerID, customerID)
			break;
		case 'UpdateLineItemQuantity':
			return updateLineItemQuantity(salesOrderNum, lineItemNum, lineItemQty)
			break;
		case 'GetJobStatus':
			return scopes.avJobStatus.getJobStatus(jobIDs);
			break;
		case 'FifoRebuild':
			if (itemIDs && itemIDs.length > 0) {
				for (var item = 0; item < 1; item++) {
					if (application.getUUID(itemIDs[item])) {
						scopes["avInventoryRebuilds"].rebuildFIFO(itemIDs[item].toString(), nResetOnHand, nResetOpeningBalance, bFixOnly, nForceOnHandReset);
					}
				}
				
				response = new Object();
				response.message = 'Success';
				return response;
			}
			
		case 'TestWebToPrint': 
			return validateWebToPrintOrder(oTestingDetails);
		default:
			break;
	}
	response.message = 'There was no paramater RequestType matching: ' + requestType
	return response;
}

/**
 * Test Web to Print Order
 * @param {{sPathToMappingFile:String, sInputFileName:String, sTestCode:String, bUseOriginalLogic: Boolean}} oTestingDetails
 *
 * @return
 * @properties={typeid:24,uuid:"CCD3404E-583B-4ED5-8455-A4146F4F9B9A"}
 */
function validateWebToPrintOrder(oTestingDetails) { 
	var response = new Object();
	if(oTestingDetails.sInputFileName == null || oTestingDetails.sPathToMappingFile == null || oTestingDetails.sTestCode == null) {
		response.missingInfo = 'Missing input parameters. Need to pass in InputFileName, MappingFilePath, TestCode.'
		return response;
	}
	
	var sFilePath = oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Input\\' + oTestingDetails.sInputFileName
	// Create xmlRequestFile object to mimic that which is created by the Rest Web Service API so we can reuse function forms.api_web_service.createNewOrder(xmlRequestFile,obj.sender_id)
	/** @type {Object} */
	var xmlRequestFile = new Object()
	var xml_document = plugins.XML.XMLDocument([sFilePath]);
	try {
		xmlRequestFile = Packages.org.json.XML.toJSONObject(xml_document.getAsText())
	} catch(ex) {
		throw new Error('Failed to create an order because file could not be converted to XML')
	}

	response = createNewOrder(xmlRequestFile, '', null, oTestingDetails);

	return response;
}

/**
 * Return response with an array of open sales orders.
 * @param {String} mailerID_param
 * @param {String} customerID_param
 *
 * @return
 * @properties={typeid:24,uuid:"F009F1EB-A229-4481-89D7-DB0DC6165AF0"}
 */
function getOpenSalesOrdersByMailer(mailerID_param, customerID_param) {
	var response = new Object()

	var oSQL = new Object()

	//Sales Orders
	oSQL.sql = "SELECT DISTINCT f.cust_code, e.custaddr_code,  b.ordh_document_num, b.ordh_description, b.ordh_order_date,c.ordrevh_promise_date, c.ordrevh_expected_date, d.sequence_nr, d.ordrevd_prod_desc, d.ordrevd_qty_ordered, isnull(g.job_number,'') \
				FROM _v_ord_ship_address as a \
				inner join sa_order as b on a.ordh_id = b.ordh_id and b.ordh_process_externally = 1 and a.org_id = ? \
				inner join sa_order_revision_header as c on b.ordh_id = c.ordh_id and c.ordrevh_order_status in ('Open', 'Released For Prep') \
				inner join sa_order_revision_detail as d on d.ordrevh_id = c.ordrevh_id \
				inner join sa_customer_address as e on e.custaddr_id = b.ordh_shipto_custaddr_id and e.custaddr_code = ? \
				inner join sa_customer as f on f.cust_id = b.cust_id and f.cust_code = ? \
				left join prod_job as g on g.job_id = d.job_id";

	oSQL.args = [globals.org_id, mailerID_param, customerID_param];

	/*** @type {JSDataSet} */
	var dsDataOrder = globals.avUtilities_sqlDataset(oSQL);

	var arrayOfOrders = new Array()
	for (var idx = 1; idx <= dsDataOrder.getMaxRowIndex(); idx++) {
		var order = dsDataOrder.getRowAsArray(idx)
		var orderItem = new Object()
		orderItem.CustomerCode = order[0]
		orderItem.ShipToCode = order[1]
		orderItem.OrderNumber = order[2]
		orderItem.OrderDesc = order[3]
		orderItem.OrderDate = scopes.avUtils.dateToString(order[4])
		orderItem.DueAtCustomerDate = scopes.avUtils.dateToString(order[5])
		orderItem.ExpectedShipDate = scopes.avUtils.dateToString(order[6])
		orderItem.LineItemNumber = order[7]
		orderItem.LineItemDesc = order[8]
		orderItem.LineItemQty = order[9]
		orderItem.JobNumber = order[10]
		arrayOfOrders.push(orderItem)
	}
	response.xml = new Object()
	response.xml.message = 'Success'
	response.xml.order = arrayOfOrders
	return response
}

/**
 * @return
 * @properties={typeid:24,uuid:"7EA2999C-482B-4418-B915-F7BA2498933A"}
 * @AllowToRunInFind
 */
function updateLineItemQuantity(salesOrderNum_param, lineItemNum_param, lineItemQty) {
	var response = new Object(),
		rDetail,
		rQty;
	response.xml = new Object()

	// GD - Oct 6, 2014: Set flag to tell us that this is an api call, and avoid updating the UI
	scopes.avDetail.apiCall = 1;

	/** @type{JSFoundSet<db:/avanti/sa_order_revision_detail>} */
	var line_item_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revision_detail')
	if (line_item_fs.find()) {
		//Search on Open and Release for Prep based on line item number and sales order number.
		line_item_fs.org_id = globals.org_id
		line_item_fs.sequence_nr = lineItemNum_param
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_document_num = salesOrderNum_param
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_process_externally = 1
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.ordrevh_order_status = 'Open'
		line_item_fs.newRecord()
		line_item_fs.org_id = globals.org_id
		line_item_fs.sequence_nr = lineItemNum_param
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_document_num = salesOrderNum_param
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_process_externally = 1
		line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.ordrevh_order_status = 'Released For Prep'
		if (line_item_fs.search() > 0) {

			//CH 10-31-2014: Update Shipped and Backordered amount.
			var original_qty_shipped = line_item_fs.ordrevd_qty_shipped
			var original_qty_backordered = line_item_fs.ordrevd_qty_backord
			var new_qty_ordered = utils.stringToNumber(lineItemQty)

			line_item_fs.ordrevd_qty_ordered = new_qty_ordered
			if (original_qty_shipped == 0) {
				line_item_fs.ordrevd_qty_backord = new_qty_ordered
			} else if (new_qty_ordered <= original_qty_shipped && original_qty_backordered > 0) {
				line_item_fs.ordrevd_qty_backord = 0
				line_item_fs.ordrevd_qty_shipped = new_qty_ordered
			} else if (new_qty_ordered > original_qty_shipped && original_qty_backordered > 0) {
				line_item_fs.ordrevd_qty_backord = new_qty_ordered - original_qty_shipped
			} else if (new_qty_ordered <= original_qty_shipped && original_qty_backordered <= 0) {
				line_item_fs.ordrevd_qty_backord = 0
				line_item_fs.ordrevd_qty_shipped = new_qty_ordered
			} else {
				//Check if there is quantity to be shipped and if not, then add to back ordered
				var iQtyAvailable = globals.avCalcs_item_getItemAvailability(line_item_fs.item_id, line_item_fs.uom_id, line_item_fs.whse_id, null, 0, null, line_item_fs.getSelectedRecord());
				if (iQtyAvailable >= new_qty_ordered) {
					line_item_fs.ordrevd_qty_backord = 0
					line_item_fs.ordrevd_qty_shipped = new_qty_ordered
				} else {
					line_item_fs.ordrevd_qty_backord = new_qty_ordered - iQtyAvailable
					line_item_fs.ordrevd_qty_shipped = iQtyAvailable
				}

			}

			// GD - Oct 6, 2014: Need to update the section/task qty as well as the line
			rDetail = line_item_fs.getSelectedRecord();
			rQty = rDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base.getRecord(1);
			rQty.ordrevdqty_qty = line_item_fs.ordrevd_qty_ordered;
			forms.sa_order_revision_detail_quantity_tbl.setSectionQtyTaskQty(rDetail, rQty);

			line_item_fs.ordrevd_processed_externally = 1

			// Recalculate the totals and prices.
			try {
				/*globals.avBase_oCalcs = new Object();
				 forms.sa_order_revision_detail_tbl.foundset.loadRecords(line_item_fs)
				 forms.sa_order_revision_detail_tbl.onRecordSelection(null,null)
				 forms.sa_order_revision_detail_tbl._qty = utils.stringToNumber(lineItemQty)
				 forms.sa_order_revision_detail_tbl._type = line_item_fs.sysworktype_id
				 forms.sa_order_revision_detail_tbl._whse = line_item_fs.whse_id
				 forms.sa_order_revision_detail_tbl._description = line_item_fs.ordrevd_prod_desc
				 forms.sa_order_revision_detail_tbl._uom = line_item_fs.uom_id
				 forms.sa_order_revision_detail_tbl._mode = 'u'
				 forms.sa_order_revision_detail_tbl.saveAddItemRow(null)*/
				globals.svy_nav_form_name = 'sa_order_tbl'
				globals.avSales_selectedRevisionDetailQtyUUID = line_item_fs.sa_order_revision_detail_to_sa_order_revision_detail_qty.ordrevdqty_id
				globals.avSales_selectedRevisionDetailID = line_item_fs.ordrevd_id
				//globals.avSales_selectedRevisionHeaderNumber = line_item_fs.sa_order_revision_detail_to_sa_order_revision_header.ordrevh_revision

				line_item_fs.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base.ordrevdqty_qty = utils.stringToNumber(lineItemQty);
				forms.sa_order_revision_detail_quantity_tbl.foundset.clear();
				forms.sa_order_revision_detail_quantity_tbl.foundset.loadRecords(line_item_fs.sa_order_revision_detail_to_sa_order_revision_detail_qty);

				databaseManager.saveData(forms.sa_order_revision_detail_quantity_tbl.foundset);

				forms.sa_order_revision_detail_quantity_tbl._recordEdit = 1;

				forms.sa_order_revision_detail_quantity_tbl.recalculate();

				databaseManager.saveData();

				response.xml.message = 'Line Item: ' + lineItemNum_param + ' updated to quantity of: ' + utils.stringToNumber(lineItemQty)
				response.xml.status = 'SUCCESS'
			} catch (ex) {
				response.xml.message = 'Sales order number: ' + salesOrderNum_param + 'Line Item: ' + lineItemNum_param + ' update failed due to\n' + ex.message + '\n'
				response.xml.stack = ex.stack
				response.xml.status = 'ERROR'
			}

		} else {
			response.xml.message = 'ERROR: No line item could be found for sales order number: ' + salesOrderNum_param + ' and line item: ' + lineItemNum_param + ' that is Open/Released For Prep and set to process externally.'
			response.xml.status = 'ERROR'
		}
	}

	// GD - Oct 6, 2014: Set flag to tell us that this is an api call, and avoid updating the UI
	scopes.avDetail.apiCall = 0;

	return response
}

/**
 * @AllowToRunInFind
 * @param {String} jobIDs_param String of comma separated values of job ids
 * @return
 * @properties={typeid:24,uuid:"0BE8F12C-9A8C-4D70-A4EB-6AFCAF68A927"}
 */
function getJobStatusByID(jobIDs_param) {
	var arrayOfJobs = new Array()
	var arrayOfJobIDs = jobIDs_param.split(',')
	var response = new Object()

	if (arrayOfJobIDs != null && arrayOfJobIDs.length > 0) {
		for (var job_idx = 0; job_idx < arrayOfJobIDs.length; job_idx++) {

			var job_number_param = arrayOfJobIDs[job_idx]
			var job_status = new Object()

			if (job_number_param) {
				/** @type {JSFoundSet<db:/avanti/prod_job>} */
				var prod_job_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job')

				job_status.job_number = job_number_param
				if (prod_job_fs.find()) {
					prod_job_fs.job_number = job_number_param
					if (prod_job_fs.search() > 0) {
						job_status.job_status = prod_job_fs.jobstat_id
						arrayOfJobs.push(job_status)
					} else {
						// SL-4562: CH - Added search by order number to return all jobs and their status matching that order.
						/** @type {JSFoundSet<db:/avanti/sa_order>} */
						var order_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order')

						if (order_fs.find()) {
							order_fs.ordh_document_num = job_number_param
							if (order_fs.search() > 0) {

								for (var idx = 1; idx <= order_fs.sa_order_to_prod_job.getSize(); idx++) {
									job_status = new Object()
									job_status.job_number = order_fs.sa_order_to_prod_job.getRecord(idx).job_number
									job_status.job_status = order_fs.sa_order_to_prod_job.getRecord(idx).jobstat_id
									arrayOfJobs.push(job_status)
								}

							} else {
								job_status.job_status = 'No matching job ID.'
								arrayOfJobs.push(job_status)
							}
						}
					}
				}
			}
		}
	}

	response.xml = new Object()
	response.xml.message = 'Success'
	response.xml.job = arrayOfJobs

	return response
}

/**
 * Process PUT request
 * @return
 * @properties={typeid:24,uuid:"80A34BAF-59A0-4789-9B7D-44A635E0F702"}
 */
function ws_update() {

	/* Loop through the arguments passed in the URL, looking to populate the RequestType

	 /*
	 * requestType: The type of request it is which can be:
	 * 			'UpdateLineItemQuantity': Update line item quantity of open sales order by sales order number.
	 */
	var requestType = ''

	var response = scopes.avLogin.authenticateForAPI(arguments)

	if (response != null) {
		return response
	}
	response = new Object()
	for (var i = 0; i < arguments.length; i++) {
		if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
		} else {
			for (var key in arguments[i]) {
				if (key == 'RequestType') {
					requestType = arguments[i][key][0]
				}
			}
		}
	}
	switch (requestType) {

	default:
		break;
	}
	response.message = 'Nothing run'
	return response;
}

/**
 * Creates Import return Json object
 * @param {String} sErrorMessage
 *
 * @return {Object}
 * @properties={typeid:24,uuid:"CFF9C1D3-42E7-4F02-9589-7FDB0BC59DCB"}
 */
function createImportOrderErrorResponse (sErrorMessage) {
	var response = {};
	response.jobID = '';	
	response.jobDetails = {};
	response.jobDetails.evaluationError = sErrorMessage;
	
	return response;
}

/**
 * @AllowToRunInFind
 * 
 * Validates Unique id for the import
 * @param xmlRequestFile
 * @param {String} senderID
 * @param {String} requestType
 * @param {JSFoundSet<db:/avanti/sys_sender>} sys_sender_fs
 *
 * @return {{bIsError: Boolean, sUniqueId: String, oResponse: Object}}
 * @properties={typeid:24,uuid:"3D99BD40-8F5F-441E-8BB6-3FD41B81FA87"}
 */
function validateUniqueIdForImportSalesOrder(xmlRequestFile, senderID, requestType, sys_sender_fs) {
	var unique_id = 'not setup';
	
	/** @type {{bIsError: Boolean, sUniqueId: String, sResponse: String}} */
	var oResult = [];
	oResult.bIsError = false;

	if (!senderID) {
		oResult.bIsError = true;
		oResult.oResponse = createImportOrderErrorResponse('Sender Id is not set in the request Url parameters');
		return oResult;
	}

	/** @type {JSRecord<db:/avanti/sys_sender>} */
	var rSysSender;
	// Search for XML mappings between XML and Avanti XML Schema as setup in the Web to Print maintenance program based on Sender ID.
	if (sys_sender_fs.find()) {
		sys_sender_fs.sender_id = senderID;
		sys_sender_fs.org_id = globals.org_id;
		if (sys_sender_fs.search() > 0) {
			rSysSender = sys_sender_fs.getRecord(1);
			if (!rSysSender.unique_id_path) {
				return oResult;
			}
			unique_id = eval('xmlRequestFile.' + rSysSender.unique_id_path);
			// This is used to filter out the xml:lang namespaces, etc.
			if (unique_id == '[object Object]') {
				unique_id = eval('xmlRequestFile.' + rSysSender.unique_id_path + '.content');
			}

			if (requestType == scopes.avWebToPrint.sCreateOrderRequestType && unique_id) {
				/**@type {JSRecord<db:/avanti/sys_order_import_unique_ids>} */
				var rOrdImportUniqueId = scopes.avDB.getRec('sys_order_import_unique_ids', ['unique_id_value', 'org_id'], [unique_id, globals.org_id]);
				if (rOrdImportUniqueId) {
					oResult.bIsError = true;
					oResult.oResponse = createImportOrderErrorResponse('Sales order could not be created as the unique ID value: ' + unique_id + ' is already in use on another order: ' + rOrdImportUniqueId.ord_document_number);
					return oResult;
				}
			}

		}
		else {
			oResult.bIsError = true;
			oResult.oResponse = createImportOrderErrorResponse('Sender Id: ' + senderID + ' was not found in the database');
			return oResult;
		}
	}
	
	oResult.sUniqueId = unique_id;
	return oResult;
}

/**
 * This function handles the HTTP Post request and reads in the XML file to determine what action
 * to perform.
 *
 * @param {{xml:{requestType, Jobs, Item, pricingRequest:{customerCode:String, contactFullName: String, externalJobID: String, shippingAddresses: Object, product:Array, workTemplate: Array, inventory:{item:Object}}}}} xmlRequestFile XML file from the HTTP Post
 *
 * @return
 * @properties={typeid:24,uuid:"570200E7-2129-452A-B012-05D4EFE4B646"}
 * @AllowToRunInFind
 */
function ws_create(xmlRequestFile) {
    /* Loop through the arguments passed in the URL, looking to populate the SenderID and RequestType
     * SenderID: The ID of where the XML is coming from and is associated to the SenderID setup in the
     * 			XML mapper.  This ID will be used to load the XML Mapping details to populate the XML
     */
    var senderID = '';
    /*
     * requestType: The type of request it is which can be:
     * 			'NewOrder': Create a new order based on content of XML and SenderID
     * 			'InventoryStatus': Get the status of the item
     * 			'Pricing': Get the pricing of an item, task or job
     * 			'JobStatus': Get the status of a job.
     */
    var requestType = '';
    var username = '';
    var oTestingDetails = new Object();
    var db_log_parent_id;

    try {

        for (var i = 0; i < arguments.length; i++) {
            if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings

            }
            else {
                for (var key in arguments[i]) {
                    if (key == 'SenderID') {
                        senderID = arguments[i][key][0];
                    }
                    else if (key == 'RequestType') {
                        requestType = arguments[i][key][0];
                        scopes.avWebToPrint.sImportOrderRequestType = requestType;
                    }
                    else if (key == 'TestCode') {
                        oTestingDetails.sTestCode = arguments[i][key][0];
                    }
                    else if (key == 'MappingFilePath') {
                        oTestingDetails.sPathToMappingFile = arguments[i][key][0];
                    }
                    else if (key == 'InputFileName') {
                        oTestingDetails.sInputFileName = arguments[i][key][0];
                    }
                    else if (key == 'UseOriginalLogic') {
                        oTestingDetails.bUseOriginalLogic = arguments[i][key][0];
                    }
                    else if (key == 'Username') {
                        username = arguments[i][key][0];
                    }
                }
            }
        }

        var response = new Object();

        scopes.avWebToPrint.bFromWebToPrint = true;
        scopes.avWebToPrint.bSpawnSections = 0;
        db_log_parent_id = globals.dbLog('Received xml request using username '+ username, 'wtp_request', 'import', 'wtp', 'http', null, 'web_to_print', null, null, null, null, null);

        response = scopes.avLogin.authenticateForAPI(arguments);

        if (response != null) {
            return response;
        }
        
        globals.avPref_dimension_unit = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.MeasurementSystem);
        
        response = new Object();
        // Process a new order request
        if (requestType == scopes.avWebToPrint.sCreateOrderRequestType ||
        	requestType == scopes.avWebToPrint.sUpdateOrderRequestType ||
        	requestType == scopes.avWebToPrint.sEstimateOrderRequestType ||
        	requestType == scopes.avWebToPrint.sNewEstimateRequestType) {
        		
            try {
            	
            	/** @type {JSFoundSet<db:/avanti/sys_sender>} */
            	var sys_sender_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sender');
            	
            	var oValidationResult = validateUniqueIdForImportSalesOrder(xmlRequestFile, senderID, requestType, sys_sender_fs);
            	if (oValidationResult.bIsError) {
            		return oValidationResult.oResponse;
            	}
            	
            	var unique_id = oValidationResult.sUniqueId;

                try {
                    // Remove namespaces:
                    var sObjectJSON = JSON.stringify(xmlRequestFile);
                    var nNumberOfNamespaces = utils.stringPatternCount(sObjectJSON, 'xmlns:');
                    for (var nNamespaceIndex = 1; nNamespaceIndex <= nNumberOfNamespaces; nNamespaceIndex++) {
                        var nPositionIndex = utils.stringPosition(sObjectJSON, 'xmlns:', 1, nNamespaceIndex);
                        var sNamespace = sObjectJSON.substring(nPositionIndex + 5, utils.stringPosition(sObjectJSON, ':', nPositionIndex + 7, 1) - 2);
                        sObjectJSON = utils.stringReplace(sObjectJSON, sNamespace + ':', '');
                    }
                    xmlRequestFile = JSON.parse(sObjectJSON);

                }
                catch (ex) {
                    if (ex.message) {
                        application.output(ex.message);
                        globals.dbLogUpdate(db_log_parent_id, 'Error! ' + globals.getWTPCreateUpdateMessage(requestType) + senderID + ' and unique ID ' + unique_id + ' processing may still finish but encountered exception ' + ex.message, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', globals.getWTPPurposeMessage(requestType), ex);
                        globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', ex.message + '\nStack Trace: ' + ex.stack);
                    }
                }
                
                
                db_log_parent_id = globals.dbLog('Starting to process a file using senderID ' + senderID + ' and unique ID ' + unique_id, 'wtp_request', 'import', 'wtp', 'http', application.getUUID(globals.org_id), 'web_to_print', 'Summary', null);
                scopes.avWebToPrint.sLogParentID = db_log_parent_id; // used for logging to this job import
                globals.dbLogWriteDetails(globals.UUIDtoString(db_log_parent_id), 'wtp_queue', null, scopes.avUtils.convertJSONStringToXML(sObjectJSON));
                
                var bIsAvantiApiQueued = false;
                var hcAvantiApiHeadlessClient;
                if (scopes.avWebToPrint.sImportOrderRequestType === scopes.avWebToPrint.sCreateOrderRequestType &&
                	sys_sender_fs.return_response_early) {
            		hcAvantiApiHeadlessClient = createAvantApiHeadlessClient();
            		if (!hcAvantiApiHeadlessClient || !hcAvantiApiHeadlessClient.isValid()) {
            			throw new Error('Failed to create a headless client: avanti_api. Sales Order is not going to be created.');
            		}            		
            	}
                                     	

                /** @type {{jobID: String}} */
                var result = createNewOrder(xmlRequestFile, senderID, db_log_parent_id, oTestingDetails, null, false);                
                
                /** @type {String} */
                var response_details = globals.formatResultFromObjectToString(result);                

                processResponseInLog(db_log_parent_id, senderID, unique_id, result, response_details, 'http', requestType);
                
                createOrderUniqueIdsRecord (result, sys_sender_fs.getRecord(1), unique_id, requestType);
                
                var pricingXml = produceResponceForEstimateOrderOrNewEstimate(result, db_log_parent_id);
                if (pricingXml) {                	
                	result = pricingXml; 
                }

                if (hcAvantiApiHeadlessClient &&
                	hcAvantiApiHeadlessClient.isValid() &&
					result.oXMLOrderDetails) {
                    var aXMLOrderDetails = new Array();
                    var oXMLOrder = result.oXMLOrderDetails;                    
                                        
                    oXMLOrder.sClientID = hcAvantiApiHeadlessClient.getClientID();
                    aXMLOrderDetails.push(oXMLOrder);
                    bIsAvantiApiQueued = true;   
                                        
                    hcAvantiApiHeadlessClient.queueMethod(null, "processPostOrderTasks", aXMLOrderDetails, web2PrintHeadlessClientCallBack);
                    
                    result.oXMLOrderDetails = null;
                }
                else {
                	// if avanti api headless client created and never used we need to shutdown it if not working
                    shutdownAvantiAPIHeadlessClientIfNotUsed (hcAvantiApiHeadlessClient, bIsAvantiApiQueued);
                }
                
                // Empty API response feature SL-27975
				if (requestType == scopes.avWebToPrint.sCreateOrderRequestType ||
					requestType == scopes.avWebToPrint.sUpdateOrderRequestType ||
					requestType == scopes.avWebToPrint.sNewEstimateRequestType) {
					if (!isApiResponseRequired(senderID)) {
						return null;
					}
				}
                
                if (scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AddXmlToResponse) === 1) {
                	var resultWithXmlRoot = new Object();
                	resultWithXmlRoot.xml = result;                
                	return resultWithXmlRoot;
                }
                else {
                	return result;
                }
            }
            catch (ex) {                
                if (ex.message) {                    
                    globals.dbLogUpdate(db_log_parent_id, 'Error! ' + globals.getWTPCreateUpdateMessage(requestType) + senderID + ' and unique ID ' + unique_id + ' failed to create a job based on exception ' + ex.message, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', globals.getWTPPurposeMessage(requestType), ex);
                    globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', ex.message + '\nStack Trace: ' + ex.stack);
                }
                var sMsgDetails = '';
                if (response_details) {
                    sMsgDetails = response_details.toString();
                }
                
                // if avanti api headless client created and never used we need to shutdown it if not working
                shutdownAvantiAPIHeadlessClientIfNotUsed (hcAvantiApiHeadlessClient, bIsAvantiApiQueued);
                
                throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, sMsgDetails];
            }

        }
        else if (requestType == scopes.avWebToPrint.sCancelOrderRequestType) {
        	
			db_log_parent_id = globals.dbLog('Starting to cancel a sales order using senderID ' + senderID, 'wtp_request', 'import', 'wtp', 'http', application.getUUID(globals.org_id), 'web_to_print', 'Summary', null);

			var cancelResult = cancelSalesOrderRestApi(senderID, xmlRequestFile, db_log_parent_id, oTestingDetails);
			if (cancelResult.jobID) {
				globals.dbLogUpdate(db_log_parent_id, 'Success! Request to cancel an order using sender ID ' + senderID + ' and unique ID ' + unique_id + ' completed with the result: ' + cancelResult.jobID, 'Success', cancelResult.jobID, 'wtp_request', 'import', 'wtp', 'http', 'Canceling Order');
			} else {
				globals.dbLogUpdate(db_log_parent_id, 'Error! Request to cancel an order using sender ID ' + senderID + ' failed to create a job based on error ' + cancelResult.status, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', 'Canceling Order');
			}			
            
            if (scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AddXmlToResponse) === 1) {
            	resultWithXmlRoot = new Object();
                resultWithXmlRoot.xml = cancelResult;                
                return resultWithXmlRoot;
            }
            else {
            	return result;
            }
            
        }
        else if (xmlRequestFile.xml != undefined) {
            switch (xmlRequestFile.xml.requestType) {
                case 'InventoryStatus':
                    response = getInventoryStatus(xmlRequestFile);
                    break;
                case 'Pricing':
                    response = getPricing(xmlRequestFile);
                    break;
                case 'JobStatus':
                	response = getJobStatus(xmlRequestFile);
                    break;
                default:
                    break;
            }
        }
        else {
        	throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, requestType + ' request type was not found'];
        }
        security.logout();

        return response;
    }
    catch (ex) {
        if (ex.message) {
            globals.dbLogUpdate(db_log_parent_id, 'Error! ' + globals.getWTPCreateUpdateMessage(requestType) + senderID + ' and unique ID ' + unique_id + ' failed to create a job based on exception ' + ex.message, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', globals.getWTPPurposeMessage(requestType), ex);
            globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', ex.message + '\nStack Trace: ' + ex.stack);
            sMsgDetails = '';
            if (response_details) {
                sMsgDetails = response_details.toString();
            }

            throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, sMsgDetails];
        }

        throw ex;
    }
    finally {
    	scopes.avWebToPrint.sImportOrderRequestType = '';
    }
}

/**
 * Return true if API response is required
 * @param {String} sSenderId
 *
 * @return {Boolean}
 * @properties={typeid:24,uuid:"E928BFED-536F-479C-BAD3-65EE804D4BFF"}
 */
function isApiResponseRequired (sSenderId) {
	var nNoApiResponse = scopes.avDB.SQLQuery("SELECT no_api_response FROM sys_sender WHERE org_id = ? AND sender_id = ?", null, [globals.org_id, sSenderId]);
	if (nNoApiResponse && nNoApiResponse > 0) {
		return false;
	}
	return true;
}

/**
 * Create Unique Id table record to prevent adding orders with the same Ids (SL-17344)
 * @param {{jobID: String}} oResult
 * @param {JSRecord<db:/avanti/sys_sender>} rSysSender
 * @param {String|Object} sUniqueIdValue
 * @param {String} sRequestType
 *
 * @properties={typeid:24,uuid:"F16F0F51-EB57-4112-9934-3EAA360400C0"}
 */
function createOrderUniqueIdsRecord (oResult, rSysSender, sUniqueIdValue, sRequestType) {
	if (oResult.jobID &&
		oResult.jobID.indexOf('Error') < 0 &&
		sUniqueIdValue &&
		rSysSender &&
		rSysSender.unique_id_path &&
		sRequestType == scopes.avWebToPrint.sCreateOrderRequestType) {
			
    	/** @type {JSFoundSet<db:/avanti/sys_order_import_unique_ids>} */
        var fsOrderUniqueIds = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_order_import_unique_ids');
        fsOrderUniqueIds.newRecord();
        fsOrderUniqueIds.org_id = globals.org_id;
        fsOrderUniqueIds.unique_id_path = rSysSender.unique_id_path;
        fsOrderUniqueIds.unique_id_value = sUniqueIdValue.toString();
        fsOrderUniqueIds.ord_document_number = oResult.jobID;
        
        databaseManager.saveData(fsOrderUniqueIds);
    }
}

/**
 * @return {plugins.headlessclient.JSClient} created headless client
 * @properties={typeid:24,uuid:"D68AD75A-544C-4C60-8ACD-7EC281A037F5"}
 */
function createAvantApiHeadlessClient () {
	/** @type {{ 
	 *  username:String,
	 *  password:String
	 *  }} */
    var user_info = globals.avBase_getServoyUser('jdf_process_jobs_user');
    
    // replace 4th param with ["nodebug"] to get it to run in dev env - otherwise it wont run - get 500 error
    var hcWebToPrint = plugins.headlessclient.createClient("avanti_api", user_info.username, user_info.password, null);
    
    return hcWebToPrint;
}

/**
 * Shutdown avanti api headless client if not used
 * @param {plugins.headlessclient.JSClient} hcAvantiApiHeadlessClient
 * @param {Boolean} bIsAvantiApiQueued
 *
 * @properties={typeid:24,uuid:"6740B8DA-16C7-4470-A5EB-CC08FB0A8A0E"}
 */
function shutdownAvantiAPIHeadlessClientIfNotUsed (hcAvantiApiHeadlessClient, bIsAvantiApiQueued) {
	// if avanti api headless client created and never used we need to shutdown it if not working
    if (hcAvantiApiHeadlessClient &&
    	hcAvantiApiHeadlessClient.isValid() &&
    	!bIsAvantiApiQueued) {    	
    	hcAvantiApiHeadlessClient.shutdown(true);
    }
}

/**
 * Delete Sales Order
 * @param {String} sOrderNUmber
 *
 * @properties={typeid:24,uuid:"C7FE579A-F2F5-4F6F-B5B0-6C23BEECED7E"}
 */
function deleteSalesOrder (sOrderNUmber) {
	if (!sOrderNUmber) {
		return;
	}
	/**@type {JSRecord<db:/avanti/sa_order>} */
    var rOrder = scopes.avDB.getRec('sa_order', ['ordh_document_type', 'ordh_document_num'], ['ORD', sOrderNUmber]);
    if (rOrder) {
    	rOrder.foundset.deleteRecord(rOrder);
    }
}

/**
 * Get Order Number from Job number
 * @param {String} sJobId
 *
 * @return {String}
 * @properties={typeid:24,uuid:"12E447F1-C1ED-4822-B396-68D7D69D07E8"}
 */
function getOrderNumber (sJobId) {
	var sOrderNumber = sJobId;
	var nJobSeparatorIndex = sJobId.lastIndexOf('-');
	if (nJobSeparatorIndex >= 0) {
		sOrderNumber = sJobId.substring(0,nJobSeparatorIndex);
	}
	return sOrderNumber;
}

/**
 * Produce XML response for RFQ and GetPricing http request types
 * @param {{jobID: String}} oResponse
 * @param {UUID} db_log_parent_id
 *
 * @return {Object} result xml
 * @properties={typeid:24,uuid:"921F8933-32C1-4C1F-A7D8-BC437CA71C25"}
 */
function produceResponceForEstimateOrderOrNewEstimate (oResponse, db_log_parent_id) {	
	if (scopes.avWebToPrint.sImportOrderRequestType !== scopes.avWebToPrint.sNewEstimateRequestType &&
		scopes.avWebToPrint.sImportOrderRequestType !== scopes.avWebToPrint.sEstimateOrderRequestType) {
			return null;
		}
	
	if (!oResponse.jobID || oResponse.jobID.indexOf('Error') >= 0) {
		return null;
	}
	
	var sOrderNumber = getOrderNumber(oResponse.jobID);
		
	var xmldoc = createXMLDocumentForRFQAndGetPricing(sOrderNumber);
    
    var sXmlString = scopes.avUtils.writeXMLtoString(xmldoc);  
    
    var xmlConvertedToJson = new Object();	
	try {
		xmlConvertedToJson = Packages.org.json.XML.toJSONObject(sXmlString);
	} catch(ex) {
		throw new Error('Failed to create an order because file could not be converted to XML');
	}
	
	if (scopes.avWebToPrint.sImportOrderRequestType === scopes.avWebToPrint.sEstimateOrderRequestType) {
		deleteSalesOrder(sOrderNumber);
		globals.dbLog('Completed temporary order removal', 'wtp_request', 'import', 'wtp', '', application.getUUID(globals.org_id), 'logging', 'Detail', db_log_parent_id, null, null, null);
		globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', sXmlString);
	}
   	
    return xmlConvertedToJson;
}

/**
 * Creates XML Document for GetPricing or RFQ request types
 * @param {String} sOrderNumber Sales Order Number
 *
 * @return {org.w3c.dom.Document} returns xml document
 * @properties={typeid:24,uuid:"291842D9-B955-4BFC-B7C4-F43C1BBC726D"}
 */
function createXMLDocumentForRFQAndGetPricing (sOrderNumber) {
	var xmldoc = Packages.javax.xml.parsers.DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
	
	var bodyNode = xmldoc.createElement('soap:Body');
	
    var pricingResponseNode = xmldoc.createElement('Pricing_ListResponse');
    pricingResponseNode.setAttribute('xmlns','Avanti.Webservice.Pricing');
    
    var pricingListResultNode = xmldoc.createElement('Pricing_ListResult');
    
    if (scopes.avWebToPrint.sImportOrderRequestType === scopes.avWebToPrint.sNewEstimateRequestType) {
    	var currencyNode = xmldoc.createElement('estimateNo');	
    	currencyNode.setTextContent(sOrderNumber);
    	pricingListResultNode.appendChild(currencyNode);
	}
    
    pricingListResultNode.appendChild(buildLineItemsXmlNode (sOrderNumber, xmldoc));
    
    addOrderPricingXmlResponse(sOrderNumber, xmldoc, pricingListResultNode);
    
    pricingResponseNode.appendChild(pricingListResultNode);
    bodyNode.appendChild(pricingResponseNode);
    xmldoc.appendChild(bodyNode);
    
    return xmldoc;
}

/**
 * Adds Order Information to resuilt XML
 * @param {String} sOrderNumber 
 * @param {org.w3c.dom.Document} xmldoc
 * @param {org.w3c.dom.Element} pricingListResultNode
 *
 * @properties={typeid:24,uuid:"6DA27456-CD9F-4FE4-84CE-F8E80DD8B1F7"}
 */
function addOrderPricingXmlResponse (sOrderNumber, xmldoc, pricingListResultNode) {
	var oSQL = new Object()
	
	oSQL.sql = "SELECT 	sorh.ordrevh_subtotal_amount, \
						sorh.ordrevh_postage_amount, \
						sorh.ordrevh_shipping_amount, \
						sorh.ordrevh_contract_charges, \
						sorh.ordrevh_total_taxes, \
						sorh.ordrevh_total_amount \
				FROM   sa_order AS so \
					INNER JOIN sa_order_revision_header AS sorh \
					ON sorh.ordh_id = so.ordh_id \
				WHERE  so.ordh_document_num = ? AND \
				so.org_id = ?";

	oSQL.args = [sOrderNumber, globals.org_id];

	/*** @type {JSDataSet} */
	var dsDataOrder = globals.avUtilities_sqlDataset(oSQL);
	var oOrderInfo = dsDataOrder.getRowAsArray(1);
	
	var subTotalNode = xmldoc.createElement('SubTotal');
	var nSubTotal = parseFloat(oOrderInfo[0]);
	subTotalNode.setTextContent(globals.avUtilities_roundNumber(nSubTotal, 2).toString());
	pricingListResultNode.appendChild(subTotalNode);
	
	var postageNode = xmldoc.createElement('Postage');	
	var nPostage = parseFloat(oOrderInfo[1]);	
	postageNode.setTextContent(globals.avUtilities_roundNumber(nPostage, 2).toString());
	pricingListResultNode.appendChild(postageNode);
	
	var freightRevenueNode = xmldoc.createElement('FreightRevenue');	
	var nFreightRevenue = parseFloat(oOrderInfo[2]);	
	freightRevenueNode.setTextContent(globals.avUtilities_roundNumber(nFreightRevenue, 2).toString());
	pricingListResultNode.appendChild(freightRevenueNode);
	
	var contractChargesNode = xmldoc.createElement('ContractCharges');	
	var nContractCharges = parseFloat(oOrderInfo[3]);	
	contractChargesNode.setTextContent(globals.avUtilities_roundNumber(nContractCharges, 2).toString());
	pricingListResultNode.appendChild(contractChargesNode);
	
	var totalTaxesNode = xmldoc.createElement('TotalTaxes');	
	var nTotalTaxes = parseFloat(oOrderInfo[4]);	
	totalTaxesNode.setTextContent(globals.avUtilities_roundNumber(nTotalTaxes, 2).toString());
	pricingListResultNode.appendChild(totalTaxesNode);
	
	var totalNode = xmldoc.createElement('Total');	
	var nTotal = parseFloat(oOrderInfo[5]);	
	totalNode.setTextContent(globals.avUtilities_roundNumber(nTotal, 2).toString());
	pricingListResultNode.appendChild(totalNode);
	
	
	var currencyNode = xmldoc.createElement('Currency');	
	currencyNode.setTextContent(getCurrency());
	pricingListResultNode.appendChild(currencyNode);
}

/**
 * Returns organization currency iso code
 * @return {String} returns currency iso code
 * @properties={typeid:24,uuid:"C8C8595C-5B7F-4788-A191-85EE052D5839"}
 */
function getCurrency () {
	if ( _to_sys_organization.org_default_curr_id ) {
		return _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code;
	} 
	
	return '';
}

/**
 * Creates Line Items information XML part
 * @param {String} sOrderNumber
 * @param {org.w3c.dom.Document} xmldoc
 *
 * @return {org.w3c.dom.Node} xml part for LineItems
 * @properties={typeid:24,uuid:"CE5886EA-343F-425D-8919-F13075206A58"}
 */
function buildLineItemsXmlNode (sOrderNumber, xmldoc) {
	
	var itemsNode = xmldoc.createElement('Items');

	var oSQL = new Object()
	
	oSQL.sql = "SELECT 	sord.sequence_nr, \
						ii.itemtype_code, \
						systw_item.sysworktype_code, \
						systw.sysworktype_code, \
						stw_by_display_code_id.worktype_code, \
						ii_by_display_code_id.item_code, \
						sord.ordrevd_prod_desc, \
						sord.ordrevd_qty_ordered, \
						sord.ordrevd_unit_price, \
						suom.uom_decimals_price, \
						sord.ordrevd_disc_amt, \
						sord.ordrevd_extended_price \
				FROM   sa_order AS so \
					INNER JOIN sa_order_revision_header AS sorh \
					ON sorh.ordh_id = so.ordh_id \
					LEFT OUTER JOIN sa_order_revision_detail AS sord \
                    ON sorh.ordrevh_id = sord.ordrevh_id \
                    LEFT OUTER JOIN sa_task_worktype AS stw_by_display_code_id \
                    ON stw_by_display_code_id.worktype_id = sord.display_code_id \
                    LEFT OUTER JOIN in_item AS ii_by_display_code_id \
                    ON ii_by_display_code_id.item_id = sord.display_code_id \
                    LEFT OUTER JOIN in_item AS ii \
                    ON ii.item_id = sord.item_id \
                    LEFT OUTER JOIN sa_task_worktype AS stw \
                    ON stw.worktype_id = sord.worktype_id \
                    LEFT OUTER JOIN sys_task_worktype AS systw_item \
                    ON stw.sysworktype_id = systw_item.sysworktype_id \
                    LEFT OUTER JOIN sys_task_worktype AS systw \
                    ON sord.sysworktype_id = systw.sysworktype_id \
                    LEFT OUTER JOIN sys_unit_of_measure AS suom \
                    ON suom.uom_id = sord.uom_id \
                WHERE  	so.ordh_document_num = ? AND \
               		 	so.org_id = ? AND \
               		 	so.ordh_document_type = ? \
               	ORDER BY sord.sequence_nr";

	var sEstimateOrOrder = 'ORD'
	if (scopes.avWebToPrint.sImportOrderRequestType === scopes.avWebToPrint.sNewEstimateRequestType) {
		sEstimateOrOrder = 'EST';
	}
	oSQL.args = [sOrderNumber, globals.org_id, sEstimateOrOrder];

	/*** @type {JSDataSet} */
	var dsDataOrder = globals.avUtilities_sqlDataset(oSQL);

	for (var idx = 1; idx <= dsDataOrder.getMaxRowIndex(); idx++) {
		var lineItemInfo = dsDataOrder.getRowAsArray(idx)
		
		var itemNode = xmldoc.createElement('Item');				
		
		var itemNumberNode = xmldoc.createElement('ItemNumber');
		itemNumberNode.setTextContent(lineItemInfo[0]);
		itemNode.appendChild(itemNumberNode);
		
		
		var sItemType = 'WIZ';
		var sItemTypeCode = lineItemInfo[1];
		var sItemSysworktypeCode = lineItemInfo[2];
		var sSysworktypeCode = lineItemInfo[3];		 
		if (sItemTypeCode && sItemSysworktypeCode &&
		    (sItemTypeCode === scopes.avUtils.ITEM_TYPE.Product ||
		     sItemTypeCode === scopes.avUtils.ITEM_TYPE.FinishedGood)) {
		    sItemType = sItemSysworktypeCode;
		}
		else if (sSysworktypeCode) {
			sItemType = sItemSysworktypeCode;
		}		
		var itemTypeNode = xmldoc.createElement('ItemType');
		itemTypeNode.setTextContent(sItemType);
		itemNode.appendChild(itemTypeNode);
		
		var sWorktypeCode = lineItemInfo[4];
		if (!sWorktypeCode) {
			sWorktypeCode = lineItemInfo[5];
		}
		var itemCodeNode = xmldoc.createElement('ItemCode');
		itemCodeNode.setTextContent(sWorktypeCode);
		itemNode.appendChild(itemCodeNode);
		
		var itemDescriptionNode = xmldoc.createElement('ItemDescription');
		itemDescriptionNode.setTextContent(lineItemInfo[6]);
		itemNode.appendChild(itemDescriptionNode);
		
		var itemOrderedNode = xmldoc.createElement('ItemOrdered');
		itemOrderedNode.setTextContent(lineItemInfo[7]);
		itemNode.appendChild(itemOrderedNode);
		
		var itemUnitPriceNode = xmldoc.createElement('ItemUnitPrice');
		var fUnitPrice = parseFloat(lineItemInfo[8]);
		var nUomDecimalPrice = parseInt(lineItemInfo[9]);
		var sUnitPriceRounded = scopes.avUtils.calculateUnitPriceWithoutFormat(nUomDecimalPrice, fUnitPrice);		
		itemUnitPriceNode.setTextContent(sUnitPriceRounded);
		itemNode.appendChild(itemUnitPriceNode);
		
		var itemPercentDiscountNode = xmldoc.createElement('PercentDiscount');
		itemPercentDiscountNode.setTextContent(lineItemInfo[10]);
		itemNode.appendChild(itemPercentDiscountNode);
		
		var itemExtPriceNode = xmldoc.createElement('ItemExtPrice');
		var nExtPrice = parseFloat(lineItemInfo[11]);
		itemExtPriceNode.setTextContent(globals.avUtilities_roundNumber(nExtPrice, 2).toString());
		itemNode.appendChild(itemExtPriceNode);
						
		itemsNode.appendChild(itemNode);
		
	}
	
	return itemsNode;
}

/**
 * @AllowToRunInFind
 * 
 * Cancel Sales Order
 * @param {String} senderID
 * @param xmlRequestFile
 * @param db_log_parent_id
 * @param {{sPathToMappingFile:String, sInputFileName:String, sTestCode:String, bUseOriginalLogic: Boolean}} oTestingDetails
 *
 * @return
 * @properties={typeid:24,uuid:"6214DD50-D2E4-49E7-B8F0-E57C31C9F782"}
 */
function cancelSalesOrderRestApi (senderID, xmlRequestFile, db_log_parent_id, oTestingDetails) {
	try {

        try {
            // Remove namespaces:
            var sObjectJSON = JSON.stringify(xmlRequestFile);
            var nNumberOfNamespaces = utils.stringPatternCount(sObjectJSON, 'xmlns:');
            for (var nNamespaceIndex = 1; nNamespaceIndex <= nNumberOfNamespaces; nNamespaceIndex++) {
                var nPositionIndex = utils.stringPosition(sObjectJSON, 'xmlns:', 1, nNamespaceIndex);
                var sNamespace = sObjectJSON.substring(nPositionIndex + 5, utils.stringPosition(sObjectJSON, ':', nPositionIndex + 7, 1) - 2);
                sObjectJSON = utils.stringReplace(sObjectJSON, sNamespace + ':', '');
            }
            xmlRequestFile = JSON.parse(sObjectJSON);

        }
        catch (ex) {
            if (ex.message) {
                application.output(ex.message)
                globals.dbLogUpdate(db_log_parent_id, 'Error! Request to cancel an order using sender ID ' + senderID + ' processing may still finish but encountered exception ' + ex.message, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', 'Cancelling Order', ex);
                globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', ex.message + '\nStack Trace: ' + ex.stack);
            }
        }
        
        scopes.avWebToPrint.sLogParentID = db_log_parent_id; // used for logging to this job import       

        var result = cancelOrder(xmlRequestFile, senderID, db_log_parent_id, oTestingDetails, null, false);

        /** @type {String} */
        var response_details = globals.formatResultFromObjectToString(result);        
        
        if (!result.jobID) {
        	throw new Error (result.status);
        }
        
        return result;
    }
    catch (ex) {                
        if (ex.message) {                    
            globals.dbLogUpdate(db_log_parent_id, 'Error! Request to cancel an order using sender ID ' + senderID + ' failed to create a job based on exception ' + ex.message, 'Error', null, 'wtp_request', 'import', 'wtp', 'http', 'Cancelling Order', ex);
            globals.dbLogWriteDetails(db_log_parent_id, 'wtp_request', ex.message);
        }
        var sMsgDetails = '';
        if (response_details) {
            sMsgDetails = response_details.toString();
        }
        throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, sMsgDetails];
    }
}

/**
 *  Authenticate Web Service
 *
 *  @param {String} username
 *  @param {String} password
 *
 * @properties={typeid:24,uuid:"E8FC9EEE-B224-4155-B194-7383D8445EBC"}
 * @AllowToRunInFind

 function ws_authenticate(username, password) {
 application.output('authenticate user: ' +  username + ' pass '+ password)

 // Get the web service code, username and password.
 var orgCode = username.substring(0,username.indexOf('\\'))
 var orgUsername = username.substring(username.indexOf('\\')+1)

 if(username == null || username == '' || orgUsername == null || orgUsername == '') {
 for (var i = 0; i< arguments.length; i++) {
 if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
 application.output('URL Path addition: ' + arguments[i])
 } else {
 for (var key in arguments[i]) {
 application.output('Query Parameter "' + key + '", values:  "' + arguments[i][key][0]+ '"')
 if(key == 'Username') {
 username = arguments[i][key][0]
 orgCode = username.substring(0,username.indexOf('\\'))
 orgUsername = username.substring(username.indexOf('\\')+1)
 }
 if(key == 'Password') {
 password = arguments[i][key][0]
 }
 }
 }
 }
 }

 // Check that a web service code was included.
 if(orgCode.length < 1) {
 return false
 }

 /** @type {JSFoundSet<db:/avanti/sys_organization>}
 var sys_organization_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_organization');

 if(sys_organization_fs.find() || sys_organization_fs.find()) {
 sys_organization_fs.org_web_service_code = orgCode
 if(sys_organization_fs.search() > 0) {

 // Call authentication module/method, authentication is done on server not on the client.
 var _authObj = new Object()
 _authObj.framework_db = forms.svy_sec_login.vFramework_db
 _authObj.username = orgUsername
 _authObj.password = password
 if(utils.hasRecords(sys_organization_fs.sys_organization_to_sec_owner)){
 _authObj.owner = sys_organization_fs.sys_organization_to_sec_owner.name
 }

 /** @type {{owner_id:String,user_id:String,error:String, success:Number}}
 var _return = security.authenticate('svy_sec_authenticate', 'svy_sec_checkUserPassword',[_authObj])
 if(_return != null && _return.success)
 {
 // set user id
 globals.svy_sec_lgn_user_id = _return.user_id

 //set owner id
 globals.svy_sec_lgn_owner_id = _return.owner_id

 globals.org_id = sys_organization_fs.org_id
 } else {
 return false
 }
 } else {
 return false
 }
 }
 return true
 }
 */

/**
 * Get the status of an item based on Item Code or description
 *
 * @param {{xml:{requestType, Jobs, Item, pricingRequest:{customerCode:String, contactFullName: String, externalJobID: String, shippingAddresses: Object, product:Array, workTemplate: Array, inventory:{item:Object}}}}}  xmlRequestFile XML file from the HTTP Post
 *
 * @return
 * @properties={typeid:24,uuid:"03106548-E71B-4973-B54D-3DFD0C80E864"}
 * @AllowToRunInFind
 */
function getInventoryStatus(xmlRequestFile) {
	var arrayOfObjects = new Array()
	if (xmlRequestFile.xml.Item != undefined) {
		/** @type {JSFoundSet<db:/avanti/in_item>} */
		var item_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');

		for each (/** @type {{ItemCode:String, ItemDescription:String}}*/
		var item in xmlRequestFile.xml.Item) {
			var item_code_param = item.ItemCode
			var item_desc_param = item.ItemDescription
			var avail_qty = new Object()
			if (item_code_param) {
				if (item_fs.find() || item_fs.find()) {
					item_fs.item_code = item_code_param
					if (item_fs.search() == 1) {
						avail_qty.item_code = item_fs.item_code
						avail_qty.item_desc = item_fs.item_desc1
						avail_qty.item_onhand_qty = item_fs.item_onhand_qty
						avail_qty.item_committed_qty = item_fs.item_committed_qty
						avail_qty.item_available_qty = item_fs.item_available_qty
						avail_qty.item_unavailible_qty = item_fs.item_unavailable_qty
						avail_qty.item_unusable_qty = item_fs.item_unusable_qty
						arrayOfObjects.push(avail_qty)
					}
				}
			}
			if (item_desc_param) {
				if (item_fs.find() || item_fs.find()) {
					item_fs.item_desc1 = item_desc_param
					if (item_fs.search() == 1) {
						avail_qty.item_code = item_fs.item_code
						avail_qty.item_desc = item_fs.item_desc1
						avail_qty.item_onhand_qty = item_fs.item_onhand_qty
						avail_qty.item_committed_qty = item_fs.item_committed_qty
						avail_qty.item_available_qty = item_fs.item_available_qty
						avail_qty.item_unavailible_qty = item_fs.item_unavailable_qty
						avail_qty.item_unusable_qty = item_fs.item_unusable_qty
						arrayOfObjects.push(avail_qty)
					}
				}
			}
		}

		// not found or cannot search
		return arrayOfObjects;
	}
	return new Object()
}

/**
 * Return job status of the requested items
 *
 * @param {{xml:{requestType, Item, Jobs, pricingRequest:{customerCode:String, contactFullName: String, externalJobID: String, shippingAddresses: Object, product:Array, workTemplate: Array, inventory:{item:Object}}}}}  xmlRequestFile XML file from the HTTP Post
 *
 * @return
 * @properties={typeid:24,uuid:"42953ABF-5F53-402C-9990-065E4B087AC0"}
 * @AllowToRunInFind
 */
function getJobStatus(xmlRequestFile) {

	var arrayOfObjects = new Array()
	if (xmlRequestFile.xml.Jobs != undefined) {
		/** @type {JSFoundSet<db:/avanti/prod_job>} */
		var prod_job_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job');

		for each (var job in xmlRequestFile.xml.Jobs) {
			var job_number_param = job
			var job_status = new Object()
			if (job_number_param) {
				if (prod_job_fs.find() || prod_job_fs.find()) {
					prod_job_fs.job_number = job_number_param
					if (prod_job_fs.search() == 1) {
						job_status.status = prod_job_fs.jobstat_id
						arrayOfObjects.push(job_status)
					}
				}
			}
		}
	}
	// not found or cannot search
	return arrayOfObjects
}

/**
 * Send Shipping Info via HTTP Post to a specified URL
 *
 * @param {String} job_number_param  The job number to send shipping information for.
 * @param {String} url  The url to send the HTTP Post with the shipping information to.
 * @param {String} username  The username to login to the URL
 * @param {String} password  The password to login to the URL
 *
 * @properties={typeid:24,uuid:"611147CE-9EA6-4E38-802D-A29770D6597B"}
 *
 *
 * @AllowToRunInFind
 */
function sendShippingInfo(job_number_param, url, username, password) {
	/** @type {JSFoundSet<db:/avanti/prod_job>} */
	var prod_job_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job');

	if (job_number_param) {
		if (prod_job_fs.find() || prod_job_fs.find()) {
			prod_job_fs.job_number = job_number_param
			if (prod_job_fs.search() == 1) {
				var httpClient = plugins.http.createNewHttpClient();

				var request = httpClient.createPostRequest(utils.stringTrim(url));

				request.addHeader('Content-type', 'application/xml');

				var shipping_info = '<?xml version="1.0" encoding="UTF-8"?><XML><ShippingInfo><Addr1>';

				// GD - 2013-06-21: Fixed JSDoc warnings on the relationships
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_addr1 + '</Addr1><Addr2>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_addr2 + '</Addr2><Addr3>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_addr3 + '</Addr3><City>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_city + '</City><StateProv>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_stateprov + '</StateProv><Postal>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_postal + '</Postal><Country>';
				shipping_info += prod_job_fs.prod_job_to_sa_order_revision_detail["sa_order_revision_detail_to__v_job_ord"].ord_ship_country + '</Country></ShippingInfo></XML>';

				request.setBodyContent(shipping_info);

				// Send the request and create a JMF file for the response received.
				var response = request.executeRequest(username, password);
				var responseText = response.getResponseBody();

				application.output('charset: ' + response.getCharset() + ' status: ' + response.getStatusCode() + ' response: ' + responseText);
				
				httpClient.close();
			}
		}
	}
}

/**
 * Return pricing of the requested items
 *
 * @param {{xml:{requestType, Jobs, Item, pricingRequest:{customerCode:String, contactFullName: String, externalJobID: String, shippingAddresses: Object, product:Array, workTemplate: Array, inventory:{item:Object}}}}}  xmlRequestFile XML file from the HTTP Post
 * @return
 * @properties={typeid:24,uuid:"5F3B92F8-F2B2-46C1-BAD4-D7B096579C83"}
 * @AllowToRunInFind
 */
function getPricing(xmlRequestFile) {
	var validateResult = validatePricingRequest(xmlRequestFile);

	if (validateResult == 'Success') {
		var items = new Array()
		if (xmlRequestFile.xml.pricingRequest != undefined) {

			//			var customerCode = xmlRequestFile.xml.pricingRequest.customerCode
			//			var contactFullName = xmlRequestFile.xml.pricingRequest.contactFullName
			//			var externalJobID = xmlRequestFile.xml.pricingRequest.externalJobID

			for each (/** @type{{shipToCode:String,quantityToSend:String, shippingMethod:String, shippingAddressLine:{addressLine1:String,addressLine2:String, addressLine3:String, city:String, stateProv:String, postal:String, country:String}}} */
			var shippingAddress in xmlRequestFile.xml.pricingRequest.shippingAddresses) {
				//				var shipToCode = shippingAddress.shipToCode
				//				var quantityToSend = shippingAddress.quantityToSend
				//				var shippingMethod = shippingAddress.shippingMethod

				if (shippingAddress.shippingAddressLine != undefined) {
					//					var shipppingAddressLine1 = shippingAddress.shippingAddressLine.addressLine1
					//					var shipppingAddressLine2 = shippingAddress.shippingAddressLine.addressLine2
					//					var shipppingAddressLine3 = shippingAddress.shippingAddressLine.addressLine3
					//					var shipppingAddressCity = shippingAddress.shippingAddressLine.city
					//					var shipppingAddressStateProv = shippingAddress.shippingAddressLine.stateProv
					//					var shipppingAddressPostal = shippingAddress.shippingAddressLine.postal
					//					var shipppingAddressCountry = shippingAddress.shippingAddressLine.country
				}
			}
			//			for each(var product in xmlRequestFile.xml.pricingRequest.product)
			//			{
			//
			//			}

			//			for each(/** @type{{workTemplateCode:String,workTemplateQuantity:String, workTemplateDescription:String, workTemplateNumberPages:Number, workTemplateTrimWidth:Number, workTemplateTrimLength:Number, workTemplatePaperID:String}} */
			//				var template in xmlRequestFile.xml.pricingRequest.workTemplate)
			//			{
			//				var code = template.workTemplateCode
			//				var quantity = template.workTemplateQuantity
			//				var description = template.workTemplateDescription
			//				var numberOfPages = template.workTemplateNumberPages
			//				var trimWidth = template.workTemplateTrimWidth
			//				var trimLength = template.workTemplateTrimLength
			//				var paperID = template.workTemplatePaperID
			//			}

			for each (/** @type{{itemCode:String}} */
			var item in xmlRequestFile.xml.pricingRequest.inventory.item) {
				var price = getPriceOfItem(item)
				if (price != null) {
					items.push(price)
				}
			}
			// not found or cannot search
			return items;
		}
	} else {
		items.push(validateResult)
		return items
	}
	return null
}

/**
 * Returns the price of an item.
 *
 * @param {{itemCode:String}} item The item to get the price for.
 *
 * @return
 * @properties={typeid:24,uuid:"6D07C200-117C-4923-894F-8D9E7FB0964F"}
 * @AllowToRunInFind
 */
function getPriceOfItem(item) {
	/** @type {JSFoundSet<db:/avanti/in_item>} */
	var item_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
	var item_code_param = item.itemCode
	var price = new Object()
	if (item_code_param) {
		if (item_fs.find() || item_fs.find()) {
			item_fs.item_code = item_code_param
			if (item_fs.search() == 1) {
				price.item_code = item_fs.item_code
				price.item_desc = item_fs.item_desc1
				price.item_uom_price = item_fs.in_item_to_in_item_selling_uom.itemselluom_list_price
				return price
			}
		}
	}

	return null
}

/**
 * Validate if the XML passed against a Schema.
 *
 * @param {Object} xmlRequestFile The XML file passed in from the Web Service
 *
 * @return
 * @properties={typeid:24,uuid:"178C3A56-8CB3-40B7-BD0C-EED7691D9013"}
 */
function validatePricingRequest(xmlRequestFile) {
	var schemaFactory = Packages.javax.xml.validation.SchemaFactory.newInstance("http://www.w3.org/2001/XMLSchema")
	var schemaFileLocation = new Packages.java.io.File("C:\\AvantiPricing.xsd")
	var schema = schemaFactory.newSchema(schemaFileLocation)

	var schemaValidator = schema.newValidator()

	var xmlFile = new Packages.java.io.File("C:\\testPricing.xml")

	var source = new Packages.javax.xml.transform.stream.StreamSource(xmlFile)
	try {
		schemaValidator.validate(source)
		return 'Success'
	} catch (ex) {
		application.output('Error with validation: ' + ex.message)
		return ex.message
	}
}

/**
 * Create a new order in the format of the Avanti XML Schema located at xml\job.xml of the application server
 * using the data from the XML File parameter that was attached in the HTTP Post request.
 *
 * @param {Object} xmlRequestFile XML file from the HTTP Post
 * @param {String} senderID ID of where the XML came from
 * @param {UUID} db_log_parent_id_param
 * @param {{sPathToMappingFile:String, sInputFileName:String, sTestCode:String, bUseOriginalLogic: Boolean}} oTestingDetails
 * @param {UUID} [sys_sender_id_param] ID of where the XML came from
 * @param {Boolean} bIsAutoWTPImport for Auto WTP from FTP/SFTP we need to ignore flag return_response_early
 *
 * @return
 * @properties={typeid:24,uuid:"18BAC873-33C6-4985-B6F7-4BA1BA691B83"}
 * @AllowToRunInFind
 */
function createNewOrder(xmlRequestFile, senderID, db_log_parent_id_param, oTestingDetails, sys_sender_id_param, bIsAutoWTPImport) {
	globals.devLogHC("createNewOrder, senderID: " + senderID);
	jobID = '';
	
	//globals.initAvDocsGlobalVariables();
	forms["_docs_base"].init_avDocs_oDocs();
	
	scopes.avWebToPrint.jobDetails = new Object();
	scopes.avWebToPrint.jobDetails.evaluationError = '';
	scopes.avWebToPrint.itemDetails = new Array();
	scopes.avWebToPrint.rOverrideEstimate = null;
	scopes.avWebToPrint.bCalculatedPagesets = false;
	globals.avBase_invoiceGPFeatureTokenEnabled = (_to_sys_organization.org_invoice_gp_feature_token == "EnableAvantiGPFeature515" ? true: false);

	// sl-27929 - this is used in auto-scheduling an xml order, but wasnt being set for headless client (its set when user logs in) 
	globals.avUserTimeZone = Packages.java.util.TimeZone.getTimeZone(i18n.getCurrentTimeZone());

	if (senderID) {
		scopes.avWebToPrint.uXMLSenderID = scopes.avDB.SQLQuery("SELECT sys_sender_id FROM sys_sender WHERE org_id = ? AND sender_id = ?", null, [globals.org_id, senderID]);
	}
	else {
		scopes.avWebToPrint.uXMLSenderID = null;
	}
	
	var response = new Object();
	
	var app_server_dir = globals.avUtilities_serverGetPath("/xml/job.xml");
	if (!scopes.avUtils.doesFileExist(app_server_dir)) { // check if in developer or solution deployment
		app_server_dir = globals.avUtilities_serverGetPath("/server/webapps/ROOT/xml/job.xml");
	}
	
	// Create a new file based on the Avanti XML Schema
    var new_file = null;
    if (scopes.avUtils.doesFileExist(app_server_dir)) {
        new_file = plugins.file.readTXTFile(app_server_dir);
    }

    if (new_file) {
        // Remove <?xml ... > line and removes the whitespace.
        new_file = new_file.replace(/<\/?[^>]+(>|$)/g, function(strMatch, p1) {
                return ( /xml version/.test(strMatch) ) ? '' : strMatch
            })
        // Remove comments
        new_file = new_file.replace(/<!.*?>/g, '');
        new_file = new_file.replace(/^\s+/, "");

        // Create new JS XML object that represents the Avanti XML Schema
        var xml_doc = plugins.XML.XMLDocument([app_server_dir]);      
        globals.devLogHC("createNewOrderFromXML - xml doc created, senderID: " + senderID);

        var bUseOriginalLogic = true;
        if (utils.hasRecords(_to_sys_organization)) {
            globals.avBase_plantID_for_new_recs = _to_sys_organization.org_default_plant_id;
            globals.avBase_plantID = _to_sys_organization.org_default_plant_id;
            if (utils.hasRecords(_to_sys_organization.sys_organization_to_sys_plant$default_plant)) {
                globals.avBase_divID_for_new_recs = _to_sys_organization.sys_organization_to_sys_plant$default_plant.div_id;
                globals.avBase_divID = _to_sys_organization.sys_organization_to_sys_plant$default_plant.div_id;
            }
            if (_to_sys_organization.use_new_wtp_logic == 0) {
                bUseOriginalLogic = true;
            }
            else {
                bUseOriginalLogic = false;
            }
        }

        if (oTestingDetails.bUseOriginalLogic != null && oTestingDetails.bUseOriginalLogic == 'true') {
            bUseOriginalLogic = true;
        }
        else if (oTestingDetails.bUseOriginalLogic != null && oTestingDetails.bUseOriginalLogic == 'false') {
            bUseOriginalLogic = false;
        }
        
        scopes.avWebToPrint.bUseOriginalLogic = bUseOriginalLogic;
        
        try {
        	globals.oImportOrderXML = JSON.stringify(xmlRequestFile);
        } catch (ex) {
        	// ignore exception because we already handling it earlier
        }
		
        if (bUseOriginalLogic) {
            globals.devLogHC("createNewOrderFromXML - processXMLFileRecursively");
            response = processXMLFileRecursively(xml_doc, xmlRequestFile, senderID, db_log_parent_id_param, oTestingDetails, sys_sender_id_param, bIsAutoWTPImport);
        }
        else {
            globals.devLogHC("createNewOrderFromXML - processXMLFile");
            response = scopes.avWebToPrint.processXMLFile(xml_doc, xmlRequestFile, senderID, oTestingDetails, db_log_parent_id_param, sys_sender_id_param, bIsAutoWTPImport, false);
        }
        return response;
    }
    response.warning = 'Could not load job.xml';
    return response;
}

/**
 * Create a new order in the format of the Avanti XML Schema located at xml\job.xml of the application server
 * using the data from the XML File parameter that was attached in the HTTP Post request.
 *
 * @param {Object} xmlRequestFile XML file from the HTTP Post
 * @param {String} senderID ID of where the XML came from
 * @param {UUID} db_log_parent_id_param
 * @param {{sPathToMappingFile:String, sInputFileName:String, sTestCode:String, bUseOriginalLogic: Boolean}} oTestingDetails
 * @param {UUID} [sys_sender_id_param] ID of where the XML came from
 * @param {Boolean} bIsAutoWTPImport for Auto WTP from FTP/SFTP we need to ignore flag return_response_early
 *
 * @return
 * @properties={typeid:24,uuid:"2A5DEAF5-2601-4876-9D39-EA0BBAA22C7D"}
 * @AllowToRunInFind
 */
function cancelOrder(xmlRequestFile, senderID, db_log_parent_id_param, oTestingDetails, sys_sender_id_param, bIsAutoWTPImport) {	
	jobID = '';
	
	scopes.avWebToPrint.jobDetails = new Object();
	scopes.avWebToPrint.jobDetails.evaluationError = '';
	scopes.avWebToPrint.itemDetails = new Array();
	scopes.avWebToPrint.rOverrideEstimate = null;
	scopes.avWebToPrint.bCalculatedPagesets = false;
	
	forms["_docs_base"].init_avDocs_oDocs();
	

	if (senderID) {
		scopes.avWebToPrint.uXMLSenderID = scopes.avDB.SQLQuery("SELECT sys_sender_id FROM sys_sender WHERE org_id = ? AND sender_id = ?", null, [globals.org_id, senderID]);
	}
	else {
		scopes.avWebToPrint.uXMLSenderID = null;
	}
	
	var response = new Object();
	
	//	var app_server_dir = plugins.it2be_tools.server().getApplicationServerDir()
	var app_server_dir = globals.avUtilities_serverGetPath("/xml/job.xml");
	if (!scopes.avUtils.doesFileExist(app_server_dir)) { // check if in developer or solution deployment
		app_server_dir = globals.avUtilities_serverGetPath("/server/webapps/ROOT/xml/job.xml");
	}
	// Create a new file based on the Avanti XML Schema
	var new_file = scopes.avUtils.doesFileExist(app_server_dir) ? plugins.file.readTXTFile(app_server_dir) : null;

    if (new_file) {
        // Remove <?xml ... > line and removes the whitespace.
        new_file = new_file.replace(/<\/?[^>]+(>|$)/g, function(strMatch, p1) {
                return ( /xml version/.test(strMatch) ) ? '' : strMatch
            })
        // Remove comments
        new_file = new_file.replace(/<!.*?>/g, '');
        new_file = new_file.replace(/^\s+/, "");

        // Create new JS XML object that represents the Avanti XML Schema
        var xml_doc = plugins.XML.XMLDocument([app_server_dir]);
       
        response = scopes.avWebToPrint.processXMLFile(xml_doc, xmlRequestFile, senderID, oTestingDetails, db_log_parent_id_param, sys_sender_id_param, bIsAutoWTPImport, true);
        
        return response;
    }
    response.warning = 'Could not load job.xml';
    return response;
}

/**
 * Checks if the XMl Schema Path is repeatable.
 * @param sSchemaPath
 *
 * @return
 * @properties={typeid:24,uuid:"9AEEB027-3BFB-445A-9978-64503E432480"}
 */
function isXMLSchemaPathRepeatable(sSchemaPath) {
	if(sSchemaPath.indexOf('xml.salesOrder.salesOrderLines.salesOrderLine') >= 0) {
		return true;
	} else if(sSchemaPath.indexOf('xml.salesOrder.splitBillingInfo.splitBillingLine') >= 0) {
		return true;
	} else if(sSchemaPath.indexOf('xml.salesOrder.shippingAddresses') >= 0) {
		return true;
	}
	return false;
}

/**
 * Checks if there is an array of objects at the path.
 * @param sObjectPath
 * @param oXMLObject
 *
 * @return
 * @properties={typeid:24,uuid:"2F86CC59-B324-4025-B483-A280E60D8EBB"}
 */
function isObjectPathAnArray(sObjectPath, oXMLObject) {
	return oXMLObject[sObjectPath]
}

/**
 * Process the file recursively.  This was the original method designed.
 * 
 * @param {XMLDocument} xml_doc
 * @param {Object} xmlRequestFile XML file from the HTTP Post
 * @param {String} senderID ID of where the XML came from
 * @param {UUID} db_log_parent_id_param
 * @param {{sPathToMappingFile:String, sInputFileName:String, sTestCode:String, bUseOriginalLogic: Boolean}} oTestingDetails
 * @param {UUID} [sys_sender_id_param] ID of where the XML came from
 * @param {Boolean} bIsAutoWTPImport
 *
 * @return
 * @properties={typeid:24,uuid:"66A79211-C477-4592-8CEB-CF56A513FC92"}
 * @AllowToRunInFind
 */
function processXMLFileRecursively(xml_doc, xmlRequestFile, senderID, db_log_parent_id_param, oTestingDetails, sys_sender_id_param, bIsAutoWTPImport) {
	var response = new Object();
	var xml_elements;
	
	var mappingObject = scopes.avWebToPrint.getMappingObject(oTestingDetails,sys_sender_id_param,senderID)
	
	// Loop through foundset to get the mapping from source file to XML template from XML Mapper.
	for (var nXMLIndex = 0; nXMLIndex < mappingObject.aSchemaMapping.length; nXMLIndex++) {
		/** @type {{xml_selected:String, xml_schema_selected:String, xml_selected_context_path:String, sequence_nr:Number, xml_selected_element_value: String}} */
		var oXMLRecord = mappingObject.aSchemaMapping[nXMLIndex];
		/** @type{Array} */
		var objectArray;
		var remaining_path;
		var xml_sel, new_elem;
		/** @type{XMLElement} */	
		var xml_elem, parent_elem, grand_parent_elem

		// Get the location of the data in the XML file and try to evaluate it since XML was converted to object by Servoy
		var xml_selected = oXMLRecord.xml_selected
		
		try {
			// Check if the mapping is referencing an array of objects since the Servoy Web Service converts duplicate objects into arrays.
			var is_array = evaluateXMLPathAsArray(xmlRequestFile, 'currentObject.' + oXMLRecord.xml_selected.substring(0, oXMLRecord.xml_selected.lastIndexOf('.')))
		} catch (ex) {
			scopes.avWebToPrint.jobDetails.evaluationError += ' 1 - Problem with evaluation: ' + 'currentObject.' + oXMLRecord.xml_selected.substring(0, oXMLRecord.xml_selected.lastIndexOf('.'))
		}

		// Get the location in the XML template to write over.
		/** @type{String} */
		var sXMLSchemaSelected = oXMLRecord.xml_schema_selected
		
		if(sXMLSchemaSelected != 'xml.salesOrder.salesOrderLines.salesOrderLine' && sXMLSchemaSelected != 'xml.salesOrder.salesOrderLines.salesOrderLine.sections.section') {
			var bCreateNewSiblingElement = shouldCreateNewSiblingElement(sXMLSchemaSelected);
			// If the selected location is an array and the node need to be duplicated, loop through the array.
			if (is_array && (canBeDuplicated(sXMLSchemaSelected) || bCreateNewSiblingElement) && (scopes.avWebToPrint.xmlSelectedIsNotAttributeValue(xml_selected) || bCreateNewSiblingElement)) {
				try {
					objectArray = returnArrayAtPath(xmlRequestFile, 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.')));
					remaining_path = remainingPath(xmlRequestFile, 'currentObject.' + xml_selected);
					scopes.avWebToPrint.aXMLObjects = new Array();
					// Call this function to get an array of the XML objects that are represented by the 'remaining path'.  
					// This will recursively loop through the incoming XML to find all instances of the remaining path and set the remaining path in form variable, sRemainingPath
					setArrayOfXMLObjectsAtPath(objectArray,'currentObject.' + remaining_path);
					
					if(scopes.avWebToPrint.aXMLObjects != null && scopes.avWebToPrint.aXMLObjects instanceof Array) {
						remaining_path = sRemainingPathInArray.replace('currentObject.','');
						objectArray = scopes.avWebToPrint.aXMLObjects;
					}
					
				} catch (ex) {
					scopes.avWebToPrint.jobDetails.evaluationError += ' 2 - Problem with evaluation: ' + 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.'))
				}
	
				var starting_index = 0
				var new_elements = 0
				var object_index = 0
				var item_index
				if (bCreateNewSiblingElement == true) {
					var newObjectArray = new Array();
					// Create duplicates of the existing element to create sibling in the new XML template.  Index starts at 0.
					for (item_index = 0; item_index < objectArray.length; item_index++) {
						if (sXMLSchemaSelected != null && sXMLSchemaSelected != '') {
							// Get the elements at the selected XML path of schema and create additional entries if it wasn't already created yet.
							xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(sXMLSchemaSelected, '.', '/'))
							if (bCreateNewSiblingElement == true) {
								if (item_index == 0) {
									starting_index = xml_elements.length
								}
								try {
									var keyObject = objectArray[item_index];
									var sKeyName = Object.keys(keyObject)[0];
									var bShouldAddXMLElement = false;
									if (sKeyName == remaining_path) {
										bShouldAddXMLElement = true;
									} // Added check for when there is a context path to try and evaluate the attribute value to the object so it doesn't create duplicates.
									else if (oXMLRecord.xml_selected_context_path != null && sKeyName == oXMLRecord.xml_selected_context_path && eval('objectArray[' + item_index + '].' + remaining_path.substring(0, remaining_path.lastIndexOf('.'))) == remaining_path.substring(remaining_path.lastIndexOf('.') + 1)) {
										bShouldAddXMLElement = true;
									}
	
									if (bShouldAddXMLElement) {
										xml_elem = xml_elements.pop();
										parent_elem = xml_elem.getParent();
										new_elem = xml_elem.clone();
										new_elem.text = '';
										parent_elem.addChild(new_elem);
										new_elements++;
										newObjectArray.push(objectArray[item_index]);
										// set this field as an index of which object in array to use for evaluation.
										object_index = item_index;
									}
								} catch (e) {
									application.output('Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected)
									scopes.avWebToPrint.jobDetails.evaluationError += ' Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected
								}
							}
						}
					}
					if(newObjectArray.length > 0) {
						objectArray = newObjectArray;
					}
					// Loop through array of values and write it to the new XML template
					for (item_index = 0; item_index < new_elements; item_index++) {
						//If the object array is the same number of new elements, it must be adding the array so loop through it.
						if (objectArray.length == new_elements) {
							object_index = item_index;
						}
						writeXMLValueFromArray(objectArray, xml_selected, object_index, starting_index + item_index, oXMLRecord.xml_schema_selected, xml_doc, mappingObject.oSenderDetails, mappingObject.aTaskMapping, mappingObject.aValueMapping, remaining_path, oXMLRecord.xml_selected_context_path, oXMLRecord.sequence_nr, oXMLRecord.xml_selected_element_value)
					}
				} else {
					var xmlElementArray = new Array();
					// Create duplicates of the parent in the new XML template.  Index starts at 1 since there is already an existing entry.
					for (item_index = 1; item_index < objectArray.length; item_index++) {
	
						if (sXMLSchemaSelected != null && sXMLSchemaSelected != '') {
							// Get the elements at the selected XML path of schema and create additional entries if it wasn't already created yet.
							xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(sXMLSchemaSelected, '.', '/'))
							if(oXMLRecord.evaluate_element_value && oXMLRecord.xml_selected_element_value != null && xml_elements.length < objectArray.length) {
					        	if(eval('objectArray[item_index].' + remaining_path + '.content') == oXMLRecord.xml_selected_element_value ) {
					        		try {
										xml_elem = xml_elements.pop()
										parent_elem = xml_elem.getParent()
										grand_parent_elem = parent_elem.getParent()
										grand_parent_elem.addChild(parent_elem.clone())
										xmlElementArray.push(objectArray[item_index]);
									} catch (e) {
										application.output('Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected)
										scopes.avWebToPrint.jobDetails.evaluationError += ' Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected
									}
					        	}
					        } else if (oXMLRecord.attribute_value_is_node && xml_elements.length < objectArray.length) {
					        	try {
									xml_elem = xml_elements.pop()
									parent_elem = xml_elem.getParent()
									grand_parent_elem = parent_elem.getParent()
									grand_parent_elem.addChild(parent_elem.clone())
								} catch (e) {
									application.output('Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected)
									scopes.avWebToPrint.jobDetails.evaluationError += ' Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected
								}
					        } else if (xml_elements.length < objectArray.length) {
								try {
									xml_elem = xml_elements.pop()
									parent_elem = xml_elem.getParent()
									grand_parent_elem = parent_elem.getParent()
									grand_parent_elem.addChild(parent_elem.clone())
								} catch (e) {
									application.output('Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected)
									scopes.avWebToPrint.jobDetails.evaluationError += ' Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected
								}
							}
	
						}
					}
					
					if(xmlElementArray.length > 0) {
						objectArray = xmlElementArray;
					}
					
					// Loop through array of values and write it to the new XML template
					for (item_index = 0; item_index < objectArray.length; item_index++) {
						writeXMLValueFromArray(objectArray, xml_selected, item_index, item_index, oXMLRecord.xml_schema_selected, xml_doc, mappingObject.oSenderDetails, mappingObject.aTaskMapping, mappingObject.aValueMapping, remaining_path, oXMLRecord.xml_selected_context_path, oXMLRecord.sequence_nr, oXMLRecord.xml_selected_element_value)
					}
				}
	
			} // This will occur when the reference points to an array but should only fill in one value (It will always select the first value in array! ****
			else if (is_array) {
				// write it to the new XML template
				try {
					objectArray = returnArrayAtPath(xmlRequestFile, 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.')));
					remaining_path = remainingPath(xmlRequestFile, 'currentObject.' + xml_selected);
					
					scopes.avWebToPrint.aXMLObjects = new Array();
					// Call this function to get an array of the XML objects that are represented by the 'remaining path'.  
					// This will recursively loop through the incoming XML to find all instances of the remaining path.
					setArrayOfXMLObjectsAtPath(objectArray,'currentObject.' + remaining_path);
					
					if(scopes.avWebToPrint.aXMLObjects != null && scopes.avWebToPrint.aXMLObjects instanceof Array) {
						remaining_path = sRemainingPathInArray.replace('currentObject.','');
						objectArray = scopes.avWebToPrint.aXMLObjects;
					}
				} catch (ex) {
					scopes.avWebToPrint.jobDetails.evaluationError += ' 2 - Problem with evaluation: ' + 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.'))
				}
				writeXMLValueFromArray(objectArray, xml_selected, 0, 0, oXMLRecord.xml_schema_selected, xml_doc, mappingObject.oSenderDetails, mappingObject.aTaskMapping, mappingObject.aValueMapping, remaining_path, oXMLRecord.xml_selected_context_path, oXMLRecord.sequence_nr, oXMLRecord.xml_selected_element_value)
			} else if (bCreateNewSiblingElement == true) {
				// Get the elements at the selected XML path of schema and create additional entries if it wasn't already created yet.
				xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(sXMLSchemaSelected, '.', '/'))
				try {
					xml_sel = evaluateXMLPath(xmlRequestFile, 'currentObject.' + xml_selected)
					if (xml_sel == null) {
						scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + 'currentObject.' + xml_selected + '.  Please check that you used the correct SenderID and the mapping is still valid.'
						response.jobDetails = scopes.avWebToPrint.jobDetails
						return response
					}
					if (xml_sel.toString().length > 0) {
						xml_elem = xml_elements.pop()
						parent_elem = xml_elem.getParent()
						new_elem = xml_elem.clone()
						parent_elem.addChild(new_elem)
						writeToElem(mappingObject.aTaskMapping, mappingObject.aValueMapping, new_elem, xml_selected, xml_sel, oXMLRecord.sequence_nr)
					}
	
				} catch (e) {
					application.output('Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected)
					scopes.avWebToPrint.jobDetails.evaluationError += ' Please check the mapping to see the Schema Nodes Selected is correct.  This can be an issue if the Schema is changed and reloaded. Please check: ' + sXMLSchemaSelected
				}
			} else { // Write one value from mapping to new XML template
				try {
					xml_sel = evaluateXMLPath(xmlRequestFile, 'currentObject.' + xml_selected)
					if (xml_sel == null) {
						scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + 'currentObject.' + xml_selected + '.  Please check that you used the correct SenderID and the mapping is still valid.'
						response.jobDetails = scopes.avWebToPrint.jobDetails
						return response;
					}
				} catch (e) {
					scopes.avWebToPrint.jobDetails.evaluationError += ' 3 - Problem with evaluation: ' + 'currentObject.' + xml_selected
				}
	
				// Get the location in the XML template to write over.
				sXMLSchemaSelected = oXMLRecord.xml_schema_selected
				if (sXMLSchemaSelected != null && sXMLSchemaSelected != '') {
					xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(sXMLSchemaSelected, '.', '/'))
					for each (var elem in xml_elements) {
						writeToElem(mappingObject.aTaskMapping, mappingObject.aValueMapping, elem, xml_selected, xml_sel, oXMLRecord.sequence_nr)
					}
				}
			}
		}
		
	}
	var sFilePath = application.getUserProperty('avanti.fileXMLOutFolder') + senderID + new Date().toDateString() + '.xml';
	var jsFile = plugins.file.convertToJSFile(sFilePath);
	if (jsFile) {
		// create parent folders if not exist
		var parentFile = jsFile.getParentFile();
		if (parentFile) {
			parentFile.mkdirs();
		}
		// Write file to hot folder. Will need to eventually copy to temporary location and then process it.
		xml_doc.writeToFile(jsFile.getAbsolutePath(), true);
	}
	else {
		xml_doc.writeToFile(sFilePath, true);
	}
	
	if(oTestingDetails.sPathToMappingFile != null) {
		xml_doc.writeToFile(oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\' + utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-output.xml'), true);
		var sNewFile = plugins.file.readTXTFile(oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\' + utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-output.xml'));
		
		var aValidationFileExists = plugins.file.getFolderContents(oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\valid\\', utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-valid.xml'));
		if(aValidationFileExists.length > 0) {
			var sValidationFile = plugins.file.readTXTFile(oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\valid\\' + utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-valid.xml'));
			if(sValidationFile != null) {
				response.validation = scopes.avWebToPrint.validateFile(sNewFile, sValidationFile, mappingObject.aSchemaMapping);
			}
		} else {
			response.validation = 'No validation file found at: ' + oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\valid\\' + utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-valid.xml');
		}
		
		response.info = 'File created at: ' + oTestingDetails.sPathToMappingFile + '\\' + oTestingDetails.sTestCode + '\\Output\\' + utils.stringReplace(oTestingDetails.sInputFileName,'.xml', '-output.xml'); 
		return response;
	}

	// Create an order in the database tables (int_estimate and corresponding tables from the xml file).
	try {	    
	    scopes.avWebToPrint.bOrderFromXML = true;
	    response = scopes.avWebToPrint.createSalesOrdersFromXML(xml_doc, mappingObject.fsXMLSender, db_log_parent_id_param, response, bIsAutoWTPImport);	    
	    	    
	} catch (ex) {
		globals.dbLogWriteDetails(globals.UUIDtoString(db_log_parent_id_param), 'wtp_queue', 'Error caught: ' + ex.message + '\n' + ex.stack, xml_doc.getAsText(true));
		response.exceptionError = 'Error caught: ' + ex.message + '\n' + ex.stack;
		jobID = 'Error - Exception Caught';
	}
    scopes.avWebToPrint.bOrderFromXML = false;
	return response
}

/**
 * Write value into XML element
 *
 * @AllowToRunInFind
 *
 * @param {Array} aTaskMapping
 * @param {Array} aValueMapping
 * @param {XMLElement} elem
 * @param {String} xml_selected
 * @param {Object} xml_sel
 * @param {Number} seq_nr
 *
 * @properties={typeid:24,uuid:"E3F67A94-947B-4270-B9E5-1B39178AE944"}
 */
function writeToElem(aTaskMapping, aValueMapping, elem, xml_selected, xml_sel, seq_nr) {
	var task_comment = ''

	/** @type {{task_uuid:String, task_parameter:String}} */
	var oTaskMapping 
	if (xml_sel != null && xml_sel.toString() != null) {
		if (xml_sel.toString() == '') {
			oTaskMapping = getObjectInArray(aTaskMapping, 'task_path', xml_selected);
		} else {
			oTaskMapping = getObjectInArray(aTaskMapping, 'task_path', xml_selected, 'task_value', xml_sel);
		}

	} else {
		oTaskMapping = getObjectInArray(aTaskMapping, 'task_path', xml_selected, 'task_value', 'nothing-0000-0000');
	}

	if (elem.name == 'taskValue' && oTaskMapping != null) {
		var find_comment = elem.getParent().getChild('taskComment')
		if (find_comment != null && find_comment.text != null) {
			task_comment = find_comment.text
		} else {
			task_comment = ''
		}

		elem.text = 'TASK:' + oTaskMapping.task_uuid + '|' + oTaskMapping.task_parameter + '|' + task_comment
	} else {
		/** @type {{mapping_new_value:String}} */
		var oValueMapping
		
		if (utils.stringTrim(String(xml_sel)).length > 0) {
			oValueMapping = getObjectInArray(aValueMapping, 'mapping_path', xml_selected, 'mapping_original_value', xml_sel);
		} else {
			oValueMapping = getObjectInArray(aValueMapping, 'mapping_path', xml_selected);
		}

		if (oValueMapping != null) {
			elem.text = oValueMapping.mapping_new_value
		} else {
			if (elem.name == 'comment') {
				elem.text = seq_nr + '||' + xml_sel
			} else {
				elem.text = xml_sel
			}

		}
	
	
	}
}

/**
 * Return an object in array that match the criteria on the path and values in param name and value parameters.
 * @param aToSearch
 * @param sParamName
 * @param sParamValue
 * @param [sParam2Name]
 * @param [sParam2Value]
 *
 * @return
 * @properties={typeid:24,uuid:"DFFB011F-9CEF-4736-BFEE-83EF11B4FBD5"}
 */
function getObjectInArray(aToSearch, sParamName, sParamValue, sParam2Name, sParam2Value) {
	if(aToSearch == null) {
		return null 
	} else {
		for (var nIndex = 0; nIndex < aToSearch.length; nIndex++) {
			/** @type {Object} */
			var obj = aToSearch[nIndex]
			if (obj.hasOwnProperty(sParamName)) {
				if(obj[sParamName] == sParamValue) {
					if(sParam2Name != null) {
						if (obj.hasOwnProperty(sParam2Name)) {
							if(obj[sParam2Name] == sParam2Value) {
								return obj;
							}
						}
					} else { 
						return obj;
					}
	    		}
			}
		}
	}
	return null
}

/**
 * Write a value in the new Avanti-based Schema template based on the path setup in the Web to Print Maintenance program
 * using the XML input file passed into the web service as the data source.
 *
 * @AllowToRunInFind
 *
 * @param {Object} xmlRequestFile XML input file passed in from the web service
 * @param {String} xml_selected The path in the XML Request File to find and replace in the Avanti-based template.
 * @param {Number} item_index The index in the array of objects which needs to be copied from XML input file to new Avanti-based template.
 * @param {String} xml_schema_selected The path in the Avanti XML template to copy new info into.
 * @param {XMLDocument} xml_doc The new Avanti-based XML template that is being created
 * @param {Array} aTaskMapping
 * @param {Array} aValueMapping 
 * @param {JSRecord<db:/avanti/xml_selected>} xml_rec
 *
 * @properties={typeid:24,uuid:"932D4446-2274-4F30-B8ED-C1572846BF8C"}
 */
function writeXMLValue(xmlRequestFile, xml_selected, item_index, xml_schema_selected, xml_doc, aTaskMapping, aValueMapping, xml_rec) {
	var item_sel
	try {
		item_sel = evaluateXMLPath(xmlRequestFile, 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.')) + '[' + item_index + '].' + xml_selected.substring(xml_selected.lastIndexOf('.') + 1))
		if (item_sel instanceof Array) {
			item_sel = evaluateXMLPath(xmlRequestFile, 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.')) + '[' + item_index + '].' + xml_selected.substring(xml_selected.lastIndexOf('.') + 1) + '.content')
		}
	} catch (ex) {
		scopes.avWebToPrint.jobDetails.evaluationError += ' 4 - Problem with evaluation: ' + 'currentObject.' + xml_selected.substring(0, xml_selected.lastIndexOf('.')) + '[' + item_index + '].' + xml_selected.substring(xml_selected.lastIndexOf('.') + 1)
	}

	// Get the location in the XML template to write over.
	if (xml_schema_selected != null && xml_schema_selected != '') {
		// Get the elements that exists in the path from
		var xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(xml_schema_selected, '.', '/'))

		var elem = xml_elements[item_index]

		// Checks if a value needs to be overwritten based on setup in the Web to Print XML Value Maintenance screen.
		writeToElem(aTaskMapping, aValueMapping, elem, xml_selected, item_sel, xml_rec.sequence_nr)
	}
}

/**
 * Get value of xml input that is an object in array and write to the new XML node.
 * @AllowToRunInFind
 *
 *
 * @param {Array} objectArray XML input file passed in from the web service
 * @param {String} xml_selected The path in the XML Request File to find and replace in the Avanti-based template.
 * @param {Number} object_index The index in the array of objects which needs to be copied from XML input file to new Avanti-based template.
 * @param {Number} item_index The index in the array of XML Elements which needs to be updated.
 * @param {String} xml_schema_selected The path in the Avanti XML template to copy new info into.
 * @param {XMLDocument} xml_doc The new Avanti-based XML template that is being created
 * @param {{attribute_value_is_node, attribute_value_array_base}} oSenderDetails
 * @param {Array} aTaskMapping
 * @param {Array} aValueMapping 
 * @param {String} remaining_path The path in the Avanti XML template to copy new info into.
 * @param {String} xml_selected_context_path
 * @param {Number} sequence_nr
 * @param {String} xml_selected_element_value
 *
 * @properties={typeid:24,uuid:"C9313828-F24C-47A1-8977-0DBB1338115F"}
 */
function writeXMLValueFromArray(objectArray, xml_selected, object_index, item_index, xml_schema_selected, xml_doc, oSenderDetails, aTaskMapping, aValueMapping, remaining_path, xml_selected_context_path, sequence_nr, xml_selected_element_value) {
	var item_sel
	try {
		item_sel = eval('objectArray' + '[' + object_index + '].' + remaining_path)

		if(xml_selected_element_value != null && item_sel != null) {
			if(item_sel.toString() == xml_selected_element_value || eval('item_sel.content') == xml_selected_element_value) {
				item_sel = eval('objectArray'  + '[' + object_index + '].' + xml_selected_context_path + '.content');
			} else {
				item_sel = null
			}
		} else if (item_sel == null && oSenderDetails.attribute_value_is_node) {
			var attr = remaining_path.substring(0, remaining_path.lastIndexOf('.'))
			var attr_value = remaining_path.substring(remaining_path.lastIndexOf('.') + 1)
			for (var idx = 0; idx < objectArray.length; idx++) {
				if (utils.stringReplace(eval('objectArray' + '[' + idx + '].' + attr).toString(), ' ', '_') == attr_value) {
					item_sel = eval('objectArray' + '[' + idx + '].content')
					break;
				}
			}
		} else if (item_sel != null) {
			if(eval('item_sel.content') != null) {
				item_sel = eval('item_sel.content');
			}
		}
	} catch (ex) {
		try {
			// SL-1736: [CH] If it fails to evaluate as a regular node, and it might be an attribute node that represents an array, try this.
			if (oSenderDetails.attribute_value_is_node || oSenderDetails.evaluate_element_value) {
				if(oSenderDetails.evaluate_element_value == true && xml_selected_element_value != null) {
					try {
						var elementValuePath = remaining_path.substring(0, remaining_path.lastIndexOf('.'));
						var elementValue = remaining_path.substring(remaining_path.lastIndexOf('.') + 1);
						
						if (utils.stringReplace(eval('objectArray' + '[' + object_index + '].' + elementValuePath).toString(), ' ', '_') == elementValue) {
							item_sel = eval('objectArray' + '[' + object_index + '].' + xml_selected_context_path + '.content');
							if (item_sel == null) {
								item_sel = eval('objectArray' + '[' + object_index + '].' + xml_selected_context_path);
								if (item_sel instanceof String == false) {
									item_sel = null;
								}
							}
						} else {
							
							for (var nContextIndex = 0; nContextIndex < objectArray.length; nContextIndex++) {
								if (utils.stringReplace(eval('objectArray' + '[' + nContextIndex + '].' + contextAttribute).toString(), ' ', '_') == contextAttributeValue) {
									item_sel = eval('objectArray' + '[' + nContextIndex + '].' + xml_selected_context_path + '.content');
									if (item_sel == null) {
										item_sel = eval('objectArray' + '[' + nContextIndex + '].' + xml_selected_context_path);
										if (item_sel instanceof String == false) {
											item_sel = null;
										}
									}
									break;
								}
							}
						}
					} catch (ex) {
						scopes.avWebToPrint.jobDetails.evaluationError += ' 5 - Problem with evaluation of context path: ' + 'objectArray' + '[' + nContextIndex + '].' + xml_selected_context_path
					}
				} // SL-6913 [CH] Added a context path to evaluate the array path.
				else if (xml_selected_context_path != null && xml_selected_context_path != '') {
					try {
						var contextAttribute = remaining_path.substring(0, remaining_path.lastIndexOf('.'));
						var contextAttributeValue = remaining_path.substring(remaining_path.lastIndexOf('.') + 1);
						
						if (utils.stringReplace(eval('objectArray' + '[' + object_index + '].' + contextAttribute).toString(), ' ', '_') == contextAttributeValue) {
							item_sel = eval('objectArray' + '[' + object_index + '].' + xml_selected_context_path + '.content');
							if (item_sel == null) {
								item_sel = eval('objectArray' + '[' + object_index + '].' + xml_selected_context_path);
								if (item_sel instanceof String == false) {
									item_sel = null;
								}
							}
						} else {
							
							for (var nContextIndex2 = 0; nContextIndex2 < objectArray.length; nContextIndex2++) {
								if (utils.stringReplace(eval('objectArray' + '[' + nContextIndex2 + '].' + contextAttribute).toString(), ' ', '_') == contextAttributeValue) {
									item_sel = eval('objectArray' + '[' + nContextIndex2 + '].' + xml_selected_context_path + '.content');
									if (item_sel == null) {
										item_sel = eval('objectArray' + '[' + nContextIndex2 + '].' + xml_selected_context_path);
										if (item_sel instanceof String == false) {
											item_sel = null;
										}
									}
									break;
								}
							}
						}
					} catch (ex) {
						scopes.avWebToPrint.jobDetails.evaluationError += ' 5 - Problem with evaluation of context path: ' + 'objectArray' + '[' + nContextIndex + '].' + xml_selected_context_path
					}
				} else {
					var item_pos = utils.stringToNumber(remaining_path.substr(remaining_path.lastIndexOf('.') + 1)) - oSenderDetails.attribute_value_array_base
					item_sel = eval('objectArray' + '[' + item_pos + '].content')
				}
			}

		} catch (ex) {
			scopes.avWebToPrint.jobDetails.evaluationError += ' 4 - Problem with evaluation: ' + 'objectArray.' + '[' + object_index + '].' + remaining_path
		}
	}

	// Get the location in the XML template to write over.
	if (item_sel != null && xml_schema_selected != null && xml_schema_selected != '') {
		// Get the elements that exists in the path from
		var xml_elements = xml_doc.getElementsByXPath(utils.stringReplace(xml_schema_selected, '.', '/'))

		var elem = xml_elements[item_index]

		// Checks if a value needs to be overwritten based on setup in the Web to Print XML Value Maintenance screen.
		writeToElem(aTaskMapping, aValueMapping, elem, xml_selected, item_sel, sequence_nr)

	}
}

/**
 *
 * @param xml_schema_selected The path in the Avanti-based XML template that may require duplication.
 *
 * @return
 * @properties={typeid:24,uuid:"38B2FADE-7DB3-4CD9-A453-08A5B13C7331"}
 */
function shouldCreateNewSiblingElement(xml_schema_selected) {
	if (xml_schema_selected != null) {
		var xml_schema_sub = xml_schema_selected.substring(xml_schema_selected.lastIndexOf('.') + 1)
		switch (xml_schema_sub) {
		case 'comment':
			return true
			break;
		default:
			return false
			break;
		}
	}
	return false
}

/**
 * Evaluate the XML node passed in path.
 *
 * @param {Object} currentObject
 * @param {String} path
 *
 * @properties={typeid:24,uuid:"E64EBAE9-6EB2-4850-92BA-68628CB1AEA7"}
 *
 * @return {Object}
 */
function evaluateXMLPath(currentObject, path) {
	try {
		var evaluationPath = ''
		var pathArray = path.split('.')
		if (pathArray != null) {
			evaluationPath = pathArray[0]
			for (var idx = 0; idx < pathArray.length - 1; idx++) {
				try {
					if (eval(evaluationPath + '[0]') != undefined) {
						evaluationPath += '[0].' + pathArray[idx + 1]
					} else {
						evaluationPath += '.' + pathArray[idx + 1]
					}

				} catch (ex1) {
					try {
						eval(evaluationPath)
						evaluationPath += '.' + pathArray[idx + 1]
					} catch (ex2) {
						throw 'EvalException'
					}
				}
			}

			try {
				var returnValue = eval(evaluationPath + '.content')
				if (returnValue == null) {
					returnValue = eval(evaluationPath)
					if (isEmpty(returnValue)) {
						returnValue = ''
					}
				}
				return returnValue
			} catch (ex) {
				return eval(evaluationPath)
			}
		}

		return ''
	} catch (ex) {
		application.output('Problem with evaluation: ' + path)
		scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + path
		return ''
	}
}

/**
 * Check if an object is empty when you see evaluation of [Object object]
 *
 * @param oObject
 *
 * @return
 * @properties={typeid:24,uuid:"D3BE7D03-EBFD-4190-934F-2961F61BEE72"}
 */
function isEmpty(oObject) {

	// Speed up calls to hasOwnProperty
	var hasOwnProperty = Object.prototype.hasOwnProperty;

	// null and undefined are "empty"
	if (oObject == null) return true;

	// Assume if it has a length property with a non-zero value that that property is correct.
	if (oObject.length > 0) return false;
	if (oObject.length === 0) return true;

	// Otherwise, does it have any properties of its own?
	for (var key in oObject) {
		if (hasOwnProperty.call(oObject, key)) return false;
	}

	return true;
}

/**
 * Return first array of objects based on path passed in.
 * @param {Object} currentObject
 * @param {String} path
 *
 * @return
 * @properties={typeid:24,uuid:"BA99E8FF-893F-43C3-9E7F-E1361006639E"}
 */
function returnArrayAtPath(currentObject, path) {
	try {
		var evaluationPath = ''
		var pathArray = path.split('.')
		if (pathArray != null) {
			evaluationPath = pathArray[0]
			for (var idx = 0; idx < pathArray.length - 1; idx++) {
				try {
					if (eval(evaluationPath + '[0]') != undefined) {
						return eval(evaluationPath)
					} else {
						evaluationPath += '.' + pathArray[idx + 1]
					}

				} catch (ex1) {
					try {
						eval(evaluationPath)
						evaluationPath += '.' + pathArray[idx + 1]
					} catch (ex2) {
						throw 'EvalException'
					}
				}
			}
			return eval(evaluationPath)
		}

		return ''
	} catch (ex) {
		application.output('Problem with evaluation: ' + path)
		scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + path
		return 'ERROR'
	}
}

/**
 * Check if there is another array in remaining path.
 * 
 * @param currentObject
 * @param {String} path
 *
 * @return
 * @properties={typeid:24,uuid:"BF64F800-515D-49DF-8FBD-77A5FD548967"}
 */
function checkRemainingArrayAtPath(currentObject, path) {
	
	var oObjectArrayWithPath = new Object();
	try {
		var evaluationPath = ''
		var pathArray = path.split('.')
		if (pathArray != null) {
			evaluationPath = pathArray[0]
			for (var idx = 0; idx < pathArray.length - 1; idx++) {
				try {
					if (eval(evaluationPath + '[0]') != undefined) {
						return eval(evaluationPath)
					} else {
						evaluationPath += '.' + pathArray[idx + 1]
					}

				} catch (ex1) {
					try {
						eval(evaluationPath)
						evaluationPath += '.' + pathArray[idx + 1]
					} catch (ex2) {
						throw 'EvalException'
					}
				}
			}
			return eval(evaluationPath)
		}
		return ''
	} catch (ex) {
		application.output('Problem with evaluation: ' + path)
		scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + path
		oObjectArrayWithPath.evaluationError = ' Problem with evaluation: ' + path
		return oObjectArrayWithPath;
	}
}

/**
 * Recurse through all object arrays to get lowest level objects based on remaining path.
 * @param currentObjectArray
 * @param {String} sRemainingPath
 * @properties={typeid:24,uuid:"C0E31BE3-201A-43DD-9149-BADB9FFCC3B1"}
 */
function setArrayOfXMLObjectsAtPath(currentObjectArray, sRemainingPath) {
	
	for each (var child in currentObjectArray) {
		var new_element_param = checkRemainingArrayAtPath(child, sRemainingPath);
		if(new_element_param != undefined && new_element_param instanceof Array) {
			if(sRemainingPath.indexOf('.') >= 0) {
				setArrayOfXMLObjectsAtPath(new_element_param, 'currentObject.' + remainingPath(child,sRemainingPath));
			}
		}
		else {
			scopes.avWebToPrint.aXMLObjects.push(child);
			sRemainingPathInArray = sRemainingPath;
		}
		
	}
}

/**
 * Return the remaining path after an array of objects has been returned.
 * @param {Object} currentObject
 * @param {String} path
 *
 *
 * @return
 * @properties={typeid:24,uuid:"EED83723-1DA6-43CC-8DFB-23BA9A90783B"}
 */
function remainingPath(currentObject, path) {
	try {
		var evaluationPath = ''
		var returnPath = ''
		var pathArray = path.split('.')
		if (pathArray != null) {
			evaluationPath = pathArray[0]
			for (var idx = 0; idx < pathArray.length - 1; idx++) {
				try {
					if (eval(evaluationPath + '[0]') != undefined) {
						for (var path_idx = idx + 1; path_idx < pathArray.length; path_idx++) {
							returnPath += pathArray[path_idx]

							if (pathArray.length != path_idx + 1) {
								returnPath += '.'
							}
						}

						return returnPath
					} else {
						evaluationPath += '.' + pathArray[idx + 1]
					}
				} catch (ex2) {
					throw 'EvalException'
				}
			}
		}
	} catch (ex) {
		application.output('Problem with evaluation: ' + path)
		scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + path
		return 'ERROR'
	}
	return ''
}

/**
 * Return true or false if the XML Path represents an array.
 *
 * @param {Object} currentObject
 * @param {String} path
 *
 * @return
 * @properties={typeid:24,uuid:"DD6B1D57-13F7-4338-9522-481D931EADB4"}
 */
function evaluateXMLPathAsArray(currentObject, path) {
	try {
		var evaluationPath = ''
		var pathArray = path.split('.')
		if (pathArray != null) {
			evaluationPath = pathArray[0]
			for (var idx = 0; idx < pathArray.length; idx++) {
				try {
					if (eval(evaluationPath + '[0]') != undefined) {
						return true
					} else {
						evaluationPath += '.' + pathArray[idx + 1]
					}

				} catch (ex1) {
					try {
						eval(evaluationPath)
						evaluationPath += '.' + pathArray[idx + 1]
					} catch (ex2) {
						throw 'EvalException'
					}
				}
			}
			return false
		}

		return false
	} catch (ex) {
		application.output('Problem with evaluation: ' + path)
		scopes.avWebToPrint.jobDetails.evaluationError += ' Problem with evaluation: ' + path
		return false
	}
}

/**
 * A function that is preset to determine which paths can be duplicated when creating a new Avanti-based XML Schema.
 *
 * @param {String} xml_schema_selected The path in the Avanti-based XML template that may require duplication.
 *
 * @return
 * @properties={typeid:24,uuid:"596F7FFD-AEAB-4597-B7F7-B32F2762B8AD"}
 */
function canBeDuplicated(xml_schema_selected) {
	if (xml_schema_selected.indexOf('xml.salesOrder.billingAddress') >= 0) {
		return false
	}
	var xml_schema_sub = xml_schema_selected.substring(xml_schema_selected.lastIndexOf('.') + 1)
	switch (xml_schema_sub) {
		case 'chargebackCode':
		case 'contactName':
		case 'customerCode':
		case 'customerPO':
		case 'description':
		case 'externalOrderID':
		case 'estimator':
		case 'expectedDate':
		case 'orderDate':
		case 'orderType':
		case 'plantID':
		case 'project':
		case 'promiseDate':
		case 'rushLevel':
		case 'salesPerson':
		case 'schedulePriority':
		case 'serviceRep':
		case 'shippingMethod':
			return false
			break;
		default:
			return true
			break;
	}
}

/**
 * Process the response of the web to print request in the Integration Log.
 * 
 * @param sDBLogParentID
 * @param sSenderID
 * @param sUniqueID
 * @param oResponse
 * @param oResponseDetails
 * @param sSource
 * @param sRequestType
 * 
 * @properties={typeid:24,uuid:"4C4043AE-E225-45C0-8786-B1486B747296"}
 */
function processResponseInLog(sDBLogParentID, sSenderID, sUniqueID, oResponse, oResponseDetails, sSource, sRequestType) {
    if (oResponse.jobID && oResponse.jobID.indexOf('Error') < 0) {
    	// For GetPricing we do not have new order created
    	var sOrderNum = oResponse.jobID;
    	var sPurpose = globals.getWTPPurposeMessage(sRequestType);
    	var sLogType = 'import';
    	var sCreateOrderMessageText = 'Success! ' + globals.getWTPCreateUpdateMessage(sRequestType) + sSenderID + ' and unique ID ' + sUniqueID + ' completed with the result: ' + sOrderNum;
    	var sCreateShippingMessageText = 'Success! Request to create a shipment using sender ID ' + sSenderID + ' completed';
    	if (scopes.avWebToPrint.sImportOrderRequestType == scopes.avWebToPrint.sEstimateOrderRequestType) {
    		sOrderNum = '';
    		sPurpose = 'Pricing';
    		sLogType = 'processing';
    		sCreateOrderMessageText = 'Success! Request for pricing using sender ID ' + sSenderID + ' and unique ID ' + sUniqueID + ' completed with the result: ' + sOrderNum;
    		sCreateShippingMessageText = 'Success! Request for pricing using sender ID ' + sSenderID + ' completed';
    	}
    	
        if (oResponse.jobDetails.itemDetails.length == 0) {
            var sStatus = 'Success';
            if (oResponse.warning && oResponse.warning.length > 0) {
                sStatus = 'Warning';
            }
            if (oResponse.jobDetails.jobShipped) {
                globals.dbLogUpdate(sDBLogParentID, sCreateShippingMessageText, sStatus, sOrderNum, 'wtp_request', sLogType, 'wtp', sSource, sPurpose);
            }
            else {
                globals.dbLogUpdate(sDBLogParentID, sCreateOrderMessageText, sStatus, sOrderNum, 'wtp_request', sLogType, 'wtp', sSource, sPurpose);
            }
            
            globals.dbLogWriteDetails(sDBLogParentID, 'wtp_request', globals.formatJson(oResponseDetails));
            
        }
        else {
            // problem with some of the line items
            var result_detail = 'Warning! ' + globals.getWTPCreateUpdateMessage(sRequestType) + sSenderID + ' and unique ID ' + sUniqueID + ' completed with the result: ' + sOrderNum + ', not all line items added to the sales order';
            if (scopes.avWebToPrint.sImportOrderRequestType == scopes.avWebToPrint.sEstimateOrderRequestType) {
            	result_detail = 'Warning! Request for pricing using sender ID ' + sSenderID + ' and unique ID ' + sUniqueID + ' completed with the result: ' + sOrderNum + ', not all line items added to the sales order';
            }

            globals.dbLogUpdate(sDBLogParentID, result_detail, 'Warning', oResponse.jobID, 'wtp_request', sLogType, 'wtp', sSource, sPurpose);
            globals.dbLogWriteDetails(sDBLogParentID, 'wtp_request', globals.formatJson(oResponseDetails));
        }
    }
    else if (oResponse.jobID) {
    	sPurpose = globals.getWTPPurposeMessage(sRequestType);
    	sLogType = 'import';
    	var sErrorOrderWithJobIdMessage = 'Error! ' + globals.getWTPCreateUpdateMessage(sRequestType) + sSenderID + ' and unique ID ' + sUniqueID + ' failed to create a sales order due to ' + oResponse.jobID;
    	if (scopes.avWebToPrint.sImportOrderRequestType == scopes.avWebToPrint.sEstimateOrderRequestType) {
    		sPurpose = 'Pricing';
    		sLogType = 'processing';
    		sErrorOrderWithJobIdMessage = 'Error! Request for pricing using sender ID ' + sSenderID + ' and unique ID ' + sUniqueID + ' failed to create a sales order due to ' + oResponse.jobID;
    	}
        globals.dbLogUpdate(sDBLogParentID, sErrorOrderWithJobIdMessage, 'Error', null, 'wtp_request', sLogType, 'wtp', sSource, sPurpose);
        globals.dbLogWriteDetails(sDBLogParentID, 'wtp_request', globals.formatJson(oResponseDetails));
        if (sSource == 'http') {
            throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, 'Error occured when processing the order. Please check the Integration Log.'];
        }
    }
    else {
    	sPurpose = globals.getWTPPurposeMessage(sRequestType);
    	sLogType = 'import';
    	var sErrorIncorrectImportMessage = 'Error! ' + globals.getWTPCreateUpdateMessage(sRequestType) + sSenderID + ' and unique ID ' + sUniqueID + ' failed to create a sales order.  Please review the details.';
    	if (scopes.avWebToPrint.sImportOrderRequestType == scopes.avWebToPrint.sEstimateOrderRequestType) {
    		sPurpose = 'Pricing';
    		sLogType = 'processing';
    		sErrorIncorrectImportMessage = 'Error! Request for pricing using sender ID ' + sSenderID + ' and unique ID ' + sUniqueID + ' failed to create a sales order.  Please review the details.';
    	}
        globals.dbLogUpdate(sDBLogParentID, sErrorIncorrectImportMessage, 'Error', null, 'wtp_request', sLogType, 'wtp', sSource, sPurpose);
        globals.dbLogWriteDetails(sDBLogParentID, 'wtp_request', globals.formatJson(oResponseDetails));
        if (sSource == 'http') {
            throw [plugins.http.HTTP_STATUS.SC_INTERNAL_SERVER_ERROR, 'Error occured when processing the order. Please check the Integration Log.'];
        }
    }

}

/**
 * called when the web2print headless client finishes processPostOrderTasks
 * 
 * @param jsEvent
 * @param {plugins.headlessclient.JSClient} jsClient
 *
 * @properties={typeid:24,uuid:"343ACA8B-ADB5-4C16-8344-274D8CBD23DD"}
 */
function web2PrintHeadlessClientCallBack(jsEvent, jsClient) {
    if (jsEvent.getType() == plugins.headlessclient.JSClient.CALLBACK_EXCEPTION_EVENT) {
        scopes.avUtils.quickLog("web2PrintHeadlessClientCallBack", "exception data: " + jsEvent.data);
    }
    
    if (jsClient && jsClient.isValid()) {
        jsClient.shutdown(true);        
    }
}
