columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
compatibleColumnTypes:"[[1,36,0]]",
creationOrderIndex:-1,
dataType:-4,
flags:37,
length:16,
name:"sys_org_language_uuid"
},
{
allowNull:false,
autoEnterType:5,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:12,
flags:4,
length:36,
lookupValue:"scopes.globals.org_id",
name:"org_id"
},
{
allowNull:false,
compatibleColumnTypes:"[[-9,50,0]]",
creationOrderIndex:-1,
dataType:12,
length:50,
name:"sys_org_language"
}
],
name:"sys_org_language",
tableType:0