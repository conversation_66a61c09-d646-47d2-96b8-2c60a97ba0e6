customProperties:"useCssPosition:true",
extendsID:"8ADD6C71-2C60-4BC3-B464-5D14D2B883EE",
items:[
{
cssPosition:"31,-1,-1,155,140,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"31",
width:"140"
},
dataProviderID:"_orderNumber",
enabled:true,
formIndex:1,
onDataChangeMethodID:"32750FC0-5A20-43BA-B57B-31BD2E405BCB",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"15DD9A6D-6D89-448D-986C-8950F7B791D3",
visible:true
},
name:"_orderNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"080361D2-11D5-4316-B46A-E525847D6A3E"
},
{
cssPosition:"6,-1,-1,10,253,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"6",
width:"253"
},
enabled:true,
formIndex:1,
styleClass:"gray_bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.orderSelection",
visible:true
},
name:"component_128AABA9",
styleClass:"gray_bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"42D88C56-4F7F-4E06-84A9-0672EA22E7F8"
},
{
cssPosition:"31,-1,-1,300,24,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"300",
right:"-1",
top:"31",
width:"24"
},
enabled:true,
formIndex:26,
onActionMethodID:"B715F4FD-CB7B-4299-9EA3-9F7B6B4CAD53",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupEstimate",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"42E03F06-F443-46AD-A1C4-D041ABE6DB78"
},
{
cssPosition:"-1,-1,11,5,100,24",
json:{
cssPosition:{
bottom:"11",
height:"24",
left:"5",
right:"-1",
top:"-1",
width:"100"
},
enabled:true,
onActionMethodID:"A39763DD-7074-4FC5-857B-C617C6F61D3E",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Copy",
visible:true
},
name:"btnCopy",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"4E01EF39-DBBC-4479-A309-C4311F667947"
},
{
height:396,
partType:5,
typeid:19,
uuid:"8E1136B9-7471-4F80-A521-A554A2866372"
},
{
cssPosition:"31,-1,-1,10,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"31",
width:"140"
},
enabled:true,
formIndex:1,
labelFor:"_orderNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.order",
visible:true
},
name:"lbl_orderNumber",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A9293952-2ACB-46A7-93DB-BF9E3D867F36"
},
{
cssPosition:"5,-1,-1,5,477,110",
json:{
cssPosition:{
bottom:"-1",
height:"110",
left:"5",
right:"-1",
top:"5",
width:"477"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_5A56395D",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B4C1D16B-BF79-4751-80AD-E29F3133AF48"
},
{
cssPosition:"-1,-1,11,110,100,24",
json:{
cssPosition:{
bottom:"11",
height:"24",
left:"110",
right:"-1",
top:"-1",
width:"100"
},
enabled:true,
onActionMethodID:"860CE7E3-53FB-4A17-B269-5290A4B5A364",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"Close",
visible:true
},
name:"btnClose",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"C30DD063-7A8D-4B3F-B458-E4194DC7BAA7"
},
{
cssPosition:"31,-1,-1,155,140,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"31",
width:"140"
},
dataProviderID:"_orderNumber",
enabled:true,
formIndex:1,
onDataChangeMethodID:"32750FC0-5A20-43BA-B57B-31BD2E405BCB",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"15DD9A6D-6D89-448D-986C-8950F7B791D3",
visible:true
},
name:"_orderNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"DB82D060-928A-4818-8679-B46F5D1616D0"
},
{
cssPosition:"128,6,43,5,829,225",
formIndex:10,
json:{
containedForm:"26F29983-BD37-4FD6-980E-9CFCB6D385BD",
cssPosition:{
bottom:"43",
height:"225",
left:"5",
right:"6",
top:"128",
width:"829"
},
formIndex:10,
visible:true
},
name:"tabs_219",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"F050DC5D-876D-4210-BF43-3125F4FA4415"
},
{
cssPosition:"58,-1,-1,10,140,24",
formIndex:20,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"10",
right:"-1",
top:"58",
width:"140"
},
dataProviderID:"_copyNotes",
enabled:true,
formIndex:20,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.copyNotes",
visible:true
},
name:"_copyNotes",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"F83E2FEC-5709-4E3B-BA93-1054E3996259"
}
],
name:"sa_order_copy_order_dlg",
scrollbars:33,
size:"840,562",
styleName:null,
titleText:"i18n:avanti.lbl.copyEstimateOrder",
typeid:3,
uuid:"701399D9-8D56-4D84-868B-C8A4F98EA13A"