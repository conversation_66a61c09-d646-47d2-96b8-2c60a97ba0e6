customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_order",
extendsID:"3149C819-0806-451E-BA36-6112C2C36934",
items:[
{
cssPosition:"75,-1,-1,265,95,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"265",
right:"-1",
top:"75",
width:"95"
},
enabled:true,
labelFor:"_projectPlanType",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.planType",
visible:true
},
name:"_projectPlanType_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4061A438-A1EE-4DE5-89B0-312B0EC4D6E7"
},
{
height:250,
partType:5,
typeid:19,
uuid:"456E91AF-E050-4E47-BAE6-4FC75CC53B60"
},
{
anchors:15,
cssPosition:"106px,0px,5px,0px,1665px,144px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-right",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined arrow_right",
svyUUID:"0A4B3C1C-D136-410C-9616-7643DF82E9AC",
valuelist:null,
visible:true,
width:20
},
{
autoResize:true,
dataprovider:"ordh_document_num",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.document#",
id:"ordh_document_num",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"CB04B3FF-AF8F-4060-9520-2D30F5879EA2",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"ordh_order_date",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.date",
id:"ordh_order_date",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"391970E6-1BF7-43F2-9B4A-59478E48C63C",
valuelist:null,
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"sa_order_to_sa_customer_leftJoin.cust_code",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.cust_code",
id:"cust_code",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"E0D74D47-051A-4290-B458-5BC2BD6FFAAF",
valuelist:null,
visible:true,
width:100
},
{
autoResize:true,
dataprovider:"sa_order_to_sa_customer_leftJoin.cust_name",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.cust_name",
id:"cust_name",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"16C771D7-DE02-4890-931F-6CA93EBB02A8",
valuelist:null,
visible:true,
width:210
},
{
autoResize:true,
dataprovider:"ordh_description",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.ordh_description",
id:"ordh_description",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"E9CD3209-D45B-4E7B-8C31-3D5BAF812D3C",
valuelist:null,
visible:true,
width:210
},
{
autoResize:false,
dataprovider:"ordh_is_interbranch_projplan",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.planType",
id:"ordh_is_interbranch_projplan",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"12AE4FE2-0914-466F-B8BF-1839B65094C4",
valuelist:"0512CB23-4DD8-4525-A602-8E18DF615594",
visible:true,
width:100
},
{
autoResize:true,
dataprovider:"ordh_estimate_status",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.status",
id:"ordh_estimate_status",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"4B2DB9DB-747D-494D-BB28-AA7E698F247A",
valuelist:"82191384-899F-49DC-BC3C-FC7D4DD02A25",
visible:true,
width:97
},
{
autoResize:false,
dataprovider:"ordh_tbl_workflow_action",
editType:"COMBOBOX",
enableRowGroup:true,
enableSort:true,
filterType:"VALUELIST",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.dialog.orderWorkFlowActions_title",
id:"ordh_tbl_workflow_action",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"9ABC5A8C-7A06-48AA-A6BD-D31A74F1FE3F",
valuelist:"2B66CB08-3F91-403D-B369-FEC0F9F2A4B8",
visible:true,
width:140
},
{
autoResize:true,
dataprovider:"sa_order_to_sa_sales_person_leftJoin.salesper_name",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.salesper_id",
id:"ordh_salesper_id",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D9915888-05F2-4EDB-A1DB-9C0F74A3E5E3",
valuelist:null,
visible:true,
width:90
},
{
autoResize:true,
dataprovider:"sa_order_to_sys_employee$estimator_leftJoin.empl_full_name",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.estimator",
id:"ordh_estimator_empl_id",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"2714FCE7-567A-4EC0-AB6C-A6DA15B9A25E",
valuelist:null,
visible:true,
width:72
},
{
autoResize:false,
dataprovider:"sa_order_to_sa_customer_contact_leftJoin.sa_customer_contact_to_sys_contact_leftJoin.contact_full_name",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.contactName",
id:"contact_full_name",
maxWidth:151,
minWidth:151,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"86BD10F8-50E7-42ED-95BE-3A56F215241C",
valuelist:null,
visible:true,
width:151
},
{
autoResize:true,
dataprovider:"sa_order_to_sa_order_opp.ordhopp_est_needed_by_date",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.estimateNeededBy",
id:"ordhopp_est_needed_by_date",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"253A524E-48DF-4F18-B7E3-84AAFD918570",
valuelist:null,
visible:true,
width:133
},
{
autoResize:true,
dataprovider:"ordh_reservation_expiry_date",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.ReservationExpiryDate",
id:"ordh_reservation_expiry_date",
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"A671C90D-949D-4411-A990-2EFC0CAAD3B3",
valuelist:null,
visible:true,
width:153
},
{
dndSourceDataprovider:"sa_order_to_sa_customer.sa_customer_to_sa_customer$parent.cust_name",
filterType:"TEXT",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.cust_parent_cust_id",
id:"parent_cust",
styleClass:"text-left",
svyUUID:"538FC0E7-7094-42FD-B205-138699BB3078",
width:140
}
],
cssPosition:{
bottom:"5px",
height:"144px",
left:"0px",
right:"0px",
top:"106px",
width:"1665px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"78CE5354-A7E3-401E-B721-9638E85CB688",
onCellDoubleClick:"EF4E5686-0C42-4F25-93B1-7A9D610BD407",
onColumnDataChange:"D1D8E325-639B-4639-B449-79A568C437A4",
onReady:"02E0AEE6-B5FD-45A4-B916-BBF947742BDB",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:false,
suppressColumnFilter:true,
suppressColumnSelectAll:false,
suppressRowGroups:false,
suppressSideButtons:false,
svyUUID:"7AFEAB7A-DB3F-4C1A-998F-B6C77ED73DC3"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"51375244-C6AB-474C-8245-27FB0886BD02"
},
{
height:255,
partType:8,
typeid:19,
uuid:"5BAB8160-7EF3-4128-9D6F-ABB16DB7A98B"
},
{
cssPosition:"75,-1,-1,674,210,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"674",
right:"-1",
top:"75",
width:"210"
},
enabled:true,
onActionMethodID:"2CBC111A-746F-4E95-A3C5-FD68DD6A2471",
styleClass:"btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.releaseProjectPlanBatch",
visible:true
},
name:"btnAutoRelease",
styleClass:"btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"61F1070A-237F-40FE-BC74-F15AF8DE43A6"
},
{
cssPosition:"75,-1,-1,5,95,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"75",
width:"95"
},
enabled:true,
labelFor:"_statusFilter",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"_statusFilter_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7C28B301-56B5-450E-AF6F-C314AE0DB5F2"
},
{
cssPosition:"5,0,-1,0,1665,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"5",
width:"1665"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.projectPlanTableView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8335F60A-B11B-4832-AB9E-E6E7BA891DB2"
},
{
height:106,
partType:1,
typeid:19,
uuid:"9503F9E6-867A-4D3B-A6BE-F6473F30A7E1"
},
{
cssPosition:"75,-1,-1,524,140,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"524",
right:"-1",
top:"75",
width:"140"
},
enabled:true,
onActionMethodID:"0B3878A3-C4FB-4D90-8DE7-28D7BF662DC1",
styleClass:"btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.copyEstimate",
visible:true
},
name:"btnCopy",
styleClass:"btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"9D216118-B8B0-4BCD-9942-FE91B24DF5ED"
},
{
cssPosition:"75,-1,-1,364,150,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"364",
right:"-1",
top:"75",
width:"150"
},
dataProviderID:"scopes.globals.avBase_defaultProjectPlanTypeFilter",
enabled:true,
onDataChangeMethodID:"A13EB797-F4CE-40F0-884B-D5EC65EB7360",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"E0DDD720-33AA-478B-99D1-67216F9C0C0F",
visible:true
},
name:"_projectPlanType",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BE9F659A-7D8A-4500-BF81-BEE4D86207CE"
},
{
cssPosition:"30,-1,-1,0,950,35",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"35",
left:"0",
right:"-1",
top:"30",
width:"950"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"E3AA5AE2-2F48-4A7F-A379-5FBDF7D48F50"
},
{
cssPosition:"75,-1,-1,105,150,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"105",
right:"-1",
top:"75",
width:"150"
},
dataProviderID:"scopes.globals.avBase_defaultProjectPlanStatusFilter",
enabled:true,
onDataChangeMethodID:"D50EA5F5-702E-4A16-B460-6FA690932E45",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"98CC89DC-0337-410D-A661-EC4922B7D88E",
visible:true
},
name:"_statusFilter",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"EC4069B1-1289-47D9-A757-CB0B106063E1"
}
],
name:"sa_order_project_plan_tbl",
navigatorID:"-1",
onShowMethodID:"36E070B5-5A6D-4DE3-9958-033188F815E7",
paperPrintScale:100,
scrollbars:33,
size:"1665,369",
styleName:null,
typeid:3,
uuid:"A9F8AB2E-F40E-469A-98E5-98DF1C91D666",
view:0