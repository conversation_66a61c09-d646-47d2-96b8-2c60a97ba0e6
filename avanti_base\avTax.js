/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"6257C88D-6FDE-4C1F-B9E6-BBCDDAD83008",variableType:-4}
 */
var SALES_TAX_OPTIONS = {
    BasedOnCustomerShipTo  : "C",
    BasedOnTaxType         : "Y",
    Taxable                : "T",
    NonTaxable             : "N"
};

/**
 * @properties={typeid:35,uuid:"1A6B5DA8-9066-4656-AEAD-681B29E73EA9",variableType:-4}
 */
var oCache_taskTax = {};

/**
 * @properties={typeid:35,uuid:"701DB639-42EC-4E41-BCD8-EE86560A9311",variableType:-4}
 */
var aCache_taskTaxDetails = [];

/**
 * @properties={typeid:35,uuid:"6161C134-910A-49E8-B6D4-9A8D1524714B",variableType:-4}
 */
var bSkipQtySalesTaxOption = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8A248B34-C181-4475-98C7-4F265A21B54B"}
 */
var sQtySalesTaxOption = null;

/**
 * @properties={typeid:35,uuid:"D0BB9E09-4EA2-4261-B3F3-A5254F789EEE",variableType:-4}
 */
var oTaxRates = {
    taxrate_id: [],
    taxrate_percent: []
};

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F8D72DC4-7D67-482F-8A46-AD7CB20182BB"}
 */
var sAvalaraAPIKey = '';

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"98CA0067-B1D1-4E66-A5B4-AEE9D0CDF0CD",variableType:-4}
 */
var aTaxableShippingStates = new Array();

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"9A85B28B-EA42-4116-9EC2-023080576DAF",variableType:-4}
 */
var aAddressesWithFreightCalculated = new Array();

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"71771646-600C-4A6D-BF82-DC6E4FF33BC2"}
 */
var sTaxTypeID = null;

/**
 * @public
 * @param {String} sMsg
 * @param {Boolean} [bShow]
 * @param {Boolean} [bHide]
 *
 * @properties={typeid:24,uuid:"17CD52CC-9A4B-4E63-ACB7-914403F5A16F"}
 */
function updateProcessLabelUI(sMsg, bShow, bHide) { 
	if (!scopes.avWebToPrint.bFromWebToPrint) {
		forms["sa_order_dtl"].updateProcessingLabel(sMsg,bShow,bHide);
	}
}

/**
 * Calculate tax for orders using Avalara AvaTax
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_header>} rOrderHeader
 *
 * @return
 * @properties={typeid:24,uuid:"9FD8DD10-E1AF-40DE-BB35-D903D91BEB02"}
 */
function calculateTaxForOrderAvalara(rOrderHeader) {
    try {
        // time performance
        var oStopWatch = new scopes.avUtils.StopWatch();
        oStopWatch.reset();
        oStopWatch.start();
        
        scopes.avUtils.stopWatchStart("calculateTaxForOrderAvalara");
		updateProcessLabelUI("Calculate Avalara Tax: START", true);

        var sDBLogID = globals.dbLog('Calculating tax using Avalara', 'processing_tax', 'processing', 'tax', 'http', application.getUUID(globals.org_id), 'processing_tax', 'Summary', null);
        var sOrderNumber = '';
        var oHTTPClient = plugins.http.createNewHttpClient();
        var sAvalaraURL = 'https://avatax.avalara.net';
        if (_to_sys_organization.org_avalara_url_type) {
            sAvalaraURL = 'https://development.avalara.net';
        }
        var oPostRequest = oHTTPClient.createPostRequest(sAvalaraURL + '/1.0/tax/get');
        var sCurrencyID;
        var dOrderDate;
        aAddressesWithFreightCalculated = new Array();
        var aFreightTaxes = new Array();
        var aAddressCodes = [];
        var oTaxInfo = new Object();
        var aAddresses = {};
        var aLinesWithPostalCodeState = [];
        var aTaxLineDetails = [];
        var nAddressCounter = 0;
        var rOrder = rOrderHeader.sa_order_revision_header_to_sa_order.getRecord(1);
        var rFOBAddress = scopes.avSales.getOrderFOBAddress(rOrder);
        var sHeaderID = rOrderHeader.ordrevh_id.toString();
        
        var fsMsQtyTask = null,
            rMsQtyTask = null,
            k = 0,
            nTaxAmount = 0,
            aTaxNew = [],
            /**@type {JSFoundSet<db:/avanti/sa_order_tax_detail>} */
            fsTaxDetail = null,
            m = 0, n = 0,
            iAvalaraTax = null,
            sTaxKey = "",
            fsTaxTaxType = null,
            sCustomerTaxExemptionID = null;
        
        oTaxInfo.Addresses = new Array();
        oTaxInfo.Lines = new Array();

        oTaxInfo.Commit = 'false';
        oTaxInfo.DocType = 'SalesOrder';

        oTaxInfo.Client = 'Avanti Slingshot';
        oTaxInfo.CompanyCode = _to_sys_organization.org_avalara_company_code;

        if (rOrderHeader && utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order)) {
            sOrderNumber = rOrderHeader.sa_order_revision_header_to_sa_order.ordh_document_num;
            if (utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer) && rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer.cust_code) {
                oTaxInfo.CustomerCode = rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer.cust_code;
            }
            if (rOrderHeader.sa_order_revision_header_to_sa_order.ordh_document_num) {
                oTaxInfo.DocCode = rOrderHeader.sa_order_revision_header_to_sa_order.ordh_document_num;
            }

            if (rOrderHeader.sa_order_revision_header_to_sa_order.ordh_order_date) {
                dOrderDate = rOrderHeader.sa_order_revision_header_to_sa_order.ordh_order_date;
                oTaxInfo.DocDate = utils.dateFormat(rOrderHeader.sa_order_revision_header_to_sa_order.ordh_order_date, 'yyyy-MM-dd');
            }

            sCurrencyID = rOrderHeader.sa_order_revision_header_to_sa_order.curr_id;
            if (utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sys_currency) && rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sys_currency.curr_iso_code) {
                oTaxInfo.CurrencyCode = rOrderHeader.sa_order_revision_header_to_sa_order.sa_order_to_sys_currency.curr_iso_code;
            }
        }

        if (utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted)) {
            if (utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.sa_order_revision_detail_to_in_warehouse)) {
                oTaxInfo.LocationCode = rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.sa_order_revision_detail_to_in_warehouse.whse_code;
            }

            var nLineNumber = 0;
            var oOrigin = null;
            var oFOB = null;
            
            for (var nDetailIndex = 1; nDetailIndex <= rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted$nolinegang.getSize(); nDetailIndex++) {
                
                var rOrderRevDetail = rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted$nolinegang.getRecord(nDetailIndex);
                
                if (rOrderRevDetail.item_id 
                        && utils.hasRecords(rOrderRevDetail.sa_order_revision_detail_to_in_item)
                        && rOrderRevDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avInv.ITEM_TYPE_CODE.ServiceItem
                        && rOrderRevDetail.sa_order_revision_detail_to_in_item.item_exclude_from_avatax == 1) {
                    continue;
                }
                
        		updateProcessLabelUI("Calculate Avalara Tax: Processing line item # " + nDetailIndex);
                
                for (var nMultiShipQtyIndex = 1; nMultiShipQtyIndex <= rOrderRevDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getSize(); nMultiShipQtyIndex++) {
                    var rMultiShipQty = rOrderRevDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getRecord(nMultiShipQtyIndex);
                    var rFromAddress = null;
                    var sAddressCodeFrom = '';
                    var aLineAddresses = [];

					if (nMultiShipQtyIndex % 100 == 0) {
		        		updateProcessLabelUI("Calculate Avalara Tax: Processing Address # " + nMultiShipQtyIndex);
					}
                    
                    if (rMultiShipQty.ordrevdms_qty > 0) {
                    	// ORIGIN
						if (oOrigin) {
							var oOriginClone = Object.assign({}, oOrigin);
							
							oOriginClone.AddressCode = "ORIGIN" + ( nAddressCounter + 1 );
                            aLineAddresses.push(oOriginClone);
                            oTaxInfo.Addresses[nAddressCounter] = oOriginClone;
                            nAddressCounter++;
						}
						else {
	                    	if (utils.hasRecords(rOrderRevDetail.sa_order_revision_detail_to_in_warehouse)
	                                && utils.hasRecords(rOrderRevDetail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address)) {
	                            rFromAddress = rOrderRevDetail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address.getRecord(1);
	                        }
	                        else if (utils.hasRecords(rOrder.sa_order_to_sa_order_address_billto) 
	                                && utils.hasRecords(rOrder.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address)) {
	                            rFromAddress = rOrder.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.getRecord(1);
	                        }
	                        
	                        if (rFromAddress) {
								oOrigin = new Object();
	                            
	                            sAddressCodeFrom = "ORIGIN" + ( nAddressCounter + 1 );

	                            oOrigin.AddressCode = sAddressCodeFrom;
	                            oOrigin.Line1 = rFromAddress.addr_address1;
	                            oOrigin.Line2 = rFromAddress.addr_address2;
	                            oOrigin.Line3 = rFromAddress.addr_address3;
	                            oOrigin.City = rFromAddress.addr_city;

	                            if (utils.hasRecords(rFromAddress.sys_address_to_sys_state_province)) {
	                            	oOrigin.Region = rFromAddress.sys_address_to_sys_state_province.stateprov_code;
	                            }
	                            else {
	                            	oOrigin.Region = '';
	                            }

	                            if (utils.hasRecords(rFromAddress.sys_address_to_sys_country)) {
	                            	oOrigin.Country = rFromAddress.sys_address_to_sys_country.country_code_2_char_iso;
	                            }

	                            oOrigin.PostalCode = rFromAddress.addr_postal;

	                            aAddressCodes[rFromAddress.addr_id] = sAddressCodeFrom;
	                            
	                            aLineAddresses.push(oOrigin);
	                            oTaxInfo.Addresses[nAddressCounter] = oOrigin;
	                            nAddressCounter++;
	                        }
						}
                    	
                        //DESTINATION
                        var bUseLineItemAmount = false;
                        var oDestAddress = null;

						if (oFOB) {
							var oFOBClone = Object.assign({}, oFOB);

							oFOBClone.AddressCode = rFOBAddress.custaddr_code + ( nAddressCounter + 1 );
							oDestAddress = oFOBClone;
                            aLineAddresses.push(oFOBClone);
                            oTaxInfo.Addresses[nAddressCounter] = oFOBClone;
                            nAddressCounter++;
						}
						else if (utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship)) {
                            var rMultiShip = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship.getSelectedRecord();
                            var rMultiShipAddress = null;
                            var sMSAddrCode = null;
                            
                            // FOB Address
                            if (rFOBAddress) {
								if (utils.hasRecords(rFOBAddress.sa_customer_address_to_sys_address)) {
	                            	rMultiShipAddress = rFOBAddress.sa_customer_address_to_sys_address.getRecord(1);
								}
								
                                sMSAddrCode = rFOBAddress.custaddr_code;
                            }
                            // Pickup
                            else if (utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_sys_shipping_method) 
                                    && rMultiShip.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_type == scopes.avUtils.ENUM_SHIPMETHOD_TYPE.Pickup
                                    && utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_in_warehouse)) {

                                var rWarehouse = rMultiShip.sa_order_revh_multi_ship_to_in_warehouse.getRecord(1);
                                
                                if (utils.hasRecords(rWarehouse.in_warehouse_to_sys_address)) {
                                    rMultiShipAddress = rWarehouse.in_warehouse_to_sys_address.getRecord(1);
                                }

                                sMSAddrCode = rWarehouse.whse_code;
                            }
                            else {
                                if (utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_sys_address)) {
                                    rMultiShipAddress = rMultiShip.sa_order_revh_multi_ship_to_sys_address;
                                }

                                if (utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_sa_customer_address)) {
                                    sMSAddrCode = rMultiShip.sa_order_revh_multi_ship_to_sa_customer_address.custaddr_code;
                                }
                            }
                            
                            oTaxInfo.Addresses[nAddressCounter] = new Object();
                            
                            sMSAddrCode += ( nAddressCounter + 1 );
                            oTaxInfo.Addresses[nAddressCounter].AddressCode = sMSAddrCode;
                            aAddressCodes[rMultiShip.ordrevhms_id] = sMSAddrCode;

							if (rMultiShipAddress) {
	                            oTaxInfo.Addresses[nAddressCounter].Line1 = rMultiShipAddress.addr_address1;
	                            oTaxInfo.Addresses[nAddressCounter].Line2 = rMultiShipAddress.addr_address2;
	                            oTaxInfo.Addresses[nAddressCounter].Line3 = rMultiShipAddress.addr_address3;
	                            oTaxInfo.Addresses[nAddressCounter].City = rMultiShipAddress.addr_city;
	                            oTaxInfo.Addresses[nAddressCounter].PostalCode = rMultiShipAddress.addr_postal;
	                            
	                            if (utils.hasRecords(rMultiShipAddress.sys_address_to_sys_state_province)) {
	                                oTaxInfo.Addresses[nAddressCounter].Region = rMultiShipAddress.sys_address_to_sys_state_province.stateprov_code;
	                            }
	                            else {
	                                oTaxInfo.Addresses[nAddressCounter].Region = '';
	                            }
	                            
	                            if (utils.hasRecords(rMultiShipAddress.sys_address_to_sys_country)) {
	                                oTaxInfo.Addresses[nAddressCounter].Country = rMultiShipAddress.sys_address_to_sys_country.country_code_2_char_iso;
	                            }
							}
							
							if (rFOBAddress && !oFOB) {
								oFOB = oTaxInfo.Addresses[nAddressCounter];
							}
							
							oDestAddress = oTaxInfo.Addresses[nAddressCounter];
                            aLineAddresses.push(oTaxInfo.Addresses[nAddressCounter]);
							nAddressCounter++;
                        }
                        
                        aAddresses[nLineNumber] = aLineAddresses;

                        oTaxInfo.Lines[nLineNumber] = new Object();
                        oTaxInfo.Lines[nLineNumber].LineNo = String(nLineNumber + 1);
                        oTaxInfo.Lines[nLineNumber].OriginCode = sAddressCodeFrom;
                        oTaxInfo.Lines[nLineNumber].DestinationCode = sMSAddrCode;
                        
                        var sTaxExemptionID = null;
                        if (utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sys_tax_type)) {
                            if (rMultiShipQty.sa_order_revd_multi_ship_qty_to_sys_tax_type.taxtype_code_avalara) {
                                oTaxInfo.Lines[nLineNumber].TaxCode = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sys_tax_type.taxtype_code_avalara;
                            }
                            else {
                                oTaxInfo.Lines[nLineNumber].TaxCode = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sys_tax_type.taxtype_code;
                            }
                            sTaxExemptionID = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sys_tax_type.tax_exemption_id;
                            bUseLineItemAmount = true;
                        }

                        if (utils.hasRecords(rOrder.sa_order_to_sa_customer)
                                && rOrder.sa_order_to_sa_customer.cust_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnCustomerShipTo 
                                && rOrder.sa_order_to_sa_customer.tax_exemption_id) {
                            sCustomerTaxExemptionID = rOrder.sa_order_to_sa_customer.tax_exemption_id;      
                        }                       

                        var sTaxCode = oTaxInfo.Lines[nLineNumber].TaxCode,
                            sCustomerUsageType = oTaxInfo.Lines[nLineNumber].CustomerUsageType;
                        
                        /// pop arrayOfLinesWithPostalCodeState, to be passed into processTaxGroups 
                        var oLineObject = new Object();
                        
                        oLineObject.sLineNumber = oTaxInfo.Lines[nLineNumber].LineNo;
                        oLineObject.sDestinationCode = oTaxInfo.Lines[nLineNumber].DestinationCode;
                        
                        // dest address
						if (oDestAddress) {
							oLineObject.postalCode = oDestAddress.PostalCode;
							oLineObject.state = oDestAddress.Region;
							oLineObject.country = oDestAddress.Country;
						}
                        
                        aLinesWithPostalCodeState.push(oLineObject);
                        
                        if (scopes.avAccounting.bGpSendInvoices 
                                && utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task)) {
                        
                            fsMsQtyTask = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task;
                                                       
                            for (k = 1; k <= fsMsQtyTask.getSize(); k++) {
                                
                                rMsQtyTask = fsMsQtyTask.getRecord(k);

                                if (k > 1) {
                                    oTaxInfo.Lines[nLineNumber] = new Object();
                                    oTaxInfo.Lines[nLineNumber].LineNo = String(nLineNumber + 1);
                                    oTaxInfo.Lines[nLineNumber].OriginCode = sAddressCodeFrom;
                                    oTaxInfo.Lines[nLineNumber].DestinationCode = sMSAddrCode;
                                    oTaxInfo.Lines[nLineNumber].TaxCode = sTaxCode;
                                    oTaxInfo.Lines[nLineNumber].CustomerUsageType = sCustomerUsageType;
                                }
                                
                                oTaxInfo.Lines[nLineNumber].Description = rMsQtyTask.clc_billing_code_description;
                                if (rMsQtyTask.ordrevdmstask_qty) {
                                	oTaxInfo.Lines[nLineNumber].Qty = rMsQtyTask.ordrevdmstask_qty.toLocaleString();
                                } else {
                                	oTaxInfo.Lines[nLineNumber].Qty = rMsQtyTask.ordrevdmstask_qty;
                                }
                                nTaxAmount = globals["avUtilities_roundNumber"](rMsQtyTask.ordrevdmstask_total, 2);
                                oTaxInfo.Lines[nLineNumber].ItemCode = rMsQtyTask.clc_billing_code;
                                
                                if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                                    oTaxInfo.Lines[nLineNumber].Amount = convertToCurrency(sCurrencyID, dOrderDate, nTaxAmount, false);
                                }
                                else {
                                    oTaxInfo.Lines[nLineNumber].Amount = globals.formatNumberToTwoDecimal(nTaxAmount);
                                }
                                
                                sTaxExemptionID = null;
                                fsTaxTaxType = null;
                                if (utils.hasRecords(rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_revds_task)
                                        && utils.hasRecords(rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)
                                        && utils.hasRecords(rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sys_tax_type)) {
                                    fsTaxTaxType = rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sys_tax_type;
                                }
                                if (utils.hasRecords(fsTaxTaxType)) {
                                    
                                    if (fsTaxTaxType.taxtype_code_avalara) {
                                        oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTaxType.taxtype_code_avalara;
                                    }
                                    else {
                                        oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTaxType.taxtype_code;
                                    }
                                    sTaxExemptionID = fsTaxTaxType.tax_exemption_id;
                                    bUseLineItemAmount = true;
                                }
                                
                                if (rMultiShipQty.ordrevdms_qty_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.NonTaxable 
                                        || sCustomerTaxExemptionID
                                        || ( rMultiShipQty.ordrevdms_qty_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnTaxType && sTaxExemptionID )) {
                                    if (rMultiShipQty.tax_exemption_id) {
                                        sTaxExemptionID = rMultiShipQty.tax_exemption_id;
                                    }
                                    else if (sCustomerTaxExemptionID) {
                                        sTaxExemptionID = sCustomerTaxExemptionID; 
                                    }
                                    else {
                                        oTaxInfo.Lines[nLineNumber].TaxCode = "NT";
                                    }
                        
                                    oTaxInfo.Lines[nLineNumber].CustomerUsageType = application.getValueListDisplayValue('vl_taxExemptionReasonCodes', sTaxExemptionID);
                                }
                                
                                nLineNumber++;
                                rMultiShipQty.ordrevdms_tax_line_num = nLineNumber;
                                rMsQtyTask.ordrevdmstask_tax_line_num = nLineNumber;
                                rOrderRevDetail.ordrevd_tax_line_num = nLineNumber;
                            }        
                        }
                        else {
                            //// ---- START of Regular Line Item Tax
                            
                            if (rMultiShipQty.ordrevdms_qty_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.NonTaxable
                                    || sCustomerTaxExemptionID
                                    || ( rMultiShipQty.ordrevdms_qty_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnTaxType && sTaxExemptionID )) {
                                if (rMultiShipQty.tax_exemption_id) {
                                    sTaxExemptionID = rMultiShipQty.tax_exemption_id;
                                }
                                else if (sCustomerTaxExemptionID) {
                                    sTaxExemptionID = sCustomerTaxExemptionID;
                                }
                                else {
                                    oTaxInfo.Lines[nLineNumber].TaxCode = "NT";
                                }

                                oTaxInfo.Lines[nLineNumber].CustomerUsageType = application.getValueListDisplayValue('vl_taxExemptionReasonCodes', sTaxExemptionID);
                            }
            
                            oTaxInfo.Lines[nLineNumber].ItemCode = application.getValueListDisplayValue('avSales_lineItemCodes_all', rOrderRevDetail.display_code_id);
    
                            oTaxInfo.Lines[nLineNumber].Description = rOrderRevDetail.ordrevd_prod_desc;
                            if (rMultiShipQty.ordrevdms_qty) {
                            	oTaxInfo.Lines[nLineNumber].Qty = rMultiShipQty.ordrevdms_qty.toLocaleString();
                            }
                            else {
                            	oTaxInfo.Lines[nLineNumber].Qty = rMultiShipQty.ordrevdms_qty;
                            }
    
                            nTaxAmount = globals["avUtilities_roundNumber"](rMultiShipQty.ordrevdms_taxable_amt, 2);
    
                            //Add postage tax
                            if (utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revds_task_post_ms)) {
                                var rTaskPostMultiShip = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revds_task_post_ms.getRecord(1);
    
                                if (utils.hasRecords(rTaskPostMultiShip.sa_order_revds_task_post_ms_to_sa_order_revds_task_post) 
                                        && utils.hasRecords(rTaskPostMultiShip.sa_order_revds_task_post_ms_to_sa_order_revds_task_post.sa_order_revds_task_post_to_sa_order_revds_task) 
                                        && utils.hasRecords(rTaskPostMultiShip.sa_order_revds_task_post_ms_to_sa_order_revds_task_post.sa_order_revds_task_post_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)) {
                                    var rTask = rTaskPostMultiShip.sa_order_revds_task_post_ms_to_sa_order_revds_task_post.sa_order_revds_task_post_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);
    
                                    if (rTask.task_salestax_option != scopes.avUtils.ENUM_SALES_TAX_OPTION.NonTaxable) {
                                        nTaxAmount += globals["avUtilities_roundNumber"](rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revds_task_post_ms.ordrevdstaskpostms_taxable_amt, 2);
                                    }
                                }
                            }
                            
                            if (bUseLineItemAmount) {
                                if (rMultiShipQty.ordrevdms_extended_total) {
                                    nTaxAmount = rMultiShipQty.ordrevdms_extended_total;
                                }
                                else {
                                    nTaxAmount = rOrderRevDetail.ordrevd_extended_price;
                                }
                            }
                            if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                                oTaxInfo.Lines[nLineNumber].Amount = convertToCurrency(sCurrencyID, dOrderDate, nTaxAmount, false);
                            }
                            else {
                                oTaxInfo.Lines[nLineNumber].Amount = globals.formatNumberToTwoDecimal(nTaxAmount);
                                
                            }
                            nLineNumber++;
                            rMultiShipQty.ordrevdms_tax_line_num = nLineNumber;
                            rOrderRevDetail.ordrevd_tax_line_num = nLineNumber;
                        } //// ---- END of Regular Line Item Tax                    
                    } // END of if (rMultiShipQty.ordrevdms_qty > 0)
                } // END of rMultiShipQty FOR LOOP
            } // END of rDetail FOR LOOP
            
            if (nLineNumber == 0) {
                finallyCode();
                return false;
            }

            nAddressCounter = 0;
            
            // Calculate freight tax
            /**@type {JSFoundSet<db:/avanti/sa_order_revh_multi_ship>} */
            var fsFreightMS = scopes.avDB.getFSFromSQL("SELECT ordrevhms_id FROM sa_order_revh_multi_ship WHERE ordrevh_id = ? AND ordrevhms_shipping_charges > 0 AND ordrevhms_ship_charges_taxable = 1", 
            	"sa_order_revh_multi_ship", [rOrderHeader.ordrevh_id.toString()]);
            
            for (var nMultiShipIndex = 1; nMultiShipIndex <= fsFreightMS.getSize(); nMultiShipIndex++) {
                var rMS = fsFreightMS.getRecord(nMultiShipIndex);
                
                if (aAddressesWithFreightCalculated.indexOf(rMS.ordrevhms_id) < 0) {
                    aAddressesWithFreightCalculated.push(rMS.ordrevhms_id);
                    var oNewFreight = new Object();
                    nMultiShipQtyIndex++;
                    oNewFreight.LineNo = 'Freight' + rMS.sequence_nr;
                    oNewFreight.DestinationCode = aAddressCodes[rMS.ordrevhms_id];

                    oNewFreight.ItemCode = i18n.getI18NMessage('avanti.lbl.freight');
                    oNewFreight.Description = i18n.getI18NMessage('avanti.lbl.freight');
                    oNewFreight.Qty = 1;
                    if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                        oNewFreight.Amount = convertToCurrency(sCurrencyID, dOrderDate, rMS.ordrevhms_shipping_charges, false);
                    }
                    else {
                        oNewFreight.Amount = utils.stringToNumber(utils.numberFormat(rMS.ordrevhms_shipping_charges, 2));
                    }

                    if (rMS.ordrevhms_ship_charges_taxable == 0) {
                        oNewFreight.TaxCode = 'NT';
                    }
                    else if (utils.hasRecords(rMS.sa_order_revh_multi_ship_to_sys_shipping_method) && rMS.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_avalara_tax_code) {
                        oNewFreight.TaxCode = rMS.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_avalara_tax_code;
                    }
                    oTaxInfo.Lines.push(oNewFreight);
                    nLineNumber++;
                }
            }
            
            var jsonContent = plugins.serialize.toJSON(oTaxInfo);
            oPostRequest.addHeader('Content-Type', 'application/json');
            oPostRequest.addHeader('Authorization', sAvalaraAPIKey);
            oPostRequest.setBodyContent(jsonContent);
            
    		updateProcessLabelUI("Calculate Avalara Tax: Calling Avalara");
            scopes.avUtils.stopWatchStart("oPostRequest.executeRequest");
            
            var response = oPostRequest.executeRequest();

            scopes.avUtils.devOutputTime("oPostRequest.executeRequest");
    		updateProcessLabelUI("Calculate Avalara Tax: Processing Avalara Response");

            if (response) {
                var sResponseBody = response.getResponseBody();

                if (sResponseBody) {
                    /** @type {{ResultCode, TaxLines:{LineNo, TaxCode, Taxability, BoundaryLevel, Discount, Taxable, Rate, Tax, TaxCalculated, TaxDetails:{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}}, TaxAddresses:{Address, AddressCode, City, Country, PostalCode, Region, TaxRegionId, JurisCode}}} */
                    var oAvalaraResponse = plugins.serialize.fromJSON(response.getResponseBody());
                    if (oAvalaraResponse.ResultCode == 'Error') {
                        var sErrorMessage = showAvalaraError(oAvalaraResponse);
                        globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax from AvaTax for order ' + sOrderNumber + ' due to ' + sErrorMessage, 'Error', sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                        globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', jsonContent);
                    	finallyCode();
                        return false;
                    }
                    else {
                    	if (response.getStatusCode() !== plugins.http.HTTP_STATUS.SC_OK) {
                    		globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax from AvaTax for order ' + sOrderNumber + '. Server response status code: ' + response.getStatusCode() + '. Please check the setup in the Organization program.', 'Error',
                                sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                        	finallyCode();
                            return false;
                    	}
                    	
                    	scopes.avDB.uDataBroadcastSessionID = application.getUUID().toString();
                    	
                        if (oAvalaraResponse.TaxLines) {
                            var aTaxes = {};
                            var aTax;
                            var aTaxMSQ;
                            var sSQL = "DELETE FROM sa_order_tax_detail "
                            			+ scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION) + " \
										WHERE ordrevdms_id IN ( \
											SELECT MSQ.ordrevdms_id \
											FROM sa_order_revd_multi_ship_qty MSQ \
											INNER JOIN sa_order_revh_multi_ship MS ON MSQ.ordrevhms_id = MS.ordrevhms_id \
											WHERE MS.ordrevh_id = ? \
											)";	

                            // Clear out tax details on order and shipping only after successful message.
                        	scopes.avDB.RunSQL("DELETE FROM sa_order_tax_detail " 
                        		+ scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION)
                        		+ " WHERE ordrevh_id_multiship = ?", null, [sHeaderID]);    	
                        	scopes.avDB.RunSQL("DELETE FROM sa_order_tax_detail " 
                        		+ scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION)
                        		+ " WHERE ordrevh_id = ?", null, [sHeaderID]);    	
                        	scopes.avDB.RunSQL("DELETE FROM sa_order_tax_detail " 
                        		+ scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION)
                        		+ " WHERE ordrevd_id IN (SELECT ordrevd_id FROM sa_order_revision_detail WHERE ordrevh_id = ?)", null, [sHeaderID]);    	
                        	scopes.avDB.RunSQL(sSQL, null, [sHeaderID]);    	
                            
                        	var nLastLineNumber = 0;
                        	var nTaxLine = 0;
                        	
                            for (var key in oAvalaraResponse.TaxLines) {
                                var oTaxLineObject = new Object();

                                nTaxLine++
								
								if (nTaxLine % 100 == 0) {
						    		updateProcessLabelUI("Calculate Avalara Tax: Processing Tax Line # " + nTaxLine);
								}
                                
                                oTaxLineObject.sLineNumber = oAvalaraResponse.TaxLines[key].LineNo;
                                oTaxLineObject.aTaxDetails = oAvalaraResponse.TaxLines[key].TaxDetails;
                                
                                aTaxLineDetails.push(oTaxLineObject);
                                
                            	if (oAvalaraResponse.TaxLines[key].Taxable) {
                                    for (var detailKey in oAvalaraResponse.TaxLines[key].TaxDetails) {
                                        /** @type  {{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}} */
                                        var oTaxDetailLine = oAvalaraResponse.TaxLines[key].TaxDetails[detailKey];
                                        var oTaxDetails = getTaxItem_SQL(oTaxDetailLine, oAvalaraResponse.TaxDate, rOrder.curr_id, oAvalaraResponse);
                                        var oTaxItems = new Object();
                                        
										if (oAvalaraResponse.TaxLines[key].LineNo != nLastLineNumber) {
											if (nLastLineNumber && aTax.length > 0) {
												aTaxes[nLastLineNumber] = aTax;
											}
											
											aTax = [];
	                                        nLastLineNumber = oAvalaraResponse.TaxLines[key].LineNo; 
										}
                                        
                                        oTaxItems.line_number = oAvalaraResponse.TaxLines[key].LineNo;
                                        oTaxItems.taxitem_id = oTaxDetails.sTaxItemID;
                                        
                                        if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                                            oTaxItems.taxable_amt = convertToCurrency(sCurrencyID, dOrderDate, oTaxDetailLine.Taxable, true);
                                        }
                                        else {
                                            oTaxItems.taxable_amt = Number(oTaxDetailLine.Taxable);
                                         }
                                        
                                        //Get the tax rate
                                        oTaxItems.tax_percent = utils.stringToNumber(oTaxDetailLine.Rate);
                                        oTaxItems.tax_rate_id = oTaxDetails.sTaxRateID;

                                        oTaxItems.tax_amt = 0.00;
                                        oTaxItems.tax_amt = oTaxItems.taxable_amt * oTaxItems.tax_percent;
                                        //Round up to 2 decimal places for now.
                                        oTaxItems.tax_amt = scopes.avUtils.roundNumber(oTaxItems.tax_amt, 2);

                                        if (oAvalaraResponse.TaxLines[key].LineNo.indexOf('Freight') >= 0) {
                                            aFreightTaxes.push(oTaxItems);
                                        }
                                        
                                        aTax.push(oTaxItems);
                                    }
                                }
                            }
                            
							if (nLastLineNumber && aTax.length > 0) {
								aTaxes[nLastLineNumber] = aTax;
							}
                            
                            if (utils.hasRecords(rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted$nolinegang)) {
                                fsTaxDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_tax_detail');
                                
                                for (nDetailIndex = 1; nDetailIndex <= rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted$nolinegang.getSize(); nDetailIndex++) {
                                    rOrderRevDetail = rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted$nolinegang.getRecord(nDetailIndex);

                                    if (rOrderRevDetail.item_id 
                                            && utils.hasRecords(rOrderRevDetail.sa_order_revision_detail_to_in_item)
                                            && rOrderRevDetail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avInv.ITEM_TYPE_CODE.ServiceItem
                                            && rOrderRevDetail.sa_order_revision_detail_to_in_item.item_exclude_from_avatax == 1) {
                                        continue;
                                    }
                                    
						    		updateProcessLabelUI("Calculate Avalara Tax: Processing Line Item # " + nDetailIndex);

                                    for (k = 1; k <= rOrderRevDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getSize(); k++) {
                                        rMultiShipQty = rOrderRevDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getRecord(k);
                                        
        								if (k % 100 == 0) {
        						    		updateProcessLabelUI("Calculate Avalara Tax: Processing Address # " + k);
        								}
                                        
                                        if (rMultiShipQty.ordrevdms_qty > 0) {
                                          
                                            if (scopes.avAccounting.bGpSendInvoices 
                                                    && !scopes.avAccounting.bGpDisableBillingCodes
                                                    && utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task)) {   
                                                fsMsQtyTask = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task;              
                                              
                                                for (n = 1; n <= fsMsQtyTask.getSize(); n++) {
                                                    
                                                    rMsQtyTask = fsMsQtyTask.getRecord(n);
                                                    aTaxMSQ = aTaxes[rMsQtyTask.ordrevdmstask_tax_line_num];
                                                    iAvalaraTax = null;
                                            
                                                    fsTaxDetail = rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_tax_detail;
                                                    nTaxAmount = 0.00;
                                                    
                                                    for (m = 0; m < aTaxMSQ.length; m++) {
                                                        sTaxKey = aTaxMSQ[m].line_number + "_" + aTaxMSQ[m].taxitem_id;
                                                        
                                                        if (rMsQtyTask.ordrevdmstask_tax_line_num == aTaxMSQ[m].line_number && aTaxNew.indexOf(sTaxKey) == -1) {
                                                        	fsTaxDetail.newRecord();
                                                    
                                                        	fsTaxDetail.ordhtaxdet_taxable_sales_amt = aTaxMSQ[m].taxable_amt;
                                                        	fsTaxDetail.ordhtaxdet_tax_amt = aTaxMSQ[m].tax_amt;
                                                        	fsTaxDetail.ordhtaxdet_taxrate_percent = aTaxMSQ[m].tax_percent;
                                                        	fsTaxDetail.taxitem_id = aTaxMSQ[m].taxitem_id;
                                                        	fsTaxDetail.taxrate_id = aTaxMSQ[m].taxrate_id;
                                                            fsTaxDetail.ordrevdms_id = rMultiShipQty.ordrevdms_id;
                                                            
                                                            if (!fsTaxDetail.taxitem_id) {
                                                                fsTaxDetail.taxitem_id = oTaxDetails.sTaxItemID;
                                                            }
                                                            
                                                            if (!fsTaxDetail.taxrate_id) {
                                                                fsTaxDetail.taxrate_id = oTaxDetails.sTaxRateID;
                                                            }
                                                            
                                                            scopes.avDB.insertRecWithSQL(fsTaxDetail, true, true);
                                                            
                                                            nTaxAmount += aTaxMSQ[m].tax_amt;
                                                            aTaxNew.push(sTaxKey);
                                                            iAvalaraTax = 1;
                                                        }
                                                    }

                                                    rMsQtyTask.ordrevdmstask_total_tax = nTaxAmount;     
                                                    rMsQtyTask.ordrevdmstask_flg_avalara = iAvalaraTax;     
                                                }
                                            } 
                                            else {
                                            	
                                            	 if (scopes.avAccounting.bGpSendInvoices 
                                                         && utils.hasRecords(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task)) {                                                        	 

                                                     fsMsQtyTask = rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task;              
                                                   
                                                     for (n = 1; n <= fsMsQtyTask.getSize(); n++) {
                                                         
                                                         rMsQtyTask = fsMsQtyTask.getRecord(n);
                                                         aTaxMSQ = aTaxes[rMsQtyTask.ordrevdmstask_tax_line_num];
                                                         avCalcs_salesTax_setOrderTaxDetails(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_tax_detail, aTaxMSQ, null, rMultiShipQty.ordrevdms_tax_line_num, true, rMultiShipQty.ordrevdms_id); //By Quantity and Location
                                                     }
                                            	 }
                                            	 else {
                                            		 
	                                                aTaxMSQ = aTaxes[rMultiShipQty.ordrevdms_tax_line_num];
	                                                avCalcs_salesTax_setOrderTaxDetails(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_tax_detail, aTaxMSQ, null, rMultiShipQty.ordrevdms_tax_line_num, true, rMultiShipQty.ordrevdms_id); //By Quantity and Location
                                            	 }
                                        	 }
                                            
                                            rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_tax_detail.loadAllRecords();
                                            avCalcs_salesTax_updateOrderDetailTaxDetails(rMultiShipQty.sa_order_revd_multi_ship_qty_to_sa_order_tax_detail, rOrderRevDetail.sa_order_revision_detail_to_sa_order_tax_detail, true, rOrderRevDetail.ordrevd_id);
                                        }
                                    }

                                    rOrderRevDetail.sa_order_revision_detail_to_sa_order_tax_detail.loadAllRecords();
                                    avCalcs_salesTax_updateOrderDetailTaxDetails(rOrderRevDetail.sa_order_revision_detail_to_sa_order_tax_detail, rOrderHeader.sa_order_revision_header_to_sa_order_tax_detail, true, null, rOrderHeader.ordrevh_id);
                                }
                                
                                rOrderHeader.sa_order_revision_header_to_sa_order_tax_detail$shippingtaxes.loadAllRecords();
                                avCalcs_salesTax_setOrderTaxDetails(rOrderHeader.sa_order_revision_header_to_sa_order_tax_detail$shippingtaxes, aFreightTaxes, rMultiShipQty.ordrevhms_id);
                                avCalcs_salesTax_updateOrderDetailTaxDetails(rOrderHeader.sa_order_revision_header_to_sa_order_tax_detail$shippingtaxes, rOrderHeader.sa_order_revision_header_to_sa_order_tax_detail, true, null, rOrderHeader.ordrevh_id);
                            }
                        }
                    }
                    
		    		updateProcessLabelUI("Calculate Avalara Tax: Process Tax Groups");
                    processTaxGroups_SQL(oTaxInfo, oAvalaraResponse, aLinesWithPostalCodeState, aTaxLineDetails, rOrder.curr_id);
                    updateTaxGroups(rOrderHeader.sa_order_revision_header_to_sa_order.getSelectedRecord());
                    
                	scopes.avUtils.broadcastDataChanges(scopes.avDB.uDataBroadcastSessionID, null, true);                	
                    databaseManager.saveData();

                    rOrderHeader.ordrevh_total_taxes = scopes.avDB.SQLQuery("SELECT SUM(ordhtaxdet_tax_amt) FROM sa_order_tax_detail WHERE ordrevh_id = ?", null, [rOrderHeader.ordrevh_id.toString()]);
                    rOrderHeader.ordrevh_total_amount = scopes.avSales.getOrderTotalAmount(rOrderHeader);
                    
                    globals.dbLogUpdate(sDBLogID, 'Successfully completed calculating tax from AvaTax for order ' + sOrderNumber, 'Success', sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                    globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
                    
	            	finallyCode();
                    return true;
                }
                else {
                    globals.dbLogUpdate(sDBLogID, 'No response from AvaTax for order ' + sOrderNumber, 'Error', sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                    application.output('No Response from Avalara', LOGGINGLEVEL.ERROR);
                    globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
                	finallyCode();
                    return false;
                }
            }
            globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax from AvaTax for order ' + sOrderNumber + '. Please check the setup in the Organization program.', 'Error',
                sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
            if (sResponseBody) {
            	globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
            }
            globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
        	finallyCode();
            return false;
        }
        globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax from AvaTax for order ' + sOrderNumber + '. Please check the setup in the Organization program.', 'Error',
            sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
        if (sResponseBody) {
        	globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
        }
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
    	finallyCode();
        return false;
    }
    catch (ex) {
        globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax from AvaTax for order ' + sOrderNumber + '. Please check the setup in the Organization program.', 'Error',
            sOrderNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
        if (sResponseBody) {
        	globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
        }
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
    	finallyCode();
        return false;
    }

    // had to get rid of the 'finally' block - updateUI in updateProcessingLabel causes execution to jump to finally    
    function finallyCode() {
    	
    	if (oHTTPClient) {
    		oHTTPClient.close();
    	}
    	
    	if (response) {
    	}
    	
        oStopWatch.stop();
        scopes.avSales.LogPerformance("calculateTaxForOrderAvalara", oStopWatch.total(true), scopes.avSales.EST_COMPONENT.Revision, rOrderHeader.ordrevh_id);
        scopes.avUtils.devOutputTime("calculateTaxForOrderAvalara");
		updateProcessLabelUI("Calculate Avalara Tax: DONE", null, true);
    }
}

/**
 * @public 
 * 
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetail
 * @param {Array} aTax
 * @param {UUID} [uMultiShipHeaderID]
 * @param {Number} [nLineNumber]
 * @param {Boolean} [bUseSQL]
 * @param {UUID} [uMSQID]
 * @param {UUID} [uTaskPostMsId]
 *
 * @properties={typeid:24,uuid:"CA138277-2ACF-43EF-BC80-FD6A73C3D4C5"}
 */
function avCalcs_salesTax_setOrderTaxDetails(fsTaxDetail, aTax, uMultiShipHeaderID, nLineNumber, bUseSQL, uMSQID, uTaskPostMsId) {
	if (bUseSQL) {
		avCalcs_salesTax_setOrderTaxDetails_SQL(aTax, uMultiShipHeaderID, uMSQID, uTaskPostMsId);
	}
	else {
		avCalcs_salesTax_setOrderTaxDetails_FS(fsTaxDetail, aTax, uMultiShipHeaderID, nLineNumber);
	}
}


/**
 * @private 
 * 
 * avCalcs_salesTax_setOrderTaxDetails
 *
 * <AUTHOR> Dol
 * @since Apr 14, 2013
 *
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetail
 * @param {Array} aTax
 * @param {UUID} [uMultiShipHeaderID]
 * @param {Number} [nLineNumber]
 *
 * @properties={typeid:24,uuid:"0252A844-18FC-4916-B1E1-CE7A8AAAC584"}
 */
function avCalcs_salesTax_setOrderTaxDetails_FS(fsTaxDetail, aTax, uMultiShipHeaderID, nLineNumber) {
    /***@type {{taxitem_id:String, taxable_amt:Number, tax_percent:Number, tax_rate_id:String, tax_amt:Number}}*/
    var oTaxItems = new Object();
    var rTaxDetail;

    //Write the tax detail records
    for (var m = 0; m < aTax.length; m++) {
        if (!nLineNumber || nLineNumber == aTax[m].line_number) {
            oTaxItems = aTax[m];
            var bTaxItemExists = false;

            //Check if Tax Item Exists
            for (var n = 1; n <= fsTaxDetail.getSize(); n++) {

                if (nLineNumber && nLineNumber != n) {
                    continue;
                }
                rTaxDetail = fsTaxDetail.getRecord(n);
                if (rTaxDetail.taxitem_id == oTaxItems.taxitem_id) {
                    bTaxItemExists = true;
                    break;
                }
            }

            if (!bTaxItemExists) {
                rTaxDetail = fsTaxDetail.getRecord(fsTaxDetail.newRecord());
                rTaxDetail.taxitem_id = oTaxItems.taxitem_id;
                rTaxDetail.taxrate_id = oTaxItems.tax_rate_id;
                rTaxDetail.ordhtaxdet_taxrate_percent = oTaxItems.tax_percent;
                rTaxDetail.ordhtaxdet_taxable_sales_amt = oTaxItems.taxable_amt;
                rTaxDetail.ordhtaxdet_tax_amt = globals["avUtilities_roundNumber"](oTaxItems.tax_amt, 2);

                if (uMultiShipHeaderID) {
                    rTaxDetail.ordrevhms_id = uMultiShipHeaderID;
                }
            }
            else {
                rTaxDetail.ordhtaxdet_taxable_sales_amt += oTaxItems.taxable_amt;
                rTaxDetail.ordhtaxdet_tax_amt = globals["avUtilities_roundNumber"](globals["avUtilities_roundNumber"](rTaxDetail.ordhtaxdet_taxable_sales_amt, 2) * rTaxDetail.ordhtaxdet_taxrate_percent, 2);

                if (uMultiShipHeaderID) {
                    rTaxDetail.ordrevhms_id = uMultiShipHeaderID;
                }
            }
        }
    }
}

/**
 * @private 
 * 
 * @param {Array} aTax
 * @param {UUID} [uMultiShipHeaderID]
 * @param {UUID} [uMSQID]
 * @param {UUID} [uTaskPostMsId]
 *
 * @properties={typeid:24,uuid:"3366BB46-10D4-4406-BB9F-A5B90CBAA75F"}
 */
function avCalcs_salesTax_setOrderTaxDetails_SQL(aTax, uMultiShipHeaderID, uMSQID, uTaskPostMsId) {
    /***@type {{taxitem_id:String, taxable_amt:Number, tax_percent:Number, tax_rate_id:String, tax_amt:Number}}*/
    var oTaxItems = new Object();

    //Write the tax detail records
    for (var m = aTax.length-1; m >=0 ; m--) {
        oTaxItems = aTax[m];
        
        if (!oTaxItems.taxitem_id) {
        	continue;
        }
        
        //Check if Tax Item Exists
        var sSQL = "SELECT ordhtaxdet_id, ordhtaxdet_taxable_sales_amt FROM sa_order_tax_detail WHERE taxitem_id = ? ";
        var aArgs = [oTaxItems.taxitem_id.toString()];
        
		if (uMSQID) {
			sSQL += " AND ordrevdms_id = ? ";
			aArgs.push(uMSQID.toString());
		}
		else if (uTaskPostMsId) {
			sSQL += " AND ordrevdstaskpostms_id = ? ";
			aArgs.push(uTaskPostMsId.toString());
		}

		// i was using scopes.avDB.getFSFromSQL() but the foundset returned didnt have the latest data from the db, because the rec was previously updated using sql
        var ds = scopes.avDB.getDataset(sSQL, aArgs);
		
        if (ds && ds.getMaxRowIndex()) {
			if (oTaxItems.taxable_amt) {
	        	var uTaxDetID = ds.getValue(1, 1); 
	        	var nTaxableSales = ds.getValue(1, 2) + oTaxItems.taxable_amt; 
	        	var nTaxAmt = globals["avUtilities_roundNumber"](globals["avUtilities_roundNumber"](nTaxableSales, 2) * oTaxItems.tax_percent, 2);
	        	var sUpdateSQL = "UPDATE sa_order_tax_detail \
								  SET \
									  ordhtaxdet_taxable_sales_amt = ?, \
									  ordhtaxdet_tax_amt = ? " +
									  scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.UPDATE_ACTION) + " \
								  WHERE \
									  ordhtaxdet_id = ?";
	        	var aUpdateArgs = [nTaxableSales, nTaxAmt, uTaxDetID];
	        	
	        	scopes.avDB.RunSQL(sUpdateSQL, null, aUpdateArgs);
			}
        }
        else {
            /**@type {JSFoundSet<db:/avanti/sa_order_tax_detail>} */
            var fsTaxDetail = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_tax_detail');
            
            fsTaxDetail.newRecord();
            
            fsTaxDetail.taxitem_id = oTaxItems.taxitem_id;
            fsTaxDetail.taxrate_id = oTaxItems.tax_rate_id;
            fsTaxDetail.ordhtaxdet_taxrate_percent = oTaxItems.tax_percent;
            fsTaxDetail.ordhtaxdet_taxable_sales_amt = oTaxItems.taxable_amt;
            fsTaxDetail.ordhtaxdet_tax_amt = globals["avUtilities_roundNumber"](oTaxItems.tax_amt, 2);

    		if (uMSQID) {
            	fsTaxDetail.ordrevdms_id = uMSQID;
    		}
    		else if (uTaskPostMsId) {
            	fsTaxDetail.ordrevdstaskpostms_id = uTaskPostMsId;
    		}
            
            if (uMultiShipHeaderID) {
            	fsTaxDetail.ordrevhms_id = uMultiShipHeaderID;
            }
            
            scopes.avDB.insertRecWithSQL(fsTaxDetail, true, true);
        }
    }
}

/**
 * Get the Sales tax item object.
 * 
 * This is called from calculateTaxForOrderAvalara()
 *
 * @param {{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}} oTaxDetailInfo
 * @param {Date} dTaxDate
 * @param {UUID} uCurrId
 * @param {Object} oAvalaraResponse
 *
 * @return
 * @properties={typeid:24,uuid:"578763D0-41A9-46A5-933A-5F62FD3EE635"}
 */
function getTaxItem_SQL(oTaxDetailInfo, dTaxDate, uCurrId, oAvalaraResponse) {
    var returnObject = new Object();
    /** @type {JSFoundSet<db:/avanti/sys_sales_tax_item>} */
    var fsTaxItem;
    /** @type {JSFoundSet<db:/avanti/sys_sales_tax_rate>} */
    var fsTaxRate;
    var sOldTaxCode = oTaxDetailInfo.Country + '-' + oTaxDetailInfo.Region + '-' + oTaxDetailInfo.JurisType + '-' + oTaxDetailInfo.JurisCode;
    var sTaxCode = oTaxDetailInfo.Country + oTaxDetailInfo.Region + oTaxDetailInfo.JurisCode;
    
    fsTaxItem = scopes.avDB.getFSFromSQL("SELECT TI.taxitem_id FROM sys_sales_tax_item TI WHERE TI.org_id = ? AND (TI.taxitem_code = ? OR TI.taxitem_code = ?)", "sys_sales_tax_item", 
    	[globals.org_id, sTaxCode, sOldTaxCode]);

	if (utils.hasRecords(fsTaxItem)) {
		if (fsTaxItem.taxitem_desc != oTaxDetailInfo.JurisName + ' ' + oTaxDetailInfo.TaxName || fsTaxItem.taxitem_type != 'S' || fsTaxItem.taxitem_based_on != 'P' 
			|| fsTaxItem.taxitem_rounding_rule != 'UP' || fsTaxItem.taxitem_code != sTaxCode) {
				
	        fsTaxItem.taxitem_desc = oTaxDetailInfo.JurisName + ' ' + oTaxDetailInfo.TaxName;
	        fsTaxItem.taxitem_type = 'S';
	        fsTaxItem.taxitem_based_on = 'P';
	        fsTaxItem.taxitem_rounding_rule = 'UP';
	        fsTaxItem.taxitem_code = sTaxCode;
	        
	        if (!fsTaxItem.glacct_id) {
	        	fsTaxItem.glacct_id = getSalesTaxAccountSegment(oTaxDetailInfo.Region, uCurrId);
	        }
	        
	        uTaxitemId = fsTaxItem.taxitem_id;
	        scopes.avDB.updateRecWithSQL(fsTaxItem, ["taxitem_id"], true, true);
		}

        if (utils.hasRecords(fsTaxItem.sys_sales_tax_item_to_sys_sales_tax_rate)) {
            var bTaxRateFound = false,
                bTaxRateCreated = false;
            
            // Replacing original logic with the same logic we use to get a tax rate instead of looping through foundset.
            /***@type {{taxrate_id:String, taxrate_percent:Number}}*/
            var oTaxItem = globals["avCalcs_getSalesTaxItemPercentByDate"](fsTaxItem.taxitem_id, dTaxDate);
            
            if (oTaxItem && oTaxItem.taxrate_id && oTaxItem.taxrate_percent == oTaxDetailInfo.Rate) {
                
                /** @type {JSFoundSet<db:/avanti/sys_sales_tax_rate>} */
                fsTaxRate = scopes.avDB.getFS("sys_sales_tax_rate",["taxrate_id"],[oTaxItem.taxrate_id]);
                if (fsTaxRate && fsTaxRate.getSize() == 1) {
                    uTaxRateId = fsTaxRate.taxrate_id;
                    bTaxRateFound = true;
                }
            }
            
            if (!bTaxRateFound) {

                fsTaxRate = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_rate');
                fsTaxRate.newRecord();
                fsTaxRate.taxitem_id = fsTaxItem.taxitem_id; 
                fsTaxRate.taxrate_effective_date = dTaxDate;
                fsTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
                
                logTaxGroupCreation ('Tax Rate not found. Effective Date: ' + dTaxDate.toLocaleString() + ' Percent: ' + fsTaxRate.taxrate_percent, oAvalaraResponse);
                
                uTaxRateId = fsTaxRate.taxrate_id;
                scopes.avDB.insertRecWithSQL(fsTaxRate, true, true);
                bTaxRateCreated = true;
                removeItemFromTaxCache(fsTaxItem.taxitem_id);
            }
        }
        else {
        	fsTaxRate = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_rate');
        	fsTaxRate.newRecord();
        	fsTaxRate.taxitem_id = fsTaxItem.taxitem_id; 
        	fsTaxRate.taxrate_effective_date = dTaxDate;
        	fsTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
        	logTaxGroupCreation ('No tax rates for item. Effective Date: ' + dTaxDate.toLocaleString() + ' Percent: ' + fsTaxRate.taxrate_percent, oAvalaraResponse);
        	
        	uTaxRateId = fsTaxRate.taxrate_id;
            scopes.avDB.insertRecWithSQL(fsTaxRate, true, true);
            bTaxRateCreated = true;
            removeItemFromTaxCache(fsTaxItem.taxitem_id);
        }
    }
    else {
        fsTaxItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_item');
        fsTaxItem.newRecord();
        
        fsTaxItem.taxitem_code = sTaxCode;
        fsTaxItem.taxitem_desc = utils.stringLeft(oTaxDetailInfo.JurisName + ' ' + oTaxDetailInfo.TaxName, 64);
        fsTaxItem.taxitem_type = 'S';
        fsTaxItem.taxitem_based_on = 'P';
        fsTaxItem.taxitem_rounding_rule = 'UP';
        fsTaxItem.glacct_id = getSalesTaxAccountSegment(oTaxDetailInfo.Region, uCurrId);

        if (oTaxDetailInfo.Region && oTaxDetailInfo.Country) {
        	var sSQL = "SELECT S.stateprov_sales_gl_acctseg_id \
						FROM sys_state_province S \
						INNER JOIN sys_country C ON S.country_id = C.country_id \
						WHERE \
							S.org_id = ? \
							AND S.stateprov_code = ? \
							AND C.country_code_2_char_iso = ? \
							AND S.stateprov_sales_gl_acctseg_id IS NOT NULL";
        	var uAcctSegID = scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, oTaxDetailInfo.Region, oTaxDetailInfo.Country]);
        	
            if (uAcctSegID) {
            	fsTaxItem.glacct_id = uAcctSegID;
            }
        }
        
        var uTaxitemId = fsTaxItem.taxitem_id;
        
        logTaxGroupCreation ('No tax rates. Effective Date: ' + dTaxDate.toLocaleString() + ' Percent: ' + fsTaxRate.taxrate_percent, oAvalaraResponse);
        
        scopes.avDB.insertRecWithSQL(fsTaxItem, true, true);
        
    	fsTaxRate = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_rate');
    	fsTaxRate.newRecord();
    	fsTaxRate.taxitem_id = uTaxitemId; 
        fsTaxRate.taxrate_effective_date = new Date(2001, 0, 01);
        fsTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
        var uTaxRateId = fsTaxRate.taxrate_id;
        scopes.avDB.insertRecWithSQL(fsTaxRate, true, true);
        bTaxRateCreated = true;
        removeItemFromTaxCache(fsTaxItem.taxitem_id);
    }
	
    if (bTaxRateCreated) {

        databaseManager.refreshRecordFromDatabase(fsTaxRate,-1);
        databaseManager.saveData();
    }
    
    returnObject.sTaxItemID = (fsTaxItem.taxitem_id ? fsTaxItem.taxitem_id : uTaxitemId);
    returnObject.sTaxRateID = (fsTaxRate.taxrate_id ? fsTaxRate.taxrate_id : uTaxRateId);
    
    return returnObject;
}

/**
 * This is called from calculateTaxForInvoiceAvalara - we cant use the processTaxGroups_SQL for invoicing because calculateTaxForInvoiceAvalara hasnt been converted to use SQL
 * 
 * @AllowToRunInFind
 * 
 * @param oTaxDetailInfo
 * @param {Date} dTaxDate
 * @param {UUID} uCurrId
 *
 * @return
 * @properties={typeid:24,uuid:"E71230CA-1584-48D0-BDC6-23D9C054E897"}
 */
function getTaxItem_FS(oTaxDetailInfo, dTaxDate, uCurrId) {
    var returnObject = new Object();
    /** @type {JSFoundSet<db:/avanti/sys_sales_tax_item>} */
    var fsTaxItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_item');
    var sOldTaxCode = oTaxDetailInfo.Country + '-' + oTaxDetailInfo.Region + '-' + oTaxDetailInfo.JurisType + '-' + oTaxDetailInfo.JurisCode;
    var sTaxCode = oTaxDetailInfo.Country + oTaxDetailInfo.Region + oTaxDetailInfo.JurisCode;
    var rTaxRate;
    
    if (fsTaxItems.find()) {
        fsTaxItems.taxitem_code = sTaxCode;
        fsTaxItems.newRecord();
        fsTaxItems.taxitem_code = sOldTaxCode;
        
        if (fsTaxItems.search() > 0) {
            fsTaxItems.taxitem_desc = oTaxDetailInfo.JurisName + ' ' + oTaxDetailInfo.TaxName;
            fsTaxItems.taxitem_type = 'S';
            fsTaxItems.taxitem_based_on = 'P';
            fsTaxItems.taxitem_rounding_rule = 'UP';
            
            if (!fsTaxItems.glacct_id) {
	        	fsTaxItems.glacct_id = getSalesTaxAccountSegment(oTaxDetailInfo.Region, uCurrId);
	        }
            
            if (fsTaxItems.taxitem_code == sOldTaxCode) {
                globals.dbLog('Updated tax item code from ' + sOldTaxCode + ' to new format ' + sTaxCode, 'processing_tax', 'processing', 'tax', 'http', application.getUUID(globals.org_id), 'processing_tax', 'Summary', null);
            }
            fsTaxItems.taxitem_code = sTaxCode;
            if (utils.hasRecords(fsTaxItems.sys_sales_tax_item_to_sys_sales_tax_rate)) {
                
                var bTaxRateFound = false;
                
                // Replacing original logic with the same logic we use to get a tax rate instead of looping through foundset.
                /***@type {{taxrate_id:String, taxrate_percent:Number}}*/
                var oTaxItem = globals["avCalcs_getSalesTaxItemPercentByDate"](fsTaxItems.taxitem_id, dTaxDate);
                
                if (oTaxItem && oTaxItem.taxrate_id && oTaxItem.taxrate_percent == oTaxDetailInfo.Rate) {
                    rTaxRate = scopes.avDB.getRec("sys_sales_tax_rate",["taxrate_id"],[oTaxItem.taxrate_id]);
                    if (rTaxRate) {
                        bTaxRateFound = true;
                    }
                }
                
                if (!bTaxRateFound) {
                    rTaxRate = fsTaxItems.sys_sales_tax_item_to_sys_sales_tax_rate.getRecord(fsTaxItems.sys_sales_tax_item_to_sys_sales_tax_rate.newRecord());
                    rTaxRate.taxrate_effective_date = dTaxDate; //application.getServerTimeStamp();
                    rTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
                    databaseManager.saveData(rTaxRate);
                    removeItemFromTaxCache(rTaxRate.taxitem_id);
                }
            }
            else {
                rTaxRate = fsTaxItems.sys_sales_tax_item_to_sys_sales_tax_rate.getRecord(fsTaxItems.sys_sales_tax_item_to_sys_sales_tax_rate.newRecord());
                rTaxRate.taxrate_effective_date = dTaxDate;
                rTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
                databaseManager.saveData(rTaxRate);
            }
            returnObject.sTaxItemID = fsTaxItems.taxitem_id;
            returnObject.sTaxRateID = rTaxRate.taxrate_id;
            return returnObject;
        }
        else {
            var rTaxItem = fsTaxItems.getRecord(fsTaxItems.newRecord());
            rTaxItem.taxitem_code = sTaxCode;
            rTaxItem.taxitem_desc = utils.stringLeft(oTaxDetailInfo.JurisName + ' ' + oTaxDetailInfo.TaxName, 64);
            rTaxItem.taxitem_type = 'S';
            rTaxItem.taxitem_based_on = 'P';
            rTaxItem.taxitem_rounding_rule = 'UP';
            rTaxItem.glacct_id = getSalesTaxAccountSegment(oTaxDetailInfo.Region, uCurrId);
            databaseManager.saveData(rTaxItem);
            
            rTaxRate = rTaxItem.sys_sales_tax_item_to_sys_sales_tax_rate.getRecord(rTaxItem.sys_sales_tax_item_to_sys_sales_tax_rate.newRecord());
            rTaxRate.taxrate_effective_date = new Date(2001, 0, 01);
            rTaxRate.taxrate_percent = utils.stringToNumber(oTaxDetailInfo.Rate);
            if (oTaxDetailInfo.Region) {
                /** @type {JSFoundSet<db:/avanti/sys_state_province>} */
                var fsTaxableState = scopes.avDB.getFS('sys_state_province', ['stateprov_code', 'sys_state_province_to_sys_country.country_code_2_char_iso'], [oTaxDetailInfo.Region, oTaxDetailInfo.Country]);
                if (fsTaxableState && fsTaxableState.stateprov_sales_gl_acctseg_id) {
                    rTaxItem.glacct_id = fsTaxableState.stateprov_sales_gl_acctseg_id;
                }
            }

            databaseManager.saveData(rTaxRate);
            removeItemFromTaxCache(rTaxRate.taxitem_id);
            
            returnObject.sTaxItemID = rTaxItem.taxitem_id;
            returnObject.sTaxRateID = rTaxRate.taxrate_id;
            
            return returnObject;
        }
    }
    
    return null;
}

/**
 * This is called from calculateTaxForOrderAvalara()
 * 
 * Create and set tax groups based on Postal Code from Avalara.
 * @param oTaxInfo
 * @param oAvalaraResponse
 * @param aLinesWithPostalCodeState
 * @param aTaxLineDetails
 * @param {UUID} uCurrId
 *
 * @properties={typeid:24,uuid:"FF34F669-4DCF-434B-B2F7-45C075B829DB"}
 */
function processTaxGroups_SQL(oTaxInfo, oAvalaraResponse, aLinesWithPostalCodeState, aTaxLineDetails, uCurrId) {
	scopes.avUtils.stopWatchStart("processTaxGroups");
	
    aTaxableShippingStates = getTaxableShippingStateProv();

    var taxDetailKey;
    var oTaxItem = new Object();
    /** @type {{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}} */
    var oTaxDetailInfo = new Object();

    try {
        /** @type {Array<{postalCode, state, country, aTaxDetails}>} */
        var aPostalCodeStateAndTaxLines = joinArrays(aLinesWithPostalCodeState, aTaxLineDetails, 'sLineNumber', 'sLineNumber', function(a, b) {
        		if (a && b) return { postalCode: a.postalCode, state: a.state, country: a.country, aTaxDetails: b.aTaxDetails };
            });
    }
    catch (ex) {
        application.output(ex.message, LOGGINGLEVEL.ERROR);
        throw new Error(i18n.getI18NMessage("avanti.dialog.avaTaxErrorDetected"));
    }

    for (var nPostalTaxItems = 0; nPostalTaxItems < aPostalCodeStateAndTaxLines.length; nPostalTaxItems++) {
        var sTaxGroupCode = '';
        
		if (!aPostalCodeStateAndTaxLines[nPostalTaxItems]) {
			continue;
		}
        
        if (aPostalCodeStateAndTaxLines[nPostalTaxItems].country == 'US' || isAmericanStateCode(aPostalCodeStateAndTaxLines[nPostalTaxItems].state)) {
            sTaxGroupCode = aPostalCodeStateAndTaxLines[nPostalTaxItems].postalCode;
        }
        else if (aPostalCodeStateAndTaxLines[nPostalTaxItems].country == 'CA' || isCanadianProvinceCode(aPostalCodeStateAndTaxLines[nPostalTaxItems].state)) {
            sTaxGroupCode = aPostalCodeStateAndTaxLines[nPostalTaxItems].state;
        }
        
        /** @type {JSFoundSet<db:/avanti/sys_sales_tax_group>} */
        var fsTaxGroup = scopes.avDB.getFSFromSQL("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", "sys_sales_tax_group", [globals.org_id, sTaxGroupCode]);
        /** @type {JSFoundSet<db:/avanti/sys_sales_tax_group_item>} */
        var fsTaxGroupItem;
        
        if (utils.hasRecords(fsTaxGroup)) {
            if (utils.hasRecords(fsTaxGroup.sys_sales_tax_group_to_sys_sales_tax_group_item)) {
                scopes.avDB.RunSQL("DELETE FROM sys_sales_tax_group_item " 
                	+ scopes.avDB.createBroadcastOutputStatement("sys_sales_tax_group_item", "taxgroupitem_id", scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION)
					+ " WHERE taxgroup_id = ?", null, [fsTaxGroup.taxgroup_id.toString()]);
            }
            
            for (taxDetailKey in aPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails) {
                oTaxDetailInfo = aPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails[taxDetailKey];
                oTaxItem = getTaxItem_SQL(oTaxDetailInfo, oAvalaraResponse.TaxDate, uCurrId);
                
                fsTaxGroupItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item');
                fsTaxGroupItem.newRecord();
                
                fsTaxGroupItem.taxgroup_id = fsTaxGroup.taxgroup_id;
                fsTaxGroupItem.taxitem_id = oTaxItem.sTaxItemID;
                fsTaxGroupItem.created_date = new Date();
                fsTaxGroupItem.created_by_id = globals.avBase_employeeUUID;				
                
                scopes.avDB.insertRecWithSQL(fsTaxGroupItem, true, true);
            }
            
        }
        else {
        	fsTaxGroup = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group');
            fsTaxGroup.newRecord();
            var uTaxGroupId = fsTaxGroup.taxgroup_id;
            
            fsTaxGroup.taxgroup_code = sTaxGroupCode;
            fsTaxGroup.taxgroup_desc = getTaxGroupDescription(aPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails);
            
            if (aTaxableShippingStates.indexOf(aPostalCodeStateAndTaxLines[nPostalTaxItems].state) >= 0) {
            	fsTaxGroup.taxgroup_shipping_taxable = true;
            }
            
            logTaxGroupCreation ('Avalara Tax group created. Group code: ' + sTaxGroupCode + ' with description: ' + fsTaxGroup.taxgroup_desc, oAvalaraResponse);
            
            scopes.avDB.insertRecWithSQL(fsTaxGroup, true, true);
            
            for (taxDetailKey in aPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails) {
                oTaxDetailInfo = aPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails[taxDetailKey];
                oTaxItem = getTaxItem_SQL(oTaxDetailInfo, oAvalaraResponse.TaxDate, uCurrId);

                fsTaxGroupItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group_item');
                fsTaxGroupItem.newRecord();
                
                fsTaxGroupItem.taxgroup_id = uTaxGroupId;
                fsTaxGroupItem.taxitem_id = oTaxItem.sTaxItemID;
                fsTaxGroupItem.created_date = new Date();
                fsTaxGroupItem.created_by_id = globals.avBase_employeeUUID;				
                
                scopes.avDB.insertRecWithSQL(fsTaxGroupItem, true, true);
            }
        }
    }
    
	scopes.avUtils.devOutputTime("processTaxGroups");
}

/**
 * This is called from calculateTaxForInvoiceAvalara - we cant use the processTaxGroups_SQL for invoicing because calculateTaxForInvoiceAvalara hasnt been converted to use SQL
 * 
 * @AllowToRunInFind
 * 
 * Create and set tax groups based on Postal Code from Avalara.
 * @param oTaxInfo
 * @param oAvalaraResponse
 * @param {UUID} uCurrId
 *
 * @properties={typeid:24,uuid:"DB7AB77D-EF84-4FA3-9CA4-C36AAA262AE7"}
 */
function processTaxGroups_FS(oTaxInfo, oAvalaraResponse, uCurrId) {
    var arrayOfLinesWithPostalCodeState = new Array();
    aTaxableShippingStates = getTaxableShippingStateProv();

    var taxDetailKey;
    var oTaxItem = new Object();
    var rTaxGroupItem;

    /** @type {{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}} */
    var oTaxDetailInfo = new Object();

    for (var lineKey in oTaxInfo.Lines) {
        var oLineObject = new Object();
        oLineObject.sLineNumber = oTaxInfo.Lines[lineKey].LineNo;
        oLineObject.sDestinationCode = oTaxInfo.Lines[lineKey].DestinationCode;
        for (var addressKey in oTaxInfo.Addresses) {
            if (oLineObject.sDestinationCode == oTaxInfo.Addresses[addressKey].AddressCode) {
                oLineObject.postalCode = oTaxInfo.Addresses[addressKey].PostalCode;
                oLineObject.state = oTaxInfo.Addresses[addressKey].Region;
                oLineObject.country = oTaxInfo.Addresses[addressKey].Country;
                arrayOfLinesWithPostalCodeState.push(oLineObject);
                break;
            }
        }
    }

    var arrayOfTaxLineDetails = new Array();
    for (var taxLineKey in oAvalaraResponse.TaxLines) {
        var oTaxLineObject = new Object();
        oTaxLineObject.sLineNumber = oAvalaraResponse.TaxLines[taxLineKey].LineNo;
        oTaxLineObject.aTaxDetails = oAvalaraResponse.TaxLines[taxLineKey].TaxDetails;
        arrayOfTaxLineDetails.push(oTaxLineObject);
    }

    try {
        /** @type {Array<{postalCode, state, country, aTaxDetails}>} */
        var arrayOfPostalCodeStateAndTaxLines = joinArrays(arrayOfLinesWithPostalCodeState, arrayOfTaxLineDetails, 'sLineNumber', 'sLineNumber', function(a, b) {
                return { postalCode: a.postalCode, state: a.state, country: a.country, aTaxDetails: b.aTaxDetails };
            });
    }
    catch (ex) {
        application.output(ex.message, LOGGINGLEVEL.ERROR);
        throw new Error(i18n.getI18NMessage("avanti.dialog.avaTaxErrorDetected"));
    }

    for (var nPostalTaxItems = 0; nPostalTaxItems < arrayOfPostalCodeStateAndTaxLines.length; nPostalTaxItems++) {
        var sTaxGroupCode = '';
        if (arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].country == 'US' || isAmericanStateCode(arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].state)) {
            sTaxGroupCode =arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].postalCode;
        }
        else if (arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].country == 'CA' || isCanadianProvinceCode(arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].state)) {
            sTaxGroupCode = arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].state;
        }
        /** @type {JSFoundSet<db:/avanti/sys_sales_tax_group>} */
        var fsTaxGroups = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group');
        if (fsTaxGroups.find()) {
            fsTaxGroups.taxgroup_code = sTaxGroupCode;
            if (fsTaxGroups.search() > 0) {
                if (utils.hasRecords(fsTaxGroups.sys_sales_tax_group_to_sys_sales_tax_group_item)) {
                    fsTaxGroups.sys_sales_tax_group_to_sys_sales_tax_group_item.deleteAllRecords();
                }
                for (taxDetailKey in arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails) {
                    oTaxDetailInfo = arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails[taxDetailKey];
                    oTaxItem = getTaxItem_FS(oTaxDetailInfo, oAvalaraResponse.TaxDate, uCurrId);
                    rTaxGroupItem = fsTaxGroups.sys_sales_tax_group_to_sys_sales_tax_group_item.getRecord(fsTaxGroups.sys_sales_tax_group_to_sys_sales_tax_group_item.newRecord());
                    rTaxGroupItem.taxitem_id = oTaxItem.sTaxItemID;
                }
                // Must save data here so that it can be used in the processing or assigning of new tax groups to addresses.
                databaseManager.saveData(fsTaxGroups);
            }
            else {
                var rNewTaxGroup = fsTaxGroups.getRecord(fsTaxGroups.newRecord());
                rNewTaxGroup.taxgroup_code = sTaxGroupCode;
                rNewTaxGroup.taxgroup_desc = getTaxGroupDescription(arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails);
                if (aTaxableShippingStates.indexOf(arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].state) >= 0) {
                    rNewTaxGroup.taxgroup_shipping_taxable = true;
                }
                for (taxDetailKey in arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails) {
                    oTaxDetailInfo = arrayOfPostalCodeStateAndTaxLines[nPostalTaxItems].aTaxDetails[taxDetailKey];
                    oTaxItem = getTaxItem_FS(oTaxDetailInfo, oAvalaraResponse.TaxDate);
                    rTaxGroupItem = rNewTaxGroup.sys_sales_tax_group_to_sys_sales_tax_group_item.getRecord(rNewTaxGroup.sys_sales_tax_group_to_sys_sales_tax_group_item.newRecord());
                    rTaxGroupItem.taxitem_id = oTaxItem.sTaxItemID;
                }
                logTaxGroupCreation ('New avalara tax group created. Group code: ' + sTaxGroupCode + ' with description: ' + rNewTaxGroup.taxgroup_desc, oAvalaraResponse);
                // Must save data here so that it can be used in the processing or assigning of new tax groups to addresses.
                databaseManager.saveData(rNewTaxGroup);
            }
        }
    }
}

/**
 * Log tax group creation in integration log for Thomas
 * @param {String} sMessage
 * @param {Object} oAvalaraResponse
 *
 * @properties={typeid:24,uuid:"02194195-1D0A-4B9D-866C-434E63DD495A"}
 */
function logTaxGroupCreation (sMessage, oAvalaraResponse) {
	
	// Only for Thomas
	if (_to_sys_organization && _to_sys_organization.org_name != 'THOMAS PRINTWORKS') {
		return;
	}
	
    var sAvalaraRespone = '';
    if (oAvalaraResponse) {
    	try {
    		sAvalaraRespone = plugins.serialize.toJSON(oAvalaraResponse);
    	} catch (ex) {
    		
    	}
    }
    var uDBLogID = globals.dbLog(sMessage, 'SL-28859', 'processing', 'tax', 'http', application.getUUID(globals.org_id), 'processing_tax', 'Summary', null);
    globals.dbLogWriteDetails(uDBLogID, 'SL-28859', '', sAvalaraRespone);
}

/**
 * Check if state is in the list of American states.
 * @param sStateCode
 *
 * @return
 * @properties={typeid:24,uuid:"BA9DF91E-8C82-48E3-8FAF-3BAEB639A6BC"}
 */
function isAmericanStateCode(sStateCode) {
    var aStateCodes = ['AL', 'AK', 'AS', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FM', 'FL', 'GA', 'GU', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MH', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'MP', 'OH', 'OK', 'OR', 'PW', 'PA', 'PR', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VI', 'VA', 'WA', 'WV', 'WI', 'WY', 'AE', 'AA', 'AP'];
    if (aStateCodes.indexOf(sStateCode) >= 0) {
        return true;
    }
    return false;
}

/**
 * Check if province is in the list of Canadian provinces
 * @param sProvinceCode
 *
 * @return
 * @properties={typeid:24,uuid:"C36DE4A9-16E5-4610-9D51-131C70ACEA78"}
 */
function isCanadianProvinceCode(sProvinceCode) {
    var aProvinceCodes = ['AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT'];
    if (aProvinceCodes.indexOf(sProvinceCode) >= 0) {
        return true;
    }
    return false;
}
/**
 * Join two arrays together on the primary foreign key and use the select function parameter to determine what to return.
 *
 * @param primary Array 1
 * @param foreign Array 2
 * @param primaryKey Column name to join from Array 1
 * @param foreignKey Column name to join from Array 2
 * @param select Function to define what to return from Array 1 and Array 2, e.g., function(a,b) { return { postalCode: a.postalCode, aTaxDetails: b.aTaxDetails};}
 *
 * @return
 * @properties={typeid:24,uuid:"36020028-4DCD-42F3-A2CA-9EE4386818AD"}
 */
function joinArrays(primary, foreign, primaryKey, foreignKey, select) {
    var m = primary.length, n = foreign.length, index = [], c = [];

    for (var i = 0; i < m; i++) { // loop through m items
        var row = primary[i];
        index[row[primaryKey]] = row; // create an index for primary table
    }

    for (var j = 0; j < n; j++) { // loop through n items
        var y = foreign[j];
        var x = index[y[foreignKey]]; // get corresponding row from primary
        c.push(select(x, y)); // select only the columns you need
    }
    return c;
}

/**
 * Get tax group description based on tax details returned.
 *
 * @param {Array<{JurisName}>} aTaxDetails
 *
 * @return
 * @properties={typeid:24,uuid:"0ED1B7C7-E8E9-4A6D-A1B9-48BFC3A01C45"}
 */
function getTaxGroupDescription(aTaxDetails) {
    var sTaxGroupDesc = '';
    for (var index = 0; index < aTaxDetails.length; index++) {
        sTaxGroupDesc += aTaxDetails[index].JurisName;
        if (index != aTaxDetails.length - 1) {
            sTaxGroupDesc += ', ';
        }
    }

    sTaxGroupDesc = utils.stringLeft(sTaxGroupDesc, 50);
    return sTaxGroupDesc;
}

/**
 * Update tax groups based on postal code.
 * @param {JSRecord<db:/avanti/sa_order>} rOrder
 *
 * @properties={typeid:24,uuid:"58563685-7DCF-45F6-AB87-8A6893EEEEC9"}
 */
function updateTaxGroups(rOrder) {
    if (utils.hasRecords(rOrder.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheaderid) 
            && utils.hasRecords(rOrder.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheaderid.sa_order_revision_header_to_sa_order_revh_multi_ship)) {
            	
    	var rFOBAddress = scopes.avSales.getOrderFOBAddress(rOrder);
    	var rHeader = rOrder.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheaderid.getRecord(1);
    	
        // Calculate freight tax
        for (var nMultiShipIndex = 1; nMultiShipIndex <= rHeader.sa_order_revision_header_to_sa_order_revh_multi_ship.getSize(); nMultiShipIndex++) {
            var sTaxGroupID = null;
            var rMultiShipHeader = rHeader.sa_order_revision_header_to_sa_order_revh_multi_ship.getRecord(nMultiShipIndex);

            if (utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address) && rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.addr_postal) {
                if (rFOBAddress) {
            		sTaxGroupID = rFOBAddress.taxgroup_id;
            	}
            	else if (utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_sys_shipping_method) 
                        && utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_in_warehouse)
                        && utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_in_warehouse.in_warehouse_to_sys_address)
                        && rMultiShipHeader.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_type == scopes.avUtils.ENUM_SHIPMETHOD_TYPE.Pickup) {
                        	
                    var rWarehouse = rMultiShipHeader.sa_order_revh_multi_ship_to_in_warehouse.getRecord(1);
                    var rWarehouseAddress = rWarehouse.in_warehouse_to_sys_address.getRecord(1);
                    
                    if (rWarehouseAddress.sys_address_to_sys_country.country_code == 'USA' || rWarehouseAddress.sys_address_to_sys_country.country_code == 'US') {
                    	sTaxGroupID = scopes.avDB.SQLQuery("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", null, [globals.org_id, rWarehouseAddress.addr_postal]);
                    }
                    // By province.
                    else if (utils.hasRecords(rWarehouseAddress.sys_address_to_sys_state_province) && rWarehouseAddress.sys_address_to_sys_state_province.stateprov_code) {
                       	sTaxGroupID = scopes.avDB.SQLQuery("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", null, [globals.org_id, rWarehouseAddress.sys_address_to_sys_state_province.stateprov_code]);
                    }
                }
                else {
                    if (utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address) 
                    		&& utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_country)
							&& (rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_country.country_code == 'USA' 
							|| rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_country.country_code == 'US')) {
                        	
                       	sTaxGroupID = scopes.avDB.SQLQuery("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", null, [globals.org_id, rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.addr_postal]);
                    }
                    // By province.
                    else if (utils.hasRecords(rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_state_province) 
                                && rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_state_province.stateprov_code) {
                       	sTaxGroupID = scopes.avDB.SQLQuery("SELECT taxgroup_id FROM sys_sales_tax_group WHERE org_id = ? AND taxgroup_code = ?", null, [globals.org_id, rMultiShipHeader.sa_order_revh_multi_ship_to_sys_address.sys_address_to_sys_state_province.stateprov_code]);
                    }
                }
            	
				if (sTaxGroupID && sTaxGroupID != rMultiShipHeader.taxgroup_id) {
	                rMultiShipHeader.taxgroup_id = sTaxGroupID;
	                rMultiShipHeader.ordrevhms_ship_charges_taxable = globals.getShippingTaxFlag(rMultiShipHeader);

	                scopes.avDB.RunSQL("UPDATE sa_order_revd_multi_ship_qty SET taxgroup_id = ? " 
	                	+ scopes.avDB.createBroadcastOutputStatement("sa_order_revd_multi_ship_qty", "ordrevdms_id", scopes.avUtils.e_spBroadcastRecordAction.UPDATE_ACTION)
	                	+ " WHERE ordrevhms_id = ?", null, [sTaxGroupID.toString(), rMultiShipHeader.ordrevhms_id.toString()]);
				}
            }
        }
    }
}

/**
 * Calculate tax for invoices using Avalara AvaTax
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * @param {Boolean} bCreateTransaction
 *
 * @return
 * @properties={typeid:24,uuid:"845600A0-9F6C-45A9-890D-3DFC5673CAF6"}
 */
function calculateTaxForInvoiceAvalara(rInvoice, bCreateTransaction) {
    try {
        var sDBLogID = globals.dbLog('Calculating tax using Avalara', 'processing_tax', 'processing', 'tax', 'http', application.getUUID(globals.org_id), 'processing_tax', 'Summary', null);
        var sInvoiceNumber = '';
        var oHTTPClient = plugins.http.createNewHttpClient();
        var sAvalaraURL = 'https://avatax.avalara.net';
        if (_to_sys_organization.org_avalara_url_type) {
            sAvalaraURL = 'https://development.avalara.net';
        }
        var sCurrencyID;
        var dInvoiceDate;
        var nAddressCounter = 0;
        aAddressesWithFreightCalculated = new Array();
        var aFreightTaxes = new Array();
        var aAddressCodes = new Array();
        var oTaxInfo = new Object();
        
        /** @type {JSFoundset<db:/avanti/sa_invoice_det_ship_task>} */
        var fsInvoiceShipTask;
        
        var nTaxableAmount = 0.00,
            rInvoiceShipTask = null,
            fsTaxTaxType = null,
            k = 0;
        
        var n = 0,
            m = 0,
            sTaxKey = "",
            fsInvoiceTaxDetail = null,
            rInvoiceTaxDetail = null,
            nTaxAmount = 0.00,
            aTaxNew = [],
            sCustomerTaxExemptionID = null;
        
        oTaxInfo.Addresses = new Array();
        oTaxInfo.Lines = new Array();
        oTaxInfo.Commit = 'false';

        // sl-15760 - void any existing transaction
        if (bCreateTransaction) {
            scopes.avTax.voidTransactionForInvoiceAvalara(rInvoice, true);
        }

        if (bCreateTransaction) {
            if (rInvoice.inv_record_type == 'C') {
                oTaxInfo.DocType = 'ReturnInvoice';
            }
            else {
                oTaxInfo.DocType = 'SalesInvoice';
            }
            oTaxInfo.Commit = true;
        }
        else {
            oTaxInfo.DocType = 'SalesOrder';
        }

        oTaxInfo.Client = 'Avanti Slingshot';
        oTaxInfo.CompanyCode = _to_sys_organization.org_avalara_company_code;

        if (rInvoice) {
            sInvoiceNumber = rInvoice.inv_number;
            if (utils.hasRecords(rInvoice.sa_invoice_to_sa_customer) && rInvoice.sa_invoice_to_sa_customer.cust_code) {
                oTaxInfo.CustomerCode = rInvoice.sa_invoice_to_sa_customer.cust_code;
            }
            if (rInvoice.inv_number) {
                oTaxInfo.DocCode = rInvoice.inv_number;
            }

            dInvoiceDate = rInvoice.inv_date;
            if (rInvoice.inv_date) {
                oTaxInfo.DocDate = utils.dateFormat(rInvoice.inv_date, 'yyyy-MM-dd');
            }

            sCurrencyID = rInvoice.curr_id;
            if (utils.hasRecords(rInvoice.sa_invoice_to_sys_currency) && rInvoice.sa_invoice_to_sys_currency.curr_iso_code) {
                oTaxInfo.CurrencyCode = rInvoice.sa_invoice_to_sys_currency.curr_iso_code;
            }
        }

        if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)) {
        	var chargeType = null;

            if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order_revision_detail) && 
            		utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse)) {
                oTaxInfo.LocationCode = rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.whse_code;
            }
            else if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order) && 
            		utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order.sa_order_to_sys_plant) && 
					utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order.sa_order_to_sys_plant.sys_plant_to_in_warehouse$plant_id_active_whse)) {
				oTaxInfo.LocationCode = rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order.sa_order_to_sys_plant.sys_plant_to_in_warehouse$plant_id_active_whse.whse_code;
            }
            else {
            	oTaxInfo.LocationCode = rInvoice.sa_invoice_to_sys_plant.sys_plant_to_in_warehouse$plant_id_active_whse.whse_code;
            }

            var nLineNumber = 0,
                bRefreshBillingCodeTaxTotal = false;
            
            for (var nDetailIndex = 1; nDetailIndex <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); nDetailIndex++) {
                var rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(nDetailIndex);
                
                if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail)
                		&& rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.item_id
                        && utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item)
                        && rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avInv.ITEM_TYPE_CODE.ServiceItem
                        && rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.item_exclude_from_avatax == 1) {
                    continue;
                }
                
                /** @type {JSRecord<db:/avanti/sa_order>} */
                var rOrder;
                var rFOBAddress = null;
                var sTaxExemptionID = null;

                if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order)) {
                	 rOrder = rInvoiceDetail.sa_invoice_det_to_sa_order.getRecord(1);
                     rFOBAddress = scopes.avSales.getOrderFOBAddress(rOrder);
                }
                
				if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail) && 
						utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse)) {
					chargeType = 'LINECHARGE';
				} 
				else if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order) && 
						utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order.sa_order_to_sys_plant) && 
						utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order.sa_order_to_sys_plant.sys_plant_to_in_warehouse$plant_id_active_whse)) {
					chargeType = 'ORDERCHARGE';
				} 
				else {
					chargeType = 'INVOICECHARGE';
				}

                
				if (!rInvoiceDetail.ordrevd_id) {
					//If it's an additional charge then calculate the tax info separately
					calculateTaxInfoForAdditionalCharge(chargeType);
				} 
				else {
					for (var nInvoiceShipIndex = 1; nInvoiceShipIndex <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); nInvoiceShipIndex++) {
						var sAddressCode = '';
						var sAddressCodeFrom = '';
						var bUseInvoiceLineAmount = false;
						var rInvoiceShipment = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(nInvoiceShipIndex);
						var rFromAddress = null;
						var rPack = null;

						// ORIGIN
						if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_pack_detail) && 
								utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_pack_detail.sa_pack_detail_to_sa_pack)) {

							rPack = rInvoiceShipment.sa_invoice_det_ship_to_sa_pack_detail.sa_pack_detail_to_sa_pack.getRecord(1);

							if (rPack.ship_address_from_warehouse == 0 && utils.hasRecords(rPack.sa_pack_to_sys_address_from)) {
								rFromAddress = rPack.sa_pack_to_sys_address_from.getRecord(1);
							} 
							else if (rPack.ship_address_from_warehouse == 1 && utils.hasRecords(rPack.sa_pack_to_in_warehouse_from) && 
									utils.hasRecords(rPack.sa_pack_to_in_warehouse_from.in_warehouse_to_sys_address)) {
								rFromAddress = rPack.sa_pack_to_in_warehouse_from.in_warehouse_to_sys_address.getRecord(1);
							}
						}
						// as per JP - if no pack then use line item warehouse address
						else if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail) && 
								utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse) && 
								utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address)) {

							rFromAddress = rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address.getRecord(1);
						}

						// for debugging
						//					if (application.isInDeveloper()) {
						//						rFromAddress = rOrder.sa_order_to_sa_customer.sa_customer_to_sa_customer_address_primary.sa_customer_address_to_sys_address.getRecord(1);
						//					}

						if (rFromAddress) {
							oTaxInfo.Addresses[nAddressCounter] = new Object();

							sAddressCodeFrom = "ORIGIN" + (nAddressCounter + 1);

							oTaxInfo.Addresses[nAddressCounter].AddressCode = sAddressCodeFrom;
							oTaxInfo.Addresses[nAddressCounter].Line1 = rFromAddress.addr_address1;
							oTaxInfo.Addresses[nAddressCounter].Line2 = rFromAddress.addr_address2;
							oTaxInfo.Addresses[nAddressCounter].Line3 = rFromAddress.addr_address3;
							oTaxInfo.Addresses[nAddressCounter].City = rFromAddress.addr_city;

							if (utils.hasRecords(rFromAddress.sys_address_to_sys_state_province)) {
								oTaxInfo.Addresses[nAddressCounter].Region = rFromAddress.sys_address_to_sys_state_province.stateprov_code;
							} 
							else {
								oTaxInfo.Addresses[nAddressCounter].Region = '';
							}

							if (utils.hasRecords(rFromAddress.sys_address_to_sys_country)) {
								oTaxInfo.Addresses[nAddressCounter].Country = rFromAddress.sys_address_to_sys_country.country_code_2_char_iso;
							}

							oTaxInfo.Addresses[nAddressCounter].PostalCode = rFromAddress.addr_postal;

							aAddressCodes[rFromAddress.addr_id] = sAddressCodeFrom;
							nAddressCounter++;
						}

						// DESTINATION
						if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_customer_address)) {

							var rCustomerAddress = rInvoiceShipment.sa_invoice_det_ship_to_sa_customer_address.getSelectedRecord();
							var rPackShip = null;

							// GD - Sep 6, 2019: SL-17374 "sa_invoice_det_ship" is using the billto address instead of the shipto address
							if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail) && 
									utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty) && 
									utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship)) {

								var rMultiShip = rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.sa_order_revd_multi_ship_qty_to_sa_order_revh_multi_ship.getRecord(1);

								if (rMultiShip.custaddr_id && rCustomerAddress.custaddr_id != rMultiShip.custaddr_id && 
										utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_sa_customer_address)) {
									rCustomerAddress = rMultiShip.sa_order_revh_multi_ship_to_sa_customer_address.getRecord(1);
									rInvoiceShipment.custaddr_id = rMultiShip.custaddr_id;
								}

								// It's quite possible for the packing slip to have a different shipping address, so use it if different.
								// The freight logic was failing if this occurs so making the source the same for both.
								if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail) && 
										utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_pack_detail) && 
										utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_pack_detail.sa_pack_detail_to_sa_pack)) {
									rPackShip = rInvoiceShipment.sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_pack_detail.sa_pack_detail_to_sa_pack.getRecord(1);

									if (utils.hasRecords(rPackShip.sa_pack_to_sa_customer_address) && rPackShip.sa_pack_to_sa_customer_address.custaddr_id != rCustomerAddress.custaddr_id) {
										rCustomerAddress = rPackShip.sa_pack_to_sa_customer_address.getRecord(1);
										rInvoiceShipment.custaddr_id = rPackShip.pack_shipto_addr_id;
									}
								}
							}

							var rAddress = null;

							// FOB Address
							if (rFOBAddress) {
								if (utils.hasRecords(rFOBAddress.sa_customer_address_to_sys_address)) {
									rAddress = rFOBAddress.sa_customer_address_to_sys_address.getRecord(1);
								}

								sAddressCode = rFOBAddress.custaddr_code;
							}
							// Pickup
							else if (rMultiShip && utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_sys_shipping_method) && 
									utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_in_warehouse) && 
									utils.hasRecords(rMultiShip.sa_order_revh_multi_ship_to_in_warehouse.in_warehouse_to_sys_address) && 
									rMultiShip.sa_order_revh_multi_ship_to_sys_shipping_method.shipmethod_type == scopes.avUtils.ENUM_SHIPMETHOD_TYPE.Pickup) {

								var rPickupWarehouse = rMultiShip.sa_order_revh_multi_ship_to_in_warehouse.getRecord(1);

								rAddress = rMultiShip.sa_order_revh_multi_ship_to_in_warehouse.in_warehouse_to_sys_address.getRecord(1);
								sAddressCode = rPickupWarehouse.whse_code;
							} 
							else {
								sAddressCode = rCustomerAddress.custaddr_code;

								if (rPackShip && utils.hasRecords(rPackShip.sa_pack_to_sys_address)) {
									rAddress = rPackShip.sa_pack_to_sys_address.getRecord(1);
								} 
								else if (utils.hasRecords(rCustomerAddress.sa_customer_address_to_sys_address)) {
									rAddress = rCustomerAddress.sa_customer_address_to_sys_address;
								}
							}

							oTaxInfo.Addresses[nAddressCounter] = new Object();

							sAddressCode += (nAddressCounter + 1);
							oTaxInfo.Addresses[nAddressCounter].AddressCode = sAddressCode;
							aAddressCodes[rCustomerAddress.custaddr_id] = sAddressCode;

							if (rAddress) {
								oTaxInfo.Addresses[nAddressCounter].Line1 = rAddress.addr_address1;
								oTaxInfo.Addresses[nAddressCounter].Line2 = rAddress.addr_address2;
								oTaxInfo.Addresses[nAddressCounter].Line3 = rAddress.addr_address3;
								oTaxInfo.Addresses[nAddressCounter].City = rAddress.addr_city;
								oTaxInfo.Addresses[nAddressCounter].PostalCode = rAddress.addr_postal;

								if (utils.hasRecords(rAddress.sys_address_to_sys_state_province)) {
									oTaxInfo.Addresses[nAddressCounter].Region = rAddress.sys_address_to_sys_state_province.stateprov_code;
								} 
								else {
									oTaxInfo.Addresses[nAddressCounter].Region = '';
								}

								if (utils.hasRecords(rAddress.sys_address_to_sys_country)) {
									oTaxInfo.Addresses[nAddressCounter].Country = rAddress.sys_address_to_sys_country.country_code_2_char_iso;
								}
							}

							nAddressCounter++;
						}

						sTaxExemptionID = null;

						if (utils.hasRecords(rInvoice.sa_invoice_to_sa_customer) && 
								rInvoice.sa_invoice_to_sa_customer.cust_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnCustomerShipTo && 
								rInvoice.sa_invoice_to_sa_customer.tax_exemption_id) {
							sCustomerTaxExemptionID = rInvoice.sa_invoice_to_sa_customer.tax_exemption_id;
						}

						oTaxInfo.Lines[nLineNumber] = new Object();
						oTaxInfo.Lines[nLineNumber].LineNo = String(nLineNumber + 1);
						oTaxInfo.Lines[nLineNumber].OriginCode = sAddressCodeFrom;
						oTaxInfo.Lines[nLineNumber].DestinationCode = sAddressCode;
						oTaxInfo.Lines[nLineNumber].Description = rInvoiceDetail.invd_description;

						if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_in_item) && rInvoiceDetail.sa_invoice_det_to_in_item.item_code) {
							oTaxInfo.Lines[nLineNumber].ItemCode = rInvoiceDetail.sa_invoice_det_to_in_item.item_code;
						}

						// GD - Jun 26, 2023: SL-26157 Intermittent problem getting the det_ship_task records
						// Force reload and log failures
						rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_det_ship_task.loadAllRecords();
						if (scopes.avAccounting.bGpSendInvoices 
						        && !utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_det_ship_task)) {
						            globals.dbLogUpdate(sDBLogID, 'Failed to retrieve invoice_det_ship_task records for invoice ' + sInvoiceNumber, 'Error',
			                            sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax'); 
						}

						if (scopes.avAccounting.bGpSendInvoices && utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_det_ship_task)) {
							fsInvoiceShipTask = rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_det_ship_task;
							fsInvoiceShipTask.sort("sa_invoice_det_ship_task_to_sa_invoice_det.sequence_nr asc, sa_invoice_det_ship_task_to_sa_customer_address.custaddr_code asc, clc_billing_code asc");

							for (k = 1; k <= fsInvoiceShipTask.getSize(); k++) {

								rInvoiceShipTask = fsInvoiceShipTask.getRecord(k);

								if (rInvoiceShipTask.custaddr_id != rCustomerAddress.custaddr_id) {
									continue;
								}

								// Production items
								fsTaxTaxType = null;
								if (utils.hasRecords(rInvoiceShipTask.sa_invoice_det_ship_task_to_sa_order_revds_task) && utils.hasRecords(rInvoiceShipTask.sa_invoice_det_ship_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task) && utils.hasRecords(rInvoiceShipTask.sa_invoice_det_ship_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sys_tax_type)) {

									fsTaxTaxType = rInvoiceShipTask.sa_invoice_det_ship_task_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sys_tax_type;
								}
								// Additional Charge
								else if (utils.hasRecords(rInvoiceShipTask.sa_invoice_det_ship_task_to_in_item) && utils.hasRecords(rInvoiceShipTask.sa_invoice_det_ship_task_to_in_item.in_item_to_sys_tax_type)) {

									fsTaxTaxType = rInvoiceShipTask.sa_invoice_det_ship_task_to_in_item.in_item_to_sys_tax_type;
								}
								// Finished good
								else if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail) && utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item) && utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.in_item_to_sys_tax_type)) {

									fsTaxTaxType = rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.in_item_to_sys_tax_type;
								}

								if (k > 1) {
									oTaxInfo.Lines[nLineNumber] = new Object();
									oTaxInfo.Lines[nLineNumber].LineNo = String(nLineNumber + 1);
									oTaxInfo.Lines[nLineNumber].OriginCode = sAddressCodeFrom;
									oTaxInfo.Lines[nLineNumber].DestinationCode = sAddressCode;
									oTaxInfo.Lines[nLineNumber].Description = rInvoiceDetail.invd_description;
								}

								oTaxInfo.Lines[nLineNumber].Description = rInvoiceShipTask.clc_billing_code_description;
								nTaxableAmount = globals["avUtilities_roundNumber"](rInvoiceShipTask.invdstask_total, 2);
								oTaxInfo.Lines[nLineNumber].ItemCode = rInvoiceShipTask.clc_billing_code;

								sTaxExemptionID = null;

								if (utils.hasRecords(fsTaxTaxType)) {

									if (fsTaxTaxType.taxtype_code_avalara) {
										oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTaxType.taxtype_code_avalara;
									} 
									else {
										oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTaxType.taxtype_code;
									}
									sTaxExemptionID = fsTaxTaxType.tax_exemption_id;
								}

								if (rInvoiceShipment.invds_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.NonTaxable || 
										sCustomerTaxExemptionID || 
										(rInvoiceShipment.invds_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnTaxType && sTaxExemptionID)) {

									if (rInvoiceShipment.tax_exemption_id) {
										sTaxExemptionID = rInvoiceShipment.tax_exemption_id;
									} 
									else if (sCustomerTaxExemptionID) {
										sTaxExemptionID = sCustomerTaxExemptionID;
									} 
									else {
										oTaxInfo.Lines[nLineNumber].TaxCode = "NT";
									}

									oTaxInfo.Lines[nLineNumber].CustomerUsageType = application.getValueListDisplayValue('vl_taxExemptionReasonCodes', sTaxExemptionID);
								}

								if (rInvoice.inv_record_type == 'C') {
									if (rInvoiceShipTask.invdstask_qty < 0) {
										if (rInvoiceShipTask.invdstask_qty) {
											oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipTask.invdstask_qty.toLocaleString();
										} else {
											oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipTask.invdstask_qty;
										}
									} 
									else {
										oTaxInfo.Lines[nLineNumber].Qty = new Number(-1 * rInvoiceShipTask.invdstask_qty).toLocaleString();
									}

									if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
										oTaxInfo.Lines[nLineNumber].Qty = 1;
										nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
									}
								} else {
									if (rInvoiceShipTask.invdstask_qty) {
										oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipTask.invdstask_qty.toLocaleString();
									} 
									else {
										oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipTask.invdstask_qty;
									}
								}

								//AvaTax will not accept a zero quantity, it will automatically change it to 1 so forcing a zero taxable amount so there is no calculation.
								if (oTaxInfo.Lines[nLineNumber].Qty == 0) {
									nTaxableAmount = 0;
								}

								if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
									nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
								}

								if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
									oTaxInfo.Lines[nLineNumber].Amount = convertToCurrency(sCurrencyID, dInvoiceDate, nTaxableAmount, false);
								} 
								else {
									oTaxInfo.Lines[nLineNumber].Amount = globals.formatNumberToTwoDecimal(nTaxableAmount);
								}
								
	                            // A Credit Note cannot have a negative quantity and negative amount so making the quantity positive will satisfy AvaTax
	                            if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && oTaxInfo.Lines[nLineNumber].Amount < 0 && oTaxInfo.Lines[nLineNumber].Qty < 0) {
	                                oTaxInfo.Lines[nLineNumber].Qty = Math.abs(oTaxInfo.Lines[nLineNumber].Qty);
	                            }

								if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && 
										utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice) && 
										rInvoice.inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
									oTaxInfo.Lines[nLineNumber].TaxOverride = new Object();
									oTaxInfo.Lines[nLineNumber].TaxOverride.TaxOverrideType = 'TaxDate';
									oTaxInfo.Lines[nLineNumber].TaxOverride.TaxDate = utils.dateFormat(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_date, 'yyyy-MM-dd');
									oTaxInfo.Lines[nLineNumber].TaxOverride.Reason = i18n.getI18NMessage('avanti.lbl.avalaraTaxOverrideReason');
								}

								nLineNumber++;
								rInvoiceShipTask.invdtask_tax_line_num = nLineNumber;
							}
						} 
						else {
							//// ---- START of Regular Invoice Tax

							if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sys_tax_type)) {
								if (rInvoiceShipment.sa_invoice_det_ship_to_sys_tax_type.taxtype_code_avalara) {
									oTaxInfo.Lines[nLineNumber].TaxCode = rInvoiceShipment.sa_invoice_det_ship_to_sys_tax_type.taxtype_code_avalara;
								} 
								else {
									oTaxInfo.Lines[nLineNumber].TaxCode = rInvoiceShipment.sa_invoice_det_ship_to_sys_tax_type.taxtype_code;
								}
								sTaxExemptionID = rInvoiceShipment.sa_invoice_det_ship_to_sys_tax_type.tax_exemption_id;
							}

							if (rInvoiceShipment.invds_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.NonTaxable || 
									sCustomerTaxExemptionID || 
									(rInvoiceShipment.invds_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnTaxType && sTaxExemptionID)) {

								if (rInvoiceShipment.tax_exemption_id) {
									sTaxExemptionID = rInvoiceShipment.tax_exemption_id;
								} 
								else if (sCustomerTaxExemptionID) {
									sTaxExemptionID = sCustomerTaxExemptionID;
								} 
								else {
									oTaxInfo.Lines[nLineNumber].TaxCode = "NT";
								}

								oTaxInfo.Lines[nLineNumber].CustomerUsageType = application.getValueListDisplayValue('vl_taxExemptionReasonCodes', sTaxExemptionID);
								bUseInvoiceLineAmount = true;
							}

							if (rInvoice.inv_record_type == 'C') {
								if (rInvoiceShipment.invds_invoice_qty < 0) {
									if (rInvoiceShipment.invds_invoice_qty) {
										oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipment.invds_invoice_qty.toLocaleString();
									} else {
										oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipment.invds_invoice_qty;
									}
								} 
								else {
									oTaxInfo.Lines[nLineNumber].Qty = new Number(-1 * rInvoiceShipment.invds_invoice_qty).toLocaleString();
								}

								if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
									oTaxInfo.Lines[nLineNumber].Qty = 1;
									nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
								}
							} 
							else {
								if (rInvoiceShipment.invds_invoice_qty) {
									oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipment.invds_invoice_qty.toLocaleString();
								} 
								else {
									oTaxInfo.Lines[nLineNumber].Qty = rInvoiceShipment.invds_invoice_qty;
								}
							}

							nTaxableAmount = rInvoiceShipment.invds_taxable_amt;

							if (bUseInvoiceLineAmount) {
								if (rInvoiceShipment.invds_extended_total) {
									nTaxableAmount = rInvoiceShipment.invds_extended_total;
								} 
								else {
									nTaxableAmount = rInvoiceDetail.invd_extended_total;
								}
							}

							//AvaTax will not accept a zero quantity, it will automatically change it to 1 so forcing a zero taxable amount so there is no calculation.
							if (oTaxInfo.Lines[nLineNumber].Qty == 0) {
								nTaxableAmount = 0;
							}

							if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
								nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
							}

							if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
								oTaxInfo.Lines[nLineNumber].Amount = convertToCurrency(sCurrencyID, dInvoiceDate, nTaxableAmount, false);
							} 
							else {
								oTaxInfo.Lines[nLineNumber].Amount = globals.formatNumberToTwoDecimal(nTaxableAmount);
							}
							
							// A Credit Note cannot have a negative quantity and negative amount so making the quantity positive will satisfy AvaTax
							if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && oTaxInfo.Lines[nLineNumber].Amount < 0 && oTaxInfo.Lines[nLineNumber].Qty < 0) {
							    oTaxInfo.Lines[nLineNumber].Qty = Math.abs(oTaxInfo.Lines[nLineNumber].Qty);
							}

							if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && 
									utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice) && 
									rInvoice.inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
								oTaxInfo.Lines[nLineNumber].TaxOverride = new Object();
								oTaxInfo.Lines[nLineNumber].TaxOverride.TaxOverrideType = 'TaxDate';
								oTaxInfo.Lines[nLineNumber].TaxOverride.TaxDate = utils.dateFormat(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_date, 'yyyy-MM-dd');
								oTaxInfo.Lines[nLineNumber].TaxOverride.Reason = i18n.getI18NMessage('avanti.lbl.avalaraTaxOverrideReason');
							}

							nLineNumber++;
							rInvoiceShipment.invds_tax_line_num = nLineNumber;
							rInvoiceDetail.invd_tax_line_num = nLineNumber;
						}
					}
				}
            }
            
            if (nLineNumber == 0) {
                return false;
            }
            
            if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
                    && utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice) 
                    && rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
                oTaxInfo.TaxOverride = new Object();
                oTaxInfo.TaxOverride.TaxOverrideType = 'TaxAmount';
                oTaxInfo.TaxOverride.TaxDate = utils.dateFormat(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_date, 'yyyy-MM-dd');
                oTaxInfo.TaxOverride.Reason = "Tax Only Credit Note"
                oTaxInfo.TaxOverride.TaxAmount = rInvoice.inv_salestax_amt;
            }

            nAddressCounter = 0;
            // Calculate freight tax
            for (var nFreightIndex = 1; nFreightIndex <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); nFreightIndex++) {
                var rFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(nFreightIndex);
                
                // We want to exclude zero freight lines or avaTax will include in the allocation of tax.
                if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly && rFreight.invf_freight_amount == 0) {
                    continue;
                }

                if (aAddressesWithFreightCalculated.indexOf(rFreight.invf_id) < 0) {
                    aAddressesWithFreightCalculated.push(rFreight.invf_id);
                    var oNewFreight = new Object();
                    nInvoiceShipIndex++;
                    oNewFreight.LineNo = 'Freight_' + nFreightIndex + '_' + nLineNumber;
                    rFromAddress = null;
 

                    // origin
                    if (utils.hasRecords(rFreight.sa_invoice_freight_to_sa_pack)) {
                        rPack = rFreight.sa_invoice_freight_to_sa_pack.getRecord(1);

                        if (rPack.ship_address_from_warehouse == 0 && utils.hasRecords(rPack.sa_pack_to_sys_address_from)) {
                            rFromAddress = rPack.sa_pack_to_sys_address_from.getRecord(1);
                        }
                        else if (rPack.ship_address_from_warehouse == 1 && utils.hasRecords(rPack.sa_pack_to_in_warehouse_from) 
                                && utils.hasRecords(rPack.sa_pack_to_in_warehouse_from.in_warehouse_to_sys_address)) {
                            rFromAddress = rPack.sa_pack_to_in_warehouse_from.in_warehouse_to_sys_address.getRecord(1);
                        }
                    }
                    // as per JP - if no pack then use line item warehouse address
                    else if (utils.hasRecords(rFreight.sa_invoice_freight_to_sa_order_revision_detail) 
                            && utils.hasRecords(rFreight.sa_invoice_freight_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse) 
                            && utils.hasRecords(rFreight.sa_invoice_freight_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address)) {

                        rFromAddress = rFreight.sa_invoice_freight_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.in_warehouse_to_sys_address.getRecord(1);
                    }

                    if (rFromAddress) {
                        sAddressCodeFrom = aAddressCodes[rFromAddress.addr_id];
                    }

                    // destination
					if (utils.hasRecords(rFreight.sa_invoice_freight_to_sa_ship) 
							&& utils.hasRecords(rFreight.sa_invoice_freight_to_sa_ship.sa_ship_to_sa_order) 
							&& utils.hasRecords(rFreight.sa_invoice_freight_to_sa_ship.sa_ship_to_sa_customer_address)) {
						var rDestinationCustomerAddress = rFreight.sa_invoice_freight_to_sa_ship.sa_ship_to_sa_customer_address.getRecord(1);
						sAddressCode = aAddressCodes[rDestinationCustomerAddress.custaddr_id];

						if (!sAddressCode) {
							if (utils.hasRecords(rFreight.sa_invoice_freight_to_sa_pack) 
									&& utils.hasRecords(rFreight.sa_invoice_freight_to_sa_pack.sa_pack_to_sa_customer_address)) {
								sAddressCode = aAddressCodes[rPack.sa_pack_to_sa_customer_address.custaddr_id];
							}
						}

						//If there isn't a destination address by now then there is bad data on the sa_ship record and/or packing slip
						//We need to get the customer address from the invoice shipment detail record
						//First check by packing slip reference
						if (!sAddressCode && rDestinationCustomerAddress && rPack) {
							var uInvoiceDetailCustomerShipAddress = getInvoiceDetailShipCustomerAddressByPackingSlip(rFreight.inv_id, rPack.pack_id);
							if (uInvoiceDetailCustomerShipAddress) {
								sAddressCode = aAddressCodes[uInvoiceDetailCustomerShipAddress];
								
								//Fix the address on the packing slip
								rPack.pack_shipto_addr_id = uInvoiceDetailCustomerShipAddress;
								databaseManager.saveData(rPack);
							}
						}

						//If there still isn't an valid address then check by ship location.
						if (!sAddressCode && rFreight.ship_id) {
							uInvoiceDetailCustomerShipAddress = getInvoiceDetailShipCustomerAddressByShipment(rFreight.inv_id, rFreight.ship_id);
							if (uInvoiceDetailCustomerShipAddress) {
								sAddressCode = aAddressCodes[uInvoiceDetailCustomerShipAddress];
							}
						}

						//If address cannot be found then throw error
						if (!sAddressCode) {
							var sMessage = null;
							if (rPack) {
								sMessage = i18n.getI18NMessage("avanti.dialog.missingPackingSlipShipLocationReference");
								sMessage = sMessage.replace("<packslip>", rPack.pack_doc_number.toString());
								throw new Error(sMessage);
							}
							else {
								sMessage = i18n.getI18NMessage("avanti.dialog.missingSalesOrderShipLocationReference");
								sMessage = sMessage.replace("<sales_order>", rOrder.ordh_document_num.toString());
								throw new Error(sMessage);
							}
						}
					}

                    oNewFreight.OriginCode = sAddressCodeFrom;
                    oNewFreight.DestinationCode = sAddressCode;
                    oNewFreight.ItemCode = i18n.getI18NMessage('avanti.lbl.freight');
                    oNewFreight.Description = i18n.getI18NMessage('avanti.lbl.freight');
                    
                    if (rInvoice.inv_record_type == 'C') {
                        oNewFreight.Qty = -1;
                    }
                    else {
                        oNewFreight.Qty = 1;
                    }
                    if (rFreight.invf_freight_taxable == 0) {
                        oNewFreight.TaxCode = 'NT';
                    }
                    else if (utils.hasRecords(rFreight.sa_invoice_freight_to_sys_shipping_method) && rFreight.sa_invoice_freight_to_sys_shipping_method.shipmethod_avalara_tax_code) {
                        oNewFreight.TaxCode = rFreight.sa_invoice_freight_to_sys_shipping_method.shipmethod_avalara_tax_code;
                    }

                    if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                        oNewFreight.Amount = convertToCurrency(sCurrencyID, dInvoiceDate, rFreight.invf_freight_amount, false);
                    }
                    else {
                        oNewFreight.Amount = utils.stringToNumber(utils.numberFormat(rFreight.invf_freight_amount, 2));
                    }

                    oTaxInfo.Lines.push(oNewFreight);
                    
                    if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
                            && utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice) 
                            && rInvoice.inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
                        oTaxInfo.Lines[nLineNumber].TaxOverride = new Object();
                        oTaxInfo.Lines[nLineNumber].TaxOverride.TaxOverrideType = 'TaxDate';
                        oTaxInfo.Lines[nLineNumber].TaxOverride.TaxDate = utils.dateFormat(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_date, 'yyyy-MM-dd');
                        oTaxInfo.Lines[nLineNumber].TaxOverride.Reason = i18n.getI18NMessage('avanti.lbl.avalaraTaxOverrideReason');
                    }
                    nLineNumber++;
                }
            }
            
            var oTaxInfoStates = checkStatesInAvaTaxObject(oTaxInfo);
            if (!oTaxInfoStates) {
                // Do not create an invoice transaction
                oTaxInfo.DocType = "SalesOrder";
            }
            
            /** @type {{ResultCode, TaxLines:{LineNo, TaxCode, Taxability, BoundaryLevel, Discount, Taxable, Rate, Tax, TaxCalculated, TaxDetails:{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}}, TaxAddresses:{Address, AddressCode, City, Country, PostalCode, Region, TaxRegionId, JurisCode}}} */
            var oAvalaraResponse;
            var jsonContent = plugins.serialize.toJSON(oTaxInfo);
            var sResponseBody = doRequest();
            var sErrorMessage;

            if (bCreateTransaction && sResponseBody) {
                oAvalaraResponse = plugins.serialize.fromJSON(sResponseBody);

                if (oAvalaraResponse.ResultCode == 'Error') {
                    sErrorMessage = showAvalaraError(oAvalaraResponse, true);
                    var sSummary = "DocStatus is invalid for this operation";
                    var sDetails = "Expected Saved|Posted";

                    // SL-15760 - if we get this error that means there is already a transaction for this invoice - need to delete it and try again
                    if (sErrorMessage.indexOf(sSummary) > -1 && sErrorMessage.indexOf(sDetails) > -1) {
                        scopes.avTax.voidTransactionForInvoiceAvalara(rInvoice, true);
                        sResponseBody = doRequest();
                    }
                }
            }

            if (sResponseBody) {
                oAvalaraResponse = plugins.serialize.fromJSON(sResponseBody);
                
                if (oAvalaraResponse && oAvalaraResponse.ResultCode == "Success") {
                    if (oAvalaraResponse.TaxLines) {
                        var aTax = new Array();
                        // Clear out tax details on invoice.
                        if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_tax_detail)) {
                            rInvoice.sa_invoice_to_sa_invoice_tax_detail.deleteAllRecords();
                        }
                        if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)) {
                            for (nDetailIndex = 1; nDetailIndex <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); nDetailIndex++) {
                                rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(nDetailIndex);                              
                                // Clear out tax details on invoice details.
                                if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail)) {
                                    rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.deleteAllRecords();
                                }
                                if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship)) {
                                    for (nInvoiceShipIndex = 1; nInvoiceShipIndex <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); nInvoiceShipIndex++) {
                                        rInvoiceShipment = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(nInvoiceShipIndex);
                                        // Clear out tax details by invoice shipment
                                        if (utils.hasRecords(rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_tax_detail)) {
                                            rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_tax_detail.deleteAllRecords();
                                        }
                                    }
                                }
                            }
                        }
                        if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_freight)) {
                            for (nFreightIndex = 1; nFreightIndex <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); nFreightIndex++) {
                                rFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(nFreightIndex);
                                if (utils.hasRecords(rFreight.sa_invoice_freight_to_sa_invoice_tax_detail)) {
                                    rFreight.sa_invoice_freight_to_sa_invoice_tax_detail.deleteAllRecords();
                                }
                            }
                        }

                        for (var key in oAvalaraResponse.TaxLines) {
                            if (oAvalaraResponse.TaxLines[key].Taxable || oAvalaraResponse.TaxLines[key].Tax) {
                                for (var detailKey in oAvalaraResponse.TaxLines[key].TaxDetails) {
                                    /** @type  {{Country, Region, JurisType, JurisCode, Taxable, Rate, Tax, JurisName, TaxName}} */
                                    var oTaxDetailLine = oAvalaraResponse.TaxLines[key].TaxDetails[detailKey];
                                    var oTaxDetails = getTaxItem_FS(oTaxDetailLine, oAvalaraResponse.TaxDate, rInvoice.curr_id);
                                    var oTaxItems = new Object();
                                    oTaxItems.line_number = oAvalaraResponse.TaxLines[key].LineNo;
                                    oTaxItems.taxitem_id = oTaxDetails.sTaxItemID;
                                    if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
                                        oTaxItems.taxable_amt = convertToCurrency(sCurrencyID, dInvoiceDate, oTaxDetailLine.Taxable, true);
                                    }
                                    else {
                                        oTaxItems.taxable_amt = Number(oTaxDetailLine.Taxable);
                                    }

                                    //Get the tax rate
                                    oTaxItems.tax_percent = utils.stringToNumber(oTaxDetailLine.Rate);
                                    oTaxItems.tax_rate_id = oTaxDetails.sTaxRateID;
                                    
                                    if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote) {
                                        oTaxItems.tax_amt = utils.stringToNumber(oTaxDetailLine.Tax) * -1;
                                    }
                                    else {
                                        oTaxItems.tax_amt = utils.stringToNumber(oTaxDetailLine.Tax);
                                    }
                                   
                                    //Round up to 2 decimal places for now.
                                    oTaxItems.tax_amt = scopes.avUtils.roundNumber(oTaxItems.tax_amt, 2);

                                    if (oAvalaraResponse.TaxLines[key].LineNo.indexOf('Freight') >= 0) {
                                        aFreightTaxes.push(oTaxItems);
                                    }
                                    aTax.push(oTaxItems);
                                }
                            }
                        }

                        if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)) {
                            for (nDetailIndex = 1; nDetailIndex <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); nDetailIndex++) {
                                rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(nDetailIndex);
                                
                                if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail)
                                		&& rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.item_id 
                                        && utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item)
                                        && rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.itemtype_code == scopes.avInv.ITEM_TYPE_CODE.ServiceItem
                                        && rInvoiceDetail.sa_invoice_det_to_sa_order_revision_detail.sa_order_revision_detail_to_in_item.item_exclude_from_avatax == 1) {
                                    continue;
                                }
                                
                                //If the charge is at order level or invoice level and does not have a ship record
								if (rInvoiceDetail.ordrevd_id == null && rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize() == 0) {
									if (scopes.avAccounting.bGpSendInvoices) {
										fsInvoiceTaxDetail = rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail;
										nTaxAmount = 0.00;

										for (m = 0; m < aTax.length; m++) {

											sTaxKey = aTax[m].line_number + "_" + aTax[m].taxitem_id;

											if (rInvoiceDetail.invd_tax_line_num == aTax[m].line_number && aTaxNew.indexOf(sTaxKey) == -1) {

												rInvoiceTaxDetail = fsInvoiceTaxDetail.getRecord(fsInvoiceTaxDetail.newRecord());

												rInvoiceTaxDetail.invtaxdet_taxable_sales_amt = aTax[m].taxable_amt;
												rInvoiceTaxDetail.invtaxdet_tax_amt = aTax[m].tax_amt;
												rInvoiceTaxDetail.invtaxdet_taxrate_percent = aTax[m].tax_percent;
												rInvoiceTaxDetail.taxitem_id = aTax[m].taxitem_id;
												rInvoiceTaxDetail.taxrate_id = aTax[m].taxrate_id;
												nTaxAmount += aTax[m].tax_amt;

												aTaxNew.push(sTaxKey);
											}
										}
										databaseManager.saveData(fsInvoiceTaxDetail);
									} 
									else {
										//Update Tax Details
										avCalcs_salesTax_setInvoiceTaxDetails(rInvoice, rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail, aTax, rInvoiceDetail.invd_tax_line_num);
									}
								} 
								else {
									for (k = 1; k <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); k++) {
										rInvoiceShipment = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(k);

										if (scopes.avAccounting.bGpSendInvoices && utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task)) {

											fsInvoiceShipTask = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task;

											for (n = 1; n <= fsInvoiceShipTask.getSize(); n++) {

												rInvoiceShipTask = fsInvoiceShipTask.getRecord(n);

												if (rInvoiceShipTask.custaddr_id != rInvoiceShipment.custaddr_id) {
													continue;
												}

												bRefreshBillingCodeTaxTotal = true;

												fsInvoiceTaxDetail = rInvoiceShipTask.sa_invoice_det_ship_task_to_sa_invoice_tax_detail;
												nTaxAmount = 0.00;

												for (m = 0; m < aTax.length; m++) {

													sTaxKey = aTax[m].line_number + "_" + aTax[m].taxitem_id;

													if (rInvoiceShipTask.invdtask_tax_line_num == aTax[m].line_number && aTaxNew.indexOf(sTaxKey) == -1) {

														rInvoiceTaxDetail = fsInvoiceTaxDetail.getRecord(fsInvoiceTaxDetail.newRecord());

														rInvoiceTaxDetail.invtaxdet_taxable_sales_amt = aTax[m].taxable_amt;
														rInvoiceTaxDetail.invtaxdet_tax_amt = aTax[m].tax_amt;
														rInvoiceTaxDetail.invtaxdet_taxrate_percent = aTax[m].tax_percent;
														rInvoiceTaxDetail.taxitem_id = aTax[m].taxitem_id;
														rInvoiceTaxDetail.taxrate_id = aTax[m].taxrate_id;
														nTaxAmount += aTax[m].tax_amt;

														rInvoiceTaxDetail.invds_id = rInvoiceShipment.invds_id;

														aTaxNew.push(sTaxKey);
													}
												}
												databaseManager.saveData(fsInvoiceTaxDetail);
												rInvoiceShipTask.invdstask_total_tax = nTaxAmount;
											}
										} 
										else {
											//Update Tax Details
											avCalcs_salesTax_setInvoiceTaxDetails(rInvoice, rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_tax_detail, aTax, rInvoiceShipment.invds_tax_line_num); //By shipment
										}
										avCalcs_salesTax_updateInvoiceTaxDetails(rInvoice, rInvoiceShipment.sa_invoice_det_ship_to_sa_invoice_tax_detail, rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail);
									}
								}
                                avCalcs_salesTax_updateInvoiceTaxDetails(rInvoice, rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail, rInvoice.sa_invoice_to_sa_invoice_tax_detail);
                            }
                        }

                        // Calculate freight tax
                        for (nFreightIndex = 1; nFreightIndex <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); nFreightIndex++) {
                            rFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(nFreightIndex);
                            if (rFreight.invf_freight_taxable) {
                                avCalcs_salesTax_setInvoiceTaxDetails(rInvoice, rFreight.sa_invoice_freight_to_sa_invoice_tax_detail, aFreightTaxes, nFreightIndex, true);
                                avCalcs_salesTax_updateInvoiceTaxDetails(rInvoice, rFreight.sa_invoice_freight_to_sa_invoice_tax_detail, rInvoice.sa_invoice_to_sa_invoice_tax_detail);
                            }
                        }
                    }
                }
                else {
                	sErrorMessage = showAvalaraError(oAvalaraResponse);
                    globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax using AvaTax for invoice ' + sInvoiceNumber + ' due to ' + sErrorMessage, 'Error',
                        sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                    globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', jsonContent);
                    globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
                    return false;
                }

                processTaxGroups_FS(oTaxInfo, oAvalaraResponse, rInvoice.curr_id);
                databaseManager.saveData(rInvoice.sa_invoice_to_sa_invoice_tax_detail);
                rInvoice.inv_salestax_amt = globals["avUtilities_roundNumber"](rInvoice.sa_invoice_to_sa_invoice_tax_detail.total_tax_amt, 2);
                rInvoice.inv_total_amt = globals["avUtilities_roundNumber"](rInvoice.inv_subtotal_amt - rInvoice.inv_discount_amt + rInvoice.inv_postage_amount + rInvoice.inv_freight_amt + rInvoice.inv_salestax_amt, 2);
                if (bCreateTransaction) {
                	rInvoice.inv_sent_to_avatax = 1;
                    globals.dbLogUpdate(sDBLogID, 'Successfully completed calculating tax and creating transaction in AvaTax for invoice ' + sInvoiceNumber, 'Success', sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                }
                else {
                    globals.dbLogUpdate(sDBLogID, 'Successfully completed calculating tax from AvaTax for invoice ' + sInvoiceNumber, 'Success', sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                }
                globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
                databaseManager.saveData();
                
                if (bRefreshBillingCodeTaxTotal) {
                    
                    forms["sa_invoice_det_ship_task_tbl"].getTotal(rInvoice);
                }
                return true;
            }
            else {
                globals.dbLogUpdate(sDBLogID, 'No response from AvaTax for invoice ' + sInvoiceNumber, 'Error', sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
                application.output('No Response from Avalara', LOGGINGLEVEL.ERROR);
                return false;
            }
        }

        globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax using AvaTax for invoice ' + sInvoiceNumber + '. Please check the setup in the Organization program.', 'Error',
            sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
        if (sResponseBody) {
        	globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
        }
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.avalaraFailedToCalculate'));
        return false;
    }
    catch (ex) {
        globals.dbLogUpdate(sDBLogID, 'Failed to calculate tax using AvaTax for invoice ' + sInvoiceNumber + '. Please check the setup in the Organization program.', 'Error',
            sInvoiceNumber, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
        if (sResponseBody) {
        	globals.dbLogWriteDetails(sDBLogID, 'processing_tax', '', sResponseBody);
        }
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
        return false;
    }
    finally {
        if (oHTTPClient) {
            oHTTPClient.close();
        }
    }

    function doRequest() {
        var oPostRequest = oHTTPClient.createPostRequest(sAvalaraURL + '/1.0/tax/get');

        oPostRequest.addHeader('Content-Type', 'application/json');
        oPostRequest.addHeader('Authorization', sAvalaraAPIKey);
        oPostRequest.setBodyContent(jsonContent);

        var response = oPostRequest.executeRequest();

        if (response) {
        	var ret = response.getResponseBody();
            return ret;
        }
        else {
            return null;
        }
    }
    
	function calculateTaxInfoForAdditionalCharge(addtlChargeType) {
		var rChargeAddress;
		var rbillToAddress;
		var sAddrCode;
		if (addtlChargeType == 'ORDERCHARGE') {
			rChargeAddress = rInvoiceDetail.sa_invoice_det_to_sa_order.sa_order_to_sa_order_address_billto.getRecord(1);
			sAddrCode = rChargeAddress.ordaddr_code;
			rbillToAddress = rChargeAddress.sa_order_address_to_sys_address.getRecord(1);
		} 
		else if (addtlChargeType == 'INVOICECHARGE') {
			rChargeAddress = rInvoice.sa_invoice_to_sa_customer_address$billto.getRecord(1);
			sAddrCode = rChargeAddress.custaddr_code;
			rbillToAddress = rChargeAddress.sa_customer_address_to_sys_address.getRecord(1);
		}

		if (!rbillToAddress) {
			return;
		}
		
		var originAddrCode;
		var destAddrCode;
		
		// CREATE ORIGIN ADDRESS & DESTINATION ADDRESS
		for (var j = 1; j <= 2; j++ ) {
			var sAddrCodeFrom;
			if (j == 1) {
				sAddrCodeFrom = "ORIGIN" + (nAddressCounter + 1);
				originAddrCode = sAddrCodeFrom;
			}
			else {
				sAddrCodeFrom = sAddrCode + (nAddressCounter + 1);
				destAddrCode = sAddrCodeFrom;
			}
			
			oTaxInfo.Addresses[nAddressCounter] = new Object();
			oTaxInfo.Addresses[nAddressCounter].AddressCode = sAddrCodeFrom;
			oTaxInfo.Addresses[nAddressCounter].Line1 = rbillToAddress.addr_address1;
			oTaxInfo.Addresses[nAddressCounter].Line2 = rbillToAddress.addr_address2;
			oTaxInfo.Addresses[nAddressCounter].Line3 = rbillToAddress.addr_address3;
			oTaxInfo.Addresses[nAddressCounter].City = rbillToAddress.addr_city;
			oTaxInfo.Addresses[nAddressCounter].PostalCode = rbillToAddress.addr_postal;

			if (utils.hasRecords(rbillToAddress.sys_address_to_sys_state_province)) {
				oTaxInfo.Addresses[nAddressCounter].Region = rbillToAddress.sys_address_to_sys_state_province.stateprov_code;
			} 
			else {
				oTaxInfo.Addresses[nAddressCounter].Region = '';
			}

			if (utils.hasRecords(rbillToAddress.sys_address_to_sys_country)) {
				oTaxInfo.Addresses[nAddressCounter].Country = rbillToAddress.sys_address_to_sys_country.country_code_2_char_iso;
			}
			aAddressCodes[rbillToAddress.addr_id] = sAddrCodeFrom;
			nAddressCounter++;
		}

		oTaxInfo.Lines[nLineNumber] = new Object();
		oTaxInfo.Lines[nLineNumber].LineNo = String(nLineNumber + 1);
		oTaxInfo.Lines[nLineNumber].OriginCode = originAddrCode;
		oTaxInfo.Lines[nLineNumber].DestinationCode = destAddrCode;
		oTaxInfo.Lines[nLineNumber].Description = rInvoiceDetail.invd_description;

		if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_in_item) && rInvoiceDetail.sa_invoice_det_to_in_item.item_code) {
			oTaxInfo.Lines[nLineNumber].ItemCode = rInvoiceDetail.sa_invoice_det_to_in_item.item_code;
		}


		/** @type {JSFoundSet<db:/avanti/sa_invoice_tax_detail>} */
		var fsInItem = rInvoiceDetail.sa_invoice_det_to_in_item;
		/** @type {JSFoundSet<db:/avanti/sys_tax_type>} */
		var fsTaxTypeRecord;
		if (fsInItem) {
			fsTaxTypeRecord = fsInItem.in_item_to_sys_tax_type;
		}
		
		sTaxExemptionID = null;
		if (utils.hasRecords(fsTaxTypeRecord)) {

			if (fsTaxTypeRecord.taxtype_code_avalara) {
				oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTypeRecord.taxtype_code_avalara;
			} 
			else {
				oTaxInfo.Lines[nLineNumber].TaxCode = fsTaxTypeRecord.taxtype_code;
			}
			sTaxExemptionID = fsTaxTypeRecord.tax_exemption_id;
		}

		if (utils.hasRecords(rInvoice.sa_invoice_to_sa_customer) && 
				rInvoice.sa_invoice_to_sa_customer.cust_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnCustomerShipTo && 
				rInvoice.sa_invoice_to_sa_customer.tax_exemption_id) {
			sCustomerTaxExemptionID = rInvoice.sa_invoice_to_sa_customer.tax_exemption_id;
		}

		if (rInvoiceDetail.invd_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.NonTaxable ||
				sCustomerTaxExemptionID || 
				(rInvoiceDetail.invd_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnTaxType && sTaxExemptionID)) {

			if (fsInItem.tax_exemption_id) {
				sTaxExemptionID = fsInItem.tax_exemption_id;
			} 
			else if (sCustomerTaxExemptionID) {
				sTaxExemptionID = sCustomerTaxExemptionID;
			} 
			else {
				oTaxInfo.Lines[nLineNumber].TaxCode = "NT";
			}

			oTaxInfo.Lines[nLineNumber].CustomerUsageType = application.getValueListDisplayValue('vl_taxExemptionReasonCodes', sTaxExemptionID);
		}

		if (rInvoice.inv_record_type == 'C') {
			if (rInvoiceDetail.invd_invoice_qty < 0) {
				if (rInvoiceDetail.invd_invoice_qty) {
					oTaxInfo.Lines[nLineNumber].Qty = rInvoiceDetail.invd_invoice_qty.toLocaleString();
				} 
				else {
					oTaxInfo.Lines[nLineNumber].Qty = rInvoiceDetail.invd_invoice_qty;
				}
			} 
			else {
				oTaxInfo.Lines[nLineNumber].Qty = new Number(-1 * rInvoiceDetail.invd_invoice_qty).toLocaleString();
			}

			if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
				oTaxInfo.Lines[nLineNumber].Qty = 1;
				nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
			}
			
			// A Credit Note cannot have a negative quantity and negative amount so making the quantity positive will satisfy AvaTax
			oTaxInfo.Lines[nLineNumber].Qty = Math.abs(oTaxInfo.Lines[nLineNumber].Qty);
		} 
		else {
			if (rInvoiceDetail.invd_invoice_qty) {
				oTaxInfo.Lines[nLineNumber].Qty = rInvoiceDetail.invd_invoice_qty.toLocaleString();
			} 
			else {
				oTaxInfo.Lines[nLineNumber].Qty = rInvoiceDetail.invd_invoice_qty;
			}
		}

		nTaxableAmount = rInvoiceDetail.invd_extended_total;
		//AvaTax will not accept a zero quantity, it will automatically change it to 1 so forcing a zero taxable amount so there is no calculation.
		if (oTaxInfo.Lines[nLineNumber].Qty == 0) {
			nTaxableAmount = 0;
		}

		if (rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
			nTaxableAmount = -0; //The sign must match the tax or AvaTax will fail.
		}

		if (oTaxInfo.CurrencyCode != _to_sys_organization.sys_organization_to_sys_currency.curr_iso_code) {
			oTaxInfo.Lines[nLineNumber].Amount = convertToCurrency(sCurrencyID, dInvoiceDate, nTaxableAmount, false);
		} 
		else {
			oTaxInfo.Lines[nLineNumber].Amount = globals.formatNumberToTwoDecimal(nTaxableAmount);
		}

		if (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && 
				utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice) && 
				rInvoice.inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
			oTaxInfo.Lines[nLineNumber].TaxOverride = new Object();
			oTaxInfo.Lines[nLineNumber].TaxOverride.TaxOverrideType = 'TaxDate';
			oTaxInfo.Lines[nLineNumber].TaxOverride.TaxDate = utils.dateFormat(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_date, 'yyyy-MM-dd');
			oTaxInfo.Lines[nLineNumber].TaxOverride.Reason = i18n.getI18NMessage('avanti.lbl.avalaraTaxOverrideReason');
		}

		nLineNumber++;
		rInvoiceDetail.invd_tax_line_num = nLineNumber;
	}
}


/**
 * avCalcs_salesTax_setInvoiceTaxDetails
 *
 * <AUTHOR> Dol
 * @since Apr 14, 2013
 *
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * @param {JSFoundSet<db:/avanti/sa_invoice_tax_detail>} fsTaxDetail
 * @param {Array} aTax
 * @param {Number} [nLineNumber]
 * @param {Boolean} [bIsInvoiceFreight]
 * @param {String} [uTaxExemptionId]
 *
 * @properties={typeid:24,uuid:"02FC47D5-8081-49CE-9E4E-F737C896247F"}
 */
function avCalcs_salesTax_setInvoiceTaxDetails(rInvoice, fsTaxDetail, aTax, nLineNumber, bIsInvoiceFreight, uTaxExemptionId) {
    /***@type {{taxitem_id:String, taxable_amt:Number, tax_percent:Number, tax_rate_id:String, tax_amt:Number}}*/
    var oTaxItems = new Object();
    var rTaxDetail;

    /** @type {String} */
    var sDiv = rInvoice.div_id,
        sPlant = rInvoice.plant_id;

    //Write the tax detail records
    for (var m = 0; m < aTax.length; m++) {
        var sCompareLineNumber = aTax[m].line_number;
        if (bIsInvoiceFreight) {
            sCompareLineNumber = aTax[m].line_number.substring(8, aTax[m].line_number.lastIndexOf('_'));
        }

        if (!nLineNumber || nLineNumber == sCompareLineNumber) {
            oTaxItems = aTax[m];

            var bTaxItemExists = false;

            //Check if Tax Item Exists
            for (var n = 1; n <= fsTaxDetail.getSize(); n++) {
                rTaxDetail = fsTaxDetail.getRecord(n);

                if (rTaxDetail.taxitem_id == oTaxItems.taxitem_id) {
                    bTaxItemExists = true;
                    break;
                }
            }

            if (!bTaxItemExists) {
                rTaxDetail = fsTaxDetail.getRecord(fsTaxDetail.newRecord());
                rTaxDetail.taxitem_id = oTaxItems.taxitem_id;
                rTaxDetail.taxrate_id = oTaxItems.tax_rate_id;
                rTaxDetail.invtaxdet_taxrate_percent = oTaxItems.tax_percent;
                rTaxDetail.invtaxdet_taxable_sales_amt = Number(oTaxItems.taxable_amt);
                rTaxDetail.invtaxdet_tax_amt = globals["avUtilities_roundNumber"](oTaxItems.tax_amt, 2);
            }
            else {
                rTaxDetail.taxrate_id = oTaxItems.tax_rate_id;
                rTaxDetail.invtaxdet_taxrate_percent = oTaxItems.tax_percent;
                rTaxDetail.invtaxdet_taxable_sales_amt += Number(oTaxItems.taxable_amt);
                rTaxDetail.invtaxdet_tax_amt = globals["avUtilities_roundNumber"](globals["avUtilities_roundNumber"](rTaxDetail.invtaxdet_taxable_sales_amt, 2) * rTaxDetail.invtaxdet_taxrate_percent, 2);
            }
            rTaxDetail.tax_exemption_id = uTaxExemptionId;

            //GL Account for sales tax item
            if (utils.hasRecords(rTaxDetail.sa_invoice_tax_detail_to_sys_sales_tax_item)) {
                var sSalesTaxGLAcct = rTaxDetail.sa_invoice_tax_detail_to_sys_sales_tax_item.glacct_id;

                if (rInvoice.inv_type == scopes.avUtils.INVOICE_TYPE.AdvanceBilling || rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.AdvanceBilling) {
                    sSalesTaxGLAcct = globals.avGL_getControlAccount(scopes.avUtils.CONTROL_ACCOUNT_CODE.AdvanceBillingTax, rInvoice.curr_id);
                }

                var oSalesTaxGLAcct = scopes.avAccounting.getGLAccount(sSalesTaxGLAcct, sDiv, sPlant, null, null, null, null)
                rTaxDetail.glacct_id = oSalesTaxGLAcct.glacct_id;
            }

            databaseManager.saveData(rTaxDetail);
        }
    }
}

/**
 * Display Avalara Error Message.
 * @param oAvalaraResponse
 * @param {Boolean} [bJustReturnMsg]
 *
 * @return
 * @properties={typeid:24,uuid:"8B97EA07-EDC9-4BF9-9468-BACB4D52EAF8"}
 */
function showAvalaraError(oAvalaraResponse, bJustReturnMsg) {
    var sAvalaraDisplayMessage = '';
    for (var messageKey in oAvalaraResponse.Messages) {
        if (oAvalaraResponse.Messages[0]) {
            if (oAvalaraResponse.Messages[messageKey].Severity) {
                sAvalaraDisplayMessage += 'Severity: ' + oAvalaraResponse.Messages[messageKey].Severity + '\n';
            }
            sAvalaraDisplayMessage += 'Summary: ' + oAvalaraResponse.Messages[messageKey].Summary + '\n';
            if (oAvalaraResponse.Messages[messageKey].Details) {
                sAvalaraDisplayMessage += 'Details: ' + oAvalaraResponse.Messages[messageKey].Details + '\n';
            }
            if (oAvalaraResponse.Messages[messageKey].RefersTo) {
                sAvalaraDisplayMessage += 'RefersTo: ' + oAvalaraResponse.Messages[messageKey].RefersTo + '\n';
            }
            if (oAvalaraResponse.Messages[messageKey].Source) {
                sAvalaraDisplayMessage += 'Source: ' + oAvalaraResponse.Messages[messageKey].Source + '\n';
            }
        }
    }

    if (!bJustReturnMsg) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + sAvalaraDisplayMessage, i18n.getI18NMessage('avanti.dialog.okay'));
    }

    return sAvalaraDisplayMessage;
}

/**
 * Void invoice document in AvaTax
 *
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * @param {Boolean} [bDeleteTransaction]
 * @param {Boolean} [bLogDeleteAction]
 *
 * @return
 * @properties={typeid:24,uuid:"B2856E4B-**************-C31BEC142F7A"}
 */
function voidTransactionForInvoiceAvalara(rInvoice, bDeleteTransaction, bLogDeleteAction) {
    try {
        var sLogAction = "Voiding Avalara Tax Entry";
        var oHTTPClient = plugins.http.createNewHttpClient();
        var sAvalaraURL = 'https://avatax.avalara.net';
        if (_to_sys_organization.org_avalara_url_type) {
            sAvalaraURL = 'https://development.avalara.net';
        }
        var oPostRequest = oHTTPClient.createPostRequest(sAvalaraURL + '/1.0/tax/cancel');
        aAddressesWithFreightCalculated = new Array();

        var oVoidInvoiceInfo = new Object();

        if (bDeleteTransaction) {
            oVoidInvoiceInfo.CancelCode = 'DocDeleted';
        }
        else {
            oVoidInvoiceInfo.CancelCode = 'DocVoided';
        }
        
        if (bLogDeleteAction) {
            var sDBLogID = globals.dbLog(sLogAction, 'processing_tax', 'processing', 'tax', 'http', application.getUUID(globals.org_id), 'processing_tax', 'Summary', null);
        }

        oVoidInvoiceInfo.CompanyCode = _to_sys_organization.org_avalara_company_code;
        oVoidInvoiceInfo.DocCode = rInvoice.inv_number;
        oVoidInvoiceInfo.DocType =  (rInvoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote ? "ReturnInvoice" : "SalesInvoice");

        var jsonContent = plugins.serialize.toJSON(oVoidInvoiceInfo);
        oPostRequest.addHeader('Content-Type', 'application/json');
        oPostRequest.addHeader('Authorization', sAvalaraAPIKey);
        oPostRequest.setBodyContent(jsonContent);
        var response = oPostRequest.executeRequest();

        if (response) {
            var sResponseBody = response.getResponseBody();

            if (sResponseBody) {
                /** @type {{CancelTaxResult:{ResultCode}}} */
                var oAvalaraResponse = plugins.serialize.fromJSON(response.getResponseBody());
                if (oAvalaraResponse.CancelTaxResult.ResultCode == 'Error') {
                    var sMsg = showAvalaraError(oAvalaraResponse.CancelTaxResult, true);
                    if (bLogDeleteAction) {
                        globals.dbLogUpdate(sDBLogID, sMsg + ', ' + sLogAction + ' for invoice ' + rInvoice.inv_number, 'Error', rInvoice.inv_number, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                    }
                    return false;
                }
                
                if (bLogDeleteAction) {
                    globals.dbLogUpdate(sDBLogID, 'Successfully completed ' + sLogAction + ' in AvaTax for invoice ' + rInvoice.inv_number, 'Success', rInvoice.inv_number, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
                }
                
                // Exclude update if deleting invoice otherwise a sql error is thrown.
				if (!bLogDeleteAction) {
					rInvoice.inv_sent_to_avatax = 0;
					databaseManager.saveData(rInvoice);
				}
                
                return true;
            }
            else {
            	if (bLogDeleteAction) {
                    globals.dbLogUpdate(sDBLogID, 'No response body received from AvaTax for invoice ' + rInvoice.inv_number, 'Error', rInvoice.inv_number, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
            	}
                application.output('No Response from Avalara', LOGGINGLEVEL.ERROR);
                return false;
            }
        }
        
        if (bLogDeleteAction) {
            globals.dbLogUpdate(sDBLogID, 'No response from AvaTax for invoice ' + rInvoice.inv_number, 'Error', rInvoice.inv_number, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
        }
        return false;
    }
    catch (ex) {
    	if (bLogDeleteAction) {
            globals.dbLogUpdate(sDBLogID, 'Error: ' + ex.message + ' has occurred ' + sLogAction + ' for invoice ' + rInvoice.inv_number, 'Error', rInvoice.inv_number, 'processing_tax', 'processing', 'tax', 'http', 'Processing Tax');
    	}
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.anErrorHasOccurred') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
        return false;
    }
    finally {
        if (oHTTPClient) {
            oHTTPClient.close();
        }
        
        if (response) {
        }
    }
}

/**
 * @public 
 *
 * @param {String} sSalesTaxOption
 * @param {UUID} uTaxTypeParamID
 * @param {UUID} uStateProvinceID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F1A9A6DF-9C24-4CCF-B586-C16599C1A897"}
 */
function isTaxTypeNonTaxable(sSalesTaxOption, uTaxTypeParamID, uStateProvinceID) {
    var bIsTaxTypeNonTaxable = false;
    
    if (sSalesTaxOption == 'Y' && uTaxTypeParamID && uStateProvinceID) {

        var sSQL = "SELECT p.taxtype_stprov_id \
            FROM sys_tax_type_state_prov P \
            INNER JOIN sys_tax_type T ON T.taxtype_id = P.taxtype_id \
            WHERE \
                P.stateprov_id = ? \
                AND P.taxtype_id = ?";
        
        /** @type {JSFoundset<db:/avanti/sys_tax_type_state_prov>} **/      
        var fsStateTax = scopes.avDB.getFSFromSQL(sSQL, "sys_tax_type_state_prov", [uStateProvinceID.toString(), uTaxTypeParamID.toString()]);

        if (fsStateTax && fsStateTax.getSize() == 1) {
            
            var rStateTax = fsStateTax.getRecord(1);

            var nTaxTypeTaxable = rStateTax.sys_tax_type_state_prov_to_sys_tax_type.taxtype_taxable;
            var nOnForState = rStateTax.taxtype_taxable;
            var bIsTaxTypeTaxable = false;

            if (nTaxTypeTaxable && nOnForState) {
                bIsTaxTypeTaxable = true;
            }
            else if (!nTaxTypeTaxable && !nOnForState) {
                bIsTaxTypeTaxable = false;
            }

            bIsTaxTypeNonTaxable = !bIsTaxTypeTaxable;
        }
        else {
            scopes.avUtils.UUID_1 = uTaxTypeParamID;
            if (utils.hasRecords(_to_sys_tax_type$uuid1) 
                    && _to_sys_tax_type$uuid1.taxtype_taxable) {
                        
                bIsTaxTypeNonTaxable = true;
            }
        }
    }
    
    return bIsTaxTypeNonTaxable;
}

/**
 * Convert amount to currency
 * @param {UUID} sCurrencyID
 * @param {Date} dCurrencyDate
 * @param {Number} nAmount
 * @param {Boolean} bReverse
 * @properties={typeid:24,uuid:"65DADF41-2A7F-46A1-9692-93F322225BC5"}
 * @return {Number}
 */
function convertToCurrency(sCurrencyID, dCurrencyDate, nAmount, bReverse) {
    var total_amnt_currency_conversion = nAmount;
    // calculating the currency rate for selected estimate record
    /**@type {Number}*/
    var currencyRate = 1;

    /**@type {Date}*/
    var estimateDate = plugins.DateUtils.dateFormat(dCurrencyDate, 'yyyy-MM-dd');

    if (estimateDate && sCurrencyID) {
        currencyRate = globals.getEstimateCurrencyRate(estimateDate, sCurrencyID.toString());
        if (!currencyRate) {
            currencyRate = 1;
        }
    }

    if (bReverse) {
        total_amnt_currency_conversion = total_amnt_currency_conversion * currencyRate;
    }
    else {
        total_amnt_currency_conversion = total_amnt_currency_conversion / currencyRate;
    }
    // Convert to number
    return Number(total_amnt_currency_conversion.toFixed(2));
}

/**
 * @public
 *
 * @param {String} sZipCode
 *
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"0EC0C118-F99D-433E-8A83-6B7CDD21B23B"}
 */
function getTaxGroupFromZipCode(sZipCode) {
    var sTaxGroupID = null;

    //SL-19730: As per Josh, we first need to lookup the entire ZIP and then fallback to the first 5
    if (sZipCode && sZipCode.length >= 5) {
        sTaxGroupID = getTaxGroup();

        if (!sTaxGroupID) {
            if (sZipCode.indexOf("-") >= 5) {
                sZipCode = sZipCode.substr(0, sZipCode.indexOf("-"));
                sTaxGroupID = getTaxGroup();
            }
        }
    }

    /**
     */
    function getTaxGroup() {
        globals.avSalesTax_CurrentZipCode = sZipCode;

        if (utils.hasRecords(_to_sys_sales_tax_group$avsalestax_currentzipcode) &&
        		_to_sys_sales_tax_group$avsalestax_currentzipcode.taxgroup_active) {
            return _to_sys_sales_tax_group$avsalestax_currentzipcode.taxgroup_id;
        }
        else {
            return null;
        }
    }

    return sTaxGroupID;
}

/**
 * @properties={typeid:24,uuid:"2FCE9269-E93B-4AC8-B9D4-618EA6AAF330"}
 */
function initTaxRateCache() {
    oTaxRates = {};
    oTaxRates.taxrate_id = [];
    oTaxRates.taxrate_percent = [];
}

/**
 * Update invoice tax details
 *
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * @param {JSFoundSet<db:/avanti/sa_invoice_tax_detail>} fsTaxDetailSource
 * @param {JSFoundSet<db:/avanti/sa_invoice_tax_detail>} fsTaxDetailDestination
 * @param {String} [uTaxExemptionId] - Tax exemption code
 *
 * @properties={typeid:24,uuid:"22698CAA-5BF1-4271-8F86-************"}
 */
function avCalcs_salesTax_updateInvoiceTaxDetails(rInvoice, fsTaxDetailSource, fsTaxDetailDestination, uTaxExemptionId) {

    /** @type {String} */
    var sDiv = rInvoice.div_id,
        sPlant = rInvoice.plant_id;

    for (var i = 1; i <= fsTaxDetailSource.getSize(); i++) {
        var rTaxDetailSource = fsTaxDetailSource.getRecord(i);

        //Check if tax item exists in destination
        var bTaxItemExists = false;

        //Check if Tax Item Exists
        for (var j = 1; j <= fsTaxDetailDestination.getSize(); j++) {
            var rTaxDetailDestination = fsTaxDetailDestination.getRecord(j);

            if (rTaxDetailDestination.taxitem_id == rTaxDetailSource.taxitem_id) {
                bTaxItemExists = true;
                break;
            }
        }

        if (!bTaxItemExists) {
            rTaxDetailDestination = fsTaxDetailDestination.getRecord(fsTaxDetailDestination.newRecord());
            rTaxDetailDestination.taxitem_id = rTaxDetailSource.taxitem_id;
        }

        if (uTaxExemptionId) {
            rTaxDetailDestination.tax_exemption_id = uTaxExemptionId;
        }
       
        rTaxDetailDestination.taxrate_id = rTaxDetailSource.taxrate_id;
        rTaxDetailDestination.invtaxdet_taxrate_percent = rTaxDetailSource.invtaxdet_taxrate_percent;
        rTaxDetailDestination.invtaxdet_taxable_sales_amt += rTaxDetailSource.invtaxdet_taxable_sales_amt;
        rTaxDetailDestination.invtaxdet_tax_amt += rTaxDetailSource.invtaxdet_tax_amt;

        //GL Account for sales tax item
        if (utils.hasRecords(rTaxDetailDestination.sa_invoice_tax_detail_to_sys_sales_tax_item)) {
            var sSalesTaxGLAcct = rTaxDetailDestination.sa_invoice_tax_detail_to_sys_sales_tax_item.glacct_id;

            if (rInvoice.inv_type == scopes.avUtils.INVOICE_TYPE.AdvanceBilling || rInvoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.AdvanceBilling) {
                sSalesTaxGLAcct = globals.avGL_getControlAccount(scopes.avUtils.CONTROL_ACCOUNT_CODE.AdvanceBillingTax, rInvoice.curr_id);
            }

            var oSalesTaxGLAcct = scopes.avAccounting.getGLAccount(sSalesTaxGLAcct, sDiv, sPlant, null, null, null, null)
            rTaxDetailDestination.glacct_id = oSalesTaxGLAcct.glacct_id;
        }

        databaseManager.saveData(rTaxDetailDestination);

    }
}

/**
 * @public 
 * 
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetailSource
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetailDestination
 * @param {Boolean} [bUseSQL]
 * @param {UUID|String} [sLineItemID]
 * @param {UUID|String} [sRevHeaderID]
 *
 * @properties={typeid:24,uuid:"944B1B9E-E7E5-4FB4-8E8B-BF426B4BDE73"}
 */
function avCalcs_salesTax_updateOrderDetailTaxDetails(fsTaxDetailSource, fsTaxDetailDestination, bUseSQL, sLineItemID, sRevHeaderID) {
	if (bUseSQL) {
		avCalcs_salesTax_updateOrderDetailTaxDetails_SQL(fsTaxDetailSource, sLineItemID, sRevHeaderID);
	}
	else {
		avCalcs_salesTax_updateOrderDetailTaxDetails_FS(fsTaxDetailSource, fsTaxDetailDestination);
	}
}

/**
 * @private
 *  
 * Update invoice tax details
 *
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetailSource
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetailDestination
 *
 * @properties={typeid:24,uuid:"94D57B9B-5E55-41DD-AE54-AEAE55D466EA"}
 */
function avCalcs_salesTax_updateOrderDetailTaxDetails_FS(fsTaxDetailSource, fsTaxDetailDestination) {

    for (var i = 1; i <= fsTaxDetailSource.getSize(); i++) {
        var rTaxDetailSource = fsTaxDetailSource.getRecord(i);

        //Check if tax item exists in destination
        var bTaxItemExists = false;

        //Check if Tax Item Exists
        for (var j = 1; j <= fsTaxDetailDestination.getSize(); j++) {
            var rTaxDetailDestination = fsTaxDetailDestination.getRecord(j);

            if (rTaxDetailDestination.taxitem_id == rTaxDetailSource.taxitem_id) {
                bTaxItemExists = true;
                break;
            }
        }

        if (!bTaxItemExists) {
            rTaxDetailDestination = fsTaxDetailDestination.getRecord(fsTaxDetailDestination.newRecord());
            rTaxDetailDestination.taxitem_id = rTaxDetailSource.taxitem_id;
        }

        rTaxDetailDestination.taxrate_id = rTaxDetailSource.taxrate_id;
        rTaxDetailDestination.ordhtaxdet_taxrate_percent = rTaxDetailSource.ordhtaxdet_taxrate_percent;
        rTaxDetailDestination.ordhtaxdet_taxable_sales_amt += rTaxDetailSource.ordhtaxdet_taxable_sales_amt;
        rTaxDetailDestination.ordhtaxdet_tax_amt += globals["avUtilities_roundNumber"](rTaxDetailSource.ordhtaxdet_tax_amt, 2);

        databaseManager.saveData(rTaxDetailDestination);

    }
}

/**
 * @private 
 * 
 * @param {JSFoundSet<db:/avanti/sa_order_tax_detail>} fsTaxDetailSource
 * @param {UUID|String} [sLineItemID]
 * @param {UUID|String} [sRevHeaderID]
 *
 * @properties={typeid:24,uuid:"08FD2AF7-27C8-400E-ADD1-745375D7263E"}
 */
function avCalcs_salesTax_updateOrderDetailTaxDetails_SQL(fsTaxDetailSource, sLineItemID, sRevHeaderID) {
    for (var i = 1; i <= fsTaxDetailSource.getSize(); i++) {
        var rTaxDetailSource = fsTaxDetailSource.getRecord(i);
        var sSQL = "SELECT ordhtaxdet_id FROM sa_order_tax_detail WHERE taxitem_id = ?";
        var aArgs = [rTaxDetailSource.taxitem_id.toString()];
        
		if (sLineItemID) {
			sSQL += " AND ordrevd_id = ?";
			aArgs.push(sLineItemID.toString());
		}
		else if (sRevHeaderID) {
			sSQL += " AND ordrevh_id = ?";
			aArgs.push(sRevHeaderID.toString());
		}
        
		// i was using scopes.avDB.getFSFromSQL() but the foundset returned didnt have the latest data from the db, because the rec was previously updated using sql
        var uTaxDetID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		
        if (uTaxDetID) {
			if (rTaxDetailSource.ordhtaxdet_taxable_sales_amt) {
	        	var sUpdateSQL = "UPDATE sa_order_tax_detail \
								  SET \
									  ordhtaxdet_taxable_sales_amt = ordhtaxdet_taxable_sales_amt + ?, \
									  ordhtaxdet_tax_amt = ordhtaxdet_tax_amt + ? " +
									  scopes.avDB.createBroadcastOutputStatement("sa_order_tax_detail", "ordhtaxdet_id", scopes.avUtils.e_spBroadcastRecordAction.UPDATE_ACTION) + " \
								  WHERE \
									  ordhtaxdet_id = ?";
	        	var aUpdateArgs = [rTaxDetailSource.ordhtaxdet_taxable_sales_amt, globals["avUtilities_roundNumber"](rTaxDetailSource.ordhtaxdet_tax_amt, 2), uTaxDetID.toString()];
	        	
	        	scopes.avDB.RunSQL(sUpdateSQL, null, aUpdateArgs);
			}
        }
        else {
            /**@type {JSFoundSet<db:/avanti/sa_order_tax_detail>} */
            var fsTaxDetailDestination = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_tax_detail');
            
            fsTaxDetailDestination.newRecord();
            
    		if (sLineItemID) {
                fsTaxDetailDestination.ordrevd_id = sLineItemID;
    		}
    		else if (sRevHeaderID) {
                fsTaxDetailDestination.ordrevh_id = sRevHeaderID;
    		}            
            
            fsTaxDetailDestination.taxitem_id = rTaxDetailSource.taxitem_id;
            fsTaxDetailDestination.taxrate_id = rTaxDetailSource.taxrate_id;
            fsTaxDetailDestination.ordhtaxdet_taxrate_percent = rTaxDetailSource.ordhtaxdet_taxrate_percent;
            fsTaxDetailDestination.ordhtaxdet_taxable_sales_amt = rTaxDetailSource.ordhtaxdet_taxable_sales_amt;
            fsTaxDetailDestination.ordhtaxdet_tax_amt = globals["avUtilities_roundNumber"](rTaxDetailSource.ordhtaxdet_tax_amt, 2);

            scopes.avDB.insertRecWithSQL(fsTaxDetailDestination, true, true);
        }
    }
}


/**
 * Get Taxable Shipping State Provinces
 *
 * <AUTHOR> Dol
 * @since Jan 13, 2020
 * @returns {Array}
 * @private
 *
 * @properties={typeid:24,uuid:"9ED7E6A8-21BA-4298-9513-7B2A3813410F"}
 */
function getTaxableShippingStateProv() {
    return scopes.avDB.SQLQueryReturnMultiRecArray("SELECT stateprov_code FROM sys_state_province WHERE org_id = ? AND stateprov_shipping_taxable = 1", [globals.org_id]);
}


/**
 * This gets the customer address from from the invoice ship detail table by packing slip
 * 
 * @param {UUID} uInvoiceId
 * @param {UUID} uPackId
 *
 * @return
 * @properties={typeid:24,uuid:"8794FD00-9FB3-425A-A7BD-BAFE0E40E81D"}
 */
function getInvoiceDetailShipCustomerAddressByPackingSlip(uInvoiceId, uPackId) {
	if (uInvoiceId && uPackId) {
		 var sSQL = "SELECT TOP(1) sa_invoice_det_ship.custaddr_id \
		 			FROM sa_invoice_det_ship \
		 			INNER JOIN sa_ship_detail ON sa_invoice_det_ship.shipd_id = sa_ship_detail.shipd_id \
		 			INNER JOIN sa_pack_detail ON sa_ship_detail.shipd_id = sa_pack_detail.shipd_id \
		 			INNER JOIN sa_pack ON sa_pack_detail.pack_id = sa_pack.pack_id \
		 			INNER JOIN sa_invoice_det ON sa_invoice_det_ship.invd_id = sa_invoice_det.invd_id \
		 			WHERE (sa_invoice_det_ship.org_id = ?) AND (sa_invoice_det.inv_id = ?) AND (sa_pack_detail.pack_id = ?)  \
	             ";

	        var aArgs = [globals.org_id, uInvoiceId.toString(), uPackId.toString()];
	        return scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	else {
		return null;
	}
}

/**
 * This gets the customer address from from the invoice ship detail table by shipment
 * 
 * @param {UUID} uInvoiceId
 * @param {UUID} uShipId
 *
 * @return
 * @properties={typeid:24,uuid:"CC914254-7D69-4F52-9C6E-24C222296B49"}
 */
function getInvoiceDetailShipCustomerAddressByShipment(uInvoiceId, uShipId) {
	if (uInvoiceId && uShipId) {
		 var sSQL = "SELECT TOP (1) sa_invoice_det_ship.custaddr_id \
		 			FROM sa_invoice_det_ship \
		 			INNER JOIN sa_ship_detail ON sa_invoice_det_ship.shipd_id = sa_ship_detail.shipd_id \
		 			INNER JOIN sa_ship ON sa_ship_detail.ship_id = sa_ship.ship_id \
		 			INNER JOIN sa_invoice_det ON sa_invoice_det_ship.invd_id = sa_invoice_det.invd_id \
		 			WHERE (sa_invoice_det_ship.org_id = ?) AND (sa_invoice_det.inv_id = ?) AND (sa_ship.ship_id = ?) \
	             ";

	        var aArgs = [globals.org_id, uInvoiceId.toString(), uShipId.toString()];
	        return scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	else {
		return null;
	}
}

/**
 *  @param {JSRecord<db:/avanti/sa_order_revision_header>} rOrderHeader
 *
 * @properties={typeid:24,uuid:"75606DD0-FB28-4A92-A2FB-ECF651CB1FA7"}
 */
function setOrderTaskTaxInfoCache(rOrderHeader) {
    
    var i = 0,
        j = 0,
        m = 0,
        n = 0,
        rOrderDetail = null,
        rMultiShipQuantity = null,
        rMsQtyTask = null,
        fsTaxDetail = null,
        rTaxDetail = null,
        oTax = {
            taxable_amt : 0.00,
            tax_amt     : 0.00,
            tax_percent : 0.00,
            taxitem_id  : "",
            taxrate_id  : "",
            ordrevdms_id: "",
            ordrevd_id  : "",
            ordrevh_id  : ""
        },
        sKey = "";
    
    oCache_taskTax = {};
    aCache_taskTaxDetails = [];
    
    for (i = 1; i <= rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getSize(); i++) {

        rOrderDetail = rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getRecord(i);

        for (j = 1; j <= rOrderDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getSize(); j++) {

            rMultiShipQuantity = rOrderDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getRecord(j);

            for (m = 1; m <= rMultiShipQuantity.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task.getSize(); m++) {

                rMsQtyTask = rMultiShipQuantity.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task.getRecord(m);
                fsTaxDetail = rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_tax_detail;
                fsTaxDetail.loadAllRecords();
                
                sKey = rMsQtyTask.clc_billing_code 
                        + "_" + rMsQtyTask.ordrevdms_id
                        + "_" + rMsQtyTask.custaddr_id
                        + "_" + rMsQtyTask.ordrevd_id 
                        + "_" + rMsQtyTask.item_id 
                        + "_" + rMsQtyTask.ordrevdmstask_qty;

                oCache_taskTax[sKey] = {
                    totalTax:   rMsQtyTask.ordrevdmstask_total_tax,
                    taxLineNum: rMsQtyTask.ordrevdmstask_tax_line_num,
                    flgAvalara: rMsQtyTask.ordrevdmstask_flg_avalara
                };

                aCache_taskTaxDetails[sKey] = [];

                for (n = 1; n <= fsTaxDetail.getSize(); n++) {

                    rTaxDetail = fsTaxDetail.getRecord(n);

                    oTax = {};
                    oTax.taxable_amt = rTaxDetail.ordhtaxdet_taxable_sales_amt;
                    oTax.tax_amt = rTaxDetail.ordhtaxdet_tax_amt;
                    oTax.tax_percent = rTaxDetail.ordhtaxdet_taxrate_percent;
                    oTax.taxitem_id = (rTaxDetail.taxitem_id) ? rTaxDetail.taxitem_id.toString() : null;
                    oTax.taxrate_id = (rTaxDetail.taxrate_id) ? rTaxDetail.taxrate_id.toString() : null;
                    oTax.ordrevdms_id = (rTaxDetail.ordrevdms_id) ? rTaxDetail.ordrevdms_id.toString() : null;
                    oTax.ordrevd_id = (rTaxDetail.ordrevd_id) ? rTaxDetail.ordrevd_id.toString() : null;
                    oTax.ordrevh_id = (rTaxDetail.ordrevh_id) ? rTaxDetail.ordrevh_id.toString() : null;
                    aCache_taskTaxDetails[sKey].push(oTax);
                }
            }
        }
    } 
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_header>} rOrderHeader
 *
 * @properties={typeid:24,uuid:"30EBCE00-6D35-4781-A128-22344B7FBC74"}
 */
function getOrderTaskTaxInfoCache(rOrderHeader) {
    
    var i = 0,
        j = 0,
        m = 0,
        n = 0,
        rOrderDetail = null,
        rMultiShipQuantity = null,
        rMsQtyTask = null,
        fsTaxDetail = null,
        rTaxDetail = null,
        oTax = {
            taxable_amt : 0.00,
            tax_amt     : 0.00,
            tax_percent : 0.00,
            taxitem_id  : "",
            taxrate_id  : "",
            ordrevdms_id: "",
            ordrevd_id  : "",
            ordrevh_id  : ""
        },
        sKey = "";
    
    for (i = 1; i <= rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getSize(); i++) {

        rOrderDetail = rOrderHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getRecord(i);

        for (j = 1; j <= rOrderDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getSize(); j++) {

            rMultiShipQuantity = rOrderDetail.sa_order_revision_detail_to_sa_order_revd_multi_ship_qty.getRecord(j);

            for (m = 1; m <= rMultiShipQuantity.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task.getSize(); m++) {

                rMsQtyTask = rMultiShipQuantity.sa_order_revd_multi_ship_qty_to_sa_order_revd_multi_ship_qty_task.getRecord(m);

                sKey = rMsQtyTask.clc_billing_code 
                        + "_" + rMsQtyTask.ordrevdms_id
                        + "_" + rMsQtyTask.custaddr_id
                        + "_" + rMsQtyTask.ordrevd_id 
                        + "_" + rMsQtyTask.item_id 
                        + "_" + rMsQtyTask.ordrevdmstask_qty;

                if (oCache_taskTax[sKey]) {

                    rMsQtyTask.ordrevdmstask_total_tax = oCache_taskTax[sKey].totalTax;
                    rMsQtyTask.ordrevdmstask_tax_line_num = oCache_taskTax[sKey].taxLineNum;
                    rMsQtyTask.ordrevdmstask_flg_avalara = oCache_taskTax[sKey].flgAvalara;

                    for (n = 0; n < aCache_taskTaxDetails[sKey].length; n++) {

                        fsTaxDetail = rMsQtyTask.sa_order_revd_multi_ship_qty_task_to_sa_order_tax_detail;
                        rTaxDetail = fsTaxDetail.getRecord(fsTaxDetail.newRecord());

                        oTax = aCache_taskTaxDetails[sKey][n];

                        rTaxDetail.ordhtaxdet_taxable_sales_amt = oTax.taxable_amt;
                        rTaxDetail.ordhtaxdet_tax_amt = oTax.tax_amt;
                        rTaxDetail.ordhtaxdet_taxrate_percent = oTax.tax_percent;
                        rTaxDetail.taxitem_id = oTax.taxitem_id;
                        rTaxDetail.taxrate_id = oTax.taxrate_id;
                        rTaxDetail.ordrevdms_id = oTax.ordrevdms_id;
                        rTaxDetail.ordrevd_id = oTax.ordrevd_id;
                        rTaxDetail.ordrevh_id = oTax.ordrevh_id;
                    }
                    databaseManager.saveData(fsTaxDetail);
                    databaseManager.saveData(rMsQtyTask);
                }
            }
        }
    }
}

/**
 *  @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 *
 * @properties={typeid:24,uuid:"965E1984-FDBD-4EF4-9405-72E16F439DA9"}
 */
function setInvoiceTaskTaxInfoCache(rInvoice) {
    
    var i = 0,
        j = 0,
        n = 0,
        rInvoiceDetail = null,
        rInvoiceDetailTask = null,
        fsTaxDetail = null,
        rTaxDetail = null,
        oTax = {
            taxable_amt : 0.00,
            tax_amt     : 0.00,
            tax_percent : 0.00,
            tax_exemption_id : "",
            taxitem_id  : "",
            taxrate_id  : "",
            invds_id    : "",
            invd_id     : "",
            inv_id      : ""
        },
        sKey = "";
    
    oCache_taskTax = {};
    aCache_taskTaxDetails = [];

    for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {

        rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);

        for (j = 1; j <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task.getSize(); j++) {

            rInvoiceDetailTask = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task.getRecord(j);

            fsTaxDetail = rInvoiceDetailTask.sa_invoice_det_ship_task_to_sa_invoice_tax_detail;
            fsTaxDetail.loadAllRecords();
            
            sKey = rInvoiceDetailTask.clc_billing_code 
                    + "_" + rInvoiceDetailTask.custaddr_id 
                    + "_" + rInvoiceDetailTask.invds_id 
                    + "_" + rInvoiceDetailTask.invd_id
                    + "_" + rInvoiceDetailTask.invdstask_qty.toString()
					+ "_" + rInvoiceDetailTask.invdstask_unit_price.toString();

            oCache_taskTax[sKey] = {
                totalTax:   rInvoiceDetailTask.invdstask_total_tax,
                taxLineNum: rInvoiceDetailTask.invdtask_tax_line_num
            };

            aCache_taskTaxDetails[sKey] = [];

            for (n = 1; n <= fsTaxDetail.getSize(); n++) {

                rTaxDetail = fsTaxDetail.getRecord(n);

                oTax = {};
                oTax.taxable_amt = rTaxDetail.invtaxdet_taxable_sales_amt;
                oTax.tax_amt = rTaxDetail.invtaxdet_tax_amt;
                oTax.tax_percent = rTaxDetail.invtaxdet_taxrate_percent;
                oTax.tax_exemption_id = rTaxDetail.tax_exemption_id;
                oTax.taxitem_id = (rTaxDetail.taxitem_id) ? rTaxDetail.taxitem_id.toString() : null;
                oTax.taxrate_id = (rTaxDetail.taxrate_id) ? rTaxDetail.taxrate_id.toString() : null;
                oTax.invds_id = (rTaxDetail.invds_id) ? rTaxDetail.invds_id.toString() : null;
                oTax.invd_id = (rTaxDetail.invd_id) ? rTaxDetail.invd_id.toString() : null;
                oTax.inv_id = (rTaxDetail.inv_id) ? rTaxDetail.inv_id.toString() : null;
                aCache_taskTaxDetails[sKey].push(oTax);
            }
        }
    } 
}

/**
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 *
 * @properties={typeid:24,uuid:"7A3CE1DD-C7CF-483C-BEA7-71B17F72060A"}
 */
function getInvoiceTaskTaxInfoCache(rInvoice) {

    var i = 0,
        j = 0,
        n = 0,
        rInvoiceDetail = null,
        rInvoiceDetailTask = null,
        fsTaxDetail = null,
        rTaxDetail = null,
        oTax = {
            taxable_amt: 0.00,
            tax_amt: 0.00,
            tax_percent: 0.00,
            tax_exemption_id : "",
            taxitem_id: "",
            taxrate_id: "",
            invds_id: "",
            invd_id: "",
            inv_id: ""
        },
        sKey = "";

    for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {

        rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);

        for (j = 1; j <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task.getSize(); j++) {

            rInvoiceDetailTask = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship_task.getRecord(j);

            fsTaxDetail = rInvoiceDetailTask.sa_invoice_det_ship_task_to_sa_invoice_tax_detail;
            fsTaxDetail.loadAllRecords();

            sKey = rInvoiceDetailTask.clc_billing_code 
                    + "_" + rInvoiceDetailTask.custaddr_id 
                    + "_" + rInvoiceDetailTask.invds_id 
                    + "_" + rInvoiceDetailTask.invd_id
                    + "_" + rInvoiceDetailTask.invdstask_qty.toString()
					+ "_" + rInvoiceDetailTask.invdstask_unit_price.toString();

            if (oCache_taskTax[sKey]) {

                rInvoiceDetailTask.invdstask_total_tax = oCache_taskTax[sKey].totalTax;
                rInvoiceDetailTask.invdtask_tax_line_num = oCache_taskTax[sKey].taxLineNum;

                for (n = 0; n < aCache_taskTaxDetails[sKey].length; n++) {

                    rTaxDetail = fsTaxDetail.getRecord(fsTaxDetail.newRecord());

                    oTax = aCache_taskTaxDetails[sKey][n];

                    rTaxDetail.invtaxdet_taxable_sales_amt = oTax.taxable_amt;
                    rTaxDetail.invtaxdet_tax_amt = oTax.tax_amt;
                    rTaxDetail.invtaxdet_taxrate_percent = oTax.tax_percent;
                    rTaxDetail.tax_exemption_id = oTax.tax_exemption_id;
                    rTaxDetail.taxitem_id = oTax.taxitem_id;
                    rTaxDetail.taxrate_id = oTax.taxrate_id;
                    rTaxDetail.invds_id = oTax.invds_id;
                    rTaxDetail.invd_id = oTax.invd_id;
                    rTaxDetail.inv_id = oTax.inv_id;
                }
                databaseManager.saveData(fsTaxDetail);
                databaseManager.saveData(rInvoiceDetailTask);
            }
        }
    }
}

/**
 * Check to see if AvaTax call should be avoided based on GP integration's excluded states
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 11, 2022
 * @param {Object} oTaxInfo
 * @returns {Object} Modified oTaxInfo
 * @public
 *
 * @properties={typeid:24,uuid:"ACA6B0FB-2E40-42D2-8012-E3B3EB30AE7F"}
 */
function checkStatesInAvaTaxObject(oTaxInfo) {
    
    if (!scopes.avAccounting.bGpSendInvoices
            || $_to_acc_dynamics_gp_integration.dynamics_gp_exclude_states_from_avatax != 1) {
        return oTaxInfo;
    }
    
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = {},
        /***@type {JSDataSet} */
        dsData, 
        aExcludedStates = [],
        aExcludedAddrCodes = [],
        bRunAvaTax = false,
        i = 0;

    oSQL.sql = "SELECT sp.stateprov_code \
                FROM acc_dynamics_gp_avatax_stateprov gp \
                INNER JOIN sys_state_province sp ON sp.stateprov_id = gp.stateprov_id \
                WHERE gp.org_id = ? \
                AND gp.is_excluded = ?";
    oSQL.args = [globals.org_id.toString(), 1];
    dsData = globals["avUtilities_sqlDataset"](oSQL);
    
    if (dsData && dsData.getMaxRowIndex() > 0) {
        
        aExcludedStates = dsData.getColumnAsArray(1);

        for (i = 0; i < oTaxInfo.Addresses.length; i++) {
            
            if (aExcludedStates.indexOf(oTaxInfo.Addresses[i].Region) > -1) {
                aExcludedAddrCodes.push(oTaxInfo.Addresses[i].AddressCode);
            }
        }
        
        for (i = 0; i < oTaxInfo.Lines.length; i++) {
            
            if (aExcludedAddrCodes.indexOf(oTaxInfo.Lines[i].DestinationCode) == -1) {
                
                bRunAvaTax = true;
                break;
            }
        }
        
        if (!bRunAvaTax) {
            oTaxInfo = null;
        }
    }
    
    return oTaxInfo;
}

/**
 * Get Default Tax item Sales Tax G/L Segment
 * 
 * @param {String} sStateProvCode
 * @param {UUID} uCurrId
 * 
 * @return {UUID} uGlAcctSegmentId
 *
 * @properties={typeid:24,uuid:"482DB943-0329-47C7-8BE6-F1D16148AF37"}
 */
function getSalesTaxAccountSegment(sStateProvCode, uCurrId) {
	if (!sStateProvCode || !uCurrId) {
		return null;
	}
	
	/** @type {JSRecord<db:/avanti/sys_state_province>} */
	var rStateProv = scopes.avDB.getRec("sys_state_province",["stateprov_code"],[sStateProvCode]);
	
	if (rStateProv && rStateProv.stateprov_salestax_gl_acctseg_id) {
		return rStateProv.stateprov_salestax_gl_acctseg_id
	}
	
	var uControlAccountSegment = globals.avGL_getControlAccount('SALES TAX', uCurrId);
	
	if (uControlAccountSegment) {
		return uControlAccountSegment;
	}
}

/**
 * Remove Tax Item from Tax Cache
 * 
 * @param {UUID} uTaxItemId
 *
 * @properties={typeid:24,uuid:"9F6A1F78-683D-45AE-A06B-227A6AB5830A"}
 */
function removeItemFromTaxCache(uTaxItemId) {
    if (uTaxItemId) {
        for (var keyItem in scopes.avTax.oTaxRates.taxrate_id) {
            if (keyItem.startsWith("'" + uTaxItemId)) {
                delete scopes.avTax.oTaxRates.taxrate_id[keyItem];
            }
        }

        for (var keyRate in scopes.avTax.oTaxRates.taxrate_percent) {
            if (keyRate.startsWith("'" +uTaxItemId)) {
                delete scopes.avTax.oTaxRates.taxrate_percent[keyRate];
            }
        }
    }
}
