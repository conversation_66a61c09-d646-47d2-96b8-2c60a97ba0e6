/**
 * @properties={type:8,typeid:36,uuid:"5F1E75F4-013E-49BE-813A-FF3A0AAF9F01"}
 */
function itemwhse_committed_qty() {
	var nCommitted = scopes.avDB.SQLQuery(scopes.avInv.getItemCommittedSQL(item_id, whse_id));
	
	if (nCommitted == null) {
		nCommitted = 0;
	}
	
	return nCommitted;
}

/**
 * @properties={type:8,typeid:36,uuid:"26D1F63D-8ED6-4F9D-A601-D922EC10FB5B"}
 */
function itemwhse_unavailable_qty() {
	var nUnavailable = 0;
	
	if (utils.hasRecords(in_item_warehouse_to_in_warehouse)) {
		var rWare = in_item_warehouse_to_in_warehouse.getRecord(1);
		
		if (rWare.whse_unavailable) {
			nUnavailable = itemwhse_onhand_qty;
		}
		else if (rWare.whse_enable_bin_locations) {
			var sSQL = "SELECT SUM(ib.itemwhseloc_onhand_qty) \
						FROM in_item_warehouse_location ib \
						INNER JOIN in_warehouse_location b ON ib.whseloc_id = b.whseloc_id \
						WHERE \
							ib.org_id = ? \
							AND ib.itemwhse_id = ? \
							AND b.whseloc_unavailable = 1";
			var aArgs = [globals.org_id, itemwhse_id.toString()];
			
			nUnavailable = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		}
	}
	
	if (nUnavailable == null) {
		nUnavailable = 0;
	}
	
	return nUnavailable;
}

/**
 * @properties={type:8,typeid:36,uuid:"F4B62E16-B28C-4909-928B-547BF0F5A041"}
 */
function itemwhse_unusable_qty() {
	var nUnusable = 0;
	
	if (utils.hasRecords(in_item_warehouse_to_in_warehouse)) {
		var rWare = in_item_warehouse_to_in_warehouse.getRecord(1);
		
		if (rWare.whse_unusable) {
			nUnusable = itemwhse_onhand_qty;
		}
		else if (rWare.whse_enable_bin_locations) {
			var sSQL = "SELECT SUM(ib.itemwhseloc_onhand_qty) \
						FROM in_item_warehouse_location ib \
						INNER JOIN in_warehouse_location b ON ib.whseloc_id = b.whseloc_id \
						WHERE \
							ib.org_id = ? \
							AND ib.itemwhse_id = ? \
							AND b.whseloc_unusable = 1";
			var aArgs = [globals.org_id, itemwhse_id.toString()];
			
			nUnusable = scopes.avDB.SQLQuery(sSQL, null, aArgs);
		}
	}
	
	if (nUnusable == null) {
		nUnusable = 0;
	}
	
	return nUnusable;
}

/**
 * @properties={type:8,typeid:36,uuid:"33F2F38F-1A71-4772-9F34-E89CDEA96765"}
 */
function itemwhse_backord_qty() {
	var nBOTot = scopes.avDB.SQLQuery(scopes.avInv.getItemBOSQL(item_id, whse_id));
	
	if (nBOTot == null) {
		nBOTot = 0;
	}
	
	return nBOTot;
}

/**
 * @properties={type:8,typeid:36,uuid:"2277E4BF-39AE-42A6-B436-4066CA76F640"}
 */
function itemwhse_onpo_qty() {
	var nPOTot = scopes.avDB.SQLQuery(scopes.avInv.getItemOnPOSQL(item_id, whse_id));
	
	if (nPOTot == null) {
		nPOTot = 0;
	}
	
	return nPOTot;
}

/**
 * @properties={type:8,typeid:36,uuid:"0436E835-E9C0-48EC-90DA-FEB62C704E83"}
 */
function itemwhse_reserved_qty() {
	var nResTot = scopes.avDB.SQLQuery(scopes.avInv.getItemReservedSQL(item_id, whse_id));
	
	if (nResTot == null) {
		nResTot = 0;
	}
	
	return nResTot;
}

/**
 * @properties={type:8,typeid:36,uuid:"11A67661-7975-46A7-BD02-32533DA69B06"}
 */
function clc_item_onhand_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_onhand_qty
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_onhand_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"F4D54190-D862-4EE3-AACB-C0A2CF5575E9"}
 */
function clc_item_committed_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_committed_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_committed_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"15ABE3E1-B30D-4FA4-916E-4C7B0983B83F"}
 */
function clc_item_unavailable_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;
        
        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_unavailable_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_unavailable_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"0B2635CF-67EE-44ED-91F2-087DD6A1B60E"}
 */
function clc_item_unusable_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;
        
        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_unusable_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_unusable_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"2BE11A54-F0E4-4781-9138-A6ACEFC84088"}
 */
function clc_item_available_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;
        
        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_qtyAvailable;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
     }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_available_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"6714CB21-E77A-46C9-B2DB-D6D44444C741"}
 */
function clc_item_reserved_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;
        
        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_reserved_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_reserved_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"8D5C5800-A068-4CD2-AD66-129687824D94"}
 */
function clc_item_backord_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_backord_qty
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_backord_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"94509FA3-7943-4612-A1BF-CAD80B5B390D"}
 */
function clc_item_onpo_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_onpo_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_onpo_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"CA7A3CBB-B85E-4C63-9614-7CF89404D381"}
 */
function clc_item_onporeq_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_onporeq_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_onporeq_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"555ADA0E-F67F-4A44-BF30-215853EB7D58"}
 */
function clc_item_intransit_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_intransit_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_intransit_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={typeid:36,uuid:"8CFA18BB-1FD0-4A4D-BAE2-76728DFACB4F"}
 */
function clc_item_inproduction_qty()
{
    if (globals.avBase_plantID != 'ALL' ) {
        var nQty = 0,
            i = 0,
            rRec = null,
            /***@type{JSFoundSet<db:/avanti/in_item_warehouse>} */
            fs = foundset;

        for (i = 1; i <= fs.getSize(); i++) {
            rRec = fs.getRecord(i);
            nQty += rRec.itemwhse_inproduction_qty;
        }
        return utils.numberFormat(nQty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
    else {
        return utils.numberFormat(in_item_warehouse_to_in_item.item_inproduction_qty, globals.avUtilities_ItemQtyFormat(in_item_warehouse_to_in_item.item_decimal_places));
    }
}

/**
 * @properties={type:6,typeid:36,uuid:"14BCF39C-04AC-4D0F-A467-42E173620C27"}
 */
function itemwhse_qtyAvailable() {
	if (in_item_warehouse_to_in_item.item_is_virtual) {
		return 0;
	}
	else {
		return itemwhse_onhand_qty - itemwhse_committed_qty - itemwhse_unavailable_qty;
	}
}
