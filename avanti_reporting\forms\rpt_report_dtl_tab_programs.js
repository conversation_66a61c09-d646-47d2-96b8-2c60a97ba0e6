/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"EECBB452-C066-48BF-BFC4-9D78BD00BB2F",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"EB5CC4A6-3AA9-45D5-A114-B7A4FDBF5396"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"A6E45FE3-076A-463F-A13B-BE4DD29672C7"}
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
 return _super.onShowForm(firstShow, event);
}
/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"7226B0D2-ACBB-4205-A228-A6963E11AB1E"}
 */
function onRecordSelection(event)
{
	globals.avRpt_selectedProgram = program_name;
//	globals.avUtilities_shuffleGrey("rpt_report_dtl_tab_menus");
	
	// Check for menus, otherwise clear the subMenus tab
	if (forms.rpt_report_dtl_tab_menus.foundset.getSize() == 0)
	{
		globals.avRpt_selectedPopMenuID = 1;
		
	}
}

/**
 * @AllowToRunInFind
 * 
 * TODO generated, please specify type and doc for the params
 * @param foundsetindex
 * @param columnindex
 * @param record
 * @param event
 *
 * @properties={typeid:24,uuid:"2FC5C9DD-5B00-483F-A747-9F0D68D593E3"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnSelect" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onRecordSelection(event);
	}
}
