/**
 * Loads the estimateQuantity_fallback vl
 *
 * <AUTHOR>
 * @since 2012-09-14
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_header>} rRec - The order revision header record to build the qtys vl for
 *
 *
 * @properties={typeid:24,uuid:"73516B5F-0CC2-40A0-B13C-2005375CC701"}
 */
function loadEstimateQuantityFallback_vl(rRec) {
	var aReturn = new Array();
	var aDisplay = new Array();

	var rDetail;
	var rQty;
	var i, k;
	for (i = 1; i <= rRec.sa_order_revision_header_to_sa_order_revision_detail.getSize(); i++) {
		rDetail = rRec.sa_order_revision_header_to_sa_order_revision_detail.getRecord(i);

		for (k = 1; k <= rDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty.getSize(); k++) {
			rQty = rDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty.getRecord(k);

			//Manually format the display value to include thousand separators. This is only needed in Titanium until Servoy releases the fix for combo boxes with fallback value lists in a grid.
			var sDisplayVal = rQty.ordrevdqty_qty ? rQty.ordrevdqty_qty.toString().split(".").map(function(s, j) {
					return j ? s : s.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
				}).join(".") : null;
			aDisplay.push(sDisplayVal);
			aReturn.push(rQty.ordrevdqty_id);
		}
	}
	application.setValueListItems("vl_EstimateQuantity_fallback", aDisplay, aReturn);
}
