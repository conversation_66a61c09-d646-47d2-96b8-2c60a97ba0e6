/**
 * @type {JSEvent}
 * @properties={typeid:35,uuid:"846362CD-20F2-4DB2-A288-5F424942F40D",variableType:-4}
 */
var SOURCE_EVENT = null;

/**
 * @type {scopes.svyToolbarFilter.NgGridListComponentFilterRenderer}
 *
 * @properties={typeid:35,uuid:"CE29FDF1-AB2F-41A2-9F07-014A2FBC0794",variableType:-4}
 */
var toolbarFilter;

/**
 * @properties={typeid:35,uuid:"3B007E9D-C783-40C3-BC79-A56437C0B4D6",variableType:-4}
 */
var sActiveStatusColumn = null;

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"49022F8D-0884-4C4D-8F3D-649AB693B73B",variableType:-4}
 */
var aExcludeActiveCols = ['global_shipto_addr_active', 'globaladdr_active'];

/**
 * @param {Boolean} firstShow
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"65FB7CD6-8BED-4AED-8A42-E3732D9C146D"}
 */
function onShowForm(firstShow, event) {

	//Reset the toolbarFilter for CRM modules so toolbarFilter won't contain data when switching tabs from other modules.
	if (firstShow || (globals.nav_program_name && globals.nav_program_name.toLowerCase().substring(0, 4).indexOf('crm_') > -1)) {
		toolbarFilter = null;
		sActiveStatusColumn = null;
		_qs_searchActive = 'Active';
	}

	// Prevents running the setup logic multiple times
	if (!toolbarFilter) {
		elements.qs_searchActive.enabled = false;
		elements.qs_searchActive.visible = false;
		var parentFormName = scopes.svyUI.getParentFormName(forms[controller.getName()]);
		/** @type {RuntimeForm<av_base_form>} */
		var currentForm = parentFormName ? forms[parentFormName] : null;
		var grid = currentForm ? currentForm.getMainGridComponent() : null;
		var hasGrid = !!grid;
		
		if (hasGrid) {
			try {
				// Check if svyToolbarFilter scope is fully initialized
				if (scopes.svyToolbarFilter && scopes.svyToolbarFilter.createFilterToolbar) {
					toolbarFilter = scopes.svyToolbarFilter.createFilterToolbar(elements.qs_filtersList, grid);
					if (toolbarFilter) {
						var jsTable = databaseManager.getTable(toolbarFilter.getFoundSet());
						if (jsTable) {
							var jsColumns = jsTable.getColumnNames();
							if (jsColumns) {
								for (var i = 0; i < jsColumns.length; i++) {
									if (aExcludeActiveCols.indexOf(jsColumns[i]) == -1 &&
											jsColumns[i].indexOf("_active", jsColumns[i].length - 7) !== -1 &&
											databaseManager.getTable(toolbarFilter.getFoundSet()).getColumn(jsColumns[i]).getType() === JSColumn.INTEGER) {
										sActiveStatusColumn = jsColumns[i];
										elements.qs_searchActive.enabled = true;
										elements.qs_searchActive.visible = true;
										applyFilters(true, sActiveStatusColumn, _qs_searchActive);
										break;
									}
								}
							}
						}
					}
				} else {
					// svyToolbarFilter scope not ready yet, retry after a short delay
					application.output('svyToolbarFilter scope not fully initialized yet, retrying in 100ms...', LOGGINGLEVEL.WARNING);
					application.executeLater(function() {
						// Retry the initialization
						if (scopes.svyToolbarFilter && scopes.svyToolbarFilter.createFilterToolbar && !toolbarFilter) {
							try {
								toolbarFilter = scopes.svyToolbarFilter.createFilterToolbar(elements.qs_filtersList, grid);
								if (toolbarFilter) {
									var jsTable = databaseManager.getTable(toolbarFilter.getFoundSet());
									if (jsTable) {
										var jsColumns = jsTable.getColumnNames();
										if (jsColumns) {
											for (var i = 0; i < jsColumns.length; i++) {
												if (aExcludeActiveCols.indexOf(jsColumns[i]) == -1 &&
														jsColumns[i].indexOf("_active", jsColumns[i].length - 7) !== -1 &&
														databaseManager.getTable(toolbarFilter.getFoundSet()).getColumn(jsColumns[i]).getType() === JSColumn.INTEGER) {
													sActiveStatusColumn = jsColumns[i];
													elements.qs_searchActive.enabled = true;
													elements.qs_searchActive.visible = true;
													applyFilters(true, sActiveStatusColumn, _qs_searchActive);
													break;
												}
											}
										}
									}
									// Update layout position after successful initialization
									updateQsFilterListLayoutPosition();
								}
							} catch (retryError) {
								application.output('Failed to initialize svyToolbarFilter on retry: ' + retryError.message, LOGGINGLEVEL.ERROR);
							}
						}
					}, 100);
					return; // Exit early to prevent further execution
				}
			} catch (e) {
				application.output('Error initializing svyToolbarFilter: ' + e.message + '. Stack: ' + (e.stack || 'No stack trace available'), LOGGINGLEVEL.ERROR);
				// Continue execution without toolbar filter
			}
		}
	
		elements.qs_quickSearch.enabled = hasGrid;
		elements.qs_quickSearch.enabled = hasGrid;
		elements.qs_btnSearch.enabled = hasGrid;
		elements.qs_btnShowFilters.enabled = hasGrid;
		elements.qs_filtersList.enabled = hasGrid;
		
		//Position qs_filtersList based on qs_searchActive's visibility
		updateQsFilterListLayoutPosition();
	}
	else if (sActiveStatusColumn) {
		applyFilters(true, sActiveStatusColumn, _qs_searchActive);
	}

	
	// Make sure to enable the form every time it's displayed
	application.executeLater(setReadonly, 500, [false]);

}

/**
 * @properties={typeid:24,uuid:"2D926A16-19E5-4214-B53B-676B8BA967FC"}
 */
function updateQsFilterListLayoutPosition() {
    var leftPos = 360;
    if (elements.qs_searchActive.visible) {
        elements.qs_searchActive.cssPosition.left = leftPos;
        leftPos += Number(elements.qs_searchActive.cssPosition.width) + 2;  // Add spacing
    }
    elements.qs_filtersList.cssPosition.left = leftPos;
}

/**
 * Click event. dataTarget parameter is used to identify inner html elements (by their data-target attribute).
 *
 * @param {JSEvent} event
 * @param {String} dataTarget
 *
 * @properties={typeid:24,uuid:"E2390E48-37B9-40D5-91C4-F9F785982850"}
 */
function onActionShowFilters(event, dataTarget) {
	if (toolbarFilter) {
		toolbarFilter.showPopupFilterPicker(event.getSource());
	}
}

/**
 * Click event. dataTarget parameter is used to identify inner html elements (by their data-target attribute).
 *
 * @param {JSEvent} event

 *
 * @properties={typeid:24,uuid:"5D93D55D-EB28-4EA7-9F7A-B89A1857B88E"}
 */
function onActionClearFilters(event, dataTarget) {

	if (_qs_quickSearch) {
		_qs_quickSearch = null;
		search();
// 		elements.qs_quickSearch.requestFocus();
	}
}

/**
 * Called when the mouse is clicked on a list entry.
 *
 * @param entry
 * @param {Number} index
 * @param {String} dataTarget
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"57024F02-B924-40E8-9CEE-A23E5F190067"}
 */
function onClickFiltersList(entry, index, dataTarget, event) {
	if (toolbarFilter) {
		toolbarFilter.onClick(entry, index, dataTarget, event);
	}
}

/**
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean} TRUE on success, FALSE on failure
 * @override
 *
 * @properties={typeid:24,uuid:"340F5C1B-F960-4073-895C-CDB31A67E078"}
 * @AllowToRunInFind
 */
function quickSearch_onDataChange(oldValue, newValue, event) {
	search();
	return true;
}

/**
 * @properties={typeid:24,uuid:"729E776F-5D49-4A99-B823-13648DFECE97"}
 * @AllowToRunInFind
 */
function search() {
	if (toolbarFilter) {
		toolbarFilter.search(_qs_quickSearch);
	}
}

/**
 * @properties={typeid:24,uuid:"805A10E3-BA53-4D6F-8984-0C3AF82256B0"}
 */
function clearAllFilters() {
	_qs_quickSearch = '';
	if (toolbarFilter) {
		toolbarFilter.clearFilterUI();
		search();
	}
}

/**
 * @param {Boolean} readonly
 *
 * @properties={typeid:24,uuid:"9DDD8159-1E74-47BF-9E69-A8E7FD8A0C7D"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Click event. dataTarget parameter is used to identify inner html elements (by their data-target attribute).
 *
 * @param {JSEvent} event
 * @param {String} dataTarget
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0ECDB3C4-F3BF-43B0-A1F9-43CCE42D1DBB"}
 */
function onAction_getGridMenu(event, dataTarget) {

	SOURCE_EVENT = event;
	var sFormName = scopes.svyUI.getParentFormName(forms[controller.getName()]);
	scopes.avGrid.CURRENT_GRID_FORM = sFormName;
	scopes.avGrid.getGridMenu(sFormName);
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"8F31EB4C-3576-476A-85AB-4967964093E3"}
 */
function onDataChange_qs_searchActive(oldValue, newValue, event) {
	applyFilters(true, sActiveStatusColumn, newValue);
	return true
}

/**
 * Apply filter to the foundset
 * @param bReApplyFilters
 * @param sActiveStatusColumnName
 * @param sStatusFilter
 *
 * @properties={typeid:24,uuid:"0A71D038-BD47-4C02-984F-45EB27AED8F2"}
 * @AllowToRunInFind
 */
function applyFilters(bReApplyFilters, sActiveStatusColumnName, sStatusFilter) {
	if (bReApplyFilters && toolbarFilter) {
		var sStatusFilterValue = null;
		var frmFoundset = toolbarFilter.getFoundSet();
		
		if (!frmFoundset) return;
		
		if (sStatusFilter == 'Active') {
			sStatusFilterValue = 1;
		} 
		else if (sStatusFilter == 'Inactive') {
			sStatusFilterValue = 0;
		}
		
		frmFoundset.removeFoundSetFilterParam('QSActiveStatusFilter');
		if (sStatusFilterValue != null) {
			frmFoundset.addFoundSetFilterParam(sActiveStatusColumnName, '=', sStatusFilterValue, 'QSActiveStatusFilter');
		}
		
		frmFoundset.loadAllRecords();
		var parentFormName = scopes.svyUI.getParentFormName(forms[controller.getName()]);
		/** @type {RuntimeForm<av_base_form>} */
		var currentForm = parentFormName ? forms[parentFormName] : null;
		var grid = currentForm ? currentForm.getMainGridComponent() : null;
		if (grid) {
			grid.myFoundset.foundset.loadRecords(frmFoundset);
		}
	}
}
