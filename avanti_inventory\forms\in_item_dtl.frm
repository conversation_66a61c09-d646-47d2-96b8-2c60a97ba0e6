customProperties:"useCssPosition:true",
dataSource:"db:/avanti/in_item",
encapsulation:0,
extendsID:"38B73C1D-FA33-4396-9C83-C9715186CACC",
items:[
{
cssPosition:"59,-1,-1,10,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"59",
width:"130"
},
enabled:true,
labelFor:"item_desc1",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_desc1",
visible:true
},
name:"lbl_item_desc1",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"00541128-F783-4BFA-BEED-6920E7DD3CF8"
},
{
cssPosition:"140,-1,-1,10,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"140",
width:"130"
},
enabled:true,
labelFor:"item_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_type",
visible:true
},
name:"lbl_item_type",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1A0FFF96-605D-4D9D-A03D-E79ADC6A229A"
},
{
cssPosition:"31,-1,-1,640,98,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"640",
right:"-1",
top:"31",
width:"98"
},
dataProviderID:"item_status",
enabled:true,
onDataChangeMethodID:"8E50072D-356D-4B5B-ABF6-596BB7A1B686",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:-2,
valuelistID:"7919F1BF-7869-431A-8C7E-0E191E9DDE10",
visible:true
},
name:"item_status",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"38847913-698E-453C-85FE-D646F3EE2A23"
},
{
cssPosition:"32,-1,-1,586,49,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"586",
right:"-1",
top:"32",
width:"49"
},
enabled:true,
labelFor:"item_status",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_status",
visible:true
},
name:"lbl_item_status",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3932F8E5-5AD9-406B-A543-3391E56265C3"
},
{
cssPosition:"60,-1,-1,855,24,24",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"\\\"sys_image_id\\\"\",\
\"\\\"Images\\\"\"\
],\
parameters:[]\
}\
}",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"855",
right:"-1",
top:"60",
width:"24"
},
enabled:true,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:-2,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"component_B4892517",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"41357F2F-54E0-4281-8752-B2CD539F31E7"
},
{
cssPosition:"140,-1,-1,145,260,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"140",
width:"260"
},
dataProviderID:"itemtype_code",
enabled:true,
onDataChangeMethodID:"E57CB97D-7D6C-4574-9A7A-A5F5C20B669C",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:6,
valuelistID:"79055AE2-2122-463C-8991-FEBD868FCA0A",
visible:true
},
name:"item_type",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"414E5786-4A77-4871-93D9-01EADE620D43"
},
{
cssPosition:"59,-1,-1,145,595,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"59",
width:"595"
},
dataProviderID:"item_desc1",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:2,
visible:true
},
name:"item_desc1",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"439099AF-5E22-42D1-8775-A034BD41D0B2"
},
{
cssPosition:"113,-1,-1,10,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"113",
width:"130"
},
enabled:true,
labelFor:"itemclass_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.itemclass_id",
visible:true
},
name:"lbl_itemclass_id",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"445A1B89-74DA-4450-9A2F-4835B99BC3C1"
},
{
height:580,
partType:5,
typeid:19,
uuid:"5D4A4474-229D-4321-A522-BCF80652C1B1"
},
{
cssPosition:"140,-1,-1,870,124,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"870",
right:"-1",
top:"140",
width:"124"
},
dataProviderID:"item_expiry_date",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:7,
visible:true
},
name:"item_expiry_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"5D73527F-FBB7-4C22-AB48-2540385A1B96"
},
{
cssPosition:"113,-1,-1,411,24,24",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'itemclass_id'\",\
\"'Item Classes'\"\
],\
parameters:[]\
}\
}",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"411",
right:"-1",
top:"113",
width:"24"
},
enabled:true,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:-2,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnClassLookup",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"60D8E713-A2FB-4340-9E92-1D9A140BF56E"
},
{
cssPosition:"86,-1,-1,10,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"86",
width:"130"
},
enabled:true,
labelFor:"item_desc2",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_desc2",
visible:true
},
name:"lbl_item_desc2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"61937FC5-6C96-4D01-97BA-27F5FF489400"
},
{
cssPosition:"140,-1,-1,445,113,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"445",
right:"-1",
top:"140",
width:"113"
},
enabled:true,
labelFor:"item_creation_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_creation_date",
toolTipText:"i18n:avanti.tooltip.item_creation_date",
visible:true
},
name:"lbl_item_creation_date",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6351B816-DDE0-495C-80EB-467623643C47"
},
{
cssPosition:"112,-1,-1,855,25,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"855",
right:"-1",
top:"112",
width:"25"
},
enabled:true,
onActionMethodID:"3F7F80FA-B0D5-4D1A-B93A-CD3471D6F304",
styleClass:"listview_noborder label_bts",
tabSeq:-2,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnDeleteHeader",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"63520495-DC9E-4BF7-9877-71A1EB3F8F19"
},
{
cssPosition:"86,-1,-1,145,595,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"86",
width:"595"
},
dataProviderID:"item_desc2",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:3,
visible:true
},
name:"item_desc2",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6E96A53B-B06C-4542-BC84-F2E48B61EDE5"
},
{
cssPosition:"32,-1,-1,145,218,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"32",
width:"218"
},
dataProviderID:"item_code_no_revision",
editable:true,
enabled:true,
onDataChangeMethodID:"409A057C-1C8A-495A-B579-5F84314EFF8B",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:1,
visible:true
},
name:"item_code_no_revision",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"85A26FCB-3DB3-4D90-B4E4-B0863FD2F5A0"
},
{
cssPosition:"113,-1,-1,145,260,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"113",
width:"260"
},
dataProviderID:"itemclass_id",
enabled:true,
onDataChangeMethodID:"992E4AD8-997B-4C9D-A290-7EF8C5839BC2",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:4,
valuelistID:"D5B2BA61-B992-4A13-8BEA-8DFDEEA08821",
visible:true
},
name:"itemclass_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8B9EF779-EC00-4609-94CD-9F68CC913318"
},
{
cssPosition:"32,-1,-1,145,248,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"32",
width:"248"
},
dataProviderID:"item_code_no_revision",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:1,
visible:true
},
name:"item_code",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8D5AA7D3-74E3-4865-9945-E05BA7908253"
},
{
cssPosition:"85,-1,-1,855,25,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"855",
right:"-1",
top:"85",
width:"25"
},
enabled:true,
onActionMethodID:"98C308D7-124C-4723-BA0F-7934D400C9D0",
styleClass:"listview_noborder label_bts",
tabSeq:-2,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAddHeader",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"90E18CCD-59D1-41D8-86BB-BE8AD559390F"
},
{
cssPosition:"32,-1,-1,750,62,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"750",
right:"-1",
top:"32",
width:"62"
},
enabled:true,
labelFor:"item_status",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.images",
visible:true
},
name:"component_DF048DCA",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"95F64BBA-457C-4A4D-B6BA-651DEA3B8F29"
},
{
cssPosition:"113,-1,-1,570,170,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"570",
right:"-1",
top:"113",
width:"170"
},
dataProviderID:"sys_org_language_uuid",
enabled:true,
onDataChangeMethodID:null,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:4,
valuelistID:"BB960F0A-5C9D-4388-9282-85A7D9095AAC",
visible:true
},
name:"sys_org_language_uuid",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"96FA7C6F-9963-4B0C-8F79-FC3D5C5C8C53"
},
{
cssPosition:"59,-1,-1,750,100,78",
json:{
cssPosition:{
bottom:"-1",
height:"78",
left:"750",
right:"-1",
top:"59",
width:"100"
},
dataProviderID:"in_item_to_sys_images.sys_image",
editable:false,
enabled:true,
styleClass:"imagemedia_bts",
tabSeq:-2,
visible:true
},
name:"in_image",
styleClass:"imagemedia_bts",
typeName:"bootstrapcomponents-imagemedia",
typeid:47,
uuid:"A258C031-D184-48FD-B997-180DBD5CD96E"
},
{
cssPosition:"32,-1,-1,890,110,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"890",
right:"-1",
top:"32",
width:"110"
},
enabled:true,
onActionMethodID:"0041290B-216C-4A7C-B0CB-79A83822CD06",
styleClass:"btn btn-default button_bts",
tabSeq:-2,
text:"i18n:avanti.lbl.specifications",
visible:true
},
name:"btnSpecifications",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"A84785D2-1A53-451C-8731-28EA044CB612"
},
{
cssPosition:"140,-1,-1,570,123,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"570",
right:"-1",
top:"140",
width:"123"
},
dataProviderID:"item_creation_date",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:5,
visible:true
},
name:"item_creation_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"AE1B3A8A-288C-4B89-A0FA-C9F7BE6A58F8"
},
{
cssPosition:"172,1,0,0,1000,408",
json:{
cssPosition:{
bottom:"0",
height:"408",
left:"0",
right:"1",
top:"172",
width:"1000"
},
onChangeMethodID:"F050A0F0-2FE7-41B4-BEDD-7942DADC68AE",
styleClass:"scrollable-tabpanel",
tabs:[
{
containedForm:"98B6E6F1-2074-4398-AECE-7F70C13E3ECA",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"_to_in_item$foundset",
svyUUID:"BA7B745A-6A16-4F29-AD7B-0A875B680826",
text:"i18n:avanti.lbl.itemDetails"
},
{
containedForm:"18D9DDEA-C252-471A-B8A6-1AFC3333D74F",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"in_item_to_in_item_selling_uom",
svyUUID:"A6641F80-F6C8-4F1E-BC0F-7342DD383F02",
text:"i18n:avanti.lbl.itemselluom_sell_uom_id"
},
{
containedForm:"2E719255-278C-4961-A7D4-6A0F444BD62F",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"in_item_to_in_item_supplier",
svyUUID:"C2825AD0-9571-4DA1-9E15-B8643FF0CE62",
text:"i18n:avanti.lbl.itemSupplier"
},
{
containedForm:"4B025A90-F5B6-4374-9863-1F85C76D2576",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"in_item_to_in_item_warehouse",
svyUUID:"DFE29E24-A84E-44AC-A0FE-201574AC0EFB",
text:"i18n:avanti.tab.item_warehouses"
},
{
containedForm:"CA7E4EDD-729F-44F1-B289-EB82CCC578DA",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"_to_in_item$foundset.in_item_to_in_item_bill_of_material",
svyUUID:"0B10148E-1AAC-4D34-B0EB-41F2791609CC",
text:"i18n:avanti.tab.ItemBillOfMaterials"
},
{
containedForm:"1473FDF3-33E1-492A-8045-4446503A89CB",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:null,
svyUUID:"B8071C11-49B8-4743-800F-D08688FC5D8B",
text:"i18n:avanti.lbl.UDF"
}
]
},
name:"tab1",
styleClass:"scrollable-tabpanel",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"B542462C-2165-42B8-B783-F4B36D302F18"
},
{
cssPosition:"5,1,-1,0,1000,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"1",
top:"5",
width:"1000"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.itemDetailView",
visible:true
},
name:"lbl_group_item",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B64912CE-D92B-4D02-88A8-58F82EC435E4"
},
{
cssPosition:"32,-1,-1,378,69,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"378",
right:"-1",
top:"32",
width:"69"
},
enabled:true,
labelFor:"item_revision",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.revision",
visible:true
},
name:"lbl_item_revision",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CF7EF805-82A2-46CF-BF6D-3AF926C54D33"
},
{
cssPosition:"32,-1,-1,10,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"32",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_code",
visible:true
},
name:"lbl_item_code",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D7A13A26-896B-4946-A099-01E5C79F7701"
},
{
cssPosition:"32,-1,-1,451,107,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"451",
right:"-1",
top:"32",
width:"107"
},
dataProviderID:"item_revision",
editable:true,
enabled:true,
onDataChangeMethodID:"E0EC9D29-BF7A-4942-AE37-095C79A7B4E6",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:1,
visible:true
},
name:"item_revision",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D7B17519-0A39-4411-8DAA-D8028EFE4385"
},
{
cssPosition:"113,-1,-1,445,114,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"445",
right:"-1",
top:"113",
width:"114"
},
enabled:true,
labelFor:"sys_org_language_uuid",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.language",
visible:true
},
name:"lbl_sys_org_language_uuid",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E951A38E-60D4-4871-8782-00E93C63F462"
},
{
cssPosition:"140,-1,-1,745,114,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"745",
right:"-1",
top:"140",
width:"114"
},
enabled:true,
labelFor:"item_expiry_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_expiry_date",
visible:true
},
name:"lbl_item_expiry_date",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ED1909FD-1D81-4BB8-8F7E-C1B11C8BA197"
}
],
name:"in_item_dtl",
onRecordEditStartMethodID:"11DA4019-5E0B-45BF-BD2B-AB5159DF6A1E",
paperPrintScale:100,
scrollbars:33,
size:"1001,580",
typeid:3,
uuid:"17EB14F0-4A24-40CD-8136-D541ADC989B9"