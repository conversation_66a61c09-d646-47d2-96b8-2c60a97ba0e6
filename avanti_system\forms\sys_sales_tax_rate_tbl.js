/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3EA95947-99FB-45DB-BE0D-C24F0AAF033E",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"2BBB9A6B-9E82-424E-AC7D-4661BA81F32E"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"F6438E8B-2493-4396-A420-F8461FDB55DA"}
 */
function onShowForm(_firstShow, _event) {

	 if (_firstShow) {
	 	if (!_gridReady) {
	 		application.executeLater(onShowForm, 500, [true, _event]);
	 		return null;
	 	}
	 }

	 _super.onShowForm(_firstShow, _event)
	 
	 elements.grid.getColumn(elements.grid.getColumnIndex("taxrate_effective_date")).format = globals.avBase_dateFormat;
	 elements.grid.getColumn(elements.grid.getColumnIndex("taxrate_percent")).format = "0.00000%";
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6294CCD8-1D05-4122-B3B9-0225CBFAAAD0"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnDelete") {
		onAction_btnDelete(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"AF7C683F-67C0-459C-A6F1-CC0F7740D13E"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "taxrate_percent") {
		onDataChange_taxRate(oldValue, newValue, event);
	}
	if (col.id == "taxrate_effective_date") {
		onDataChange_effectiveDate(oldValue, newValue, event);
	}
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8B6FEF7A-240C-427C-88C2-CC60BD71A9CA"}
 */
function onAction_addRate(event) {
	if (!scopes.avUtils.isNavModeReadOnly()) {
		foundset.newRecord();
	}
	
	return;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"58CB07CB-A15A-418C-9EB4-065B81DC9966"}
 */
function onDataChange_taxRate(oldValue, newValue, event) {
	if (newValue == null) taxrate_percent = 0;
	resetTaxCache();
	return true;
}

/**
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5AA86F20-557D-4B5A-A534-B8F179605CA1"}
 */
function onAction_btnDeleteAll(event) {
    if (foundset.getSize() > 0) {
        if (scopes.avText.showYesNoQuestion('deleteAllMsg') == scopes.avText.yes) {
            foundset.deleteAllRecords();
            resetTaxCache();
        }
    }
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F9B3ABF4-2582-455C-86E7-6E91B7C56F63"}
 */
function onDataChange_effectiveDate(oldValue, newValue, event) {
    resetTaxCache();
    return true;
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F69EAC12-A880-48C6-B185-93DBA2C9AB2B"}
 */
function onAction_btnDelete(event) {
    var bDelete = scopes.globals.avUtilities_delete(event);
    
    if (bDelete) {
        resetTaxCache();
    }
}

/**
 * Reset Tax Cache
 *
 * @properties={typeid:24,uuid:"1DC670BA-637A-4527-B7F5-36AEA68057AD"}
 */
function resetTaxCache() {
 // Reset tax cache otherwise the rate table will get messed up the next AvaTax call.
    scopes.avTax.initTaxRateCache();
}
