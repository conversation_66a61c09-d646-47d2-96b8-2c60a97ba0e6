<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.12.2.final using JasperReports Library version 6.12.2-75c5e90a222ab406e416cbf590a5397028a52de3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="purchaseOrder_1" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="139dbdf4-8be3-40e7-8d12-82f450522b4e">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Avanti"/>
	<parameter name="pSQL" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["SELECT * FROM _v_pack where pack_id = 'C948BB49-C6C3-47EC-9C7E-A7D3AA0636D7' "]]></defaultValueExpression>
	</parameter>
	<parameter name="pHeader" class="java.lang.Object" isForPrompting="false">
		<defaultValueExpression><![CDATA[null]]></defaultValueExpression>
	</parameter>
	<parameter name="pFooter" class="java.lang.Object" isForPrompting="false">
		<defaultValueExpression><![CDATA[null]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDate" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["dd-MM-yyyy"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDateSmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[( $P{formatDate}.equals( "dd MMM yyyy" ) ? "dd/MM/yyyy" : "MM/dd/yyyy" )]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrency" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["$#,##0.00"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrencySmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{formatCurrency}.substring(0,$P{formatCurrency}.length()-3).toString()]]></defaultValueExpression>
	</parameter>
	<parameter name="pTitle" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Servoy Workspaces\\Avanti_1\\!_avanti_artifacts\\slingshot_artifacts\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="pLogo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p54" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Show ISO Doc Nr.]]></parameterDescription>
		<defaultValueExpression><![CDATA["ISO DOC Nr. xxx"]]></defaultValueExpression>
	</parameter>
	<parameter name="currentDateTime" class="java.util.Date" isForPrompting="false">
		<defaultValueExpression><![CDATA[new java.util.Date()]]></defaultValueExpression>
	</parameter>
	<parameter name="pFormLogo" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="formatDecimals" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["000000000000"]]></defaultValueExpression>
	</parameter>
	<parameter name="p128" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="p129" class="java.lang.Integer" isForPrompting="false">
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
	<parameter name="p130" class="java.lang.Integer" isForPrompting="false">
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
	<parameter name="p131" class="java.lang.Integer" isForPrompting="false">
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
	<parameter name="p245" class="java.lang.Integer" isForPrompting="false">
		<parameterDescription><![CDATA[Hide or show packing bin location 1-hide]]></parameterDescription>
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="p418" class="java.lang.Integer" isForPrompting="false"/>
	<queryString>
		<![CDATA[$P!{pSQL}]]>
	</queryString>
	<field name="pack_id" class="java.lang.String"/>
	<field name="org_id" class="java.lang.String"/>
	<field name="pack_doc_number" class="java.lang.String"/>
	<field name="pack_ship_date" class="java.sql.Timestamp"/>
	<field name="pack_address_name" class="java.lang.String"/>
	<field name="ship_addr_id" class="java.lang.String"/>
	<field name="pack_address_1" class="java.lang.String"/>
	<field name="pack_address_2" class="java.lang.String"/>
	<field name="pack_address_3" class="java.lang.String"/>
	<field name="pack_city" class="java.lang.String"/>
	<field name="pack_postal" class="java.lang.String"/>
	<field name="pack_country_name" class="java.lang.String"/>
	<field name="pack_stateprov_code" class="java.lang.String"/>
	<field name="pack_stateprov_name" class="java.lang.String"/>
	<field name="pack_country_code" class="java.lang.String"/>
	<field name="pack_customer_name" class="java.lang.String"/>
	<field name="pack_contact_first_and_last" class="java.lang.String"/>
	<field name="pack_shipping_method" class="java.lang.String"/>
	<field name="pack_reprint_count" class="java.lang.Integer"/>
	<field name="contact_full_phone" class="java.lang.String"/>
	<field name="contact_business_email" class="java.lang.String"/>
	<field name="contact_business_fax" class="java.lang.String"/>
	<field name="cust_id" class="java.lang.String"/>
	<field name="ship_address_from_warehouse" class="java.lang.Integer"/>
	<field name="ship_address_from_cust_id" class="java.lang.String"/>
	<field name="ship_address_from_whse_id" class="java.lang.String"/>
	<field name="ship_from_addr_id" class="java.lang.String"/>
	<field name="ship_from_address_name" class="java.lang.String"/>
	<field name="ship_from_address1" class="java.lang.String"/>
	<field name="ship_from_address2" class="java.lang.String"/>
	<field name="ship_from_address3" class="java.lang.String"/>
	<field name="ship_from_city" class="java.lang.String"/>
	<field name="ship_from_stateprov_code" class="java.lang.String"/>
	<field name="ship_from_stateprov_name" class="java.lang.String"/>
	<field name="ship_from_postalzip" class="java.lang.String"/>
	<field name="ship_from_country_code" class="java.lang.String"/>
	<field name="ship_from_country_name" class="java.lang.String"/>
	<field name="pack_shipto_contact_phone" class="java.lang.String"/>
	<field name="shipper_address_from_warehouse" class="java.lang.Integer"/>
	<field name="shipper_address_cust_id" class="java.lang.String"/>
	<field name="total_qty_prod_receipt" class="java.lang.Double"/>
	<field name="globaladdr_active" class="java.lang.Integer"/>
	<field name="int_carrier_id" class="java.lang.String"/>
	<field name="shipper_phone" class="java.lang.String"/>
	<field name="shipto_contact" class="java.lang.String"/>
	<field name="shipto_contact_phone" class="java.lang.String"/>
	<field name="form_lang_code" class="java.lang.String"/>
	<field name="plant_use_logo" class="java.lang.Integer"/>
	<field name="plant_rpt_form_logo" class="java.lang.Object"/>
	<field name="plant_rpt_footer" class="java.lang.Object"/>
	<field name="plant_rpt_header" class="java.lang.Object"/>
	<field name="shipmethod_type" class="java.lang.String"/>
	<group name="Packing Slip" isStartNewPage="true" isResetPageNumber="true" footerPosition="ForceAtBottom">
		<groupExpression><![CDATA[$F{pack_id}]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="78">
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="106" y="49" width="160" height="25" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					</textElement>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="1" y="49" width="105" height="25" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					</textElement>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="106" y="28" width="160" height="20" backcolor="#CCCCCC" uuid="63e231f6-9b11-4810-b4c6-1f0d1a329e79"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="Verdana" size="8" isBold="true"/>
						<paragraph leftIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$R{rpt.Shipper}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="1" y="28" width="105" height="20" backcolor="#CCCCCC" uuid="5e1b42cf-2b74-4447-9fd3-e26a85513bbc"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="Verdana" size="8" isBold="true"/>
						<paragraph leftIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$R{rpt.TimeShipped}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="371" y="28" width="163" height="20" backcolor="#CCCCCC" uuid="f25a17fd-9a10-4b15-86ba-7525f74fc106"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="Verdana" size="8" isBold="true"/>
						<paragraph leftIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$R{rpt.Receiver}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="371" y="49" width="163" height="25" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					</textElement>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="266" y="28" width="105" height="20" backcolor="#CCCCCC" uuid="01179376-34b6-4428-b2b5-05dbd03c06d2"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="Verdana" size="8" isBold="true"/>
						<paragraph leftIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$R{rpt.TimeDelivered}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="266" y="49" width="105" height="25" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					</textElement>
				</textField>
				<subreport>
					<reportElement x="0" y="4" width="534" height="21" isRemoveLineWhenBlank="true" printWhenGroupChanges="Packing Slip" uuid="ab3ea9ba-591f-40d1-94ee-6195d689a703"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pPACK_ID">
						<subreportParameterExpression><![CDATA[$F{pack_id}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDate">
						<subreportParameterExpression><![CDATA[$P{formatDate}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDateSmall">
						<subreportParameterExpression><![CDATA[$P{formatDateSmall}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrency">
						<subreportParameterExpression><![CDATA[$P{formatCurrency}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrencySmall">
						<subreportParameterExpression><![CDATA[$P{formatCurrencySmall}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PackingSlipComments.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="196" splitType="Stretch">
			<rectangle>
				<reportElement x="268" y="95" width="266" height="100" uuid="f485c50f-69f9-4b8d-86f1-f1a23d3622cc"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="1" y="95" width="267" height="100" uuid="f485c50f-69f9-4b8d-86f1-f1a23d3622cc"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<image vAlign="Middle">
				<reportElement x="1" y="0" width="303" height="75" isRemoveLineWhenBlank="true" uuid="f20d9296-2b85-4911-8727-d237b1d71c33">
					<printWhenExpression><![CDATA[(($F{shipper_address_from_warehouse} == 1) && ($F{shipper_address_cust_id} == null)) ||
(($F{shipper_address_from_warehouse} == null) && ($F{shipper_address_cust_id} == null))]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(DefaultJasperReportsContext.getInstance()).loadAwtImageFromBytes((byte[])$F{plant_rpt_form_logo})]]></imageExpression>
			</image>
			<textField textAdjust="StretchHeight">
				<reportElement x="316" y="43" width="109" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pack_doc_number}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="">
				<reportElement x="425" y="43" width="109" height="14" uuid="2b094525-09ee-4d16-9af0-b47da840a017">
					<printWhenExpression><![CDATA[$F{form_lang_code}.equals("fr_CA") ? Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pack_ship_date}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatDate}]]></patternExpression>
			</textField>
			<textField>
				<reportElement x="310" y="0" width="224" height="18" uuid="36be9fff-47a1-46b7-84fe-e9c6bb0d4d7a"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="none">
					<font fontName="Verdana" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.PackSlip}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="316" y="20" width="109" height="23" backcolor="#CCCCCC" uuid="6f442892-11ee-48d7-a500-2cabfc59bd0f"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.PackSlipNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="425" y="20" width="109" height="23" backcolor="#CCCCCC" uuid="0b2cc064-b68d-4d6d-8c14-38484a7cdcff"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.ShipDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1" y="81" width="267" height="14" backcolor="#CCCCCC" uuid="c05c9a18-2c6a-4999-9444-47d76d6e2b4d"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.ShippingDetails}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="98" width="49" height="14" uuid="0f908a7f-b989-416a-9b21-923feaaf702d"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.ShipVia} + ":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="272" y="98" width="32" height="14" uuid="3073d1dc-d6b2-4213-ac5c-3b2590f2f30a"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.Attn} + ":"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="304" y="98" width="230" height="14" uuid="f0d443e2-a194-44d0-b1f8-8ca9c314b71f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{pack_contact_first_and_last} == null  || $F{pack_contact_first_and_last}.isEmpty() ? "" : $F{pack_contact_first_and_last})]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="272" y="114" width="262" height="50" uuid="cc8c2b2d-bfff-4e67-bde6-3c3d2dd0bf37">
					<printWhenExpression><![CDATA[(!$F{shipmethod_type}.equals("P") ? Boolean.TRUE : Boolean.FALSE)]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{pack_address_name} == null ||$F{pack_address_name}.isEmpty() ? "" : $F{pack_address_name} + "\n" ) +
( $F{pack_address_1} == null || $F{pack_address_1}.isEmpty() ? "" : $F{pack_address_1} + "\n" ) +
( $F{pack_address_2} == null || $F{pack_address_2}.isEmpty() ? "" : $F{pack_address_2} + "\n" ) +
( $F{pack_address_3} == null || $F{pack_address_3}.isEmpty() ? "" : $F{pack_address_3} + "\n" ) +
(
  ($F{pack_city} == null || $F{pack_city}.isEmpty()) && ($F{pack_stateprov_code} == null || $F{pack_stateprov_code}.isEmpty())
  && ($F{pack_stateprov_code} == null || $F{pack_postal}.isEmpty())? "" :
( $F{pack_city} == null ||$F{pack_city}.isEmpty() ? "" : $F{pack_city} + ", " ) +
( $F{pack_stateprov_code} == null ||$F{pack_stateprov_code}.isEmpty() ? "" :$F{pack_stateprov_code} + "  " ) +
( $F{pack_postal} == null || $F{pack_postal}.isEmpty() ? "" : $F{pack_postal} + "\n")

)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="268" y="81" width="266" height="14" backcolor="#CCCCCC" uuid="5fecace9-9ada-47c7-939d-701edbe2d01d"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.DeliverTo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement x="54" y="98" width="204" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pack_shipping_method}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="310" y="61" width="224" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{pack_reprint_count} == 0 ? $R{rpt.Original} : $R{rpt.Reprint} + "-" + $F{pack_reprint_count} )]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="315" y="165" width="90" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a">
					<printWhenExpression><![CDATA[(!$F{shipmethod_type}.equals("P") ||   ($F{shipmethod_type}.equals("P") && ($P{p418}.equals(new Integer(1)) )) ? Boolean.TRUE : Boolean.FALSE)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pack_shipto_contact_phone} == null  || $F{pack_shipto_contact_phone}.isEmpty() ? "" :$F{pack_shipto_contact_phone}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="272" y="165" width="43" height="14" uuid="f916ee7a-c068-412f-be8e-8afc829c41c9"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.Phone} + ":"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement x="444" y="165" width="90" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a">
					<printWhenExpression><![CDATA[(!$F{shipmethod_type}.equals("P") ||   ($F{shipmethod_type}.equals("P") && ($P{p418}.equals(new Integer(1)) )) ? Boolean.TRUE : Boolean.FALSE)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contact_business_fax} == null  || $F{contact_business_fax}.isEmpty() ? "" : $F{contact_business_fax}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="407" y="165" width="37" height="14" uuid="0a431b2e-9ba9-4edd-84c7-b1de20c7c9f6"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.Fax} + ":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="272" y="179" width="43" height="14" uuid="11de3b8f-b447-406f-8843-8f7849e99c4d"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Verdana" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.Email} + ":"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="315" y="179" width="219" height="14" uuid="6d41144a-2b45-40ca-956f-9de99be52a8a">
					<printWhenExpression><![CDATA[(!$F{shipmethod_type}.equals("P") ||   ($F{shipmethod_type}.equals("P") && ($P{p418}.equals(new Integer(1)) )) ? Boolean.TRUE : Boolean.FALSE)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contact_business_email} == null  || $F{contact_business_email}.isEmpty() ? "" :$F{contact_business_email}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="4" y="114" width="262" height="79" uuid="e7fc05c0-f7d2-44ed-a128-e7da1c411604"/>
				<textElement verticalAlignment="Top">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{ship_from_address_name} == null ||$F{ship_from_address_name}.isEmpty() ? "" : $F{ship_from_address_name} + "\n" ) +
( $F{ship_from_address1} == null || $F{ship_from_address1}.isEmpty() ? "" : $F{ship_from_address1} + "\n" ) +
( $F{ship_from_address2} == null || $F{ship_from_address2}.isEmpty() ? "" : $F{ship_from_address2} + "\n" ) +
( $F{ship_from_address3} == null || $F{ship_from_address3}.isEmpty() ? "" : $F{ship_from_address3} + "\n" ) +
(
  ($F{ship_from_city} == null || $F{ship_from_city}.isEmpty()) && ($F{ship_from_stateprov_code} == null || $F{ship_from_stateprov_code}.isEmpty())
  && ($F{ship_from_stateprov_code} == null || $F{ship_from_postalzip}.isEmpty())? "" :
( $F{ship_from_city} == null ||$F{ship_from_city}.isEmpty() ? "" : $F{ship_from_city} + ", " ) +
( $F{ship_from_stateprov_code} == null ||$F{ship_from_stateprov_code}.isEmpty() ? "" :$F{ship_from_stateprov_code} + "  " ) +
( $F{ship_from_postalzip} == null || $F{ship_from_postalzip}.isEmpty() ? "" : $F{ship_from_postalzip} + "\n")

)]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="">
				<reportElement x="425" y="43" width="109" height="14" uuid="ce3cabd6-0037-4721-82f9-752f203087e1">
					<printWhenExpression><![CDATA[$F{form_lang_code}.equals("fr_CA") ? Boolean.TRUE : Boolean.FALSE]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pack_ship_date}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatDate}]]></patternExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="272" y="114" width="262" height="50" uuid="e29745a0-814c-4d4c-92a0-6b7fab87b490">
					<printWhenExpression><![CDATA[($F{shipmethod_type}.equals("P") ? Boolean.TRUE : Boolean.FALSE)]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{pack_address_name} == null ||$F{pack_address_name}.isEmpty() ? "" : $F{pack_address_name} + "\n" ) +
"*** PICKUP ***"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="21" splitType="Stretch">
			<printWhenExpression><![CDATA[new Boolean($F{total_qty_prod_receipt} == 0)]]></printWhenExpression>
			<subreport>
				<reportElement x="1" y="0" width="533" height="21" uuid="b90d2604-8c6c-4351-acd1-c490eb219be0"/>
				<subreportParameter name="p130">
					<subreportParameterExpression><![CDATA[$P{p130}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p131">
					<subreportParameterExpression><![CDATA[$P{p131}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDate">
					<subreportParameterExpression><![CDATA[$P{formatDate}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p245">
					<subreportParameterExpression><![CDATA[$P{p245}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDateSmall">
					<subreportParameterExpression><![CDATA[$P{formatDateSmall}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_CONNECTION">
					<subreportParameterExpression><![CDATA[$P{REPORT_CONNECTION}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatCurrency">
					<subreportParameterExpression><![CDATA[$P{formatCurrency}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatCurrencySmall">
					<subreportParameterExpression><![CDATA[$P{formatCurrencySmall}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="pPACK_ID">
					<subreportParameterExpression><![CDATA[$F{pack_id}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDecimals">
					<subreportParameterExpression><![CDATA[$P{formatDecimals}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p129">
					<subreportParameterExpression><![CDATA[$P{p129}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p128">
					<subreportParameterExpression><![CDATA[$P{p128}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PackingSlipDetail.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="21">
			<printWhenExpression><![CDATA[new Boolean($F{total_qty_prod_receipt} > 0)]]></printWhenExpression>
			<subreport>
				<reportElement x="1" y="0" width="533" height="21" uuid="428f8603-f792-42c1-92cf-5743962918ab"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p130">
					<subreportParameterExpression><![CDATA[$P{p130}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="pPACK_ID">
					<subreportParameterExpression><![CDATA[$F{pack_id}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p131">
					<subreportParameterExpression><![CDATA[$P{p131}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDate">
					<subreportParameterExpression><![CDATA[$P{formatDate}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDecimals">
					<subreportParameterExpression><![CDATA[$P{formatDecimals}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p245">
					<subreportParameterExpression><![CDATA[$P{p245}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p129">
					<subreportParameterExpression><![CDATA[$P{p129}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatDateSmall">
					<subreportParameterExpression><![CDATA[$P{formatDateSmall}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p128">
					<subreportParameterExpression><![CDATA[$P{p128}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatCurrency">
					<subreportParameterExpression><![CDATA[$P{formatCurrency}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_CONNECTION">
					<subreportParameterExpression><![CDATA[$P{REPORT_CONNECTION}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="formatCurrencySmall">
					<subreportParameterExpression><![CDATA[$P{formatCurrencySmall}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PackingSlipDetailWithReceived.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="13" splitType="Stretch">
			<textField textAdjust="StretchHeight">
				<reportElement x="416" y="1" width="100" height="12" uuid="1d56c0be-8f1e-42c1-8d3e-89a7848e1fdd"/>
				<textElement textAlignment="Right">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{rpt.Page} + " " + $V{PAGE_NUMBER} + " " + $R{rpt.of} + " "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Group" evaluationGroup="Packing Slip">
				<reportElement x="516" y="1" width="19" height="12" uuid="001e59ee-7df6-4dc3-9886-54a94167dd3e"/>
				<textElement textAlignment="Right">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMMMM dd, yyyy  h.mm a">
				<reportElement x="1" y="1" width="134" height="10" uuid="2af8d9f4-46d8-43bb-967f-521eeba756e8">
					<printWhenExpression><![CDATA[$F{form_lang_code}.equals("fr_CA") ? Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.SimpleDateFormat("MMMMM dd, yyyy h.mm a",$P{REPORT_LOCALE}).format($P{currentDateTime})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="135" y="1" width="281" height="10" uuid="2af8d9f4-46d8-43bb-967f-521eeba756e8">
					<printWhenExpression><![CDATA[( $P{p54}.isEmpty()? Boolean.FALSE : Boolean.TRUE)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p54}]]></textFieldExpression>
			</textField>
			<textField pattern="MMMMM dd yyyy  h.mm a">
				<reportElement x="1" y="1" width="134" height="10" uuid="a141feb9-3b77-492b-8bbc-44845c4eda7a">
					<printWhenExpression><![CDATA[$F{form_lang_code}.equals("fr_CA") ? Boolean.TRUE : Boolean.FALSE]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.SimpleDateFormat("dd MMMMM yyyy h.mm a",$P{REPORT_LOCALE}).format($P{currentDateTime})]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
