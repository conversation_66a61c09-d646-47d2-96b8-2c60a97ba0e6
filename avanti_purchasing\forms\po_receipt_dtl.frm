customProperties:"useCssPosition:true",
dataSource:"db:/avanti/po_receipt",
extendsID:"3DF9114A-BDD8-4EF3-AEDD-59E7874347A7",
items:[
{
cssPosition:"177,-1,-1,10,140,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"177",
width:"140"
},
enabled:true,
formIndex:9,
labelFor:"porec_reference",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.receiptReference",
visible:true
},
name:"porec_reference_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"04CCD48A-EBF7-47A3-A3C7-F9A0F088F70A"
},
{
cssPosition:"37,-1,-1,295,5,22",
formIndex:28,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"295",
right:"-1",
top:"37",
width:"5"
},
enabled:true,
formIndex:28,
styleClass:"label_bts text-center",
tabSeq:-1,
text:"-",
visible:true
},
name:"component_D2EE8A16",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1212ED1A-5EF2-4701-9BD2-ED624D7B9561"
},
{
cssPosition:"37,-1,-1,300,30,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"300",
right:"-1",
top:"37",
width:"30"
},
dataProviderID:"porec_receipt_version",
editable:false,
enabled:true,
formIndex:21,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:-2,
visible:true
},
name:"porec_receipt_version",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"198C2C8F-AF8D-4D01-AD92-F54FC989151B"
},
{
cssPosition:"37,-1,-1,835,180,22",
formIndex:67,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"835",
right:"-1",
top:"37",
width:"180"
},
enabled:true,
formIndex:67,
onActionMethodID:"F320AA90-D6E6-49F8-967A-3DCF463AE740",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.resetToUpdate",
visible:true
},
name:"btnResetToUpdate",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"20EA56D8-73B0-48E7-82AF-AAC3716D1A16"
},
{
cssPosition:"30,-1,-1,5,417,261",
json:{
cssPosition:{
bottom:"-1",
height:"261",
left:"5",
right:"-1",
top:"30",
width:"417"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_422B340F",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"228C825A-6219-492C-A1D2-990450630A08"
},
{
cssPosition:"231,-1,-1,10,140,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"231",
width:"140"
},
enabled:true,
formIndex:14,
labelFor:"porec_waybill",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.waybillNumber",
visible:true
},
name:"porec_waybill_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2412D681-7ABC-4360-ADFB-9E2EA52338C6"
},
{
cssPosition:"37,-1,-1,335,24,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"335",
right:"-1",
top:"37",
width:"24"
},
enabled:true,
formIndex:26,
onActionMethodID:"E233F7F0-96A6-4A71-B48C-88A34336F3CC",
styleClass:"label_bts",
tabSeq:3,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupEstimate",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"291E9483-C6E1-4DC5-B563-631D60422CD9"
},
{
cssPosition:"172,-1,-1,432,140,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"172",
width:"140"
},
enabled:true,
formIndex:4,
labelFor:"porec_setup_amt",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.setupAmount",
visible:true
},
name:"porec_setup_amt_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2A63EF6E-1E9E-4425-8E64-2377FB069F6F"
},
{
cssPosition:"4,-1,-1,679,100,20",
enabled:false,
formIndex:66,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"679",
right:"-1",
top:"4",
width:"100"
},
dataProviderID:"po_receipt_to_po_receipt_register.porreg_number",
editable:false,
enabled:false,
formIndex:66,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"porreg_number",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2B63039A-C236-4DD3-B71F-E425F5A863D0"
},
{
cssPosition:"4,-1,-1,789,80,20",
formIndex:29,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"789",
right:"-1",
top:"4",
width:"80"
},
enabled:true,
formIndex:29,
labelFor:"porec_status",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"porec_status_label",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"326A36CF-6385-4377-BB76-E6E9BED74207"
},
{
cssPosition:"64,-1,-1,155,257,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"64",
width:"257"
},
dataProviderID:"po_receipt_to_po_purchase.po_purchase_to_ap_supplier.supplier_name",
editable:false,
enabled:true,
formIndex:6,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:-2,
visible:true
},
name:"supplier_name",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"382B195C-281C-4D9A-A13D-F66330239AFC"
},
{
cssPosition:"145,-1,-1,576,149,22",
formIndex:6,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"145",
width:"149"
},
dataProviderID:"porec_setup_amt_method",
enabled:true,
formIndex:6,
onDataChangeMethodID:"4EBD2746-29C0-402A-B0D4-B97B4812DFD3",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:13,
valuelistID:"88680EA8-CA9E-4245-B622-477AE7557FAB",
visible:true
},
name:"porec_setup_amt_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"384EDB1A-565A-4D98-9F69-E4701E22DFDA"
},
{
cssPosition:"91,-1,-1,155,140,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"91",
width:"140"
},
dataProviderID:"po_receipt_to_po_purchase.po_purchase_to_sys_currency$po_currency.curr_name",
editable:false,
enabled:true,
formIndex:12,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:-2,
visible:true
},
name:"curr_name",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"41093862-068B-4119-ACEB-6C0E1BDE1AB4"
},
{
cssPosition:"30,-1,-1,427,593,261",
json:{
cssPosition:{
bottom:"-1",
height:"261",
left:"427",
right:"-1",
top:"30",
width:"593"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_5A55A42C",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"45853039-36EA-426C-883E-6C1652BA990C"
},
{
cssPosition:"4,-1,-1,555,120,20",
formIndex:65,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"555",
right:"-1",
top:"4",
width:"120"
},
enabled:true,
formIndex:65,
labelFor:"porreg_number",
styleClass:"text-right label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.registerNumber",
visible:true
},
name:"porreg_number_label",
styleClass:"text-right label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"46038E94-8665-4DB0-AEC8-BE8AED083C83"
},
{
cssPosition:"91,-1,-1,10,140,22",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"91",
width:"140"
},
enabled:true,
formIndex:11,
labelFor:"curr_name",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.currency_Rate",
visible:true
},
name:"curr_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"475E0C09-8240-4027-92FC-389F0A1E658D"
},
{
cssPosition:"37,-1,-1,576,149,22",
formIndex:7,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"37",
width:"149"
},
dataProviderID:"porec_freight_amt_method",
enabled:true,
formIndex:7,
onDataChangeMethodID:"1B26D0C5-80A8-466C-B497-04170C3C9E5A",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:9,
valuelistID:"6A870D9E-284E-466B-87D0-7FD01637DF42",
visible:true
},
name:"porec_freight_amt_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"48C4E65F-5F6E-4027-8B68-E4417ACDA151"
},
{
cssPosition:"258,-1,-1,10,140,22",
formIndex:16,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"258",
width:"140"
},
enabled:true,
formIndex:16,
labelFor:"shipmethod_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shipmethod_id",
visible:true
},
name:"shipmethod_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"51AF447F-4AB2-4D42-8D97-255EE4A881B5"
},
{
cssPosition:"258,-1,-1,432,293,22",
formIndex:16,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"258",
width:"293"
},
enabled:true,
formIndex:16,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"lblCancelledReceipt",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"529D86AE-C328-4684-AAF2-DBE421E8A6F7"
},
{
height:700,
partType:5,
typeid:19,
uuid:"5300B1BA-F2D2-4E19-9225-F2382536972E"
},
{
cssPosition:"37,-1,-1,432,140,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
formIndex:8,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.freightAllocation",
visible:true
},
name:"porec_freight_amt_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"57E3B522-91B3-460D-8B5E-6014A6A23499"
},
{
cssPosition:"91,-1,-1,576,149,22",
formIndex:11,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"91",
width:"149"
},
dataProviderID:"po_receipt_to_po_purchase.po_freight_in_bill_method",
enabled:true,
formIndex:11,
onDataChangeMethodID:"A664E14F-FD2B-4D31-8B57-765479BBD0DD",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:11,
valuelistID:"3D8F6C70-FB02-4311-8FE6-134242ED5880",
visible:true
},
name:"po_freight_in_bill_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"588F52A4-**************-B348562F3A78"
},
{
cssPosition:"64,-1,-1,10,140,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
formIndex:5,
labelFor:"supplier_name",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.supplier_name_2",
visible:true
},
name:"supplier_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"602739C9-FD26-448F-BF2D-130FEAB0A8E5"
},
{
cssPosition:"64,-1,-1,576,149,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"64",
width:"149"
},
dataProviderID:"porec_freight_amt",
editable:true,
enabled:true,
formIndex:3,
onDataChangeMethodID:"C71E2A65-C9DB-4C8E-8E35-38B1AB9B23A4",
selectOnEnter:false,
styleClass:"text-right textbox_bts",
tabSeq:10,
visible:true
},
name:"porec_freight_amt",
styleClass:"text-right textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6A41916C-D77B-4FB1-A474-132A22F32176"
},
{
cssPosition:"37,-1,-1,155,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"37",
width:"140"
},
dataProviderID:"po_id",
editable:true,
enabled:true,
formIndex:2,
onDataChangeMethodID:"A2CD8437-4775-4967-90DC-52492FA8D034",
styleClass:"typeahead_bts",
tabSeq:2,
valuelistID:"2860D4DE-9716-43FA-BF80-01A73AEFA5A8",
visible:true
},
name:"po_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6EF5B0F3-298E-4866-A3A3-7377A642C43C"
},
{
cssPosition:"37,-1,-1,835,180,22",
formIndex:17,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"835",
right:"-1",
top:"37",
width:"180"
},
enabled:true,
formIndex:17,
onActionMethodID:"5387E8AC-3F33-4654-A607-DFEA34DFABB1",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.readyToPost",
visible:true
},
name:"btnReadyToPost",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"70F8F859-5BAD-4F7D-9B42-9AEA97B16839"
},
{
cssPosition:"172,-1,-1,727,13,22",
formIndex:15,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"727",
right:"-1",
top:"172",
width:"13"
},
enabled:true,
formIndex:15,
styleClass:"over_warning label_bts text-center",
tabSeq:-1,
text:"%%clc_setup_amt_over%%",
visible:true
},
name:"modSetupOverride",
styleClass:"over_warning label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8513BA45-C8A0-4855-BC3B-BCC03FE31C6E"
},
{
cssPosition:"91,-1,-1,432,139,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"91",
width:"139"
},
enabled:true,
formIndex:10,
labelFor:"po_freight_in_bill_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.freightMethod",
visible:true
},
name:"po_freight_in_bill_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"*************-4A39-A69D-FCB0DF221565"
},
{
cssPosition:"1,-1,-1,5,1015,26",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"26",
left:"5",
right:"-1",
top:"1",
width:"1015"
},
enabled:true,
formIndex:2,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.PurchaseReceiptDetailView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"97F06BB2-8B85-481B-8B36-B102EFA47906"
},
{
cssPosition:"91,-1,-1,300,112,22",
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"300",
right:"-1",
top:"91",
width:"112"
},
dataProviderID:"porec_exchange_rate",
editable:false,
enabled:true,
formIndex:13,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:-2,
visible:true
},
name:"porec_exchange_rate",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"9AC1504C-2436-4D46-9091-4FE04BCA0695"
},
{
cssPosition:"150,-1,-1,155,140,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"150",
width:"140"
},
dataProviderID:"porec_receipt_date",
enabled:true,
formIndex:7,
onDataChangeMethodID:"83CAEF98-5BD8-4C2F-B524-003B882CDF23",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:4,
visible:true
},
name:"porec_receipt_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"AA907A15-21AF-4B65-9F80-70D4AD36E6FE"
},
{
cssPosition:"258,-1,-1,155,260,22",
formIndex:17,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"258",
width:"260"
},
dataProviderID:"shipmethod_id",
editable:true,
enabled:true,
formIndex:17,
styleClass:"typeahead_bts",
tabSeq:8,
valuelistID:"432C4C95-6A1B-4E14-83AF-3C1933A66646",
visible:true
},
name:"shipmethod_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"AC42FF50-E80A-44F3-BD4C-0151902976D2"
},
{
cssPosition:"64,-1,-1,727,13,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"727",
right:"-1",
top:"64",
width:"13"
},
enabled:true,
formIndex:14,
styleClass:"over_warning label_bts text-center",
tabSeq:-1,
text:"%%clc_freight_amt_over%%",
visible:true
},
name:"modSpoils",
styleClass:"over_warning label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B41A9549-F0B2-4F39-AF7E-DE0C14D25709"
},
{
cssPosition:"204,-1,-1,155,260,22",
formIndex:15,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"204",
width:"260"
},
dataProviderID:"porec_freight_supplier_id",
editable:false,
enabled:true,
formIndex:15,
styleClass:"typeahead_bts",
tabSeq:6,
valuelistID:"BFF9C0C7-52EC-43EE-9652-15BECC1FD7F0",
visible:true
},
name:"porec_freight_supplier_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BCB7F896-9220-47F1-847C-9F488897877E"
},
{
cssPosition:"145,-1,-1,432,140,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"145",
width:"140"
},
enabled:true,
formIndex:9,
labelFor:"porec_setup_amt_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.setupAllocation",
visible:true
},
name:"porec_setup_amt_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BE164003-3632-4C39-BA5E-9E5CF0272E77"
},
{
cssPosition:"37,-1,-1,10,140,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
formIndex:4,
labelFor:"po_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.purchaseOrderNumber",
visible:true
},
name:"po_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BE585DEF-10EE-42DE-B4ED-2BEFE36A2CC4"
},
{
cssPosition:"150,-1,-1,10,140,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"150",
width:"140"
},
enabled:true,
formIndex:8,
labelFor:"porec_receipt_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.receiptDate",
visible:true
},
name:"porec_receipt_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C0D63CBA-5D04-447F-8882-6E0D896F84CF"
},
{
cssPosition:"177,-1,-1,155,260,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"177",
width:"260"
},
dataProviderID:"porec_reference",
editable:true,
enabled:true,
formIndex:10,
onDataChangeMethodID:"C49DF09F-**************-D316CA4E18D9",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:5,
visible:true
},
name:"porec_reference",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CAE78DC8-F675-44B9-8994-DF512EC904D4"
},
{
cssPosition:"123,-1,-1,11,406,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"123",
width:"406"
},
enabled:true,
formIndex:1,
styleClass:"gray_bold label_bts",
tabSeq:-1,
text:"Receipt",
visible:true
},
name:"component_672A31C4",
styleClass:"gray_bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D6BA9754-2DC8-4110-8FC8-6A7794D4082F"
},
{
cssPosition:"172,-1,-1,576,149,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"172",
width:"149"
},
dataProviderID:"porec_setup_amt",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"4A1CEA80-F0D4-4DE8-83AD-A68EB7C22A31",
selectOnEnter:false,
styleClass:"text-right textbox_bts",
tabSeq:14,
visible:true
},
name:"porec_setup_amt",
styleClass:"text-right textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DFCFA9AA-8C6E-4A73-BB0D-807F64698E30"
},
{
cssPosition:"118,-1,-1,576,149,22",
formIndex:13,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"118",
width:"149"
},
dataProviderID:"po_receipt_to_po_purchase.po_freight_in_cost_method",
enabled:true,
formIndex:13,
onDataChangeMethodID:"444CBBAE-2998-4F3C-AE84-755D19E248A4",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:12,
valuelistID:"C661C802-E5D6-41B2-8F5B-607E6EF5CCF3",
visible:true
},
name:"po_freight_in_cost_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"E19F8596-5556-4042-8EE6-B8549EF20C66"
},
{
cssPosition:"204,-1,-1,10,140,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"204",
width:"140"
},
enabled:true,
formIndex:14,
labelFor:"porec_waybill",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FreightSupplier",
visible:true
},
name:"porec_freight_supplier_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E1A52734-09E8-4327-8D97-E56008BF79FE"
},
{
cssPosition:"295,5,5,5,1015,400",
formIndex:27,
json:{
cssPosition:{
bottom:"5",
height:"400",
left:"5",
right:"5",
top:"295",
width:"1015"
},
formIndex:27,
styleClass:"scrollable-tabpanel",
tabs:[
{
containedForm:"C0433DFA-0C45-4D97-8BB9-EC5519EDCADE",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"po_receipt_to_po_receipt_detail",
svyUUID:"206588F9-CFE7-47A2-B5A6-624647B3DC6F",
text:"i18n:avanti.lbl.receiptDetails"
},
{
containedForm:"5D9AF649-68C1-418C-B660-3BFB46213963",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"po_receipt_to_po_receipt_dist",
svyUUID:"6EC3C0B9-790D-4CBF-B410-83E8A70BCE16",
text:"i18n:avanti.lbl.distributions"
},
{
containedForm:"A3E319AD-F586-4527-BEC3-B1EB3DB33EB5",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabIntegrationDetails",
relationName:"_to_po_receipt$foundset",
svyUUID:"51E1D2EC-17D8-4502-9029-79988161C167",
text:"i18n:avanti.lbl.IntegrationDetails"
}
]
},
name:"tabDetails",
styleClass:"scrollable-tabpanel",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"E5A436AA-EE80-46E7-BF99-1BB5C8645F66"
},
{
cssPosition:"118,-1,-1,432,139,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"118",
width:"139"
},
enabled:true,
formIndex:12,
labelFor:"po_freight_in_cost_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FreightCostMethod",
visible:true
},
name:"po_freight_in_cost_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EB56E57F-132F-4B56-9D22-6CD9072E0F4E"
},
{
cssPosition:"38,-1,-1,576,149,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"576",
right:"-1",
top:"38",
width:"149"
},
dataProviderID:"_freightMethodExpensed",
editable:false,
enabled:true,
formIndex:1,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:16,
visible:false
},
name:"porec_freight_amt_method_text",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"EFE849BF-E422-4AC4-A57F-F2DAB56CEEA2",
visible:false
},
{
cssPosition:"64,-1,-1,432,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"432",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
formIndex:2,
labelFor:"porec_freight_amt",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.freightAmount",
visible:true
},
name:"porec_freight_amt_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F21CC271-A31E-49B7-97D5-97BBC5712549"
},
{
cssPosition:"4,-1,-1,875,140,20",
enabled:false,
formIndex:30,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"875",
right:"-1",
top:"4",
width:"140"
},
dataProviderID:"porec_status",
editable:false,
enabled:false,
formIndex:30,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:1,
valuelistID:"3A2519EC-3979-4A60-B9E5-0F7DACC4CB4F",
visible:true
},
name:"porec_status",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F30C2839-7528-4F03-8E70-42E88E0C0F59"
},
{
cssPosition:"231,-1,-1,155,260,22",
formIndex:15,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"231",
width:"260"
},
dataProviderID:"porec_waybill",
editable:true,
enabled:true,
formIndex:15,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:7,
visible:true
},
name:"porec_waybill",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"FE89A285-E282-42FE-A3AE-523354E81BF4"
}
],
name:"po_receipt_dtl",
scrollbars:33,
size:"1025,700",
styleName:null,
typeid:3,
uuid:"5D0EFDD6-5EA6-42B0-BAF9-08E3FD9F648E"