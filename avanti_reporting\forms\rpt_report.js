/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D60D2C19-FEEB-4018-8980-27B2AEFF8F61"}
 */
var _outputFormat = 'view';

/** @type {String}
 * @properties={typeid:35,uuid:"94412F57-BE6C-46CB-9DDE-535139218D42"}
 */
var timeZone = '';

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"E6C5C485-B73C-4084-B0F3-0BDE62A934A5",variableType:-4}
 */
var aReportByteArray = new Array();

/**
 * Creates a menu in the framework, or adds a child to a menu (if iParentMenuID is provided)
 *
 * <AUTHOR>
 * @since 2011-05-17
 *
 * @param {Number} [iParentMenuID] - If creating a child, then provide nav_popmenu_id of the parent, otherwise parent is created
 * @param {JSRecord<db:/avanti/rpt_report>} [rRpt] - the report record
 *
 * @returns {Number} The nav_popmenu_id of the newly created menu in the framework
 *
 * @properties={typeid:24,uuid:"00CE8E94-4D2D-4C6A-A55E-3B229ED442FC"}
 */
function rptAddPopMenu(iParentMenuID, rRpt) {
	var bAttach = false;

	// Get a Menu foundset
	/** @type {JSFoundSet<db:/svy_framework/nav_popmenu>}*/
	var fsMenu;
	var sForm;
	if (iParentMenuID) {
		sForm = "rpt_report_dtl_tab_subMenus";
		fsMenu = forms[sForm].foundset;
	} else {
		sForm = "rpt_report_dtl_tab_menus";
		fsMenu = forms[sForm].foundset
	}

	// Add the [nav_popmenu] record
	var rMenu = fsMenu.getRecord(fsMenu.newRecord(false));
	if (rMenu == null) {
		application.output('Menu object not created.')
	}
	var iMenuID = fsMenu.nav_popmenu_id;
	//	fsMenu.org_id = globals.org_id;
	// GD - 2013-12-11: Need to set the report owner for system reports
	if (!rRpt || rRpt.is_system === 1) {
		fsMenu.owner_id = null;
	} else {
		fsMenu.owner_id = globals.owner_id;
	}
	fsMenu.program_name = globals.avRpt_selectedProgram;
	fsMenu.sequence_nr = fsMenu.getSize();
	fsMenu.sequence_icon = globals.icon_shuffleGrey;
	fsMenu.label = i18n.getI18NMessage("avanti.lbl.pleaseSpecify");
	fsMenu.menu_type = "R";
	fsMenu.in_edit = 0;
	fsMenu.in_browse = 1;

	//	// Only attach the new menu to a report if no other menu for this program is attached
	//	if (!utils.hasRecords(rpt_report_to_nav_popmenu$avrpt_selectedreport))
	//	{
	//		fsMenu.rpt_id = globals.avRpt_selectedReport;
	//		fsMenu.method = "globals.avRpt_runReport('" + globals.avRpt_selectedReport + "')";
	//		bAttach = true;
	//	}

	// If this is a child, then set the parentID
	if (iParentMenuID > 0) {
		fsMenu.parent_popmenu_id = iParentMenuID
	} else {
		fsMenu.parent_popmenu_id = null;
	}

	databaseManager.saveData();

	if (bAttach) {
		// Recalculate the form views
		databaseManager.recalculate(forms.rpt_report_dtl_tab_programs.foundset);
		databaseManager.recalculate(forms.rpt_report_dtl_tab_menus.foundset);
		databaseManager.recalculate(forms.rpt_report_dtl_tab_subMenus.foundset);
	}

	// GD - 2012-06-16: Setting the global here does not work, because it could be a child
	//	globals.avRpt_selectedPopMenuID = iMenuID;

	fsMenu.selectRecord(iMenuID);

	if (iParentMenuID) {
		forms.app_report_dtl_tab_subMenus.elements.grid.requestFocus(forms.app_report_dtl_tab_subMenus.elements.grid.getColumnIndex("fldMenu"));
	} else {
		forms.rpt_report_dtl_tab_menus.elements.grid.requestFocus(forms.rpt_report_dtl_tab_menus.elements.grid.getColumnIndex("fldMenu"));
	}

	return iMenuID;
}

/**
 * Deletes the current menu from the framework, does not delete if the menu has children (delete them first)
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-05-17
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {boolean} Returns TRUE or FALSE
 *
 * @properties={typeid:24,uuid:"31238429-E915-4F1E-BDE6-BE6BFCF18C96"}
 */
function rptDeleteMenu(event) {
	var sForm = event.getFormName();
	/** @type {JSFoundSet<db:/svy_framework/nav_popmenu>}*/
	var fsForm = forms[sForm].foundset;

	// Check for children
	if (utils.hasRecords(fsForm.nav_popmenu_to_nav_popmenu$children) && !fsForm.parent_popmenu_id) {
		return false;
	}

	// Check for system menu (no owner_id)
	{
		//		if (fsForm.owner_id == globals.systemOwnerUUID && globals.avBase_developmentMode != 1)
		if (fsForm.owner_id == null && globals.avBase_developmentMode != 1) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.deleteMenu_title"),
				i18n.getI18NMessage("avanti.dialog.deleteMenu_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"));
			return false;
		}
	}

	// Call the web dialog in the framework
	var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
		i18n.getI18NMessage("avanti.dialog.deleteMsg"), i18n.getI18NMessage("avanti.dialog.cancel"),
		i18n.getI18NMessage("avanti.dialog.ok"));

	if (sAns != i18n.getI18NMessage("avanti.dialog.ok")) {
		return false;
	}

	//	// If a child, then set the parent to the report before deleting the child
	//	if (fsForm.parent_popmenu_id)
	//	{
	//		fsForm.rpt_report_to_nav_popmenu$submenu_to_menu.rpt_id = fsForm.rpt_id;
	//		fsForm.rpt_report_to_nav_popmenu$submenu_to_menu.method = fsForm.method;
	//	}

	// Delete the menu function
	var bResult;
	if (utils.hasRecords(fsForm.nav_popmenu_to_nav_function)) {
		var iSecKeyID = fsForm.nav_popmenu_to_nav_function.security_key_id;
		forms.rpt_report.rptDeleteMenuFunction(iSecKeyID);

		// Remove the nav_navigation_key for this menu
		bResult = globals.avSecurity_deleteNavigationKey(iSecKeyID);

		// Delete the security key
		bResult = globals.avSecurity_deleteSecurityKey(iSecKeyID);

		if (bResult == false) {

		}
	}

	//	// Need to delete the security_key from associated tables (sec_user_right && nav_navigation_keys)
	//	rptSelectMenu(event);

	fsForm.deleteRecord();
	databaseManager.saveData();

	return true;
}

/**
 * Sets/clears a menu for use by the current report (globals.avRpt_selectedReport)
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-05-17
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {JSFoundSet<db:/svy_framework/nav_popmenu>} [fsForm] - pass in the optional foundset containing the pop menu to set
 * @returns {boolean} Returns TRUE or FALSE
 *
 * @properties={typeid:24,uuid:"00AD57D7-5C3F-4D28-AC4D-DF65AC3AE229"}
 * @AllowToRunInFind
 */
function rptSelectMenu(event, fsForm) {
	/// Trap for nothing selected
	if (!globals.avRpt_selectedProgram || !globals.avRpt_selectedReport || !globals.avRpt_selectedPopMenuID) return false;

	var rRpt = null;
	
	if (!fsForm) {
    	var sForm = event.getFormName();
    	fsForm = forms[sForm].foundset;
    	rRpt = forms.rpt_report_dtl.foundset.getSelectedRecord();
	}
	else {
	    rRpt = _to_rpt_report$avrpt_selectedreport.getRecord(1);
	}

	if (fsForm.rpt_id == globals.avRpt_selectedReport 
	        || !fsForm.rpt_id) {
	            
		// Check for children
		if (utils.hasRecords(fsForm.nav_popmenu_to_nav_popmenu$children)) {
			return false;
		}

		// If this report is already assigned to a menu, then exit
		if (utils.hasRecords(rpt_report_nav_program_to_nav_popmenu$avrpt_selectedreport)) {
			return false;
		}

		// Get the security_key used for this report
		
		var bSystem = false;
		if (rRpt.is_system == 1) {
		    
    		bSystem = true;
		}
		var iKeyID = globals.avSecurity_getSecurityKeyID_report(globals.UUIDtoString(rRpt.rpt_id), rRpt.rpt_name, rRpt.program, bSystem);
		var iMenuID = fsForm.nav_popmenu_id;

		if (fsForm.rpt_id == globals.avRpt_selectedReport) {
			fsForm.rpt_id = null;
			fsForm.method = null;
			fsForm.function_id = rptDeleteMenuFunction(iKeyID);
			if (bSystem) {
				// Need to set this as a system menu
				fsForm.owner_id = null; //globals.systemOwnerUUID;
			} else {
				fsForm.owner_id = globals.owner_id;
			}

			// Remove the nav_navigation_key for this menu
			globals.avSecurity_deleteNavigationKey(iKeyID);
			//			rptDeleteNavigationKey(iKeyID, iMenuID);

			// Delete the security key
			globals.avSecurity_deleteSecurityKey(iKeyID);
		} else {
			fsForm.rpt_id = globals.avRpt_selectedReport;
			fsForm.method = "globals.avRpt_runReport('" + globals.avRpt_selectedReport + "')";
			fsForm.function_id = rptAddMenuFunction(iKeyID, globals.avRpt_selectedReport);
			if (bSystem) {
				// Need to set this as a system menu
				fsForm.owner_id = null; //globals.systemOwnerUUID;
			} else {
				fsForm.owner_id = globals.owner_id;
			}

			// Add the security key to this menu
			globals.avSecurity_addNavigationKey(iKeyID, rRpt.program, "R", iMenuID); //rptAddNavigationKey(iKeyID, iMenuID);

			// Add the key to the necessary groups
			var dsOwners;
			var dsGroups;
			var sOwnerID;
			var k, m;
			if (bSystem) {
				// Add it to all groups for all owners
				dsOwners = globals.avSecurity_getOwners();
				if (dsOwners) {
					for (k = 1; k <= dsOwners.getMaxRowIndex(); k++) {
						sOwnerID = dsOwners.getValue(k, 1);

						dsGroups = globals.avSecurity_getGroups(sOwnerID);

						if (dsGroups) {
							for (m = 1; m <= dsGroups.getMaxRowIndex(); m++) {
								globals.avSecurity_addGroupUserRight(iKeyID, dsGroups.getValue(m, 1), null, true);
							}
						}
					}
				}
			} else {
				// Add it only to the owners groups
				dsGroups = globals.avSecurity_getGroups(globals.owner_id);

				if (dsGroups) {
					for (m = 1; m <= dsGroups.getMaxRowIndex(); m++) {
						globals.avSecurity_addGroupUserRight(iKeyID, dsGroups.getValue(m, 1), null, true);
					}
				}
			}
		}

		databaseManager.saveData(fsForm.getSelectedRecord());
		forms.rpt_report_dtl_tab_programs.elements.grid.myFoundset.foundset.reloadWithFilters();
		forms.rpt_report_dtl_tab_subMenus.elements.grid.myFoundset.foundset.reloadWithFilters();
		forms.rpt_report_dtl_tab_menus.elements.grid.myFoundset.foundset.reloadWithFilters();

		return true;
	} else {
		// This menu is used for a different report
		return false;
	}

}

/**
 * Adds a Report menu function
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-20
 *
 * @param {Number} iKeyID
 * @param {String} sRptID
 *
 * @returns {Number}
 *
 *
 * @properties={typeid:24,uuid:"4D903380-26F0-4E60-947E-9B833E75C7E1"}
 */
function rptAddMenuFunction(iKeyID, sRptID) {
	/** @type {JSFoundSet<db:/svy_framework/nav_function>}*/
	var fsFunction = databaseManager.getFoundSet(globals.avBase_dbase_framework, "nav_function");
	//	fsFunction.loadRecords(0);

	/** @type {JSFoundSet<db:/svy_framework/nav_function_arguments>}*/
	var fsFunctionArgs = databaseManager.getFoundSet(globals.avBase_dbase_framework, "nav_function_arguments");
	//	fsFunctionArgs.loadRecords(0);

	// Add the function
	fsFunction.newRecord();
	var iFunctionID = fsFunction.function_id;
	fsFunction.method = 'globals.avRpt_runReport';
	fsFunction.label = "i18n:avanti.lbl.runReport";
	fsFunction.flag_i18n = 1;
	fsFunction.security_key_id = iKeyID;

	// Add the function argument
	fsFunctionArgs.newRecord();
	fsFunctionArgs.function_id = iFunctionID;
	fsFunctionArgs.arg_type = 1;
	fsFunctionArgs.argument = sRptID; //"'" + sRptID + "'";

	return iFunctionID;
}

/**
 * Deletes a Report Menu function
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-20
 *
 * @param {Number} iKeyID
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"E05D3505-0F3E-4576-940E-39428B9ACF3A"}
 */
function rptDeleteMenuFunction(iKeyID) {
	/** @type {JSFoundSet<db:/svy_framework/nav_function>}*/
	var fsFunction = databaseManager.getFoundSet(globals.avBase_dbase_framework, "nav_function");
	//	fsFunction.loadRecords(0);

	/** @type {JSFoundSet<db:/svy_framework/nav_function_arguments>}*/
	var fsFunctionArgs = databaseManager.getFoundSet(globals.avBase_dbase_framework, "nav_function_arguments");
	//	fsFunctionArgs.loadRecords(0);

	if (fsFunction.find() || fsFunction.find()) {
		fsFunction.security_key_id = iKeyID;
		fsFunction.search();

		var iFunctionID;
		if (fsFunction.getSize() == 1 && fsFunction.security_key_id == iKeyID) {
			iFunctionID = fsFunction.function_id;
			//			fsFunction.deleteAllRecords();

			//			if (fsFunction.getSize() > 199) fsFunction.getRecord(databaseManager.getFoundSetCount(fsFunction));
			fsFunction.deleteAllRecords();
		}
	}

	if (fsFunctionArgs.find() || fsFunctionArgs.find()) {
		fsFunctionArgs.function_id = iFunctionID;
		fsFunctionArgs.search();

		if (fsFunctionArgs.getSize() == 1 && fsFunctionArgs.function_id == iFunctionID) {
			//			fsFunctionArgs.deleteAllRecords();

			//			if (fsFunctionArgs.getSize() > 199) fsFunctionArgs.getRecord(databaseManager.getFoundSetCount(fsFunctionArgs));
			fsFunctionArgs.deleteAllRecords();
		}
	}

	return;
}

/**
 * Writes the resource file for the report
 *
 * @author: Gary Dotzlaw, 2011-05-25
 *
 * @properties={typeid:24,uuid:"1F59759C-2185-4F6F-85FD-6B9B0A2D1C6A"}
 *
 * @returns {boolean}: TRUE or FALSE
 * @AllowToRunInFind
 */
function rptWriteResourceFile() {
	//	return true;

	var i;

	// Get all the resources for this report
	var oSQL = new Object();
	/***@type {String}*/
	var sSQL;
	sSQL = globals.avUtilities_appSqlQueryGet('SELECT', null, 'rpt_getResources', globals.avBase_dbase_version).query;
	oSQL.sql = sSQL;
	oSQL.args = [globals.owner_id];
	oSQL.table = "rpt_resources";
	/*** @type {JSFoundSet<db:/avanti/rpt_resources>} */
	var fsRes = globals.avUtilities_sqlFoundset(oSQL);

	if (fsRes.getSize() > 0) {
		// Get the i18n Keys for this report
		var rRes;
		var aMsgs = new Array();
		var aLabels = new Array();

		for (i = 1; i <= fsRes.getSize(); i++) {
			rRes = fsRes.getRecord(i);

			aMsgs.push(rRes.rpt_resources_to_i18n_messages.message_value);
			aLabels.push(rRes.message_key);

			//			aMsgs.push(i18n.getI18NMessage(rRes.message_key));
			//			aLabels.push(rRes.rpt_res_label);
		}

		// Write the resource file to the server
		//var sReportName = _to_rpt_report$avrpt_selectedreport.rpt_filename; // fsRes.rpt_resources_to_rpt_report.rpt_filename;
		var sPath = plugins.jasperPluginRMI.reportDirectory; //forms.rpt_report_dtl.getReportPath(_to_rpt_report$avrpt_selectedreport.is_system); //forms.rpt_report_dtl.getReportPath(fsRes.rpt_resources_to_rpt_report.is_system);
		var sResourceName = "i18nReport.properties"; //sReportName.slice(0, sReportName.search(/\./m));
		var jsFile = plugins.file.createFile(sPath + sResourceName);

		// Prepare the content
		var sOutput = ""; //"# Locale for report: " + sReportName + "\n";
		//		sOutput += "text.test=Hello World"
		for (i = 0; i < aMsgs.length; i++) {
			sOutput += aLabels[i] + "=" + aMsgs[i] + "\n";
		}

		// Write the file
		plugins.file.writeTXTFile(jsFile, sOutput);

		return true;
	}

	return false;
}

/**
 * Renders the Jasper report
 *
 * @author: Gary Dotzlaw, 2011-05-25
 *
 * @param {{aParamNames: Array, aParamValues: Array, whereClause: String, reportName: String}} [oFilters] - optional filter params being passed
 *
 * @properties={typeid:24,uuid:"53ADFF3A-9C9F-4A7A-9657-A99FF8D8A10C"}
 *
 * @returns nothing
 * @AllowToRunInFind
 */
function rptRenderReport(oFilters) {
	application.updateUI();

	var sCallBackFormMethod;
	var aDataArray = new Array();
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	
	
	/** @type {string}*/
	var currentTabFormName = globals.nav.getCurrentTabFormName() || "";
	var bModuleCRM = false;
	bModuleCRM = utils.stringTrim(globals.nav_program_name).substr(0, 3).toUpperCase() == "CRM" || globals.nav_program_name == "Activities"; // Activities program is under CRM module.
	
	//	sCallBackFormMethod = "forms.utils_dev_dtl.sayHello()";
	//	aDataArray = ["083761C3-F6F6-4071-9FF5-B456409C713B", "64EC3F18-9894-499D-9959-4C379CDEC745"];
	if (globals.avRpt_callBackFormMethod) {
		sCallBackFormMethod = globals.avRpt_callBackFormMethod;
		globals.avRpt_callBackFormMethod = null;
	}
	if (globals.avRpt_dataArray) {
		aDataArray = globals.avRpt_dataArray;
		globals.avRpt_dataArray = null;
	}

	// GD - Dec 23, 2014: supporting the app_report table
	/** @type {JSFoundSet<db:/avanti/rpt_report>}*/
	var fsReport = null;
	var fsRep = null;
	var rRep = null;
	var rRep_id = null;
	if (utils.hasRecords(_to_app_rpt_report$avrpt_selectedreport_base)) {
		fsReport = _to_app_rpt_report$avrpt_selectedreport_base;

		/** @type {JSFoundSet<db:/avanti/rpt_report>} */
		fsRep = scopes.avDB.getFS('rpt_report', ['app_rpt_id'], [fsReport.rpt_id]);
		/*** @type {JSRecord<db:/avanti/rpt_report>} */
		rRep = fsRep.getSelectedRecord();
		rRep_id = rRep.rpt_id;

	} else {
		fsReport = _to_rpt_report$avrpt_selectedreport;
		/** @type {JSFoundSet<db:/avanti/rpt_report>} */
		fsRep = scopes.avDB.getFS('rpt_report', ['rpt_id'], [fsReport.rpt_id]);
		/*** @type {JSRecord<db:/avanti/rpt_report>} */
		rRep = fsRep.getSelectedRecord();
		rRep_id = rRep.rpt_id;

	}
	//databaseManager.getFoundSet(globals.avBase_dbase_avanti, "rpt_report");
	//var sReportID = globals.avRpt_selectedReport;
	
	var i;

	
	
	// Find the report in question, or exit if it does not exist
	if (!utils.hasRecords(fsReport)) return false;

	// This is the report we are going to launch
	var sOldReportName = fsReport.rpt_filename;
	if (!sOldReportName) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportFilenameError_title"),
			i18n.getI18NMessage("avanti.dialog.reportFilenameError_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"));
		return false;
	}
	
	// If this is a velocity report, then use the alternate method
	if (fsReport.rpt_type == "V") {
		return rptVelocityReport(fsReport.getSelectedRecord());
	}

	// Set the Jasper Report directory
	plugins.jasperPluginRMI.relativeReportsDirectory = forms.rpt_report_dtl.getReportPath(fsReport.getSelectedRecord());
    
	// Write the resource file for i18n
	//	forms.rpt_report_dtl.rptWriteResourceFile();

	// The report has compiled, so use the .jasper in the name now
	var sReportName = sOldReportName.replace(".jrxml", ".jasper");
	
	if ((globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ReportPurchaseOrder_summarizedShippingLoc) == 1) && (sReportName == "purchaseOrder.jasper") )
	{
		sReportName    = "purchaseOrder_summarizedShippingLoc.jasper";
		sOldReportName = "purchaseOrder_summarizedShippingLoc.jrxml";
	}
	
	if ((globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ShippingLabelFormat) == 4) && (sReportName == "Standard Shipping Label_2_31x4PreprintedPerBox.jasper") )
    {
        sReportName    = "Standard Shipping Label_2_31x4Preprinted.jasper";
        sOldReportName = "Standard Shipping Label_2_31x4Preprinted.jrxml";
    }
	

	// GD - Apr 4, 2019: This was throwing an error in Servoy 8.4
	// Not sure we need this here; there should always be a compiled report on the server
    // Check to see if the report needs to be compiled
    if (sOldReportName.search(/\.jrxml/) > -1) {
        try {
            if (!plugins.jasperPluginRMI.compileReport(sOldReportName, sReportName)) {
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportFilenameError_title"),
                    i18n.getI18NMessage("avanti.dialog.reportFilenameError_msg"),
                    i18n.getI18NMessage("avanti.dialog.ok"));
                return false;
            }
        }
        catch (err) {
            scopes.avUtils.log("Error compiling report: " + sReportName, scopes.avUtils.LOGGER.Reporting, LOGGINGLEVEL.ERROR);
        }
    }
    else if (sOldReportName.search(/\.jasper/) > -1) {
        // Not a .jasper file either
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportFilenameError_title"),
            i18n.getI18NMessage("avanti.dialog.reportFilenameError_msg"),
            i18n.getI18NMessage("avanti.dialog.ok"));
        return false;
    }

   var bSingleReportFile = false;
	var bSystem = false;
	var sRpt_id = null;
	if (fsReport.is_system == 1) bSystem = true;
	if (bSystem && fsReport.app_rpt_id) {
		sRpt_id = fsReport.app_rpt_id;
	} else {
		sRpt_id = fsReport.rpt_id;
	}
	var oQuery = globals.avUtilities_appSqlQueryGet("REPORT", sRpt_id, null, null, bSystem);
	//	var oQuery = globals.avUtilities_appSqlQueryGet("REPORT", globals.UUIDtoString(fsReport.rpt_id), null, null, bSystem);
	if (!oQuery || oQuery.queryID != sRpt_id) //fsReport.rpt_id)
	{
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportSQLError_title"),
			i18n.getI18NMessage("avanti.dialog.reportSQLError_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"));
		return false;
	}
	var sSQL = oQuery.query;
	var sOrigSQL = oQuery.query;
	var sAlias = oQuery.alias;
	var sTable = oQuery.mainTable;
	var sKey = oQuery.pkReference;
	var sKeyNoAlias;
	if (sKey) sKeyNoAlias = sKey.replace(sAlias, "");
	if (sKey) sKeyNoAlias = sKeyNoAlias.replace(".", "");
	var rRec;
	var m;
	/**@type {Number}*/
    var currencyRate = 1;
    var currencyMethod = scopes.avUtils.CURRENCY_EXCHANGE_METHOD.Divide;
    /**@type {UUID} */
    var currency_id;
    var currencyFormat;
    
	//var aSelRecs = new Array();
	var aFileNames = new Array();
	var oParams = new Object();
	/**@type {JSRecord<db:/avanti/sa_order>} ***/
	var rOrd,
		rOrdH,
		/**@type {JSRecord<db:/avanti/prod_job>} ***/
		rJob;
	
	// SAAD - 2020-05-08 : Flag set to true If the report using pSQL with predefined Where Clause
	var bPredefinedWhereClause = false;
	
	if (fsReport.rpt_filter_form == 'rpt_report_dlg_endOfShiftReport' || fsReport.rpt_filter_form == 'rpt_report_dlg_InventoryShortageReport' || fsReport.rpt_filter_form == 'rpt_report_dlg_invoiceShipmentsDetail' || 
	    fsReport.rpt_filter_form == 'rpt_report_dlg_loadingReportByDepartment' || fsReport.rpt_filter_form ==  'rpt_report_dlg_loadingReportByEmployee' || fsReport.rpt_filter_form ==  'rpt_report_dlg_loadingReportByMachine' || 
	    fsReport.rpt_filter_form == 'rpt_report_dlg_work_in_process_by_csr' || fsReport.rpt_filter_form == 'rpt_report_dlg_salesOrderFinancialReport' || fsReport.rpt_filter_form == 'rpt_report_dlg_salesOrderStatusReport' ||
	    fsReport.rpt_filter_form == 'rpt_report_dlg_outstandingClosedPeriodTransactions' || fsReport.rpt_filter_form == 'rpt_report_dlg_accounting_postage_recon' || fsReport.rpt_filter_form == 'rpt_report_dlg_po_expediting_report' ||
	    fsReport.rpt_filter_form == 'rpt_report_dlg_purchaseReceiptsStockingReport_ByPO' || fsReport.rpt_filename == 'RequestforQuote_withSupplier.jrxml' || fsReport.rpt_filename == 'RequestforQuote.jrxml'||
	    fsReport.rpt_filter_form == 'rpt_report_dlg_marginReport' || fsReport.rpt_filter_form == 'rpt_report_dlg_SchedulingLoadingReportByDepartment' || fsReport.rpt_filter_form == 'rpt_report_dlg_shippingDetail' ||
	    fsReport.rpt_filter_form == 'rpt_report_dlg_work_order' || fsReport.rpt_filter_form == 'rpt_report_dlg_work_order_lineitem_labels' || fsReport.rpt_filter_form == 'rpt_report_dlg_timesheetReport' || 
		fsReport.rpt_filter_form == 'rpt_report_dlg_SchedulingloadingReportByMachine' || fsReport.rpt_filter_form == 'rpt_report_dlg_SchedulingloadingReportByEmployee' || fsReport.rpt_filter_form == 'rpt_report_dlg_salesreport_by_worktype'	|| 
		fsReport.rpt_filter_form == 'rpt_report_dlg_avanti_implementation_activity' ||	
		fsReport.rpt_filter_form == 'rpt_report_dlg_purchaseBackorderFlushReport_ByPO' || fsReport.rpt_filter_form == 'rpt_report_dlg_jobCosting' || fsReport.rpt_filter_form == 'rpt_report_dlg_job_shipping' ||
		fsReport.rpt_filter_form == 'rpt_report_dlg_custom_purchase_order_request'	|| fsReport.rpt_filter_form == 'rpt_report_dlg_params_divplant_date_eststatus' || fsReport.rpt_filter_form == 'rpt_report_dlg_document_mgmt') {
	    
	    bPredefinedWhereClause = true;
	}
		
	// GD - 2013-08-14: If we are to ignore pks, then do not build a where clause
	if (fsReport.rpt_flg_ignore_pks != 1) {
		// Build the WHERE clause
		if (globals.svy_nav_form_name == "rpt_report_dtl") {
			// Do not use any PKs in the WHERE
			sSQL = globals.avUtilities_sqlSpliceIn(sSQL, " WHERE " + sAlias + ".org_id = '" + globals.org_id + "' ", "ORDER BY");

		} else {
			var fsForm;
			//		var sKey = globals.avUtilities_tableGetPKName(sTable, globals.avBase_dbase_avanti);
			var aPKs = new Array();
			var sPKs = "";
			var iRevision = 0;
			if (aDataArray && aDataArray.length > 0) {
				aPKs = aDataArray;
			} else if (globals.svy_nav_form_name == 'rpt_reportUI_dtl') {
				sSQL = globals.avUtilities_appSqlQueryGet('SELECT', null, 'rpt_getBaseForm_byTable', globals.avBase_dbase_version).query;
				oSQL.sql = sSQL;
				oSQL.server = globals.avBase_dbase_framework;
				oSQL.args = [sTable, 1];
				var dsProgram = globals.avUtilities_sqlDataset(oSQL);

				if (dsProgram && dsProgram.getMaxRowIndex() > 0) {
					var sTblForm = dsProgram.getValue(1, 1) + "_tbl";
					fsForm = forms[sTblForm].foundset;

					aPKs = [];

					for (m = 1; m <= fsForm.getSize(); m++) {
						rRec = fsForm.getRecord(m);
						aPKs.push(rRec[sKeyNoAlias]);
					}
				}
			} else if (globals.svy_nav_form_name == "prod_job_view_tbl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				//				fsForm = forms["prod_job_view_tbl_main"].foundset; // GD - 2014-01-13: Changed this to a new form
				// GD - 2013-01-11: Just going to use the selected record for now
				/*** @type {JSRecord<db:/avanti/prod_job>} ***/
				var rFormRec = fsForm.getSelectedRecord();
				
				if (rFormRec) {
	                aPKs = [rFormRec.job_id];
	                
	                if (utils.hasRecords(rFormRec.prod_job_to_sa_order_revision_detail)
	                        && utils.hasRecords(rFormRec.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header)
	                        && utils.hasRecords(rFormRec.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revh_doc)) {
	                    scopes.avDocuments.checkDocImageBlob(rFormRec.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revh_doc, "JOB");
	                }
				
				if (sReportName == "JobTicket_Custom.jasper") {
					aPKs = [rFormRec.job_id];
				}
				
				
				}
				
			} else if (globals.svy_nav_form_name == "sa_invoice_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/sa_invoice_register>} ***/
				var rFormRec3 = fsForm.getSelectedRecord();
				aPKs = [rFormRec3.invreg_id];
			} 
			else if (globals.svy_nav_form_name == "prod_receipt_dtl") {
                fsForm = forms[globals.svy_nav_form_name].foundset;
                sKey = 'receipt_id';
                aPKs = [fsForm.getSelectedRecord()['prodrec_id']];
            }  
			else if (globals.svy_nav_form_name == "po_receipt_dtl") {
                fsForm = forms[globals.svy_nav_form_name].foundset;
				if (sReportName == 'Label_Roll_Placard.jasper') {
					sKey = 'intraneh_or_porec_id';
				} 
				else {
					sKey = 'receipt_id';
				}
                aPKs = [fsForm.getSelectedRecord()['porec_id']];
                processReceiptsFromProductionForPOReceipt(aPKs[0]);
            }
			// Manifest Report Validation
            else if ((globals.svy_nav_form_name == "sa_pack_tbl" || globals.svy_nav_form_name == "sa_pack_dtl") && fsReport.rpt_name == 'Manifest Report') {
            	fsForm = forms[globals.svy_nav_form_name].foundset;
                sKey = 'pack_id';
                aPKs = [fsForm.getSelectedRecord()['pack_id']];
                var nCheckGlobaladdr = 0;
                nCheckGlobaladdr =	fsForm["globaladdr_active"];
                if (nCheckGlobaladdr == null || nCheckGlobaladdr ==0){
                	nCheckGlobaladdr =  0;
                }
                if(nCheckGlobaladdr == 0){
                	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
            			i18n.getI18NMessage("avanti.dialog.validationGlobalAddress"),
            			i18n.getI18NMessage("avanti.dialog.ok"));
            		return false;
                }
                if (fsForm["pack_status"] != 'C' && fsForm["pack_status"] != 'P'){
                	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
            			i18n.getI18NMessage("avanti.dialog.validationPackingSlipStatus"),
            			i18n.getI18NMessage("avanti.dialog.ok"));
            		return false;
                }
            } 
            else if (globals.svy_nav_form_name == "po_receipt_tbl") {
                fsForm = forms[globals.svy_nav_form_name].foundset;
                
                if (sReportName == 'Label_Roll_Placard.jasper') {
            		sKey = 'intraneh_or_porec_id';	
            	}
            	else {
            		sKey = 'receipt_id';	
            	}

                /*** @type {JSRecord<db:/avanti/po_receipt>} ***/
                var rPOReceipt;
                var sTransNum;
                aPKs = [];
                aFileNames = [];
                
                for (var r = 1; r <= fsForm.getSize(); r++) {
                    rPOReceipt = fsForm.getRecord(r);
                    
                    if (rPOReceipt.print_check_flag == 1) {
                        if (utils.hasRecords(rPOReceipt.po_receipt_to_in_item_trans_header)) {
                            sTransNum = rPOReceipt.po_receipt_to_in_item_trans_header.itemtransh_transaction_no;
                        }
                        else {
                            sTransNum = null;
                        }
                        
                        aPKs.push(rPOReceipt.porec_id);
                        aFileNames.push(i18n.getI18NMessage("avanti.lbl.BoxLabelForPOReceiptTransaction") + " " + sTransNum);

                        processReceiptsFromProductionForPOReceipt(rPOReceipt.porec_id);
                    }
                }
                
                if (aPKs.length == 0) {
                    aPKs = [fsForm.getSelectedRecord()['porec_id']];
                    processReceiptsFromProductionForPOReceipt(aPKs[0]);
                }
            } 
			else if (globals.svy_nav_form_name == "sa_cash_receipt_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/sa_cash_receipt_register>} ***/
				var rCashReceiptReg = fsForm.getSelectedRecord();
				aPKs = [rCashReceiptReg.sacr_reg_id];
			} else if (globals.svy_nav_form_name == "in_transaction_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/in_transaction_register>} ***/
				var rInvRegPK = fsForm.getSelectedRecord();
				aPKs = [rInvRegPK.itreg_id];
			} else if (globals.svy_nav_form_name == "prod_receipt_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/prod_receipt_register>} ***/
				var rProdRecRegPK = fsForm.getSelectedRecord();
				aPKs = [rProdRecRegPK.prodrec_reg_id];
			} else if (globals.svy_nav_form_name == "po_receipt_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/po_receipt_register>} ***/
				var rFormRec4 = fsForm.getSelectedRecord();
				aPKs = [rFormRec4.porreg_id];   
			} else if (globals.svy_nav_form_name == "sf_login") { // && fsReport.rpt_name == 'Shipping Label'
			
                 /**Array<UUID> *
                 * @properties={typeid:35,uuid:"A195F053-04C8-4D5A-8121-0BC383A8109A",variableType:-4}
                 */
                var aLabelBoxIDs = new Array();

                /** @type {JSFoundSet<db:/avanti/prod_job_box_label_box>} */
                var fsShoplabels = forms['sf_box_labels'].createselectedLabelsFS();
                
                /** @type {JSRecord<db:/avanti/prod_job_box_label_box>} */
                var rShopfloorlblBoxes;

                for (var q = 1; q <= fsShoplabels.getSize(); q++) {
                    rShopfloorlblBoxes = fsShoplabels.getRecord(q);
                    aLabelBoxIDs.push(rShopfloorlblBoxes.pjblb_id);
                }
                                
                for (var t = 0; t < aLabelBoxIDs.length; t++) {

                    aPKs.push(aLabelBoxIDs[t]);
                }
                
                bSingleReportFile = true;

			} else if (globals.svy_nav_form_name == "prod_daily_entry_register_dtl") {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/prod_job_cost_register>} ***/
				var rjobCostReceiptReg = fsForm.getSelectedRecord();
				aPKs = [rjobCostReceiptReg.jc_reg_id];
			}
			// sl-2096
			else if (globals.svy_nav_form_name == "sf_main_dtl" && fsReport.rpt_name == 'Job Requirements Report') {
				sKey = 'job_number'
				aPKs.push(forms['sf_main_dtl'].foundset.job_number);
				aFileNames.push(forms['sf_main_dtl'].foundset.job_number);
			}
			 else if(globals.svy_nav_form_name =='in_trans_entry_header_main' || globals.svy_nav_form_name =='in_trans_entry_header_dtl'){
                fsForm = forms[globals.svy_nav_form_name].foundset;
                /** @type {JSRecord<db:/avanti/in_trans_entry_header>} */
                var rInventoryTran = fsForm.getSelectedRecord();
                var typeCheck = rInventoryTran.intranstype_id;
                
                if (sReportName == 'Inventory Transaction Transfer Slip.jasper' && typeCheck != globals.avBase_Inventory_GetTransactionTypeUUID('IT','WTO')) {
                	
                	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
            			i18n.getI18NMessage("avanti.dialog.validationTransferSlip"),
            			i18n.getI18NMessage("avanti.dialog.ok"));
            		return false;
                }
                else if (sReportName == 'Label_Roll_Placard.jasper' && typeCheck != globals.avBase_Inventory_GetTransactionTypeUUID('IT','RBTO')) {

                	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
            			i18n.getI18NMessage("avanti.dialog.validationTransferSlip"),
            			i18n.getI18NMessage("avanti.dialog.validationRollPlacard"),
            			i18n.getI18NMessage("avanti.dialog.ok"));
            		return false;
                }
                else if (sReportName == 'Replenishment.jasper' && typeCheck != globals.avBase_Inventory_GetTransactionTypeUUID('IT','BTO')) {
                	
                	globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
            			i18n.getI18NMessage("avanti.dialog.validationReplenishmentReport"),
            			i18n.getI18NMessage("avanti.dialog.ok"));
            		return false;
                }
               
                if (typeCheck == globals.avBase_Inventory_GetTransactionTypeUUID('IT','WTO') && rInventoryTran.intraneh_posted_date == null) {
                    
                    globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.warning"),
                        i18n.getI18NMessage("avanti.dialog.validationTransferSlipPostedDate"),
                        i18n.getI18NMessage("avanti.dialog.ok"));
                    return false;
                    
                }
                else if ((typeCheck == globals.avBase_Inventory_GetTransactionTypeUUID('IT','WTO') && rInventoryTran.intraneh_posted_date != null)
                		|| (typeCheck == globals.avBase_Inventory_GetTransactionTypeUUID('IT','RBTO') && sReportName == 'Label_Roll_Placard.jasper')
						|| (typeCheck == globals.avBase_Inventory_GetTransactionTypeUUID('IT','BTO') && sReportName == 'Replenishment.jasper')){
 
                	if (sReportName == 'Label_Roll_Placard.jasper') {
                		sKey = 'intraneh_or_porec_id';	
                	}
                	else if(sReportName == 'Replenishment.jasper'){
                		sKey = 'transh.intraneh_id';
                	}
                	else {
                		sKey = 'intraneh_id';	
                	}
                }
                else {
                	 
                	sKey = 'receipt_id';
                	
                }
                
                aPKs = [rInventoryTran.intraneh_id];

           }
			 else if (globals.svy_nav_form_name == "sa_invoice_tbl" || 
					 (bModuleCRM && (currentTabFormName == "crm_customer_invoice_tbl" || currentTabFormName == "crm_job_invoice_tbl"))) {
				if (bModuleCRM && (currentTabFormName == "crm_customer_invoice_tbl" || currentTabFormName == "crm_job_invoice_tbl")) {
				    if (currentTabFormName == "crm_job_invoice_tbl") {
				        /**@type {JSFoundSet<db:/avanti/sa_invoice_det>}*/
				        var fsDetForm = forms[currentTabFormName].foundset;
				        fsForm = fsDetForm.sa_invoice_det_to_sa_invoice;
				    }
				    else {
				        fsForm = forms[currentTabFormName].foundset;
				    }
				}
				else {
				    fsForm = forms[globals.svy_nav_form_name].foundset;
				}
				/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
				var rInvoice;

				//Loop selected Invoices
				var iMax = fsForm.getSize();
				for (var inv = 1; inv <= iMax; inv++) {
					rInvoice = fsForm.getRecord(inv);
					if (rInvoice.print_check_flag == 1) {
						aPKs.push(rInvoice.inv_id);
						aFileNames.push(i18n.getI18NMessage("avanti.lbl.invoice") + " " + rInvoice.inv_number + " - " + rInvoice.inv_address_name);
					}
				}

				// calculating the currency rate for selected estimate record
                currencyRate = 1;
                
                currency_id = fsForm["curr_id"];
                
                // need to copy this code for credit note
                if (globals.svy_nav_form_name == "sa_invoice_tbl" ){
                    /**@type {Date}*/
                    var estimateDate;
                    estimateDate = plugins.DateUtils.dateFormat(fsForm["inv_date"], 'yyyy-MM-dd');
                }
                
				if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackCurrencyOnEstSOInv) == 1 && currency_id) {

					currencyRate = fsForm["inv_exchange_rate"];
					
					currencyRate = !currencyRate ? 1 : currencyRate;

				} 
				else if (estimateDate && currency_id) {

					currencyRate = globals.getEstimateCurrencyRate(estimateDate, currency_id.toString());

					currencyRate = !currencyRate ? 1 : currencyRate;
				}
                
				oParams.pCurrencyRate = currencyRate;
				oParams.pCurrExchangeMethod = globals.getCurrencyMethod(currency_id.toString());
				
                currencyFormat = globals.avBase_getCurrencyFormat(currency_id, false);

                oParams.pOrgCurrencyFormat = currencyFormat.slice(0, currencyFormat.search(/\|/));
                if (oParams.pOrgCurrencyFormat == '€#.##0,00'){
                    oParams.pOrgCurrencyFormat = '€#,###.00'; // Temporary until we can use the proper format
                }
                
				//if no selections, use the selected invoice
				if (aPKs.length == 0) {
					aPKs = [fsForm[sKeyNoAlias]];
				}
			
				rInvoice = fsForm.getSelectedRecord();

                if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_doc)) {
                    scopes.avDocuments.checkINVDocImageBlob(rInvoice, "INV");
                }
                
                
			 
			 } else if (globals.svy_nav_form_name == "sa_credit_invoice_tbl" || globals.svy_nav_form_name == "sa_credit_invoice_dtl" ) {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
				var rCreditInvoice;

				//Loop selected Invoices
				var icMax = fsForm.getSize();
				for (var creinv = 1; creinv <= icMax; creinv++) {
					rCreditInvoice = fsForm.getRecord(creinv);
					if (rCreditInvoice.print_check_flag == 1) {
						aPKs.push(rCreditInvoice.inv_id);
						aFileNames.push(i18n.getI18NMessage("avanti.lbl.creditNote") + " " + rCreditInvoice.inv_number + " - " + rCreditInvoice.inv_address_name);
					}
				}

				
                // calculating the currency rate for selected estimate record
                /**@type {Number}*/
                var currencyRateCR = 1;
                
                /**@type {UUID} */
                var currency_idCR = fsForm["curr_id"];
                
                /**@type {Date}*/
                var creditNoteDate;
                
                if (globals.svy_nav_form_name == "sa_invoice_tbl" || globals.svy_nav_form_name == "sa_invoice_dtl"){
                    
                    creditNoteDate = plugins.DateUtils.dateFormat(fsForm["inv_date"], 'yyyy-MM-dd');
                }
                
				if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackCurrencyOnEstSOInv) == 1 && currency_idCR) {

					currencyRateCR = fsForm["inv_exchange_rate"];
					
					currencyRateCR = !currencyRateCR ? 1 : currencyRateCR;

				} 
				else if (creditNoteDate && currency_idCR) {
                    currencyRateCR = globals.getEstimateCurrencyRate(estimateDate,currency_idCR.toString());

                    currencyRateCR = !currencyRateCR ? 1 : currencyRateCR;
                }
                
				oParams.pCurrencyRate = currencyRateCR;
				oParams.pCurrExchangeMethod = globals.getCurrencyMethod(currency_idCR.toString());
                
                var currencyFormatCR= globals.avBase_getCurrencyFormat(currency_idCR, false);
                oParams.pOrgCurrencyFormat = currencyFormatCR.slice(0, currencyFormatCR.search(/\|/));
                if (oParams.pOrgCurrencyFormat == '€#.##0,00'){
                    oParams.pOrgCurrencyFormat = '€#,###.00'; // Temporary until we can use the proper format
                }				
                
                
                //if no selections, use the selected invoice
				if (aPKs.length == 0) {
					rCreditInvoice = fsForm.getSelectedRecord();
					aPKs.push(rCreditInvoice.inv_id);
				}
			}else if ( (globals.svy_nav_form_name == "sa_order_dtl" || globals.svy_nav_form_name == "sa_order_tbl") && (globals.avSales_PrintJobTicketFlag == 1 || sReportName == "Job Ticket By KIT.jasper"
						)) //Printing job tickets from sales order release dialog
			{
			    
			    fsForm = forms[globals.svy_nav_form_name].foundset;
                rOrd = fsForm.getSelectedRecord();
                if (utils.hasRecords(rOrd.sa_order_to_sa_order_revision_header$active.sa_order_revision_header_to_sa_order_revh_doc)) {
                    scopes.avDocuments.checkDocImageBlob(rOrd.sa_order_to_sa_order_revision_header$active.sa_order_revision_header_to_sa_order_revh_doc, "EST");
                }

				bSingleReportFile = true; // we want all the job ticket in one PDF so the user can print them all out with a single click.
				
				// GD - Jun 23, 2016: SL-9131 - Only print the jobs for the sections the user as selected
				if (globals.avSales_PrintJobTicketSections && globals.avSales_PrintJobTicketSections != "all") {
					
					var sSections = globals.avSales_PrintJobTicketSections.replace("\n",","),
						aSections = sSections.split(","),
						/** @type {JSRecord<db:/avanti/sa_order_revision_detail_section>} */ 
						rSection = null;
						
					for (i = 0; i < aSections.length; i++) {
						
						rSection = scopes.avDB.getRec("sa_order_revision_detail_section",["ordrevds_id"],[aSections[i]]);

						if (rSection && rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.job_id) {
							
							if (aPKs.indexOf(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.job_id) == -1) {
								
								aPKs.push(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.job_id);
							}
						}
					}
				} else if (utils.hasRecords(forms[globals.svy_nav_form_name].foundset.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail)
						&& sReportName == "Job Ticket By KIT.jasper"){
					
						/** @type {JSRecord<db:/avanti/sa_order>} */ 
						var jRec;
					    var fsLineitems = rOrd.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail;
							for (var index = 1; index <= fsLineitems.getSize(); index++) {
		                	jRec = fsLineitems.getRecord(index);
		                	aPKs.push(jRec.job_id);
		                }
						
					} else {
					
					for (i = 0; i < globals.avSales_PrintJobTicketArray.length; i++) {
						
						aPKs.push(globals.avSales_PrintJobTicketArray[i]);
						
					}
				}
			} else if (globals.svy_nav_form_name == "sa_order_estimate_tbl" || globals.svy_nav_form_name == "sa_order_estimate_dtl" || 
			           globals.svy_nav_form_name == "sa_invoice_tbl" || globals.svy_nav_form_name == "sa_invoice_dtl" || 
			           (bModuleCRM && currentTabFormName == "sa_order_estimate_crm_tbl")) {
			               
                if (bModuleCRM && currentTabFormName == "sa_order_estimate_crm_tbl") {
                    fsForm = forms[currentTabFormName].foundset;
                    aPKs = [fsForm[sKeyNoAlias]];
                }
                else {
                    fsForm = forms[globals.svy_nav_form_name].foundset;
                    aPKs = [fsForm[sKeyNoAlias]];
                }
				/**@type {Date}*/
				estimateDate;
				
				if(globals.svy_nav_form_name.search("sa_order") > -1 || (bModuleCRM && currentTabFormName.search("sa_order") > -1))
				{
					estimateDate = plugins.DateUtils.dateFormat(fsForm["ordh_order_date"], 'yyyy-MM-dd');
					
					iRevision = fsForm["sa_order_to_sa_order_revision_header$avsales_selectedrevisionheaderid.ordrevh_revision"];
				}
				else
				{
					estimateDate = plugins.DateUtils.dateFormat(fsForm["inv_date"], 'yyyy-MM-dd');
					
					iRevision = 1;
				}
				
				rOrd = fsForm.getSelectedRecord();
                if (rOrd.getDataSource() == 'db:/avanti/sa_order' && utils.hasRecords(rOrd.sa_order_to_sa_order_revision_header)) {
                    if (utils.hasRecords(rOrd.sa_order_to_sa_order_revision_header$active.sa_order_revision_header_to_sa_order_revh_doc)) {
                        scopes.avDocuments.checkDocImageBlob(rOrd.sa_order_to_sa_order_revision_header$active.sa_order_revision_header_to_sa_order_revh_doc, "EST");
                    }
                }
				
				// calculating the currency rate for selected estimate record
				currencyRate = 1;
				
				currency_id = fsForm["curr_id"];
				
				if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackCurrencyOnEstSOInv) == 1 && currency_id) {

					if ( (utils.hasRecords(rOrd.sa_order_to_sa_order_revision_header) 
							&& (globals.svy_nav_form_name == "sa_order_estimate_tbl" || globals.svy_nav_form_name == "sa_order_estimate_dtl" 
								|| (bModuleCRM && currentTabFormName == "sa_order_estimate_crm_tbl")))) {

						currencyRate = rOrd.sa_order_to_sa_order_revision_header.ordrevh_exchange_rate;
						currencyRate = !currencyRate ? 1 : currencyRate;

					} else if (globals.svy_nav_form_name == "sa_invoice_tbl" || globals.svy_nav_form_name == "sa_invoice_dtl") {

						currencyRate = fsForm["inv_exchange_rate"];
						currencyRate = !currencyRate ? 1 : currencyRate;

					}

				} else if (estimateDate && currency_id) {

					currencyRate = globals.getEstimateCurrencyRate(estimateDate, currency_id.toString());

					currencyRate = !currencyRate ? 1 : currencyRate;
				}


                
				oParams.pCurrencyRate = currencyRate;
				oParams.pCurrExchangeMethod = globals.getCurrencyMethod(currency_id.toString());	
				
				currencyFormat= globals.avBase_getCurrencyFormat(currency_id, false);
				
				oParams.pOrgCurrencyFormat = currencyFormat.slice(0, currencyFormat.search(/\|/));
				if (oParams.pOrgCurrencyFormat == '€#.##0,00'){
				    oParams.pOrgCurrencyFormat = '€#,###.00'; // Temporary until we can use the proper format
				}
				
				
				/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
				var rDocInvoice;
				
				rDocInvoice = fsForm.getSelectedRecord();
				
				if (utils.hasRecords(rDocInvoice.sa_invoice_to_sa_invoice_doc)) {
                    scopes.avDocuments.checkINVDocImageBlob(rDocInvoice, "INV");
                }
                
			}  
			else if ((globals.svy_nav_form_name == "sa_postage_recon_dtl" || globals.svy_nav_form_name == "sa_postage_recon_tbl") 
			        && utils.hasRecords(forms[globals.svy_nav_form_name].foundset.sa_postage_reconciliation_to_sa_order_revds_task_post)) {
			        fsForm = forms[globals.svy_nav_form_name].foundset.sa_postage_reconciliation_to_sa_order_revds_task_post;
			        
			        aPKs.push(fsForm.getRecord(1)['pr_id']);
			}
			else if (globals.svy_nav_form_name == "po_purchase_tbl" || globals.svy_nav_form_name == "po_purchase_dtl" 
			         || globals.svy_nav_form_name == "po_planned_dtl") {
                var bPrintAll = false;
                if (globals.svy_nav_form_name == "po_planned_dtl") {
                    fsForm = forms["po_planned_detail_purchase_order_list"].foundset;                    
                    bPrintAll = true;
                }
                else {
                    fsForm = forms[globals.svy_nav_form_name].foundset;
                }

                /*** @type {JSRecord<db:/avanti/po_purchase>} ***/
                var poRec;
                iMax = fsForm.getSize();
                var oPoParams = { };
                for (index = 1; index <= iMax; index++) {
                    poRec = fsForm.getRecord(index);
                    if (bPrintAll || poRec.print_check_flag == 1) {
                        aPKs.push(poRec.po_id);
                        aFileNames.push(i18n.getI18NMessage("avanti.lbl.purchaseOrder") + " " + poRec.po_document_number);
                        oPoParams[poRec.po_id] = scopes.avReporting.getPOParamObject(poRec);
                    }
                }

                if (aPKs.length == 0) {
                    poRec = fsForm.getSelectedRecord();
                    aPKs = [poRec.po_id];
                    var poParam = scopes.avReporting.getPOParamObject(poRec);
                    oParams['pHasDropShipItem'] = poParam.pHasDropShipItem;
                    oParams['pQuery'] = poParam.pQuery;
                    oPoParams[poRec.po_id] = poParam;
                }
                
              
                poRec = fsForm.getSelectedRecord();

                if (utils.hasRecords(poRec.po_purchase_to_sys_document)) {
                    scopes.avDocuments.checkPODocImageBlob(poRec, "PO");
                }
                
			}
			else if (globals.svy_nav_form_name == "sa_pack_batch_dtl") {
				bSingleReportFile = true;
				var p;

				// labels - keep packs to print in array
				if(forms['sa_pack_dtl'].aPackIDsToPrint.length > 0){
					for(p=0; p<forms['sa_pack_dtl'].aPackIDsToPrint.length; p++){
						aPKs.push(forms['sa_pack_dtl'].aPackIDsToPrint[p]);
					}
				}
				// packing slip
				else{
					fsForm = forms['sa_pack_batch_det'].foundset;

					for(p=1; p<=fsForm.getSize(); p++){
						aPKs.push(fsForm.getRecord(p)['pack_id']);
					}
				}
			}
			//Not sure yet how to proceed
			//		else if (globals.svy_nav_form_name == "sa_invoice_tbl")
			//		{
			//			fsForm = forms[globals.svy_nav_form_name].foundset;
			//			/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
			//			var rFormRec3 = fsForm.getSelectedRecord();
			//			aPKs = [rFormRec3.inv_id];
			//		}
			else if (globals.svy_nav_form_name.search("_dtl") > -1) {
				fsForm = forms[globals.svy_nav_form_name].foundset;
				aPKs = [fsForm[sKeyNoAlias]];
				iRevision = fsForm["sa_order_to_sa_order_revision_header$avsales_selectedrevisionheaderid.ordrevh_revision"];
			} else // GD - 2013-06-17: Any other form, use the selected record only
			{
				fsForm = forms[globals.svy_nav_form_name].foundset;
				fsForm = forms[globals.svy_nav_form_name].foundset;
				aPKs = [fsForm[sKeyNoAlias]];
			}
			
			// sl-2096 - added 1st,2nd and last conditions here
			if (sKey && aPKs && aPKs.length > 0 && aPKs[0]) {
				for (i = 0; i < aPKs.length; i++) {					
					if(aPKs[i] == null){
						continue;
					}					
					if (i == aPKs.length - 1) {
						sPKs += "'" + aPKs[i] + "'";
					} else {
						sPKs += "'" + aPKs[i] + "',";
					}
				}
				if(sPKs != '' && sPKs.length > 0){
					if(sPKs.substr(sPKs.length - 1) == ','){					
						sPKs = sPKs.slice(0, -1);
					}
				}	
				
				// GD - May 20, 2014: Need to add condition for revision 0 only, in case of change orders
				if (globals.svy_nav_form_name == "prod_job_view_tbl") {
					sSQL = globals.avUtilities_sqlSpliceIn(sSQL, " WHERE " + sAlias + ".ordrevh_revision = 0 AND " + sAlias + ".org_id = '" + globals.org_id + "' AND " + sKey + " IN (" + sPKs + ") ", "ORDER BY");
				} else {
					sSQL = globals.avUtilities_sqlSpliceIn(sSQL, " WHERE " + sAlias + ".org_id = '" + globals.org_id + "' AND " + sKey + " IN (" + sPKs + ") ", "ORDER BY");
				}
			} else {
				globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.runReport_title"),
					i18n.getI18NMessage("avanti.dialog.runReport_msg"),
					i18n.getI18NMessage("avanti.dialog.ok"));

				return false;
			}

		}
	} 
	// SAAD May 2020 : Skip the below statement to not insert the ORG_ID if the report is already using where clause with org_id
	else if(!bPredefinedWhereClause) {
	    sSQL = globals.avUtilities_sqlSpliceIn(sSQL, " WHERE " + sAlias + ".org_id = '" + globals.org_id + "' ", "ORDER BY");
	}

	
	//Pre Report Validation
    if ((globals.svy_nav_form_name.substr(0, 11) == "sa_invoice_" || globals.svy_nav_form_name.substr(0, 18) == "sa_credit_invoice_") && sOutputFormat != 'view' && !bModuleCRM) {
        fsForm = forms[globals.svy_nav_form_name].foundset;
		var fsInvoice = fsForm.duplicateFoundSet();

		if (aPKs) {
			for (var j = 0; j < aPKs.length; j++) {
				sPK = aPKs[j];

				if (fsInvoice.selectRecord(application.getUUID(sPK))) {
					/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
					var rInv = fsInvoice.getSelectedRecord();
                    try {
                        scopes.avTax.sAvalaraAPIKey = _to_sys_organization.org_avalara_api_key;

                        // sl-15760 - added inv_status conditions - only call avalara if changing from open to printed
                        // sl-16292 - added pPrintStatus condition - only call avalara tax for final print
                        if (scopes.avTax.sAvalaraAPIKey && _to_sys_organization.org_avalara_calc_tax_inv_final
                                && (rInv.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
                                    || (rInv.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.Invoice && getFilterPropertyValue("pPrintStatus") == "P"))
                                    && rInv.inv_status != 'P' && rInv.inv_status != 'U') {

                            var nOriginalTax = rInv.inv_salestax_amt;
                            
                            var bAvalaraSuccess;
                            if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AvalaraTaxTransaction)) {
                            	bAvalaraSuccess = scopes.avTax.calculateTaxForInvoiceAvalara(rInv, true);
                            }
                            else {
                            	bAvalaraSuccess = scopes.avTax.calculateTaxForInvoiceAvalara(rInv, false);
                            }
                            
                            if (bAvalaraSuccess 
                                    && nOriginalTax != rInv.inv_salestax_amt) {
                                        
								if (_to_sys_organization.org_avalara_recalc_auto_accept != 1 && scopes.avText.showYesNoQuestion('taxAmountIsDifferentThanOriginal') != scopes.avText.yes) {
                                    rInv.inv_status = 'O';
                                    return false;
								}
								scopes.globals["avInvoice_recalulateInvoiceTotal"](rInv, null, null, null, null, true);
                            }
                        }
                    }
                    catch (ex) {
                        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), ex.message, i18n.getI18NMessage('avanti.dialog.ok'));
                    }
				}
			}
		}
	}
	
	// Build the parameters
	if (_to_sys_organization.report_header) {
	    oParams.pHeader = _to_sys_organization.report_header;
	}
	if (_to_sys_organization.report_footer) {
	    oParams.pFooter = _to_sys_organization.report_footer;
	}
	if (_to_sys_organization.report_form_logo) {
	    oParams.pFormLogo = _to_sys_organization.report_form_logo;
	}
	oParams.SUBREPORT_DIR = "\\";
	oParams.pTitle = globals.avRpt_reportTitle; //fsReport.rpt_name;
	oParams.pOrgID = globals.org_id;
	
	if (globals.org_name == null) {
		
		if(globals.org_id != null) {
		
			/***@type {JSRecord<db:/avanti/sys_organization>} */
	        var rOrg = scopes.avDB.getRec('sys_organization', ['org_id'], [globals.org_id]);
	        oParams.pOrgName = globals.org_name = rOrg.org_name;
		}
		else {
			oParams.pOrgName = " ";
		}
		
	}
	else {
			oParams.pOrgName = globals.org_name;	
	}
	
	oParams.pSQL = sSQL;
	oParams.pRev = iRevision;
	
    if (globals.avBase_employeeUUID != null) {
    	/***@type {JSRecord<db:/avanti/sys_employee>} */
        var rEmp = scopes.avDB.getRec('sys_employee', ['empl_id'], [globals.avBase_employeeUUID]);
    	
        if(rEmp) {
        	oParams.pPrintedbyEmp = rEmp.empl_full_name;	
        }
        else {
        	oParams.pPrintedbyEmp = "";
        }
    }
    else {
    	oParams.pPrintedbyEmp = "";
    }
    
	// GD - May 30, 2016: SL-8809 - Get the sections into an array and pass as param to report
	if (globals.avSales_PrintJobTicketSections && globals.avSales_PrintJobTicketSections != "all") {
		
		oParams.pSections = globals.avSales_PrintJobTicketSections.replace("\n",",");
		
	} else {
		
		oParams.pSections = null;
	}
	
	// GD - 2014-02-09: For Change Orders
	if (globals.svy_nav_form_name === 'sa_order_tbl') {

		rOrd = forms["sa_order_tbl"].foundset.getSelectedRecord();
		rOrdH = rOrd.sa_order_to_sa_order_revision_header$first_rev.getRecord(1); //rOrd.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1);
		if (rOrdH != null) {
			oParams.pRev = rOrdH.ordrevh_revision;
			oParams.pOrdH = rOrd.ordh_id.toString();
		}

	} else if (globals.svy_nav_form_name === 'prod_job_view_tbl' || globals.svy_nav_form_name === 'sf_main_dtl') {

		rJob = forms["prod_job_view_tbl"].foundset.getSelectedRecord();
		
		if (rJob && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail)
		        && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header)
		        && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order)) {
            rOrd = rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order;
            rOrdH = rOrd.sa_order_to_sa_order_revision_header$first_rev.getRecord(1);
            oParams.pRev = rOrdH.ordrevh_revision;
            oParams.pOrdH = rOrd.ordh_id.toString(); 
        }

	} else if (globals.svy_nav_form_name === 'sa_order_estimate_tbl' || globals.svy_nav_form_name === 'sa_order_estimate_dtl' || (bModuleCRM && currentTabFormName === "sa_order_estimate_crm_tbl")) {
        if (bModuleCRM && currentTabFormName === "sa_order_estimate_crm_tbl") {
            rOrd = forms[currentTabFormName].foundset.getSelectedRecord();
        }
        else {
            rOrd = forms[globals.svy_nav_form_name].foundset.getSelectedRecord();
        }
		// GD - Aug 3, 2016: SL-9258: Estimates need to use the selected revision, not the first revision
		rOrdH = rOrd.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1);
		
		if (rOrdH != null) {
			oParams.pRev = rOrdH.ordrevh_revision;
			oParams.pOrdH = rOrd.ordh_id.toString();
		}
	} else if (globals.svy_nav_form_name === 'sa_order_dtl'){

		// GD - Jun 10, 2016: Grab these parameters from Orders
		rOrd = forms["sa_order_dtl"].foundset.getSelectedRecord();
		if (fsReport.rpt_filename.search("_chgOrd") > - 1) {
			
			rOrdH = rOrd.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1);
			
		} else {
			
			rOrdH = rOrd.sa_order_to_sa_order_revision_header$first_rev.getRecord(1); //rOrd.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1);
			
		}
		if (rOrdH != null) {
			oParams.pRev = rOrdH.ordrevh_revision;
			oParams.pOrdH = rOrd.ordh_id.toString();
		}
	}

	// GD - 2013-08-14: Inserting optional filter params
    if (oFilters) {
        
    	if (oFilters.aParamNames) {
    		for (i = 0; i < oFilters.aParamNames.length; i++) {
                oParams[oFilters.aParamNames[i]] = oFilters.aParamValues[i];
            }	
    	}
    	

        // Splice in the Where clause
        if (oFilters.whereClause) {
            sSQL = globals.avUtilities_sqlSpliceIn(oParams.pSQL, oFilters.whereClause, "ORDER BY");
            oParams.pSQL = sSQL;
        }

        if (oFilters.reportName) {
            globals.avRpt_reportTitle = oFilters.reportName;
        }

    }

	//	application.output("Revision to print: " + iRevision)

	// GD - 2013-04-01: Now add all the report parameters

	//	oSQL.sql = globals.avUtilities_appSqlQueryGet('SELECT', null, '', globals.avBase_dbase_version).query;
	oSQL.sql = "SELECT p.pref_number, \
				CASE WHEN sp.syspref_value IS NOT NULL THEN sp.syspref_value ELSE p.pref_default_value END AS prefValue, \
				p.pref_data_type \
				FROM app_preference p \
				LEFT OUTER JOIN sys_preference sp ON p.pref_id = sp.pref_id \
				WHERE p.prefnode_id IN \
				( \
				SELECT pn.prefnode_id FROM app_preference_node pn \
				) AND sp.org_id = ?"
	oSQL.args = [globals.org_id];
	var dsData = globals.avUtilities_sqlDataset(oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0) {
		var nMaxRowIndex = dsData.getMaxRowIndex();
		for (i = 1; i <= nMaxRowIndex; i++) {
			if (dsData.getValue(i, 3) == "I") {
				oParams["p" + dsData.getValue(i, 1)] = parseInt(dsData.getValue(i, 2));
			} else {
				oParams["p" + dsData.getValue(i, 1)] = dsData.getValue(i, 2);
			}
		}
	}

	// Set date and currency format
	oParams.formatDate = globals.avBase_dateFormat;
	oParams.formatDateTime = globals.avBase_dateTimeFormat;
	oParams.formatCurrency = globals.avBase_currencyFormat.slice(0, globals.avBase_currencyFormat.search(/\|/));
	if (oParams.formatCurrency == '€#.##0,00') oParams.formatCurrency = '€#,###.00'; // Temporary until we can use the proper format
	
	if (sReportName == 'Sales Order Financial Report_ByOrder.jasper' 
		|| sReportName == 'Sales Order Financial Report_ByCustomer.jasper' 
		|| sReportName == 'Sales Order Financial Report_BySalesRep.jasper' 
		|| sReportName == 'Sales Order Status Report_ByCustomer.jasper' 
		|| sReportName == 'Sales Order Status Report_ByOrder.jasper' 
		|| sReportName == 'Sales Order Status Report_BySalesRep.jasper') {

		if (_to_sys_organization.org_default_curr_id) {
			oParams.pOrgCurrencyFormat = globals.avBase_currencyFormat_order.slice(0, globals.avBase_currencyFormat.search(/\|/));
		} 
		else if (oParams.pOrgCurrencyFormat == null) {
			oParams.pOrgCurrencyFormat = globals.avBase_currencyFormat.slice(0, globals.avBase_currencyFormat.search(/\|/));
		}
	}
	
	//SL-21957 Using organizational timezone for organzations who have Atlantic Timezone set
	if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {
        oParams.currentDateTime = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
    }
    else {
        
        oParams.currentDateTime = application.getTimeStamp();
    }
	
    
    // Saad SL-24428: Passing user's timezone detail - GMT+05:00
    oParams.pUserTimeZone = i18n.getCurrentTimeZone();
    // Saad SL-24201: Set Client TimeZone Value to iReport's built-in parameter 
    oParams['REPORT_TIME_ZONE'] = globals.avUserTimeZone;
    
    //Organization Timezone
    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id != null) {
    	oParams.pOrgTimeZone = _to_sys_organization.org_timezone_id;
    }
    
    const offsetInMinutes = scopes.avDate.getServerTimeZoneOffset();
    const sServertimezone = scopes.avDate.getTimeZoneFromOffset(offsetInMinutes);
    oParams.pServerTimeZone =  sServertimezone;
    
    // This report does not need to write a file
	var sOutputFormat = globals.avRpt_outputFormat;
	if (!sOutputFormat) sOutputFormat = "pdf";
	
	if (sReportName == "Jobs Shipped But not Invoiced Report.jasper" && sOutputFormat == "excel") {
		sReportName = "Jobs Shipped But not Invoiced Report_simplified.jasper";
		sOldReportName = "Jobs Shipped But not Invoiced Report_simplified.jrxml";
	} 
	else if (sReportName == "Sales Order Status Report_ByOrder.jasper" && sOutputFormat == "excel") {
		sReportName = "Sales Order Status Report_ByOrder_Simplified.jasper";
		sOldReportName = "Sales Order Status Report_ByOrder_Simplified.jrxml";
	}
	else if (sReportName == "Inventory Shortage Report.jasper" && sOutputFormat == "excel") {
		sReportName = "Inventory Shortage Report_simplified.jasper";
		sOldReportName = "Inventory Shortage Report_simplified.jrxml";
	}
	else if (sReportName == "Inventory_Valuation_Report.jasper" && sOutputFormat == "excel") {
		
		if (scopes.globals.avBase_rptInventorySimplifiedWithRolls == 1) {
			sReportName = "Inventory_Valuation_Report_simplified_with_rolls.jasper";
			sOldReportName = "Inventory_Valuation_Report_simplified_with_rolls.jrxml";
		}
		else {
			sReportName = "Inventory_Valuation_Report_simplified_without_rolls.jasper";
			sOldReportName = "Inventory_Valuation_Report_simplified_without_rolls.jrxml";
		}
	}
	else if (sReportName == "Employee Job Summary Report.jasper" && sOutputFormat == "excel") {
		sReportName = "Employee Job Summary Report_Simplified.jasper";
		sOldReportName = "Employee Job Summary Report_Simplified.jrxml";
	}
	else if (sReportName == "Inventory Count Sheet.jasper" && sOutputFormat == "excel") {
		sReportName = "Inventory Count Sheet_Simplified.jasper";
		sOldReportName = "Inventory Count Sheet_Simplified.jrxml";
	}
	else if (sReportName == "Estimate_by_Customer_Report.jasper" && sOutputFormat == "excel") {
		sReportName = "Estimate_by_Customer_Report_Simplified.jasper";
		sOldReportName = "Estimate_by_Customer_Report_Simplified.jrxml";
	}
	
	if (sReportName == "Invoice_detail_view_simplified.jasper") {
		sOutputFormat = "excel";
	}
	
	//Add outformat to parameters list 
	oParams.outputFormat =  sOutputFormat;
	
	// Set the right file extension type
	var sSuffix = "";
	switch (sOutputFormat) {
	case 'pdf':
		sSuffix += '.pdf';
		break;
	case 'excel':
		sSuffix += '.xls';
		break;
	case 'xml':
		sSuffix += '.xml';
		break;
	case 'csv':
		sSuffix += '.csv';
		break;
	case 'html':
		sSuffix += '.html';
		break;
	case 'rtf':
		sSuffix += '.rtf';
		break;
	case 'txt':
		sSuffix += '.txt';
		break;
	case 'odt':
		sSuffix += '.odt';
		break;
	case 'docx':
		sSuffix += '.docx';
		break;
	case 'view':
	default:
		break;
	}

	// If we have to prepare an output file, then use the temp directory
	//	var sServerDir = plugins.it2be_tools.server().getApplicationServerDir() + "/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/";
	/*** @type {String} */
	var sServerDir = globals.avUtilities_serverGetPath("/server/webapps/ROOT/uploads/" + globals.org_id + "_downloads/");
	plugins.file.createFolder(sServerDir); // Create the folder if it does not exist;

	/*** @type {String} */
	var sFileName;

	// GD - 2012-11-27: Using the global for the file name
	sFileName = globals.avRpt_reportTitle;
	
	
	// Saad - SL-28114 - Added a limit to manage the filename length, including the directory path.
	var sServerDirLen = sServerDir.toString().length;
	var nMaxFilenameLen = 250 - (sServerDirLen >=0 ? sServerDirLen : 0) ;
	sFileName = globals.avRpt_reportTitle.toString().substring(0,nMaxFilenameLen);
	
	// Sanitize the filename by removing any characters that are not 
	// letters (a-z, A-Z), numbers (0-9), dashes (-), underscores (_), or dots (.).
	// This ensures the file name is valid and safe for use in file systems
	sFileName = sFileName.replace(/[^a-zA-Z0-9-_\. ]/g, '');
	
	var sZipFolder = null;
	// GD - 2014-03-14: for batch printing
	if (aFileNames.length > 1) {
		sZipFolder = fsReport.getRecord(1).rpt_name + "_" + utils.dateFormat(new Date(), "yyyyMMdd_HHmm");
	}
	var bLastFile = false;

	/*** @type {Boolean} */
	var bSuccess;

	if (bSingleReportFile != true && aPKs && aPKs.length > 0 && sOutputFormat != "csv") {
		var pMax = aPKs.length;
		for (p = 0; p < pMax; p++) {
			/**@type {String} **/
			var sPK = aPKs[p];
			var sPKn = aPKs[p];

			// took out the condition that only add single quotes if sPK not a string. needs to do for string too  
			if(sPK == '' || sPK == null){
				sPK = 'null';
			} else {
				sPK = "'" + sPK + "'";
			}
			//SQL// GD - May 20, 2014: Need to add condition for revision 0 only, in case of change orders
			if (globals.svy_nav_form_name == "prod_job_view_tbl") {
				oParams.pSQL = globals.avUtilities_sqlSpliceIn(sOrigSQL, " WHERE " + sAlias + ".ordrevh_revision = 0 AND " + sAlias + ".org_id = '" + 
				    globals.org_id + "' AND " + sKey + " IN (" + sPK + ") ", "ORDER BY");				
			} 
			if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ReportPurchaseOrder_summarizedShippingLoc)!=1 && 
			        (globals.svy_nav_form_name == "po_purchase_tbl" ||
			                globals.svy_nav_form_name == "po_planned_dtl") && 
			                oPoParams) {
	            oParams['pHasDropShipItem'] = oPoParams[sPKn].pHasDropShipItem;
                oParams['pQuery'] = oPoParams[sPKn].pQuery;
			}
			else {
			    oParams.pSQL = globals.avUtilities_sqlSpliceIn(sOrigSQL, " WHERE " + sAlias + ".org_id = '" + 
			                      globals.org_id + "' AND " + sKey + " IN (" + sPK + ") ", "ORDER BY");
			    if (oParams) {
			        if (globals.svy_nav_form_name == "po_purchase_tbl" || globals.svy_nav_form_name == "po_purchase_dtl" 
			            || globals.svy_nav_form_name == "po_planned_dtl") {
                        oParams['pHasDropShipItem'] = oPoParams[sPKn].pHasDropShipItem;
			        }
                    oParams['pQuery'] = oParams.pSQL;
                }   
			}
			//Set the Filename for batch printing
			if (aFileNames.length > 0){
			  	sFileName = aFileNames[p];
			}
			if (p == pMax - 1){
			    bLastFile = true;
			}
			var bCombinePdfs = false;
			
			if(globals.svy_nav_form_name == "po_planned_dtl"){
			    bCombinePdfs = true;
			}

			bSuccess = runJasperReport(sFileName, sSuffix, sServerDir, sOutputFormat, oParams, sReportName, sZipFolder, bLastFile,bCombinePdfs, rRep_id);
			if (!bSuccess){
			    return false; 
			}
		}

	} else {
		bSuccess = runJasperReport(sFileName, sSuffix, sServerDir, sOutputFormat, oParams, sReportName, null, true,false, rRep_id);
		if (!bSuccess) return false;

	}

	//	// GD - 2013-03-05: Clean-up the filename
	//	if (sFileName)
	//	{
	//		sFileName = sFileName.replace(/\//g,"_");
	//		sFileName = sFileName.replace(/\\/g,"_");
	//		sFileName = sFileName.replace(/\*/g,"_");
	//		sFileName = sFileName.replace(/\?/g,"_");
	//		sFileName = sFileName.replace(/\:/g,"-");
	//		sFileName = sFileName.replace(/\</g,"_");
	//		sFileName = sFileName.replace(/\>/g,"_");
	//		sFileName = sFileName.replace(/\|/g,"_");
	//		sFileName = sFileName.replace(/\"/g,"'");
	//		sFileName = sFileName.replace(/\n/g,"");
	//		sFileName = sFileName.replace(/\r/g,"");
	//		sFileName = sFileName.replace(/\r\n/g,"");
	//	}
	//	sFileName += sSuffix;
	//
	//	var sPath = sServerDir ;
	//	sPath = forms["_docs_base"].setPathSep(sPath) + sFileName;
	//	var sRemotePath = "/" + globals.org_id + "_downloads/" + sFileName;
	//	var jsFile;
	//	var jsFolder;
	//
	//
	////	var myLocale = new java.util.Locale('us');//using the ISO 3166 language code
	////	var myLocaleString = "en";//"us_EN"; //myLocale.toString();
	////	application.output("Locale: " + myLocaleString);
	//
	//	if (sOutputFormat == 'csv')
	//	{
	//		/*** @type {String} */
	//		var spSQL = params.get("pSQL")
	//		globals.avUtilities_writeExportFile(spSQL, globals.avDocs_userDownloadDirectory + "/" + sFileName);
	//	}
	//	else
	//	{
	//		try
	//		{
	//			application.updateUI();
	//			if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT)
	//			{
	//				plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, sPath, sOutputFormat, params);
	//				globals.updateUI();
	//			}
	//			else
	//			{
	//				// Client side report
	//				plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, globals.avDocs_userDownloadDirectory + "/" + sFileName, sOutputFormat, params);
	//			}
	//
	//		}
	//		catch (error)
	//		{
	//			/*** @type {String} */
	//			var sMsg = error['message'];
	//
	//			if (sMsg.search("util.JRImageLoader.loadImage") > -1)
	//			{
	//				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportImageError_title"),
	//					i18n.getI18NMessage("avanti.dialog.reportImageError_msg"),
	//					i18n.getI18NMessage("avanti.dialog.ok"));
	//			}
	//			else
	//			{
	//				application.output(sMsg);
	//				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportError_title"),
	//					i18n.getI18NMessage("avanti.dialog.reportError_msg") + sMsg,
	//					i18n.getI18NMessage("avanti.dialog.ok"));
	//			}
	//			return false;
	//		}
	//	}

	// If viewing, then nothing further needs to be done
	if (sOutputFormat == 'view') {
		callBack();
	}

	//Post Report Update Routines
	if (globals.svy_nav_form_name.substr(0, 11) == "sa_invoice_" && sOutputFormat != 'view' && !bModuleCRM) {
		fsForm = forms[globals.svy_nav_form_name].foundset;
		fsInvoice = fsForm.duplicateFoundSet();

		if (aPKs) {
			for (j = 0; j < aPKs.length; j++) {
				sPK = aPKs[j];

				if (fsInvoice.selectRecord(application.getUUID(sPK))) {
					/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
					rInv = fsInvoice.getSelectedRecord();

					if (rInv && rInv.inv_status != "U" && rInv.inv_subtotal_amt >= 0) {
					    if (!globals.isWorkatoIntegration() 
                                || (globals.isWorkatoIntegration() 
                                        && Math.abs(Number(rInv.inv_gldist_balance.toFixed(2))) == 0)
                                        && scopes.avAccounting.hasAllDistributionAccounts(rInv)) {
                            rInv.inv_status = scopes.globals.avInv_printStatus; //"P"; //Printed Status
                            rInv.print_check_flag = 0;
                        }
					}
				}
			}
		}

		databaseManager.saveData(fsInvoice);
		forms["sa_invoice_tbl"].callbackFromPrintingInvoice();
	}
	
	if (globals.svy_nav_form_name.substr(0, 18) == "sa_credit_invoice_" && sOutputFormat != 'view') {
		fsForm = forms[globals.svy_nav_form_name].foundset;
		var fsCreditInvoice = fsForm.duplicateFoundSet();
		

		if (aPKs) {
			for (var jc = 0; jc < aPKs.length; jc++) {
				sPK = aPKs[jc];

				if (fsCreditInvoice.selectRecord(application.getUUID(sPK))) {
					/*** @type {JSRecord<db:/avanti/sa_invoice>} ***/
					var rCreditInv = fsCreditInvoice.getSelectedRecord();

					if (rCreditInv && rCreditInv.inv_status != "U" && rCreditInv.inv_subtotal_amt <= 0) {
					    if (!globals.isWorkatoIntegration() 
					            || (globals.isWorkatoIntegration() 
					                    && Math.abs(Number(rCreditInv.inv_gldist_balance.toFixed(2))) == 0)
					                    && scopes.avAccounting.hasAllDistributionAccounts(rCreditInv)) {
					        rCreditInv.inv_status = "P"; //Printed Status
                            rCreditInv.print_check_flag = 0;
					    }
					}
				}
			}
		}

		databaseManager.saveData(fsCreditInvoice);
		forms["sa_credit_invoice_tbl"].callbackFromPrintingCreditNote();
	}

	callBack();

	/**
	 * Test for callback and return
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2013-02-21
	 *
	 */
	function callBack() {
		if (sCallBackFormMethod) {
			return eval(sCallBackFormMethod);
		} else {
			return true;
		}
	}

	return true;

	function processReceiptsFromProductionForPOReceipt(sReceiptID) {
        var dsProdReceipts = scopes.avPurchasing.getReceiptsFromProductionForPOReceipt(sReceiptID);
        
        if (dsProdReceipts && dsProdReceipts.getMaxRowIndex() > 0) {
            // if there is a prod receipt for this po receipt then we print the prod rec instead of the po rec
            // so pop the po rec off the print array
            aPKs.pop();

            if (aFileNames && aFileNames.length > 0) {
                aFileNames.pop();
            }

            for (var pr = 1; pr <= dsProdReceipts.getMaxRowIndex(); pr++) {
                var sProdReceiptID = dsProdReceipts.getValue(pr, 1);
                var sProdReceiptNum = dsProdReceipts.getValue(pr, 2);

                aPKs.push(sProdReceiptID);
                aFileNames.push(i18n.getI18NMessage("avanti.lbl.BoxLabelForReceiptFromProduction") + " " + sProdReceiptNum);
            }
        }
	}
	
    function getFilterPropertyValue(sPropertyName) {
        if (oFilters) {
            for (var f = 0; f < oFilters.aParamNames.length; f++) {
                if (oFilters.aParamNames[f] == sPropertyName) {
                    return oFilters.aParamValues[f];
                }
            }
        }

        return null;
    }
}

/**
 * @return
 * @properties={typeid:24,uuid:"817C87D1-984B-4C0B-8988-097F9F42CED4"}
 */
function downloadFinished() {
	if (globals.avBase_developmentMode == 1) application.output("Download is finished.");

	// Launch the downloaded file
	globals.avUtilities_fileLaunch(globals.avRpt_fileLaunch);

	// Delete the file from the server
	if (globals.avRpt_file) {
		if (plugins.file.deleteFile(globals.avRpt_file)) {
			globals.avRpt_file = null;
		}
	}

	return true;
}

/**
 * Launch a velocity report
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-04-24
 *
 * @param {JSRecord<db:/avanti/rpt_report>} [rRpt] - The report
 * @param {Boolean} [bNoFileLaunch] - Whether or not to launch the file (default is false)
 *
 *
 * @properties={typeid:24,uuid:"561D4A6A-B401-499A-9418-8CF86F51AB31"}
 * @AllowToRunInFind
 */
function rptVelocityReport(rRpt, bNoFileLaunch) {
	if (!rRpt) return;

	var sFileName;
	var jsFolder;
	/**@type {Array<byte>} **/
	var aBytes = [],
		sBytes = "";
	var sDestination;
	var sSuffix;

	/*** @type {{
	 * [sDocID]:String,
	 * [sContactID]: String,
	 * [sCustomerID]: String,
	 * [sEmpID]: String,
	 * [sEstID]: String,
	 * [sEstRevID]: String,
	 * [sOrdID]: String,
	 * [sOrdRevID]: String,
	 * [sTemplateID]: String,
	 * [sFrom]:String,
	 * [sTo]:String,
	 * }} oArg */
	var oArg = new Object();

	//	/*** @type {JSFoundSet<db:/avanti/sa_order>} */
	//	var fsEst = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order');
	//	fsEst.loadAllRecords();
	//	var rEst = fsEst.getRecord(1);
	//	oArg.sContactID = rEst.contact_id;
	//	oArg.sCustomerID = rEst.cust_id;
	//	oArg.sEmpID = rEst.ordh_salesper_id;
	//	oArg.sEstID = rEst.ordh_id;

	oArg = getVelocityRptMergeObject(rRpt) //forms["_docs_base"].createMergeObject(oArg);

	var _sTemplate = forms.rpt_report_dtl.getReportPath(rRpt) + rRpt.rpt_filename; //"QuoteLetter.html";

	// Build the parameters
	plugins.VelocityReport.installFonts();
	//	var jsImage;
	//	if (_to_sys_organization.report_header)
	//	{
	//		jsImage = plugins.images.getImage(_to_sys_organization.report_header);
	//		oArg.pHeader = jsImage.resize(jsImage.getWidth() / 2,jsImage.getHeight() / 2).getData();
	//	}
	//	if (_to_sys_organization.report_footer)
	//	{
	//		jsImage = plugins.images.getImage(_to_sys_organization.report_footer);
	//		oArg.pFooter = jsImage.resize(jsImage.getWidth() / 2,jsImage.getHeight() / 2).getData();
	//	}
	plugins.VelocityReport.addDefaultReportParameter('pTitle', globals.avRpt_reportTitle);
	if (globals.avBase_dateFormat == "MM/dd/yyyy") {
		plugins.VelocityReport.addDefaultReportParameter('dateFormat', "MMM dd, yyyy");
	} else {
		plugins.VelocityReport.addDefaultReportParameter('dateFormat', "dd MMM yyyy");
	}
	plugins.VelocityReport.addDefaultReportParameter('formatCurrency', globals.avBase_currencyFormat.slice(0, globals.avBase_currencyFormat.search(/\|/)));

	// Go to the correct output method
	if (globals.avRpt_outputFormat == "view") // Viewer
	{
		plugins.VelocityReport.previewReport(_sTemplate, oArg);
	} else if (globals.avRpt_outputFormat == "print") // Print
	{
		plugins.VelocityReport.printReport(_sTemplate, oArg);
	} else //if (globals.avRpt_outputFormat  == "pdf")
	{
		sSuffix = "";
		switch (globals.avRpt_outputFormat) {
		case 'pdf':
			sSuffix += '.pdf';
			break;
		case 'excel':
			sSuffix += '.xls';
			break;
		case 'xml':
			sSuffix += '.xml';
			break;
		case 'csv':
			sSuffix += '.csv';
			break;
		case 'html':
			sSuffix += '.html';
			break;
		case 'rtf':
			sSuffix += '.rtf';
			break;
		case 'txt':
			sSuffix += '.txt';
			break;
		case 'odt':
			sSuffix += '.odt';
			break;
		case 'docx':
			sSuffix += '.doc';
			break;
		case 'view':
		default:
			break;
		}

		// Verify the name ends with the right suffix
		sFileName = rRpt.rpt_filename;
		sFileName = sFileName.slice(0, sFileName.search(/\./m));
		sFileName += sSuffix;

		// Select the folder where you want to copy the files to
		if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {
			sDestination = sFileName;
			bNoFileLaunch = true; // will download to the browser; no need to launch it
		} else {
			jsFolder = plugins.file.showDirectorySelectDialog(null, i18n.getI18NMessage("avanti.dialog.selectSaveLocation"));

			sDestination = jsFolder.getAbsolutePath() + "/" + sFileName;
		}

		if (sDestination) {

			sDestination = globals.avUtilities_file_setPathSep(sDestination);

			if (globals.avRpt_outputFormat == "pdf") {
				// Execute and return a PDF report:
				aBytes = plugins.VelocityReport.getPDFReport(_sTemplate, oArg, plugins.VelocityReport.REPORT.RESOLUTION_HIGH, String(plugins.VelocityReport.PDF.V1_5));

				if (aBytes && plugins.file.writeFile(sDestination, aBytes)) {
					// Launch the file
					if (!bNoFileLaunch) globals.avUtilities_fileLaunch(sDestination);
				}
			} else if (globals.avRpt_outputFormat == "excel") {
				sBytes = plugins.VelocityReport.renderTemplate(_sTemplate, oArg);

				if (sBytes) {
					if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
						if (plugins.file.writeTXTFile(sDestination, sBytes, 'UTF-8')) {
							if (!bNoFileLaunch) globals.avUtilities_fileLaunch(sDestination);
						}
					} else {
						if (plugins.file.writeTXTFile(sDestination, sBytes, 'UTF-8', 'application/vnd.ms-excel')) {
							if (!bNoFileLaunch) globals.avUtilities_fileLaunch(sDestination);
						}
					}
				}
			} else {
				sBytes = plugins.VelocityReport.renderTemplate(_sTemplate, oArg);

				if (sBytes) {
					if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
						if (plugins.file.writeTXTFile(sDestination, sBytes, 'UTF-8')) {
							if (!bNoFileLaunch) globals.avUtilities_fileLaunch(sDestination);
						}
					} else {
						if (plugins.file.writeTXTFile(sDestination, sBytes, 'UTF-8')) {
							if (!bNoFileLaunch) globals.avUtilities_fileLaunch(sDestination);
						}
					}
				}
			}
		}
	}
}

/**
 * Gets the pks to use
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-04-25
 *
 * @param {JSRecord<db:/avanti/rpt_report>} rRpt - The report
 *
 * {Object} Merge object containing all the data for the substitution
 *
 * @AllowToRunInFind
 *
 * @return
 * @properties={typeid:24,uuid:"36D5F7C7-95A4-45DD-80F2-2C07C3914107"}
 */
function getVelocityRptMergeObject(rRpt) {
	/*** @type {{
	 * [sDocID]:String,
	 * [sContactID]: String,
	 * [sCustomerID]: String,
	 * [sEmpID]: String,
	 * [sEmpSalesID]: String,
	 * [sEmpCSRID]: String,
	 * [sEmpEstID]: String,
	 * [sEstID]: String,
	 * [sEstRevID]: String,
	 * [sOrdID]: String,
	 * [sOrdRevID]: String,
	 * [sTemplateID]: String,
	 * [sFrom]:String,
	 * [sTo]:String,
	 * }} oArg */
	var oArg = new Object();
	var oSQL;
	//var aPKs;
	//var sPKs = "";
	/*** @type {JSFoundSet<db:/avanti/sa_order>} */
	//var fsForm;
	var sForm;
	var sBaseForm;
	//var sBaseTable;
	/*** @type {JSRecord<db:/avanti/sa_order>} */
	var rRec;
	//var sPK;

	// Get the reports program's base form
	oSQL = new Object();
	oSQL.sql = globals.avUtilities_appSqlQueryGet('SELECT', null, 'rpt_getBaseForm_byProgram', globals.avBase_dbase_version).query;
	oSQL.server = globals.avBase_dbase_framework;
	oSQL.args = [rRpt.program, 1];
	var dsProgram = globals.avUtilities_sqlDataset(oSQL);

	if (dsProgram && dsProgram.getMaxRowIndex() > 0) {
		sBaseForm = dsProgram.getValue(1, 1);
		//sBaseTable = dsProgram.getValue(1, 2);

		if (globals.svy_nav_form_name == "rpt_report_dtl") {
			sForm = sBaseForm + "_tbl";
			if (forms[sForm].foundset.getSize() == 0) forms[sForm].foundset.loadAllRecords();
			rRec = forms[sForm].foundset.getSelectedRecord();
		} else if (globals.svy_nav_form_name == 'rpt_reportUI_dtl') {
			sForm = sBaseForm + "_tbl";
			if (forms[sForm].foundset.getSize() == 0) forms[sForm].foundset.loadAllRecords();
			rRec = forms[sForm].foundset.getSelectedRecord();
		} else if (globals.svy_nav_form_name == sBaseForm + '_dtl') {
			rRec = forms[globals.svy_nav_form_name].foundset.getSelectedRecord();

			//			sPK = globals.avUtilities_tableGetPKName(sBaseTable,globals.avBase_dbase_avanti);
			//
			//			if (sPK)
			//			{
			//				fsForm = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sBaseTable);
			//
			//				if(fsForm.find() || fsForm.find())
			//				{
			//					fsForm[sPK] = rRec[sPK];
			//
			//					fsForm.search();
			//
			//					if (fsForm.getSize() != 1 && fsForm[sPK] != rRec[sPK])
			//					{
			//						return null;
			//					}
			//				}
			//			}
			//			else
			//			{
			//				return null;
			//			}
		} else if (globals.svy_nav_form_name == sBaseForm + '_tbl') {
			rRec = forms[globals.svy_nav_form_name].foundset.getSelectedRecord();
		} else {
			// No supported form
			return null;
		}

		switch (rRpt.program) {
		case "Contacts":
			oArg.sContactID = rRec.custcontact_id;
			break;
		case "Customers":
			oArg.sCustomerID = rRec.cust_id;
			break;
		case "Estimate_Entry":
		case "Order_Entry":
			oArg.sContactID = rRec.custcontact_id;
			oArg.sCustomerID = rRec.cust_id;
			if (utils.hasRecords(rRec.sa_order_to_sa_sales_person) && utils.hasRecords(rRec.sa_order_to_sa_sales_person.sa_sales_person_to_sys_employee$by_code)) oArg.sEmpSalesID = rRec.sa_order_to_sa_sales_person.sa_sales_person_to_sys_employee$by_code.empl_id;
			if (utils.hasRecords(rRec.sa_order_to_sys_employee$csr)) oArg.sEmpCSRID = rRec.sa_order_to_sys_employee$csr.empl_id;
			if (utils.hasRecords(rRec.sa_order_to_sys_employee$estimator)) oArg.sEmpEstID = rRec.sa_order_to_sys_employee$estimator.empl_id;
			oArg.sEstID = rRec.ordh_id;
			break;
		default:
			break;
		}
	}

	oArg = forms["_docs_base"].createMergeObject(oArg);

	return oArg;
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"EDAF0AF1-6889-40E7-B8BC-7EEC8EB030DF"}
 * @AllowToRunInFind
 */
function dc_delete(_event, _triggerForm) {
	// Delete the key as well
	/*** @type {JSFoundSet<db:/avanti/rpt_report>} */
	var fsRpt = forms[globals.svy_nav_form_name].foundset;
	/*** @type {JSRecord<db:/avanti/rpt_report>} */
	var rRpt = fsRpt.getSelectedRecord();

	//	if (rRpt.is_system == 1)
	//	{
	//		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"), i18n.getI18NMessage("avanti.dialog.deleteMsgSystemReport"), i18n.getI18NMessage("avanti.dialog.ok"));
	//		return false;
	//	}
	//
	//	// Check to see if the key exists
	//	var iSecKeyID = globals.avSecurity_getSecurityKeyID_report(rRpt.rpt_id, rRpt.rpt_name, null);
	//	globals.svy_sec_security_key_id = iSecKeyID;
	//
	//	if (utils.hasRecords(_to_sec_security_key$security_key_id)) //(Sec.getSize() > 0 && fsSec.security_key_id == iSecKeyID)
	//	{
	//		// Delete any functions for this report
	//		if (utils.hasRecords(_to_sec_security_key$security_key_id.sec_security_key_to_nav_function))
	//		{
	//			_to_sec_security_key$security_key_id.sec_security_key_to_nav_function.deleteAllRecords();
	//		}
	//		// Delete the report key itself
	//		globals.avSecurity_deleteSecurityKey(iSecKeyID);
	//	}
	//
	//
	//	return _super.dc_delete(_event, _triggerForm);

	// If this is a system report, then do not allow delete
	if (rRpt.is_system == 1 && globals.avBase_developmentMode != 1) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
			i18n.getI18NMessage("avanti.dialog.deleteMsgSystemReport"),
			i18n.getI18NMessage("avanti.dialog.ok"));
		return false;
	} else {
		var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
			i18n.getI18NMessage("avanti.dialog.deleteMsg"),
			i18n.getI18NMessage("avanti.dialog.ok"),
			i18n.getI18NMessage("avanti.dialog.cancel"));

		if (sAns != i18n.getI18NMessage("avanti.dialog.ok")) {
			return false;
		}

		// Delete the report and delete the security key
		var bSystem = false;
		if (rRpt.is_system) bSystem = true;
		var iKeyID = globals.avSecurity_getSecurityKeyID_report(globals.UUIDtoString(rRpt.rpt_id), rRpt.rpt_name, rRpt.program, bSystem);
		globals.avSecurity_deleteSecurityKey(iKeyID);

		// Clear all popmenus that use this reportID
		/*** @type {JSFoundSet<db:/svy_framework/nav_popmenu>} */
		var fsNavPopMenu = databaseManager.getFoundSet(globals.avBase_dbase_framework, 'nav_popmenu');
		if (fsNavPopMenu.find() || fsNavPopMenu.find()) {
			fsNavPopMenu.rpt_id = rRpt.rpt_id;
			fsNavPopMenu.search();

			if (fsNavPopMenu.getSize() > 0 && fsNavPopMenu.rpt_id == rRpt.rpt_id) {
				var rNavPopMenu;
				for (var i = 1; i <= fsNavPopMenu.getSize(); i++) {
					rNavPopMenu = fsNavPopMenu.getRecord(i);
					rNavPopMenu.rpt_id = null;

					// remove the function and function arguments for this menu
					// remove the function and function arguments for this menu
					if (utils.hasRecords(rNavPopMenu.nav_popmenu_to_nav_function)) {
						rNavPopMenu.nav_popmenu_to_nav_function.deleteAllRecords();
					}
					if (utils.hasRecords(rNavPopMenu.nav_popmenu_to_nav_function_arguments)) {
						rNavPopMenu.nav_popmenu_to_nav_function_arguments.deleteAllRecords();
					}

					databaseManager.saveData(rNavPopMenu);
				}
			}
		}

		// Now delete the report
		fsRpt.deleteRecord();

		// TODO - GD: 2011-05-25 - Delete the sub reports from the server?

		// TODO - GD: 2011-05-25 - Delete the main reports from the server?

	}
	return true;
	//	return _super.dc_delete(_event, _triggerForm);
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"CFA25E0A-AA15-41D9-931B-A817D02A7D58"}
 */
function dc_new(_event, _triggerForm) {
	_super.dc_new(_event, _triggerForm);

	owner_id = globals.owner_id;
}

/**
 * Run Jasper Report
 *
 * @param {String} sFileName - The filename for the report file
 * @param {String} sSuffix - output suffix
 * @param {String} sServerDir - server directory
 * @param {String} sOutputFormat - report output format
 * @param {Object} oParams - report parameters
 * @param {String} sReportName - Report Name
 * @param {String} sZipFolder - if batch printing copy files to a zip folder, zip and download (WebClient Only)
 * @param {Boolean} bLastFile
 * @param {Boolean} bCombinePdfs
 * @param {UUID} sRpt_id
 * 
 * @returns {Boolean} Return Value
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"0E85696F-CC98-4A68-8139-FDFF661C959F"}
 */
function runJasperReport(sFileName, sSuffix, sServerDir, sOutputFormat, oParams, sReportName, sZipFolder, bLastFile, bCombinePdfs, sRpt_id) {
	var bReturnValue = true;
    var bSuccess = true;
    var bSuccessFTP = true;
    var sDBLogParentUUID = null;
	var bIsCrystalRpt = false;
    var bytes = null;
	
    // Saad:  Crystal Report - Formula Substitution and Parameter variables 
    /*** @type {FormulaFieldSubstitutions} */
    var substitutions= plugins.crystalReports.createFormulaFieldSubstitutions() ;
    /*** @type {ParameterFieldSubstitutions} */ 
	var crParams = plugins.crystalReports.createParameterFieldSubstitutions();
	
	//Convert parameters to hash map
    var params = new java.util.HashMap();
 	for (var o in oParams) {
		params.put(o, oParams[o]);
		
		// Crystal Report: Formula Field 
		substitutions.add(o, oParams[o]);
		
		// Crystal Report: Parameter Fields
		if(oParams[o] != null) {
			crParams.add(o,oParams[o]);	
		}
		
			
	}
 	
	var nCurrencyRate = 1.0;
	if(oParams.pCurrencyRate != null) {
		
		nCurrencyRate  = oParams.pCurrencyRate;
	}
	
	var sCurrentyExchangeMethod = 'M';
	if (oParams.pCurrExchangeMethod != null) {
		
		sCurrentyExchangeMethod = oParams.pCurrExchangeMethod;
	}
	
	// Passing pSQL parameter to Crystal Report Plugin
	crParams.add("pSQL",oParams.pSQL);
	crParams.add("pCurrencyRate",nCurrencyRate);
	crParams.add("pCurrExchangeMethod",sCurrentyExchangeMethod);
	
	// GD - 2013-03-05: Clean-up the filename;
	if (sFileName) {
		sFileName = scopes.avReporting.cleanReportFileName(sFileName);
	}
	
	/*** @type {JSFoundSet<db:/avanti/rpt_report>} */
	var fsRpt = forms[globals.svy_nav_form_name].foundset;
	var rRpt ;
	
	var /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	 oSQL = {},
	 dsData;

	if (sRpt_id == null) {

		oSQL.server = globals.avBase_dbase_avanti;
		oSQL.sql = "SELECT TOP 1 * FROM rpt_report  \
	 			WHERE rpt_filename = ? ";

		oSQL.args = [sReportName.replace('jasper', 'jrxml')];
		dsData = globals.avUtilities_sqlDataset(oSQL);
		sRpt_id = dsData.getValue(1, 1);
	}
	
	/** @type {JSFoundSet<db:/avanti/rpt_report>} */
	fsRpt = scopes.avDB.getFS('rpt_report', ['rpt_id'], [sRpt_id]);
	/*** @type {JSRecord<db:/avanti/rpt_report_export>} */
	rRpt = fsRpt.getSelectedRecord();
	
	if (fsRpt.rpt_flg_add_timestamp == 1) {

		sFileName = sFileName + "_" + utils.dateFormat(new Date(), "yyyyMMddHHmmss");
	}
	
		
	// Set flag for type of report
	if (fsRpt.rpt_type == 'C') {
		
	 // Saad - Extract DB Connection information for Crystal Report Plugin
	    var sConn= plugins.UserManager.Server().getDbConnectionInfos().toString().split('[');
	    
	    for (var i = 0; i <= sConn.length; i++){
	          // Database Name
	          if (i == 2){
	              var sAvanti = sConn[i];
	              var sDBName = sConn.toString().substring(sConn.toString().indexOf('DatabaseName')+13,sConn.toString().indexOf(';',sConn.toString().indexOf('DatabaseName')+13))

	              var sDBAvanti = sAvanti.toString().split(',');
	              
	              for (var j = 0; j <= sDBAvanti.length; j++) {
	                  
	                  if (j == 3) {
	                      var sDBSchema = sDBAvanti[j];
	                      if (sDBSchema == '<none>') {
	                          sDBSchema = 'dbo';
	                      }
	                  }
	                  if (j == 4) {
	                      var sDBusername = sDBAvanti[j];  
	                  }
	                  if (j == 5) {
	                      var sDBPassword = sDBAvanti[j].substring(0,sDBAvanti[j].indexOf(']'));  
	                  }
	              }
	          }
	    }
	    
	    // Pass DB Info to Crystal Report Plugin
	    var DbconInfo = new Packages.com.servoy.plugins.crystalreports.ServoyDataDBInfo();

	    DbconInfo.setDatabaseName(sDBName);
	    DbconInfo.setSchema(sDBSchema);
	    DbconInfo.setUserName(sDBusername);
	    DbconInfo.setPassword(sDBPassword);

	    bIsCrystalRpt = true;	
	} 
	else {
		bIsCrystalRpt = false;
	}
	
	sFileName += sSuffix;
	
	var sPath = sServerDir;
	sPath = forms["_docs_base"].setPathSep(sPath) + sFileName;
	var sOrgPath = sServerDir + sFileName;
	var jsFile;
	var crFile;
	var _ok = i18n.getI18NMessage('avanti.dialog.ok');
	
	if (sOutputFormat == 'csv' && bIsCrystalRpt == false) {
		/*** @type {String} */
		var spSQL = params.get("pSQL");

		if (sReportName == "Custom_Shipping.jasper" || sReportName == "Custom_JobCostReport_01.jasper" || sReportName == "Custom_JobCostReport_02.jasper") {
			spSQL += params.get("pWhere");
			globals.avUtilities_writeExportFile(spSQL, globals.avDocs_userDownloadDirectory + "/" + sFileName, null, null, true);
		}
		else {
			globals.avUtilities_writeExportFile(spSQL, globals.avDocs_userDownloadDirectory + "/" + sFileName);	
		}
		
	}
    else {
        try {
            application.updateUI();
			if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {
				if (utils.hasRecords(rRpt.rpt_report_to_rpt_export)) {
					if (rRpt.rpt_report_to_rpt_export.export_folder_type == 'F' && rRpt.rpt_report_to_rpt_export.export_ftp_host != null && rRpt.rpt_report_to_rpt_export.export_ftp_username != null && rRpt.rpt_report_to_rpt_export.export_ftp_password != null && rRpt.rpt_report_to_rpt_export.export_ftp_folder != null) {

						try {
							if (bIsCrystalRpt == true) {
							
							    if (sOutputFormat == 'csv') {
		                            
	                                sOutputFormat = 'CSV'
	                            }
							    
	                            bytes = plugins.crystalReports.runReport(plugins.jasperPluginRMI.relativeReportsDirectory + sReportName, null, sOutputFormat, substitutions, crParams, DbconInfo);
								crFile = plugins.file.createFile(globals.avDocs_userDownloadDirectory + "/" + sFileName);
						       	plugins.file.writeFile(crFile,bytes);
								sOrgPath = globals.avDocs_userDownloadDirectory + "\\" + sFileName;
						  	
							}
							else {
							
								plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, sPath, sOutputFormat, params, scopes.globals.avRpt_reportLanguage);
								
							}
							
							var sCurEmpID = globals.avBase_employeeUUID;
							var sFolder = rRpt.rpt_report_to_rpt_export.export_ftp_folder;
							
							if (sCurEmpID) {
								var sSubFolder = getReportExportEmpSubFolder(sCurEmpID, sRpt_id);

								if (sSubFolder) {
									sFolder += "/" + sSubFolder;
								}
							}
							
							sDBLogParentUUID = globals.dbLog('File successfully uploaded on FTP: ' + rRpt.rpt_report_to_rpt_export.export_ftp_host + '/' + sFolder + '/' + sFileName, '', 'export', 'report', 'ftp', application.getUUID(globals.org_id), 'ftp', 'Summary', null, 'Success');
							bSuccessFTP = true;

						} catch (error) {
							sDBLogParentUUID = null;
							/*** @type {String} */
							var sMsgFTP = error['message'];
							sDBLogParentUUID = globals.dbLog('File cannot uploaded due to: ' + sMsgFTP, '', 'export', 'report', 'ftp', application.getUUID(globals.org_id), 'ftp', 'Summary', null, 'Error');
							globals.dbLog('Cannot complete upload request : ' + rRpt.rpt_report_to_rpt_export.export_ftp_host + '/' + sFolder + ' due to ' + sMsgFTP, 'xml_export', 'export', 'report', 'ftp', application.getUUID(globals.org_id), 'ftp', 'Detail', sDBLogParentUUID);
							bSuccessFTP = false;
						}

						bSuccess = scopes.avUtils.uploadReportDirectlyToFTP(sOrgPath, rRpt.rpt_report_to_rpt_export.export_ftp_host, rRpt.rpt_report_to_rpt_export.export_ftp_port, rRpt.rpt_report_to_rpt_export.export_ftp_username, rRpt.rpt_report_to_rpt_export.export_ftp_password, rRpt.rpt_report_to_rpt_export.export_ftp_hostkey, sFolder, rRpt.rpt_report_to_rpt_export.export_ftp_use_sftp, application.getUUID(globals.org_id), sDBLogParentUUID, sFileName);
						bReturnValue = false;

						if (bSuccess && bSuccessFTP) {
							globals.DIALOGS.showInfoDialog('Report Result ', 'This report has been generated in the following location: ' + rRpt.rpt_report_to_rpt_export.export_ftp_host + '/' + sFolder + '/' + sFileName, _ok);
						} else {
								globals.DIALOGS.showInfoDialog('Report Result ', 'Cannot connect to FTP site: ' + rRpt.rpt_report_to_rpt_export.export_ftp_host, _ok);	
						}

					} else if (rRpt.rpt_report_to_rpt_export.export_folder_type == 'N') {

						var networkPath = utils.stringReplace(rRpt.rpt_report_to_rpt_export.export_network_folder, '/', '\\\\');
						sPath = networkPath + '\\' +  sFileName;

						try {
							
							if (bIsCrystalRpt == true) {
								bytes = plugins.crystalReports.runReport(plugins.jasperPluginRMI.relativeReportsDirectory + sReportName, null, sOutputFormat, substitutions, crParams, DbconInfo);
								crFile = plugins.file.createFile(sPath);
						       	plugins.file.writeFile(crFile,bytes);
						       	
							}
							else {
							
								plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, sPath, sOutputFormat, params, scopes.globals.avRpt_reportLanguage);
							}
							
							sDBLogParentUUID = globals.dbLog('File successfully uploaded on network folder: ' + sPath, '', 'export', 'report', 'ftp', application.getUUID(globals.org_id), 'ftp', 'Summary', null, 'Success');
							globals.dbLogUnified('File successfully uploaded on network folder: ' + sPath, sDBLogParentUUID, 'export', 'report', 'ftp', application.getUUID(globals.org_id), '', '', sDBLogParentUUID, 'Success', 'ftp', '', 'ftp', '', sPath);
							globals.DIALOGS.showInfoDialog('Report Result ', 'This report has been generated in the following location: ' + sPath, _ok);

						} catch (error) {
							/*** @type {String} */
							var sMsgNetwork = error['message'];
							sDBLogParentUUID = globals.dbLog('Cannot upload file on network folder due to: ' + sMsgNetwork.replace('java.lang.Exception: ', ''), '', 'export', 'report', 'ftp', application.getUUID(globals.org_id), 'ftp', 'Summary', null, 'Error');
							globals.dbLogUnified('Cannot upload file on network folder due to: ' + sMsgNetwork.replace('java.lang.Exception: ', ''), sDBLogParentUUID, 'export', 'report', 'ftp', application.getUUID(globals.org_id), '', '', sDBLogParentUUID, 'Error', 'ftp', '', 'ftp', '', sPath);
							globals.DIALOGS.showInfoDialog('Report Result ', 'Cannot upload file on network folder due to: ' + sMsgNetwork.replace('java.lang.Exception: ', ''), _ok);
						}
						bReturnValue = false;
					} else if (rRpt.rpt_report_to_rpt_export.export_folder_type == 'D') {
						
						if (bIsCrystalRpt == true){
						    //params.get("pSQL")
						    
                            if (sOutputFormat == 'csv') {

                                sOutputFormat = 'CSV'
                            }

                            bytes = plugins.crystalReports.runReport(plugins.jasperPluginRMI.relativeReportsDirectory + sReportName, null , sOutputFormat, substitutions, crParams, DbconInfo);
							crFile = plugins.file.createFile(sPath);
					       	plugins.file.writeFile(crFile,bytes);
					       	
						}
						else {
							bSuccess = plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, sPath, sOutputFormat, params, scopes.globals.avRpt_reportLanguage);	
						}
					}
				} else {

					if (bIsCrystalRpt == true){
						
						bytes = plugins.crystalReports.runReport(plugins.jasperPluginRMI.relativeReportsDirectory + sReportName, null, sOutputFormat, substitutions, crParams, DbconInfo);
						crFile = plugins.file.createFile(sPath);
				       	plugins.file.writeFile(crFile,bytes);
					}
					else {
						bSuccess = plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, sPath, sOutputFormat, params, scopes.globals.avRpt_reportLanguage);	
					}
				}
			}
			else {
                // Client side report
				if(bIsCrystalRpt == true) {
				
					bytes = plugins.crystalReports.runReport(plugins.jasperPluginRMI.relativeReportsDirectory + sReportName, null, sOutputFormat, substitutions, crParams, DbconInfo);
					plugins.dialogs.showInfoDialog('Client Side - Report Name', sReportName);

				}
				else {
			
					plugins.jasperPluginRMI.runReport(globals.avBase_dbase_avanti, sReportName, globals.avDocs_userDownloadDirectory + "/" + sFileName, sOutputFormat, params, scopes.globals.avRpt_reportLanguage);	
				}
				
            }

        }
        catch (error) {
            /*** @type {String} */
            var sMsg = error['message'];
            application.output(error.stack);
            if (sMsg.search("util.JRImageLoader.loadImage") > -1) {
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportImageError_title"),
                    i18n.getI18NMessage("avanti.dialog.reportImageError_msg"),
                    i18n.getI18NMessage("avanti.dialog.ok"));
            }
            else {
                application.output(sMsg, LOGGINGLEVEL.ERROR);
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportError_title"),
                    i18n.getI18NMessage("avanti.dialog.reportError_msg") + sMsg,
                    i18n.getI18NMessage("avanti.dialog.ok"));
            }
            bReturnValue = false;

            scopes.avUtils.log("Error running report: " + sReportName + "\n" + sMsg, scopes.avUtils.LOGGER.Reporting, LOGGINGLEVEL.ERROR);
        }
    }
    
    if (bIsCrystalRpt == true && bReturnValue == false) {
		
    	crFile = plugins.file.createFile(globals.avDocs_userDownloadDirectory + "/" + sFileName);
       	plugins.file.writeFile(crFile,bytes);
	}

	if (bReturnValue == true) {
		// Launch the downloaded file if we are using the smart client
		if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
	
			globals.avUtilities_fileLaunch(globals.avDocs_userDownloadDirectory + "/" + sFileName);
			
		} else {
			
			if (!sZipFolder) {
				
					jsFile = plugins.file.convertToJSFile(sPath);
					plugins.file.writeFile(jsFile.getName(), jsFile.getBytes());	
				
			} else {
				var jsSource = plugins.file.convertToJSFile(sPath);
				
				if(bCombinePdfs){
				    if(!aReportByteArray){
				        aReportByteArray = new Array();
				    }
				    aReportByteArray.push(jsSource.getBytes());
				}
				else{
	                var jsTarget = plugins.file.convertToJSFile(sServerDir + sZipFolder + "/" + sFileName);
	                plugins.file.copyFile(jsSource, jsTarget);
				}
				if (bLastFile) //Zip and download
				{
				    if(bCombinePdfs){
				        var oCombinedPDF = plugins.pdf_output.combinePDFDocuments(aReportByteArray);
				        plugins.file.writeFile(sZipFolder+".pdf", oCombinedPDF);
				        aReportByteArray = null;
				    }
				    else{
					//Zip the file
                        jsSource = plugins.file.convertToJSFile(sServerDir + sZipFolder);
                        plugins.it2be_tools.zip(jsSource.getAbsolutePath()); // Zip a file

                        //Download the zip file
                        //					jsFile = plugins.file.convertToRemoteJSFile("/" + globals.org_id + "_downloads/" + sZipFolder + ".zip");
                        jsFile = plugins.file.convertToJSFile(sServerDir + sZipFolder + ".zip");
                        plugins.file.writeFile(jsFile.getName(), jsFile.getBytes());

                        //Delete the zip file and temp folder
                        plugins.file.deleteFolder(sServerDir + sZipFolder, false);
                        plugins.file.deleteFile(sServerDir + sZipFolder + ".zip");
				    }
				}
			}
		}
	}

	return bReturnValue;
}

/**
 * 
 * @param {UUID|String} [sEmpID]
 * @param {UUID|String} [sRptID]
 *
 * @return
 * @properties={typeid:24,uuid:"5937C3D8-E115-4F06-BA0F-E22CE75AAF4B"}
 */
function getReportExportEmpSubFolder(sEmpID, sRptID) {
	var sSQL = "SELECT f.folder \
                FROM rpt_report_export_folder f \
                INNER JOIN rpt_report_export_folder_emp e ON f.rpt_report_export_folder_id = e.rpt_report_export_folder_id \
                INNER JOIN rpt_report_export r ON r.rpt_report_export_id = f.rpt_report_export_id \
                WHERE e.empl_id = ? AND r.rpt_id = ? ";
	return scopes.avDB.SQLQuery(sSQL, null, [sEmpID.toString(), sRptID.toString()]);
}
