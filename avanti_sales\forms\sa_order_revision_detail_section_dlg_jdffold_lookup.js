/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B5060D0A-2A31-4111-8E30-AE7C46D1FC7E",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"446F8940-E71F-4BF1-BE79-01EE41E6DAFD"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"518754E6-E4F6-46AC-87F3-C89207BE8FEF"}
 */
function onShow(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	if(forms.sa_order_revision_detail_section_dlg._bCalledFromShopFloor){
		show(forms["sf_prod_job_view_sections"].foundset.getSelectedRecord())
	}
	else{
		show(forms.sa_order_revision_detail_section_tbl.foundset.getSelectedRecord())
	}
	
	if(_rSection.ordrevds_line_type == 'D'){
		elements.fldDivisions.visible = false
		elements.lblDivisions.visible = false
	}
	else{
		elements.fldDivisions.visible = true
		elements.lblDivisions.visible = true
	}
	
	databaseManager.saveData(foundset)
	foundset.sort('ordrevdsmodel_pages asc, ordrevdsmodel_prcnt_usage desc, ordrevdsmodel_imposition asc')
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"4D06EBBB-E84F-4F03-874D-EAA66937D58A"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldFolder") {
		onDataChange_folder(oldValue, newValue, event);
	}
	if (col.id == "chkSelected") {
		onDataChange_selected(oldValue, newValue, event);
	}
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FB5E2170-EB57-49B0-AB06-6844E0CB9054"}
 */
var _divCombID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"06FD7086-83EE-4AA5-89DE-BD834545E2C0"}
 */
var _sigsChosen = '';

/**
 * @properties={typeid:35,uuid:"EE1EACFF-2F3E-464B-93FF-34531F616AFE",variableType:-4}
 */
var _aSigs = [];

/**
 * @type {JSRecord<db:/avanti/sa_order_revision_detail_section>}
 * @properties={typeid:35,uuid:"AB6E0988-7ABD-48CB-8FDE-FB25CF25D8F2",variableType:-4}
 */
var _rSection = null;

/**
 * @properties={typeid:35,uuid:"B0DFAFC6-A910-4C2C-8F66-62DE28BE567E",variableType:-4}
 */
var _bUsingMultiSig = false;

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 *
 * @properties={typeid:24,uuid:"5848203A-BEF2-4A9A-BD70-915DB9549A23"}
 */
function show(rSection){
    if (forms._sa_order_est_base.getOrderEditStatus(null, null, controller.getName()) && !forms.sa_order_revision_detail_section_dlg._bCalledFromShopFloor) {
        globals.avUtilities_setFormEditMode(controller.getName(), "edit");
    	controller.readOnly = false;
    }
    else {
        globals.avUtilities_setFormEditMode(controller.getName(), "browse");
    	controller.readOnly = true;
    }

	_rSection = rSection
	_bUsingMultiSig = scopes.avSection.sectionAllowsMultiSignature(_rSection)
	
	if(_bUsingMultiSig){
		var rPress = _rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1)
		var rPressTask = rPress.sa_order_revds_press_to_sa_task.getRecord(1);
		if (forms.sa_order_revision_detail_section_dlg_tab_imposition._divCombID == null) {
			forms.sa_order_revision_detail_section_dlg_tab_imposition._divCombID = rSection.sdivcomb_id;
		}
		forms.sa_order_revision_detail_section_dlg_tab_imposition.setSigVL(_rSection, rPressTask);
		_sigsChosen = forms.sa_order_revision_detail_section_dlg_tab_imposition._sigsChosen; // set in setSigVL() above
		_aSigs = [_sigsChosen]
		elements.fldDivisions.visible = false
		elements.fldSignatures.visible = true
	}
	else{
		_divCombID = rSection.sdivcomb_id;
		elements.fldDivisions.visible = true
		elements.fldSignatures.visible = false
	}
	
	loadRecords()
}

///**

// * TODO generated, please specify type and doc for the params

// * @param oldValue

// * @param newValue

// * @param event

// * @param bSkipPrePostProcess

// *

// * @properties={typeid:24,uuid:"8F8D091F-D0BD-4384-8A8A-D03DA26694AC"}

// */

//function onDataChange_machineVar(oldValue, newValue, event, bSkipPrePostProcess)

//{

//	/***@type {JSRecord<db:/avanti/sa_order_revds_task>}*/

//	var rTask = foundset.getSelectedRecord(),

//		rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getSelectedRecord(),

//		rSec = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),

//		i = 0,

//		iTaskQtySize = 0,

//		/** @type {String} */ 

//		sFold = null;

//	

//	if (oldValue != newValue)

//	{

//		if (!bSkipPrePostProcess) preProcess();	

//		

//		iTaskQtySize = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getSize();

//		for (i = 1; i <= iTaskQtySize; i++)

//		{

//			rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(i);

//		

//		

//			if (!newValue) 

//			{

//				rTaskQty.taskmachine_id_over = null;

//				rTaskQty.taskmachine_id = null;

//				globals.avSales_jdfFoldUUID = null;

//				rTaskQty = globals.avCalcs_task_setTask(rTask,rTaskQty);

//				globals.avSales_jdfFoldUUID = rTaskQty.taskmachine_id;

//			}

//			else

//			{

//				rTaskQty.taskmachine_id = newValue;

//				rTaskQty.taskmachine_id_over = newValue;

//				globals.avSales_jdfFoldUUID = newValue;

//			}

//			

//			// Set the setup time

//			rTaskQty.tasksetup_id_over = null;

//			rTaskQty.taskspoil_id_over = null;

//			rTaskQty.ordrevdstqty_spoils_over = null;

//			databaseManager.saveData(rTaskQty);

//			

//			var iTaskTypeID = rTask.sa_order_revds_task_to_sa_task.tasktype_id;

//			var sMachDesc = globals.avCalcs_task_getMachineDescription(rTask, rTaskQty);

//			

//			databaseManager.saveData(rTask);

//			

//			// Set the setup variable

//			if (rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_variable_data == 0 || 

//				!rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_variable_data)

//			{

//				rTaskQty.tasksetup_id = globals.avCalcs_task_getVariableSetupID(rTask, sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_difficulty, sMachDesc);

//			}

//			

//			// If this is a cutter, edge binding, die cutting then set the setup and spoils variable to match

//			if (iTaskTypeID == 13 || iTaskTypeID == scopes.avTask.TASKTYPEID.EdgeBinding || iTaskTypeID == 35)

//			{

//				rTaskQty = globals.avCalcs_task_setTask(rTask, rTaskQty);

//				

//	//			rTaskQty.tasksetup_id = globals.avCalcs_task_getVariableSetupID(rTask, sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_difficulty, sMachDesc);

//	//			rTaskQty.taskspoil_id = globals.avCalcs_task_getVariableSpoilageID(rTask, sMachDesc);

////			}

//			// If this is prepress, binder, finisher or other, then set the spoils variable to match if using Feeders/Other

//			if ((iTaskTypeID == 12 || iTaskTypeID == 14 || iTaskTypeID == 17) && 

//				(foundset.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_machine_var_based_on == "F" || 

//				foundset.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_machine_var_based_on == "O") || iTaskTypeID == 6 )

//			{

//				rTaskQty.taskspoil_id = globals.avCalcs_task_getVariableSpoilageID(rTask, sMachDesc);

//				

//				// Also need to update the item qty based on unit consumed

//				var iConsume = 0;

//				if (rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_inv_unit > 0) 

//				{

//					iConsume = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_inv_unit;

//				}

//				else

//				{

//					rTaskQty.item_id = null;

//				}

//				rTaskQty.ordrevdstqty_qty_item = iConsume * rTaskQty.ordrevdstqty_qty_task;

//			}

//			

//			if (iTaskTypeID == 11 && rTask.ordrevdstask_fold_type == "S" && rTaskQty.sa_order_revds_task_qty_to_sa_task_machine)

//			{

//				globals.avSales_jdfFoldUUID = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.jdffold_id;

//				// GD - 2013-04-03: Added fix for SL-822; manual override of folding pattern is not sticking

//				if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid))

//				{

//					rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_order_revds_press_pool.over_jdffold_id = globals.avSales_jdfFoldUUID; 

//				}

//			}

//			else if (iTaskTypeID == 11)

//			{

//				// GD - 2013-04-03: Added fix for SL-822; manual override of folding pattern is not sticking

//				if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid))

//				{

//					rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_order_revds_press_pool.over_jdffold_id = null; 

//				}

//			}

//			else if (iTaskTypeID == scopes.avTask.TASKTYPEID.Laminating || iTaskTypeID == 19)

//			{	

//				rTaskQty.item_id = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.getRecord(1).item_id;

//			}

//		}

//		

//		// Set the section taskmachineID to the same thing

//		if (iTaskTypeID == 9 || iTaskTypeID == 10 || iTaskTypeID == 3)

//		{

//			rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord().taskmachine_id = newValue;

//		}

//		

//		

//		

//		// If this is a folder, then compute press values for the new JDF pattern

//		if (iTaskTypeID == 11 && rTask.ordrevdstask_fold_type == "S" && rTaskQty.sa_order_revds_task_qty_to_sa_task_machine)

//		{

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				if (globals.avSales_jdfFoldUUID) sFold = globals.avSales_jdfFoldUUID.toString();

//				forms.sa_order_revision_detail_section.btnCalculate(null, rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord(), false, sFold, true);

//			}

//		}

//		else if (iTaskTypeID == 11)

//		{

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				// GD - 2013-04-03: Added fix for SL-822; manual override of folding pattern is not sticking

//				forms.sa_order_revision_detail_section.btnCalculate(null, rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord(), true);

//			}

//

//		}

//		else if (iTaskTypeID == scopes.avTask.TASKTYPEID.Laminating || iTaskTypeID == 19)

//		{	

////			rTaskQty.item_id = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.getRecord(1).item_id;

//			

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				recalcTaskSpoilage();

//				

////				recalcTaskTotals();

//			}

//		}

//		else if (iTaskTypeID == 24)

//		{			

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				recalcTaskTotals();

//			}

//		}

//		else if (iTaskTypeID == 21 

//				|| iTaskTypeID == 25

//				|| iTaskTypeID == scopes.avTask.TASKTYPEID.EdgeBinding

//				|| iTaskTypeID == 27

//				|| iTaskTypeID == 30

//				|| iTaskTypeID == 31

//				|| iTaskTypeID == 32

//				|| iTaskTypeID == 33

//				|| iTaskTypeID == 35

//				)

//		{

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				forms.sa_order_revision_detail_section.btnCalculate(null, rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord());

//				forms.sa_order_revision_detail_section.btnCalculate(null, rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord());

//			}

//		}

//		else if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid) && 

//				rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.tasktype_id == 3)

//		{

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				forms.sa_order_revision_detail_section.btnCalculate(null, rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord());

//	//			forms.sa_order_revision_detail_section.reCalcEstimate(rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getSelectedRecord());

//			}

//		}

//		else

//		{

//			// GD - 2013-10-10: SL-1379 - Performance Tuning

//			if (!forms._sa_order_est_base.setSectionIsChangedFlag(rSec))

//			{

//				recalcTaskSpoilage();

//				

////				recalcTaskTotals();

//			}

//		}

//		

//		if (!bSkipPrePostProcess) postProcess();

//	}

//	

//	return true

//}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"2B63DEDF-213F-4248-A140-7F4B1F88948F"}
 */
function loadRecords(){
	var aPageCounts = []
	var aReturn = []
	var aDisplay = []
	var rPress = _rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1)
	var rSect
	
	if(_rSection.ordrevds_line_type == 'D'){
		rSect = _rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent
	}
	else{
		rSect = _rSection
	}
	
	if(!_bUsingMultiSig){
		_aSigs = []
		
		for(var i=1;i<=rSect.sa_order_revision_detail_section_to_sa_order_revds_div_combination.getSize();i++){
			var rec = rSect.sa_order_revision_detail_section_to_sa_order_revds_div_combination.getRecord(i) 
			aReturn.push(rec.sdivcomb_id)
			aDisplay.push(rec.sdivcomb_desc)
			
			if(rec.sdivcomb_id == _divCombID){
				_aSigs = rec.sdivcomb_desc.split(',')
			}
		}

		application.setValueListItems("avSales_sectionDivisions", aDisplay, aReturn);
	}
	
	for(i=0;i<_aSigs.length;i++){
		var aNums = _aSigs[i].split('x')
		
		if(_rSection.ordrevds_line_type == 'D'){
			// sl-5708 - dont need to lookat tot numpages - numpages is good enough
//			var nTotPages = parseInt(aNums[0]) * parseInt(aNums[1])
//			if(nTotPages == _rSection.ordrevds_pages){
			if(parseInt(aNums[1]) == _rSection.ordrevds_pages){
				aPageCounts.push(aNums[1]);
				break;
			}
		}
		else{
			aPageCounts.push(aNums[1])
		}
	}
	
	/*** @type {JSFoundset<db:/avanti/sa_order_revds_model>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revds_model')
	if(fs.find()){
		for(i=0;i<aPageCounts.length;i++){
			fs.newRecord()
			fs.ordrevds_id = rSect.ordrevds_id
			fs.task_id = rPress.task_id
			fs.sa_order_revds_model_to_app_imposition_model.impmodel_pages = parseInt(aPageCounts[i])			
		}
		
		if(fs.search()){
			foundset.loadRecords(fs)
			
			// sl-4734 - checkbox display now driven by ordrevdsmodel_show_selected - update for old recs
			if(!scopes.avDB.doesFSHaveValue(fs, 'ordrevdsmodel_show_selected', 1)){
				var max = fs.getSize()
				for(i=1;i<=max;i++){
					var r = fs.getRecord(i)
					if(r.ordrevdsmodel_selected == 1){
						r.ordrevdsmodel_show_selected = 1 // sl-4734   
						databaseManager.saveData(r)
						break
					}
				}
			}

			if(!scopes.avDB.doesFSHaveValue(fs, 'ordrevdsmodel_show_selected', 1)){
				max = fs.getSize()
				for(i=1;i<=max;i++){
					r = fs.getRecord(i)
					if(r.ordrevdsmodel_id == rPress.ordrevdsmodel_id){
						// sl-4734 - for modelled selection - just set new ordrevdsmodel_show_selected which controls whether the checkbox show clicked. dont set ordrevdsmodel_selected, which indicates the user has 
						// manually selected this option
						r.ordrevdsmodel_show_selected = 1   
						databaseManager.saveData(r)
						break
					}
				}
			}
			///
		}
	}
	
	
}

/**
 * TODO generated, please specify type and doc for the params
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"AF780C4D-44F1-48F6-A562-C78DEACD3019"}
 */
function onDataChange_divComb(oldValue, newValue, event) {
	if(_bUsingMultiSig){
		forms.sa_order_revision_detail_section_dlg_tab_imposition.onDataChange_signatures(oldValue, newValue, event)
	}
	else{
		_divCombID = newValue
		loadRecords()
		forms.sa_order_revision_detail_section_dlg_tab_imposition.onDataChange_divisions(oldValue,newValue,event)
	}
	return true
}

/**
 * @properties={typeid:24,uuid:"F816EF85-F496-445B-891B-F7FE5364FBF6"}
 */
function loadFolderVL(){
	/***@type {JSFoundset<db:/avanti/sa_task_machine>} */
	var fsFolderFolds = scopes.avDB.getFS('sa_task_machine', ['sa_task_machine_to_sa_task.tasktype_id', 'jdffold_id', 'sa_task_machine_to_sa_task.task_active'], [11, jdffold_id, 1])
	scopes.avVL.loadVLFromFS('vl_folders_by_fold', fsFolderFolds, 'sa_task_machine_to_sa_task.task_id', ['sa_task_machine_to_sa_task.task_description'])
}

/**
 * @return {JSRecord<db:/avanti/sa_order_revds_model>}
 * @properties={typeid:24,uuid:"9FB8B200-1135-4FD1-AED4-2600C05EC81C"}
 */
function getSelectedModel(){
	var max = foundset.getSize()
	
	for(var i=1;i<=max;i++){
		var r = foundset.getRecord(i)
		if(r.ordrevdsmodel_selected){
			return r
		}
	}
	
	return null
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C67BFF25-3B95-4C07-A722-2B571809035B"}
 */
function onDataChange_selected(oldValue, newValue, event) {
    
    var rSection = sa_order_revds_model_to_sa_order_revision_detail_section.getRecord(1),
        rFolder = scopes.avSection.getMainFolder(rSection),
        rFolderQty = null;
    
    if (newValue == 1) {
        
        scopes.avDB.update('sa_order_revds_model', ['ordrevds_id', 'ordrevdsmodel_pages'], [ordrevds_id, ordrevdsmodel_pages],
            ['ordrevdsmodel_selected', 'ordrevdsmodel_show_selected', 'ordrevdsmodel_folder_task_id'], [0, 0, null]);
        
        // Also need to clear any fold pattern overrides for this folder and this qty
        if (rFolder && utils.hasRecords(rFolder.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)) {

            rFolderQty = rFolder.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1);

            rFolderQty.taskmachine_id = scopes.avDB.getVal('sa_task_machine', ['task_id', 'jdffold_id'], [rFolder.task_id, jdffold_id], 'taskmachine_id');
            rFolderQty.taskmachine_id_over = rFolderQty.taskmachine_id;

        }

        ordrevdsmodel_show_selected = 1;
        ordrevdsmodel_selected = 1;

        // sl-5423 - if multi press in press pool ha to update model rec for each press - otherwise it discards override on this press for cheaper default on other press
        scopes.avDB.update('sa_order_revds_model', ['ordrevds_id', 'ordrevdsmodel_pages', 'jdffold_id', 'ordrevdsmodel_imposition'], [ordrevds_id, ordrevdsmodel_pages, jdffold_id, ordrevdsmodel_imposition],
            ['ordrevdsmodel_selected'], [1]);

        // clear impo over if there is a conflict
        if (_rSection.ordrevds_over_imposition != ordrevdsmodel_imposition) {
            _rSection.ordrevds_over_imposition = null;
            databaseManager.saveData(_rSection);
        }

        databaseManager.saveData(foundset);
    }
    else {
        ordrevdsmodel_selected = 0;
        ordrevdsmodel_folder_task_id = null;
    }
    return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3844B2FF-9CED-4B42-8381-831249E73972"}
 */
function onDataChange_folder(oldValue, newValue, event) {	
	if (newValue){
		var rFolder = scopes.avSection.getMainFolder(_rSection)
		if(rFolder && rFolder.task_id != newValue){
			rFolder.task_id = newValue
			databaseManager.saveData(rFolder)
			forms._sa_order_est_base.setVL_salesSectionTasks()
		}
	}

	return true
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @properties={typeid:24,uuid:"E590D5CB-8881-40DA-AD45-986558EBBD68"}
 */
function onRecordSelection(_event, _form) {
	 _super.onRecordSelection(_event, _form)
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"F4CC9DEE-A0C6-4BA3-B971-58A552E0E052"}
 */
function onFocusGained_folder(event) {
	if(ordrevdsmodel_selected){
//		loadFolderVL()
		elements.grid.setReadOnly(false, ["fldFolder"])
	}
	else{
		elements.grid.setReadOnly(true, ["fldFolder"])
	}
}
