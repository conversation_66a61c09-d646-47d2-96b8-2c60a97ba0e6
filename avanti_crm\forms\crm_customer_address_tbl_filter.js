/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A8BD7AF8-08A3-4926-861A-8E7F0807C22A"}
 */
var currentView = globals.avSales_CRM_MyCustomers;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"77F21B24-8067-45A1-B2BF-CF3EAC9B92E2"}
 */
var selectedRep = null;

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"3F42AA32-139F-48E2-8FE3-A283C5C9FA8A"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
			
	var result =  _super.onShowForm(firstShow, event);
	applyFilters(firstShow);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * TODO generated, please specify type and doc for the params
 * @param readonly
 *
 * @properties={typeid:24,uuid:"1FC56B57-D6BD-4213-8832-72E78D1EC771"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"266E3393-2FA3-4850-9E14-E7A48DA2EE5A"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	
	// Set globals and default view to not show managed sales reps.
	elements.managed_sales_reps.visible = false
	elements.managed_sales_reps_lbl.visible = false
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView == "Dashboard")
	{
		
	}
	else
	{
		applyFilters(true);
	}
	
}

/**
 * @AllowToRunInFind
 * 
 * Apply foundset filters.
 * @param bReApplyFilters
 *
 * @properties={typeid:24,uuid:"85859B86-A0B8-4DB1-8D9B-8EECCA07E7D7"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		// Remove any filters.
		forms.crm_customer_address_tbl.foundset.removeFoundSetFilterParam('employeeFilter');
		forms.crm_customer_address_tbl.foundset.removeFoundSetFilterParam('oneTimeFilter');
		
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
						// If a sales rep, filter by salesper_id
						case globals.avSales_CRM_SalesRep:
							if(utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=',employee_fs.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
							} else {
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						// If a sales manager, filter by salesper_id and add the 'Sales Manager' dropdown and make the managed sales rep drop down visible
						case globals.avSales_CRM_SalesManager:
							if (currentView == globals.avSales_CRM_AllCustomers) {
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
							
							if(currentView == globals.avSales_CRM_AllCustomers && selectedRep != null && selectedRep != '') {
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=', selectedRep,'employeeFilter')
							} else if (currentView == globals.avSales_CRM_AllCustomers && selectedRep == null) {
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where salesper_id in (select salesper_id from sys_employee_managed_reps where empl_id = '" + globals.avBase_employeeUUID + "')",'employeeFilter')
							} else if(currentView == globals.avSales_CRM_MyCustomers && utils.hasRecords(employee_fs.sys_employee_to_sa_sales_person)){
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=', employee_fs.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
							} else {
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							}
							break;
						case globals.avSales_CRM_CustomerServiceRep:
							forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('cust_id','sql:in',"select cust_id from sa_customer where cust_csr_empl_id = '" + globals.avBase_employeeUUID + "'",'employeeFilter')
							break;
						// If an admin
						case globals.avSales_CRM_Administrator:
							if(currentView == globals.avSales_CRM_AllCustomers){
								elements.managed_sales_reps.visible = true
								elements.managed_sales_reps_lbl.visible = true
							}
														
							if(selectedRep != null && selectedRep != '') {
								forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=', selectedRep,'employeeFilter')
							}
							break;
						default:
							// Filter by invalid uuid.
							forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
							break;
				
					}
				} else {
					// Filter by invalid uuid.
					forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('salesper_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
				}
			}
		}
		forms.crm_customer_address_tbl.foundset.addFoundSetFilterParam('custaddr_one_time','^||!=','1','oneTimeFilter')
		forms.crm_customer_address_tbl.foundset.loadAllRecords();
		forms.crm_customer_address_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.crm_customer_address_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		// Reload the table and run initial sort on it.
		//forms.crm_customer_address_tbl.foundset.loadRecords(customer_addr_fs)
	}
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"E2009E16-8560-436B-BD44-2EFB0D71EB2E"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	
	// GD - 2013-06-19: Coming in from a dashboard widget that will load its own foundset
	if (currentView != "Dashboard")
	{
		// Get employee information and their assignment type. 
		/** @type{JSFoundSet<db:/avanti/sys_employee>} */
		var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
		if(employee_fs.find() || employee_fs.find()) {
			employee_fs.empl_id = globals.avBase_employeeUUID
			if(employee_fs.search() > 0)
			{
				if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
					if(employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_SalesManager || employee_fs.sys_employee_to_app_assignment_type.assignment_desc == globals.avSales_CRM_Administrator) {
						currentView = globals.avSales_CRM_AllCustomers
					}
				}
			}
		}
	}
	
	return _super.onLoad(event)
}
