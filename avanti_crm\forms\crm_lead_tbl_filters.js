/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AE1AD447-68BC-4A04-975F-6B5F2AFA1A26"}
 */
var currentView = i18n.getI18NMessage('avanti.lbl.leadsView_myActiveLeads');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"67B73CD2-38E1-4E44-8F09-4B5FE1B6A120"}
 */
var dueDateFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"D4E68DA8-EDD6-4F5E-95C0-6628DEC01241"}
 */
var employeeFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"96EB64FB-E68B-4DA6-B4E4-B8A02726D71F"}
 */
var priorityFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"45F3DAC0-14AD-4C10-90D8-2B79BA43DF16"}
 */
var regardingFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"360FB394-7EA4-4E93-B600-12BAF4216EC1"}
 */
var typeFilter = i18n.getI18NMessage('avanti.lbl.all');

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"DE3A4271-FA53-40DA-9C9B-0DB449FCF358"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {
	// set to edit mode.
	var result =  _super.onShowForm(firstShow, event);
	applyFilters(firstShow);
	application.executeLater(setReadonly, 550, [false]);
	return result;
	
}

/**
 * @param readonly
 *
 * @properties={typeid:24,uuid:"049E83AE-4DBC-49D8-A173-************"}
 */
function setReadonly(readonly) {
	this.controller.readOnly = readonly;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"240847CC-80B7-4FF6-8438-A6AAFA89A9F6"}
 * @AllowToRunInFind
 */
function refreshUI(event) {
	
	applyFilters(true);
}

/**
 * Apply foundset filter
 * @param {Boolean} bReApplyFilters
 *
 * @properties={typeid:24,uuid:"DCFA4135-698B-41ED-8C59-E316E9EF5B23"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		// Remove any filters.
		forms.crm_lead_tbl.foundset.removeFoundSetFilterParam('statusFilter');
		forms.crm_lead_tbl.foundset.removeFoundSetFilterParam('employeeFilter') 
		
		// Assign employee and status filters based on selected view 
		if(currentView == i18n.getI18NMessage('avanti.lbl.leadsView_myActiveLeads')) 	{
			if(utils.hasRecords(_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person)) {
				forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
			} else {
			   forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
			}
			
			forms.crm_lead_tbl.foundset.addFoundSetFilterParam('is_active','=', '1','statusFilter')
		} else if (currentView == i18n.getI18NMessage('avanti.lbl.leadsView_myInactiveLeads')) {
			if(utils.hasRecords(_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person)) {
				forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
			} else {
				forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
			}
			forms.crm_lead_tbl.foundset.addFoundSetFilterParam('is_active','=', '0','statusFilter')
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.leadsView_myAllLeads')) {
			if(utils.hasRecords(_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person)) {
				forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',_to_sys_employee$avbase_employeeuuid.sys_employee_to_sa_sales_person.salesper_id,'employeeFilter')
			} else {
				forms.crm_lead_tbl.foundset.addFoundSetFilterParam('lead_owner_id','=',scopes.avUtils.ENUM_DEFAULT_GUID.Default,'employeeFilter')
			}
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.leadsView_allActiveLeads')) 	{
			forms.crm_lead_tbl.foundset.addFoundSetFilterParam('is_active','=', '1','statusFilter')
		} else if (currentView == i18n.getI18NMessage('avanti.lbl.leadsView_allInactiveLeads')) {
			forms.crm_lead_tbl.foundset.addFoundSetFilterParam('is_active','=', '0','statusFilter')
		} else if(currentView == i18n.getI18NMessage('avanti.lbl.leadsView_allLeads')) {
			
		}  else {
			
		}
			
		forms.crm_lead_tbl.foundset.loadAllRecords();
		forms.crm_lead_tbl.elements.grid.myFoundset.foundset.loadRecords(forms.crm_lead_tbl.foundset);
		forms.utils_quickSearch._qs_quickSearch = '';
		
		// Reload the table and run initial sort on it.
		//forms.crm_lead_tbl.foundset.loadRecords(foundset)
	}
}

/**
 * Callback method when form is (re)loaded.
 * Load valuelists of dropdowns for filters
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"0ABA6C17-E226-461A-A3BA-78374C65381F"}
 * @AllowToRunInFind
 */
function onLoad(event) {
	var vlFilterRealValues = new Array();
	var vlFilterDisplayValues = new Array();
	
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myActiveLeads'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myActiveLeads'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myInactiveLeads'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myInactiveLeads'))
	vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myAllLeads'))
	vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_myAllLeads'))
	
	// Get employee information and their assignment type. 
	/** @type{JSFoundSet<db:/avanti/sys_employee>} */
	var employee_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee')
	if(employee_fs.find() || employee_fs.find()) {
		employee_fs.empl_id = globals.avBase_employeeUUID
		if(employee_fs.search() > 0)
		{
			if(utils.hasRecords(employee_fs.sys_employee_to_app_assignment_type)) {
				switch (employee_fs.sys_employee_to_app_assignment_type.assignment_desc) {
					// If an admin
					case globals.avSales_CRM_Administrator:
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allActiveLeads'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allActiveLeads'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allInactiveLeads'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allInactiveLeads'))
						vlFilterDisplayValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allLeads'))
						vlFilterRealValues.push(i18n.getI18NMessage('avanti.lbl.leadsView_allLeads'))
						break;
					default:
						break;	
				}
			}
		}
	}
	
	application.setValueListItems('vl_leadView', vlFilterDisplayValues, vlFilterRealValues);
	
	vlFilterRealValues = new Array();
	vlFilterDisplayValues = new Array();
	
	return _super.onLoad(event)
}

/**
 * Perform the element default action to sort.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5F7BF869-98AA-45B6-915D-493E00905E67"}
 */
function onActionInitialSort(event) {
	
}
