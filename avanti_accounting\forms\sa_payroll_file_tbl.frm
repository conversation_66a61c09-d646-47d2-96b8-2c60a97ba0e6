customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_payroll_batch",
extendsID:"68A30514-**************-CD3BECD3BEE0",
items:[
{
height:255,
partType:8,
typeid:19,
uuid:"507C64FD-E2A3-451D-B904-2F37829941BD"
},
{
height:250,
partType:5,
typeid:19,
uuid:"59C0CFCB-9E7D-4803-A9FF-7457CDAF31D0"
},
{
anchors:15,
cssPosition:"0px,0px,5px,0px,925px,250px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
format:null,
headerStyleClass:"table",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined fiber_manual_record",
svyUUID:"DA4E6B91-**************-55B9B1AF92BD",
valuelist:null,
visible:true,
width:20
},
{
autoResize:true,
dataprovider:"prb_name",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.batchName",
id:"batch_name",
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"72088BC3-1D06-4557-A8EB-7F3CD4E70A82",
valuelist:null,
visible:true,
width:80
},
{
autoResize:true,
dataprovider:"prb_date_created",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.DateCreated",
id:"date_created",
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"AD8660D3-F169-4C19-9D1D-FC3D8D776706",
valuelist:null,
visible:true,
width:120
},
{
autoResize:true,
dataprovider:"sa_payroll_batch_to_sys_employee$created_by.empl_full_name",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.createdBy",
id:"created_by",
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"AA6A1A2E-E500-4732-9BD7-D5C10743C8A1",
valuelist:null,
visible:true,
width:160
},
{
autoResize:true,
dataprovider:"prb_date_posted",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.datePosted",
id:"date_posted",
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"AE5DFBFA-783F-4AF1-9E8F-57B79AE44CC8",
valuelist:null,
visible:true,
width:120
},
{
autoResize:true,
dataprovider:"sa_payroll_batch_to_sys_employee$posted_by.empl_full_name",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"table",
headerTitle:"i18n:avanti.lbl.postedBy",
id:"posted_by",
rowGroupIndex:-1,
styleClass:"listview",
svyUUID:"A185D098-D4A5-4FAB-A0B7-65838832FD1E",
valuelist:null,
visible:true,
width:138
}
],
columnsAutoSizing:"AUTO_SIZE",
cssPosition:{
bottom:"5px",
height:"250px",
left:"0px",
right:"0px",
top:"0px",
width:"925px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onReady:"461EB3B8-905E-4D8E-84FC-6F57197B664F",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:null,
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"D3F7D6A0-D447-4BC0-A61A-441A1D3DD2E4"
}
],
name:"sa_payroll_file_tbl",
navigatorID:"-1",
onShowMethodID:"F797688B-236A-4EB1-AA5B-F91D4DDE0EFC",
scrollbars:33,
size:"925,413",
styleName:null,
typeid:3,
uuid:"7BFE11C0-EF18-476B-B2A3-6497C0D2CA5C",
view:0