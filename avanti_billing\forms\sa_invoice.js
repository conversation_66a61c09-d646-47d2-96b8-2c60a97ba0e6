/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3C678B2E-AA95-4C3A-9C6C-91A83EA823DD"}
 */
var sRealNavMode = '';

/**
 * @properties={typeid:35,uuid:"22ADE620-48C2-44ED-83F7-E0BEB9F23664",variableType:-4}
 */
var _bUsingInvoiceDivPlantFilter = false;

/**
 * Set by: System Preferences->Accounting: Ref# 197 - Allow Edit and Delete capabilities for Invoices with a Printed-Final status.
 * @type {Boolean}
 * @properties={typeid:35,uuid:"7CD48673-E5C4-447A-AD22-20CBA2545566",variableType:-4}
 */
var _bHasRightToEditPrintedFinalInvoiceToOpen = false;

/**
 * Set by: System Preferences->Accounting: Ref# 197 - Allow Edit and Delete capabilities for Invoices with a Printed-Final status.
 * @type {Boolean}
 * @properties={typeid:35,uuid:"A46D3F67-1480-46FE-BB62-2A54DE4AA522",variableType:-4}
 */
var _bHasRightToEditPrintedFinalInvoiceToProForma = false;

/**
 * @type {Boolean}
 *  
 * @properties={typeid:35,uuid:"9B57AB0D-40ED-462A-8ED3-204D0A8CADD9",variableType:-4}
 */
var _CancelClicked = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"********-B2B2-471A-A4C9-4A6088A21E09"}
 */
var _StatusFilter = "";

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"DDAF899B-5ADC-4A04-930B-5189BEF14CBF",variableType:4}
 */
var _PaidFilter = null;

/**
 * @type {String}
 * 
 * @properties={typeid:35,uuid:"FC72AABF-83B2-4911-9393-270859A5D63C"}
 */
var _ReadyFilter = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"057CD96C-BEE8-47EB-B0A5-63BA9C52A24C"}
 */
var typeFilter = null;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"685FCDEE-FE1B-4858-B635-C9DF38ED2FA5",variableType:-4}
 */
var _EditPermission = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0BDAF733-0D3B-43E2-8A1A-D5A788FE1148"}
 */
var _InvoiceTypeFilter = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D63FC477-F921-4DA7-A1BE-580506D6060F",variableType:8}
 */
var _IntegrationStatusFilter = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3CC3A40B-4819-45D6-8524-D788382F462E",variableType:4}
 */
var _bWorkatoUseInvoiceRegister = null;

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"790117E2-1F6E-4556-B6B6-C41BE1452BAE",variableType:-4}
 */
var _bWorkatoIntegration = false;

/** *
 * @param _foundset
 *
 * @properties={typeid:24,uuid:"2F70018E-6C98-41A1-9E9C-588A07A35DC0"}
 */
function dc_new_post(_foundset) {
	 _super.dc_new_post(_foundset);
	 
	 inv_number = "** NEW **";
	 inv_date = new Date();
     inv_type = scopes.avUtils.INVOICE_TYPE.Standard;
     inv_status = 'O';   
    
     if (globals.nav_base_form_name == 'sa_credit_invoice') {
         inv_record_type = 'C';
     } else {
         inv_record_type = 'I';
     } 
	 
     forms.sa_invoice_distribution_dtl.elements.lblDistributionNotBalanced.visible = false;
     
    if ("sa_invoice_dtl" == globals.nav.form_view_01) {
        scopes.globals.avUtilities_tabSetSelectedIndex("sa_invoice_dtl", "tabs_235", 1);
    } 
    else if ("sa_credit_invoice_dtl" == globals.nav.form_view_01) {
    	scopes.globals.avUtilities_tabSetSelectedIndex("sa_credit_invoice_dtl", "tabs_235", 1);
    }
     
     setCustIdAccess();     
     
	 showSelectionDialog();	 
}

/**
 * showSelectionDialog
 *
 * @properties={typeid:24,uuid:"F82AB7A2-3C36-422E-AE09-A9820ED7E958"}
 */
function showSelectionDialog() 
{
    if (!scopes.avUtils.isNavModeReadOnly()) {
        if (globals.nav_base_form_name == 'sa_credit_invoice') {
            globals.DIALOGS.showFormInModalDialog(forms.sa_credit_invoice_dialog, -1, -1, -1, 750, 
                i18n.getI18NMessage("avanti.dialog.availableToCreditInvoices_title"), true, false, "dlgCreditNoteInvoiceDialog", true);
        }
        else {
            globals.DIALOGS.showFormInModalDialog(forms.sa_invoice_order_dialog, -1, -1, -1, 750, 
                i18n.getI18NMessage("avanti.dialog.readyToInvoiceOrders_title"), true, false, "dlgInvoiceOrderDialog", true);
        }
    }
}

/**
 * refreshUI
 *
 *
 * @properties={typeid:24,uuid:"60F63778-0094-4BFB-9B54-3A95B0798BDD"}
 */
function refreshUI()
{
	 // RG 2014-05-08 SL-2467 updated to move formatting to each forms own refresh UI to allow change in currency format
	
	setToolBarOptions();
}

/** *
 * @param {JSFoundset} _foundset
 *
 * @return
 * @properties={typeid:24,uuid:"CDA60894-1AB9-4C6B-AA96-61C9330F8BF6"}
 */
function dc_save_pre(_foundset) {

    _super.dc_save_pre(_foundset);
    if (!_foundset || _foundset.getDataSource() != 'db:/avanti/sa_invoice') {
        _foundset = foundset;
    }
    /** @type {JSRecord<db:/avanti/sa_invoice>} **/
    var rRec = _foundset.getSelectedRecord();
    var bAllowCreditNotesForZeroBalanceInvoices = Boolean(scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AllowCreditNotesForZeroBalanceInvoices));
    
    if (scopes.avAccounting.isInvoicingPeriodClosedForDate(rRec.inv_date, rRec.org_id)) {
        globals.DIALOGS.showWarningDialog(
            i18n.getI18NMessage('servoy.general.warning'), 
            i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'), 
            i18n.getI18NMessage('avanti.dialog.ok'));
        return -1;
    }
    
    //Ensure there is a valid customer now that the field allows nulls
    if (rRec && !utils.hasRecords(rRec.sa_invoice_to_sa_customer)) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.customerRequired"),
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;
    }
    
    //Ensure there is a valid customer bill to now that the field allows nulls
    if (rRec && !utils.hasRecords(rRec.sa_invoice_to_sa_customer_address$billto)) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.customerBillToRequired"),
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;
    }

    //Ensure invoice total is not negative and credit note total is not positive.
    if (rRec.inv_record_type == "C" && rRec.inv_subtotal_amt > 0) {
        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.positiveCreditNoteSubTotal"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;
    }
    else if (rRec.inv_record_type == 'I' && rRec.inv_subtotal_amt < 0 && hasNegativeProductionLine(rRec)) {
        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.negativeInvoiceSubTotal"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;

    }
    else if (rRec.inv_record_type == 'I' && rRec.inv_total_amt < 0) {
        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.negativeInvoiceTotal"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;

    }
    else if (rRec.inv_record_type == "C"  && rRec.sa_invoice_to_sa_invoice_det.getSize() == 0) {
        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"),
            i18n.getI18NMessage("avanti.dialog.CreditNote_doesnothave_line"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;

    }
    
    if (rRec.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
            && utils.hasRecords(sa_invoice_to_sa_invoice$credit_memo_invoice)
            && !bAllowCreditNotesForZeroBalanceInvoices) {
        var nInvoiceBalanceAfterCreditMemo = sa_invoice_to_sa_invoice$credit_memo_invoice.clc_balance_owing_rounded;
        var nInvoiceBalanceBeforeCreditMemo = nInvoiceBalanceAfterCreditMemo - clc_balance_owing_rounded;
        if (nInvoiceBalanceAfterCreditMemo < 0) {
            var nInvoiceBalanceBeforeCreditMemoFormatted = utils.numberFormat(nInvoiceBalanceBeforeCreditMemo,"$#,##0.00");
            sMessage = i18n.getI18NMessage("avanti.dialog.creditNoteExceedsInvoiceBalance_msg").replace("{amount}",nInvoiceBalanceBeforeCreditMemoFormatted);
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.notification"),
                sMessage,
                i18n.getI18NMessage("avanti.dialog.ok"));
            return -1;
        }
    }

    //Show warning if distribution out of balance.
    if (!forms.sa_invoice_distribution_dtl.verifyDistribution()) {
        var sMessage = "";
        if (inv_record_type == 'C') {
            sMessage = i18n.getI18NMessage("avanti.dialog.creditNoteDistributionOutOfBalance_msg");
            forms.sa_credit_invoice_dtl.elements.tabs_235.tabIndex = 3 // tabDistributions
        }
        else {
            sMessage = i18n.getI18NMessage("avanti.dialog.invoiceDistributionOutOfBalance_msg");
            forms.sa_invoice_dtl.elements.tabs_235.tabIndex = 3 // tabDistributions
        }

        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"), 
            sMessage, i18n.getI18NMessage("avanti.dialog.ok"));
    }
    
    if (utils.hasRecords(rRec.sa_invoice_to_sa_invoice_journal_entry)
    		&& rRec.inv_journal_entry_oob_amount
    		&& rRec.inv_journal_entry_oob_amount != 0) {
        sMessage = "";
        if (inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote) {
            sMessage = i18n.getI18NMessage("avanti.dialog.creditNoteJournalEntryOrDistributionTypeOutOfBalance_msg");
            scopes.globals.avUtilities_tabSelect('sa_credit_invoice_dtl', 'tabs_235', 'tabJournalEntries');
        }
        else {
            sMessage = i18n.getI18NMessage("avanti.dialog.invoiceJournalEntryOutOfBalance_msg");
            scopes.globals.avUtilities_tabSelect('sa_invoice_dtl', 'tabs_235', 'tabJournalEntries');
        }

        globals.DIALOGS.showErrorDialog(
            i18n.getI18NMessage("avanti.dialog.notification"), 
            sMessage, i18n.getI18NMessage("avanti.dialog.ok"));
    }

    // GD - Sep 13, 2016: SL-9757: Users complaining that an invoice number is not getting assigned. I found it is getting assigned, but not saving.
    // Trying a different save approach here to ensure the invoice number is getting saved.
    rRec = _foundset.getSelectedRecord();
    if (rRec && rRec.inv_number == null || rRec.inv_number == "** NEW **") {
        if (rRec.inv_record_type == 'I') {
            rRec.inv_number = globals.GetNextDocumentNumber("INV", "0");
            rRec.inv_docstream_type = "INV";
        }
        if (rRec.inv_record_type == 'C') {
            rRec.inv_number = globals.GetNextDocumentNumber("CRE", "0");
            rRec.inv_docstream_type = "CRE";
        }

        databaseManager.saveData(rRec);

        moveDocumentsToFinalLocation(rRec);
    }
    
    if (rRec.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.Invoice) {
        forms.sa_invoice_dtl.recalculateDistributions();
    }
    else if (rRec.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && rRec.inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.Rebill) {
        forms.sa_credit_invoice_dtl.recalculateDistributions();
    }

    // GD - Aug 31, 2016: SL-9757: Tim and I found this for loop and decided it looks wrong; only the current invoice should be updated here, and not every invoice
    //delete the unused tax records
    var rInv = foundset.getSelectedRecord();

    //Delete tax details
    if (rInv) {
        for (var j = rInv.sa_invoice_to_sa_invoice_tax_detail.getSize(); j >= 1; j--) {
            var rInvTaxDet = rInv.sa_invoice_to_sa_invoice_tax_detail.getRecord(j);
            
            if (rInvTaxDet.invtaxdet_taxable_sales_amt == null && rInvTaxDet.invtaxdet_tax_amt == null) {
                rInv.sa_invoice_to_sa_invoice_tax_detail.deleteRecord(rInvTaxDet);
            }
        }
    }

    for (var k = 1; k <= foundset.sa_invoice_to_sa_invoice_det.getSize(); k++) {
        var rInvDet = foundset.sa_invoice_to_sa_invoice_det.getRecord(k);

        for (var l = rInvDet.sa_invoice_det_to_sa_invoice_tax_detail.getSize(); l >= 1; l--) {
            var rInvDetTaxDet = rInvDet.sa_invoice_det_to_sa_invoice_tax_detail.getRecord(l);
            
            if (rInvDetTaxDet.invtaxdet_taxable_sales_amt == null && rInvDetTaxDet.invtaxdet_tax_amt == null) {
                rInvDet.sa_invoice_det_to_sa_invoice_tax_detail.deleteRecord(rInvDetTaxDet);
            }
        }

        for (var m = 1; m <= rInvDet.sa_invoice_det_to_sa_invoice_det_ship.getSize(); m++) {
            var rInvDetShip = rInvDet.sa_invoice_det_to_sa_invoice_det_ship.getRecord(m);

            for (l = rInvDetShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getSize(); l >= 1; l--) {
                var rInvDetShipTaxDet = rInvDetShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getRecord(l);
                if (rInvDetShipTaxDet.invtaxdet_taxable_sales_amt == null && rInvDetShipTaxDet.invtaxdet_tax_amt == null) {
                    rInvDetShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.deleteRecord(rInvDetShipTaxDet);
                }
            }
        }
    }

    return 1;
}

/**
 * Update document paths to permanent location. Meant to move documents from the temporary draft location.
 * 
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * 
 * @properties={typeid:24,uuid:"461A99BD-EFD3-40FC-9570-22975047A8D3"}
 */
function moveDocumentsToFinalLocation(rInvoice) {
    /** @type {JSFoundSet<db:/avanti/sys_document>} **/
    var fsDocument = rInvoice.sa_invoice_to_sys_document;
    for (var n = 1; n <= fsDocument.getSize(); n++) {
        /** @type {JSRecord<db:/avanti/sys_document>} **/
        var rDoc = fsDocument.getRecord(n);
        
        var oldDocPathStorage = rDoc.doc_path_storage;
        
        rDoc.doc_path = forms._docs_base.getInvoicePath();
        
        if (rDoc.doc_is_folder == 1) {
            rDoc.doc_path_storage = rDoc.doc_path;
        }
        else {
            rDoc = forms._docs_base.setFilePathStorage(rDoc);
        }
        
        try {
            plugins.file.moveFile(oldDocPathStorage, rDoc.doc_path_storage);
            
            databaseManager.saveData(rDoc);
        }
        catch (e) {
            application.output("Move File Failed: " + rDoc.doc_path_storage + "\n" + e.message + "\n" + e.stack, LOGGINGLEVEL.ERROR);
        }
    }
}

/**
 * setToolBarOptions
 *
 * @properties={typeid:24,uuid:"7A8BB79D-3D3D-4248-8A1C-9B0543642176"}
 */
function setToolBarOptions() {
    if (scopes.avUtils.isNavModeReadOnly()) {
        if (lockFinalInvoices()) {
			forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
			forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
            
			if (_bHasRightToEditPrintedFinalInvoiceToOpen || _bHasRightToEditPrintedFinalInvoiceToProForma) {
				if (globals.nav.form_view_01 == "sa_credit_invoice_dtl") {
					forms.sa_credit_invoice_dtl.elements.inv_status.enabled = true;
				}
				else {
					forms.sa_invoice_dtl.elements.inv_status.editable = true;
				}
            }
            else{
            	if (globals.nav.form_view_01 == "sa_credit_invoice_dtl") {
					forms.sa_credit_invoice_dtl.elements.inv_status.enabled = true;
				}
				else {
					forms.sa_invoice_dtl.elements.inv_status.editable = true;
				}
            }
		}
        else {
			forms.sa_invoice_dtl.elements.inv_status.editable = false;
			
            if (globals["avSecurity_checkForUserRight"](globals.nav_program_name, "btn_edit", globals.avBase_employeeUserID)) {
				forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
            }
            else {
				forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
			}

            if (globals["avSecurity_checkForUserRight"](globals.nav_program_name, "btn_delete", globals.avBase_employeeUserID) && !hasCashReceipts()) {
                forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
            }
            else {
				forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
			}
		}
	}

}

/**
 * Set record print flag
 *
 * <AUTHOR> Dol
 * @since 2013-11-19
 * 
 * @properties={typeid:24,uuid:"79F446B4-29A9-4D82-BAE5-9F22A5F4EBF6"}
 */
function setPrintCheckFlag() {
	for (var i = 1; i <= foundset.getSize(); i++) 
	{
		var rRec = foundset.getRecord(i);
		
		rRec.print_check_flag = globals.avBase_tableViewInvoiceCheckStatus;
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @properties={typeid:24,uuid:"B7E6655B-9DCF-4000-B8B7-511A68988446"}
 */
function onRecordSelection(_event, _form) {
	scopes.avBilling.$invoice_delete_button_clicked = false;
	_CancelClicked = false;
	
	_super.onRecordSelection(_event, _form)
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * @param {String} _answer
 *
 * @properties={typeid:24,uuid:"72B1B71C-18A0-401C-902B-275A139E7520"}
 */
function dc_cancel(_event, _triggerForm, _answer) {
    
    var rRec = foundset.getSelectedRecord();
    
    if (rRec.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
            && rRec.inv_number != "** NEW **" 
            && utils.hasRecords(rRec.sa_invoice_to_sa_invoice$credit_memo_invoice)) {
        var nInvoiceBalanceAfterCreditMemo = rRec.sa_invoice_to_sa_invoice$credit_memo_invoice.clc_balance_owing_rounded;
        var nInvoiceBalanceBeforeCreditMemo = nInvoiceBalanceAfterCreditMemo - clc_balance_owing_rounded;
        if (nInvoiceBalanceAfterCreditMemo < 0) {
            var nInvoiceBalanceBeforeCreditMemoFormatted = utils.numberFormat(nInvoiceBalanceBeforeCreditMemo, "$#,##0.00");
            var sMessage = i18n.getI18NMessage("avanti.dialog.creditNoteExceedsInvoiceBalance_msg").replace("{amount}", nInvoiceBalanceBeforeCreditMemoFormatted);
            globals.DIALOGS["showErrorDialog"](i18n.getI18NMessage("avanti.dialog.notification"),
                sMessage,
                i18n.getI18NMessage("avanti.dialog.ok"));
            return -1;
        }
    }
    
    var fsInvoiceDetails = forms.sa_invoice_det_tbl.foundset.duplicateFoundSet(),
        rInvoiceDetail = null,
        i = 0,
        k = 0,
        rShipDetail = null,
		SHIP_STATUS = scopes.avShipping.SHIP_STATUS,
        PACK_STATUS = scopes.avUtils.ENUM_PACK_STATUS;
    
    // canceling a new invoice - just delete it 
    if ((globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Add || sRealNavMode == scopes.avUtils.ENUM_NAV_MODE.Add) 
    		&& (inv_number == null || inv_number == "** NEW **")) {
        // Needed to add this because the invoice numbers shoudn't get assigned until you save the invoice but there seems to be some
        // databaseManager.saves along the way that is saving the invoice to the database before the user clicks on save
        // so delete the invoice if not saved when the user hits cancel.
        
        //Added the save to ensure all updates have been performed before we make any adjustments and deletion of the records.
        databaseManager.saveData();
        
        // sl-10739 - remove cogs or when we try to invoice order again no Cost of Sales show in cost center analysis 
        globals.removeCOGS(foundset.getSelectedRecord());
        
        globals.avInvoice_deleteInvoiceDocRecords(foundset.getSelectedRecord().inv_id);
        
        // SL-14716 - moved this clearing of fully invoiced flags to before the invoice is deleted
        for (i = 1; i <= fsInvoiceDetails.getSize(); i++) {
            rInvoiceDetail = fsInvoiceDetails.getRecord(i);

            if (utils.hasRecords(rInvoiceDetail.sa_invoice_det_to_sa_ship)) {
            	var rShip = rInvoiceDetail.sa_invoice_det_to_sa_ship.getRecord(1);
                
                // Set the shipments to confirmed
                if (rShip.sa_ship_to_sa_pack_detail) {
					for (var pd = 1; pd <= rShip.sa_ship_to_sa_pack_detail.getSize(); pd++) {
						var rPackDetail = rShip.sa_ship_to_sa_pack_detail.getRecord(pd);
						
						if (rPackDetail.sa_pack_detail_to_sa_pack.pack_status == PACK_STATUS.Invoiced) {
							if (scopes.avShipping.doesShipmentHaveChargebackPostedLines(rPackDetail.pack_id)) {
								rPackDetail.sa_pack_detail_to_sa_pack.pack_status = PACK_STATUS.ChargebackPosted;
							}
							else {
								rPackDetail.sa_pack_detail_to_sa_pack.pack_status = PACK_STATUS.Confirmed;
							}
						}
					}
                }
            }

            databaseManager.saveData();
        }

        // delete invoice
        _CancelClicked = true;
        scopes.avBilling.$invoice_delete_button_clicked = false;
        _super.dc_delete(_event, _triggerForm, true);
        _super.dc_cancel(_event, _triggerForm, i18n.getI18NMessage('svy.fr.lbl.ok'));
    }
    else {
        _super.dc_cancel(_event, _triggerForm, _answer);
        forms.sa_invoice_distribution_dtl.verifyDistribution();
    }
    
    sRealNavMode = null;
}

/**
 *
 * @param {Boolean} _multiDelete
 *
 * @return
 * @properties={typeid:24,uuid:"9493D663-55B6-447A-B99A-85955F99CA17"}
 */
function dc_delete_message (_multiDelete) {
	
	if (_CancelClicked == false ) scopes.avBilling.$invoice_delete_button_clicked = true;
	var sDeleteMessage = i18n.getI18NMessage("avanti.dialog.invoice_delete_msg");
	if (inv_record_type == 'C')  sDeleteMessage = i18n.getI18NMessage("avanti.dialog.creditNote_delete_msg");
	return sDeleteMessage
	
}

/**
 * @properties={typeid:24,uuid:"E216C311-8F2F-4726-8ED3-8732BCE62411"}
 */
function setCustIdAccess() {
	//Enable customer selection on credit notes not related to an invoice
    if (foundset.inv_record_type == 'I' || ( foundset.inv_record_type == 'C' && foundset.inv_credit_inv_id )) {

        if (foundset.inv_record_type == "C") {
            forms.sa_credit_invoice_dtl.elements.inv_cust_id.enabled = false;
            forms.sa_credit_invoice_dtl.elements.inv_billto_custaddr_id.enabled = false;
            
            scopes.globals.avUtilities_setStyleClass(forms.sa_credit_invoice_dtl.elements.inv_cust_id, scopes.globals.ENUM_COLOR_MODE.browse);
            scopes.globals.avUtilities_setStyleClass(forms.sa_credit_invoice_dtl.elements.inv_billto_custaddr_id, scopes.globals.ENUM_COLOR_MODE.browse);
        }
        else {
            // SL-19191: Temporarily allow user to change the customer on an invoice because we need to fix changing customer on the order after released.
            forms.sa_invoice_dtl.elements.inv_cust_id.enabled = true;
            forms.sa_invoice_dtl.elements.inv_billto_custaddr_id.enabled = true;
            

        }
    }
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
* @properties={typeid:24,uuid:"47D04DB8-71FB-4A1C-A21E-E1C7B7C8A328"}
*/
function dc_new(_event, _triggerForm) {
    
    if ("sa_invoice_dtl" == globals.nav.form_view_01) {
        scopes.globals.avUtilities_tabSetSelectedIndex("sa_invoice_dtl", "tabs_235", 1);
    }      
    
    _super.dc_new(_event, _triggerForm);
    
    if (utils.hasRecords(foundset) && foundset.getSelectedRecord().inv_record_type == 'I') {
        if (!globals.verifyDocumentStream('INV')) {
            dc_cancel(_event, _triggerForm, i18n.getI18NMessage('svy.fr.lbl.ok'));
        }
    }
    else if (utils.hasRecords(foundset) && foundset.getSelectedRecord().inv_record_type == 'C') {
        if (!globals.verifyDocumentStream('CRE')) {
            dc_cancel(_event, _triggerForm, i18n.getI18NMessage('svy.fr.lbl.ok'));
        }
    }
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
* @param {Boolean} bHideWarning
* @param {JSFoundset<db:/avanti/sa_invoice>} [fsCreditInvoice] - Credit Invoices
* 
 * @return
* @properties={typeid:24,uuid:"AC521A12-7F10-4D89-8446-C3E623A25DA8"}
*/
function dc_delete(_event, _triggerForm, bHideWarning, fsCreditInvoice) {
	
	 /** @type {JSFoundSet<db:/avanti/sa_invoice>} */
	var fsInvoice;
	
	if (fsCreditInvoice) {
		fsInvoice = fsCreditInvoice;
	}
	else {
		fsInvoice = foundset;
	}
	
    var bReturn;
    var rInvoice;
    var iMax = fsInvoice.getSize();
    var aSelectedInvoices = [];
    globals.selectedInvoices = 0;
    for (var inv = 1; inv <= iMax; inv++) {
        rInvoice = fsInvoice.getRecord(inv);
        if (rInvoice.print_check_flag == 1 && rInvoice.inv_batch_invoice_lock) {
        	rInvoice.print_check_flag = 0;
        }
        else if (rInvoice.print_check_flag == 1 && !rInvoice.inv_batch_invoice_lock) {
        	fsInvoice.selectRecord(rInvoice.inv_id);
            if (!lockFinalInvoices()) {
                aSelectedInvoices[globals.selectedInvoices] = rInvoice.inv_id;
                globals.selectedInvoices = globals.selectedInvoices + 1;
            }
        }
    }
    
    if (globals.svy_nav_form_name == "sa_invoice_tbl" && globals.selectedInvoices > 0) {
        var _nrecords = globals.selectedInvoices;
        var _message = "";
        if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.LockFinalInvoices) == 1) {
            _message = i18n.getI18NMessage('i18n:svy.fr.dlg.selected_syspref197_yes.delete').replace("selected", _nrecords.toString());
        }
        else {
            _message = i18n.getI18NMessage('i18n:svy.fr.dlg.selected_syspref197_no.delete').replace("selected", _nrecords.toString());
        }
        var _ok = i18n.getI18NMessage('avanti.dialog.ok');
        var _no = i18n.getI18NMessage('avanti.dialog.cancel');
        var _confirmation;

        _confirmation = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.record_delete'), _message, _ok, _no);

        if (_confirmation == _ok) {

            for (var i = 0; i < globals.selectedInvoices; i++) {
                rInvoice = fsInvoice.selectRecord(aSelectedInvoices[i]);
                bReturn = deleteRecord(true);
            }
        }

    }
    else if (globals.svy_nav_form_name == "sa_credit_invoice_tbl" && globals.selectedInvoices > 0) {
    	 _nrecords = globals.selectedInvoices;
         _message = "";
         if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.LockFinalInvoices) == 1) {
             _message = i18n.getI18NMessage('i18n:svy.fr.dlg.selected_syspref197_yes.deleteCreditNotes').replace("selected", _nrecords.toString());
         }
         else {
             _message = i18n.getI18NMessage('i18n:svy.fr.dlg.selected_syspref197_no.deleteCreditNotes').replace("selected", _nrecords.toString());
         }
         _ok = i18n.getI18NMessage('avanti.dialog.ok');
         _no = i18n.getI18NMessage('avanti.dialog.cancel');
         _confirmation;

         _confirmation = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.record_delete'), _message, _ok, _no);

         if (_confirmation == _ok) {

			for (i = 0; i < globals.selectedInvoices; i++) {
				if (fsInvoice.selectRecord(aSelectedInvoices[i])) {
					if (globals.isCreditNoteChangesAllowed(fsInvoice.getSelectedRecord(), 'D')) {
						bReturn = deleteRecord(true);
					}
				}
			}
         }
    }
    else {
        if (!lockFinalInvoices()) {
            bReturn = deleteRecord(false);
        }
    }

    return bReturn;

    function deleteRecord(skipConfirmation) {
        
        var sDeleteRecord;
        
        if (skipConfirmation) {
        	sDeleteRecord = i18n.getI18NMessage('avanti.dialog.ok');
        }
        else {
			if (fsInvoice.getSelectedRecord().inv_batch_invoice_lock) {
				scopes.avText.showWarning("batchInvoicingLock");
				return;
			} else {
				sDeleteRecord = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.record_delete'),
					i18n.getI18NMessage('i18n:svy.fr.dlg.delete'),
					i18n.getI18NMessage('avanti.dialog.ok'),
					i18n.getI18NMessage('avanti.dialog.cancel'));
			}
        }

        if (sDeleteRecord == i18n.getI18NMessage('avanti.dialog.ok')) {
        
            var rDelInvoice = fsInvoice.getSelectedRecord();
            
            globals.removeCOGS(rDelInvoice);
            //Store invoice number before being deleted.
            var sInvoiceNumber = rDelInvoice.inv_number;
            var sInvoiceID = rDelInvoice.inv_id;
            var sInvStatus = rDelInvoice.inv_status;
            var sInvType = rDelInvoice.inv_type;
            var fsDeletedInvoice = createInvoiceDeletedRecord(rDelInvoice);
            var result = _super.dc_delete(_event, _triggerForm, true);

            if (result == i18n.getI18NMessage('avanti.dialog.ok')) {
                fsDeletedInvoice.deleted_response = true;
                databaseManager.saveData(fsDeletedInvoice);
                // SL-8669: Reset change order as not invoiced
                scopes.avChangeOrders.resetChgOrdsAsNotInvoiced(fsDeletedInvoice.getSelectedRecord());
                
                if (sInvStatus == scopes.avUtils.INVOICE_STATUS.PrintFinal) {
                    var nPrefValue = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AvalaraTaxTransaction);
                    if (nPrefValue == 1 && sInvType != scopes.avUtils.INVOICE_TYPE.AdvanceBilling) {
                        scopes.avTax.voidTransactionForInvoiceAvalara(rDelInvoice, false, true);
                    }
                }

                globals.avInvoice_deleteInvoiceDocRecords(sInvoiceID);
            }
        }
        else {
            // Revert new records from deleted invoice tables if user cancels delete.
            databaseManager.revertEditedRecords();
            if (databaseManager.hasRecords(fsDeletedInvoice)) {
                fsDeletedInvoice.deleted_response = false;
                databaseManager.saveData(fsDeletedInvoice);
            }
        }

        return result;
    }

}

/**
 * Create a duplicate record in invoice and related tables.
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 *
 * @return
 * @properties={typeid:24,uuid:"D39452D9-B77C-4BE0-B94F-95420D91C612"}
 */
function createInvoiceDeletedRecord(rInvoice) {
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice>} */
    var fsInvoiceDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_advance_billing>} */
    var fsInvoiceAdvanceBillingDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_advance_billing');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_commission>} */
    var fsInvoiceCommissionDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_commission');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_commission_by_r>} */
    var fsInvoiceCommissionByRepDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_commission_by_r');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_commission_sumr>} */
    var fsInvoiceCommissionSummaryRepDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_commission_sumr');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_cost_centre>} */
    var fsInvoiceCostCentreDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_cost_centre');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_cost_centre_sum>} */
    var fsInvoiceCostCentreSummaryDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_cost_centre_sum');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_det>} */
    var fsInvoiceDetailDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_det');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_det_ship>} */
    var fsInvoiceDetailShipDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_det_ship');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_distribution>} */
    var fsInvoiceDistributionDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_distribution');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_freight>} */
    var fsInvoiceFreightDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_freight');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_postage>} */
    var fsInvoicePostageDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_postage');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_tax_detail>} */
    var fsInvoiceTaxDetailDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_tax_detail');
    /** @type {JSFoundSet<db:/avanti/aud_sa_invoice_tender_trans>} */
    var fsTenderTransDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sa_invoice_tender_trans');
    /** @type {JSFoundSet<db:/avanti/aud_sys_address>} */
    var fsAddressDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sys_address');
    /** @type {JSFoundSet<db:/avanti/aud_sys_comment>} */
    var fsCommentDeleted = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'aud_sys_comment');
    
    fsInvoiceDeleted.newRecord();
    if(rInvoice && fsInvoiceDeleted.getSelectedRecord()) {
        databaseManager.copyMatchingFields(rInvoice,fsInvoiceDeleted.getSelectedRecord());
        fsInvoiceDeleted.deleted_by_id = globals.avBase_employeeUUID;
        var i;
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_advance_billing.getSize(); i++) {
            var rAdvanceBillingRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_advance_billing.getRecord(i);
            fsInvoiceAdvanceBillingDeleted.newRecord();
            databaseManager.copyMatchingFields(rAdvanceBillingRecordToCopy,fsInvoiceAdvanceBillingDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_commission.getSize(); i++) {
            var rCommissionRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_commission.getRecord(i);
            fsInvoiceCommissionDeleted.newRecord();
            databaseManager.copyMatchingFields(rCommissionRecordToCopy,fsInvoiceCommissionDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_commission_by_rep.getSize(); i++) {
            var rCommissionByRepRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_commission_by_rep.getRecord(i);
            fsInvoiceCommissionByRepDeleted.newRecord();
            databaseManager.copyMatchingFields(rCommissionByRepRecordToCopy,fsInvoiceCommissionByRepDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_commission_summ_rep.getSize(); i++) {
            var rCommissionSummaryRepRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_commission_summ_rep.getRecord(i);
            fsInvoiceCommissionSummaryRepDeleted.newRecord();
            databaseManager.copyMatchingFields(rCommissionSummaryRepRecordToCopy,fsInvoiceCommissionSummaryRepDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_cost_centre.getSize(); i++) {
            var rCostCentreRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_cost_centre.getRecord(i);
            fsInvoiceCostCentreDeleted.newRecord();
            databaseManager.copyMatchingFields(rCostCentreRecordToCopy,fsInvoiceCostCentreDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_cost_centre_summary.getSize(); i++) {
            var rCostCentreSummaryRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_cost_centre_summary.getRecord(i);
            fsInvoiceCostCentreSummaryDeleted.newRecord();
            databaseManager.copyMatchingFields(rCostCentreSummaryRecordToCopy,fsInvoiceCostCentreSummaryDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {
            var rDetailRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);
            for(var j = 1; j <= rDetailRecordToCopy.sa_invoice_det_to_sa_invoice_det_ship.getSize(); j++) {
                var rDetailShipRecordToCopy = rDetailRecordToCopy.sa_invoice_det_to_sa_invoice_det_ship.getRecord(j);
                fsInvoiceDetailShipDeleted.newRecord();
                databaseManager.copyMatchingFields(rDetailShipRecordToCopy,fsInvoiceDetailShipDeleted.getSelectedRecord());
            }
            fsInvoiceDetailDeleted.newRecord();
            databaseManager.copyMatchingFields(rDetailRecordToCopy,fsInvoiceDetailDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_distribution.getSize(); i++) {
            var rDistributionRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_distribution.getRecord(i);
            fsInvoiceDistributionDeleted.newRecord();
            databaseManager.copyMatchingFields(rDistributionRecordToCopy,fsInvoiceDistributionDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++) {
            var rFreightRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);
            fsInvoiceFreightDeleted.newRecord();
            databaseManager.copyMatchingFields(rFreightRecordToCopy, fsInvoiceFreightDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_postage.getSize(); i++) {
            var rPostageRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_postage.getRecord(i);
            fsInvoicePostageDeleted.newRecord();
            databaseManager.copyMatchingFields(rPostageRecordToCopy, fsInvoicePostageDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); i++) {
            var rTaxDetailRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(i);
            fsInvoiceTaxDetailDeleted.newRecord();
            databaseManager.copyMatchingFields(rTaxDetailRecordToCopy, fsInvoiceTaxDetailDeleted.getSelectedRecord());
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_tender_trans.getSize(); i++) {
            var rTenderTransRecordToCopy = rInvoice.sa_invoice_to_sa_invoice_tender_trans.getRecord(i);
            fsTenderTransDeleted.newRecord();
            databaseManager.copyMatchingFields(rTenderTransRecordToCopy, fsTenderTransDeleted.getSelectedRecord());
        }
        if (rInvoice.sa_invoice_to_sys_address) {
            for (i = 1; i <= rInvoice.sa_invoice_to_sys_address.getSize(); i++) {
                var rAddressRecordToCopy = rInvoice.sa_invoice_to_sys_address.getRecord(i);
                fsAddressDeleted.newRecord();
                databaseManager.copyMatchingFields(rAddressRecordToCopy, fsAddressDeleted.getSelectedRecord());
            }
        }
        for (i = 1; i <= rInvoice.sa_invoice_to_sys_comment.getSize(); i++) {
            var rCommentRecordToCopy = rInvoice.sa_invoice_to_sys_comment.getRecord(i);
            fsCommentDeleted.newRecord();
            databaseManager.copyMatchingFields(rCommentRecordToCopy, fsCommentDeleted.getSelectedRecord());
        }
    }
    return fsInvoiceDeleted;
}

/**
 * @return
 * @properties={typeid:24,uuid:"12A13AD2-A63D-433A-950E-0FC7EA0753EC"}
 */
function lockFinalInvoices() {
    if (inv_id) {
        var nPrefValue = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.LockFinalInvoices);
        if (
               inv_credit_cash_receipt_id
            || inv_status == scopes.avUtils.INVOICE_STATUS.Updated 
            || inv_status == scopes.avUtils.INVOICE_STATUS.ReadyToPost
            || inv_status == scopes.avUtils.ENUM_CREDIT_NOTE_STATUS.Applied
            || inv_status == scopes.avUtils.ENUM_CREDIT_NOTE_STATUS.Posted            
            || (inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal && nPrefValue != "1")
        ) {
            return true;
        }
    }
    
    return false;
}

/**
 * Does invoice have cash receipts
 *
 * @returns {Boolean} 
 * @private
 *
 * @properties={typeid:24,uuid:"799E2448-71CB-4413-B9BF-B281B40120FF"}
 */
function hasCashReceipts() {
    //SL-16653 If invoice is used in cash receipt then not deletable
    //SL-16862 We can edit but we can't delete the invoice. 
    if (inv_id) {
        var oSQLfs;
        /** @type {JSFoundSet<db:/avanti/sa_cash_receipt_detail>} */
        oSQLfs = new Object();
        oSQLfs.sql = "SELECT sa_cash_receipt_detail_id FROM sa_cash_receipt_detail scd \
                      INNER JOIN sa_cash_receipt cs ON cs.sa_cash_receipt_id = scd.sa_cash_receipt_id \
                      WHERE scd.inv_id = ? AND scd.org_id = ? \
                      AND (ISNULL(scd.invoice_adjustment_amount, 0) != 0 \
                        OR ISNULL(scd.invoice_payment_amount, 0) != 0 \
                        OR ISNULL(scd.invoice_discount_amount, 0) != 0 \
                        OR ISNULL(scd.invoice_total_amount, 0) != 0 \
                        OR ISNULL(scd.overpayment_amount, 0) != 0)";
        oSQLfs.table = 'sa_cash_receipt_detail';
        oSQLfs.args = [inv_id.toString(), globals.org_id];
        /** @type {JSFoundSet<db:/avanti/sa_order_revision_header>}*/
        var fsCashRcpts = globals["avUtilities_sqlFoundset"](oSQLfs);
        if (utils.hasRecords(fsCashRcpts)) {
            return true;
        }
    }

    return false;
}

/**
 * Does invoice have a negative production line.
 * If there is 1 line that has a negative extended total then cannot save, as per Tom
 * 
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * 
 * @public 
 *
 * @return
 * @properties={typeid:24,uuid:"19726C46-E25A-4F50-B0AA-7BB37CE45765"}
 */
function hasNegativeProductionLine(rInvoice) {
    if (!rInvoice) {
        return false;
    }

    /***@type {{sql:String,
     *          args:Array,
     *          server:String,
     *          maxRows:Number,
     *          table:String}}*/
    var oSQL = {},
        /***@type {JSDataSet} */
        dsData;

    oSQL.args = [globals.org_id, rInvoice.inv_id.toString()];

    oSQL.sql = "SELECT COUNT(*) AS total_negative \
                    FROM sa_invoice_det AS invd \
                    INNER JOIN sa_order_revision_detail AS ord ON invd.ordrevd_id = ord.ordrevd_id \
                    WHERE (invd.org_id = ?) AND (invd.inv_id = ?) AND (invd.clc_invd_extended_total < 0) AND (ord.job_id IS NOT NULL) \
                ";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    if (dsData && dsData.getMaxRowIndex() > 0) {
        if (dsData.getValue(1, 1) > 0) {
            return true;
        }
        else {
            return false;
        }
    }
    else {
        return false;
    }
}

/**
 * @param {JSEvent} [_event]
 * @param {String} [_triggerForm]
 *
 * @return {Object}
 * @override
 *
 * @properties={typeid:24,uuid:"B2256A5E-CBA2-434C-A0B3-F31F42D32F5F"}
 */
function dc_edit(_event, _triggerForm) {
	_super.dc_edit(_event, _triggerForm);
	
	/** @type {JSRecord<db:/avanti/sa_invoice>}*/
    var rRecord;

    if (globals.nav_program_name == 'Invoice_Entry') {
        rRecord = forms.sa_invoice_dtl.foundset.getSelectedRecord();
    } else {
        rRecord = forms.sa_credit_invoice_dtl.foundset.getSelectedRecord();
    }

    if (rRecord 
            && rRecord.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote 
            && rRecord.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.Rebill) {
    	sRealNavMode = globals.nav.mode;
        globals.nav.mode = scopes.avUtils.ENUM_NAV_MODE.Browse;
    } 
}
