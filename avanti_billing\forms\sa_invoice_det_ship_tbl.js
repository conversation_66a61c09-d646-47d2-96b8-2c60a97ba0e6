/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B1FF1881-2D77-40C9-B446-1E95EB2356BE",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"B02A114D-6FA6-44F4-BA74-1F40967EB921"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"0ECADA18-3DDB-42F7-8B66-B65435A32C77"}
 */
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	
	if(globals.nav_program_name == 'Invoice_Entry') {
		if(globals.avSecurity_checkForUserRight(globals.nav_program_name,'allow_tax_group_override_invoice',globals.avBase_employeeUserID)) {
			elements.grid.setReadOnly(false, ["taxgroup_id"]);
		} else {
			elements.grid.setReadOnly(true, ["taxgroup_id"]);
		}
		
		if(globals.avSecurity_checkForUserRight(globals.nav_program_name,'allow_tax_option_override_invoice',globals.avBase_employeeUserID)) {
			elements.grid.setReadOnly(false, ["invds_salestax_option"]);
		} else {
			elements.grid.setReadOnly(true, ["invds_salestax_option"]);
		}
	}

	clearTableProperties(); //The columns keep getting messed up.
	
	_super.onShowForm(_firstShow, _event);
    globals.avUtilities_setFormEditMode(_event.getFormName(), globals.nav.mode);
	 //var sEdit = sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type == 'C' ? 'edit' : "browse";
	 
	 //globals.avUtilities_setFormEditMode(_event.getFormName(),sEdit);
	 globals.avUtilities_setFormEditMode(_event.getFormName());
	 elements.grid.getColumn(elements.grid.getColumnIndex("pack_ship_date")).format= globals.avBase_dateFormat;
	 elements.grid.getColumn(elements.grid.getColumnIndex("invds_extended_total")).format= globals.avBase_currencyFormat_invoice;
	 
	 if (globals.nav.mode == "browse" && utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail)) {
		 foundset.sort("sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship.sa_ship_to_sa_customer_address.custaddr_code asc, sa_invoice_det_ship_to_sa_pack_detail.sa_pack_detail_to_sa_pack.pack_ship_revision asc");
	 }
	 
	 if(utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det) && 
 		utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice) && 
		sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type == 'C' ) {
		 sInvoiceQtyLabelText = i18n.getI18NMessage("avanti.lbl.creditQty");
	 } else {
		 sInvoiceQtyLabelText = i18n.getI18NMessage("avanti.lbl.invoiceQty");
	 }
	 
	 elements.grid.getColumn(elements.grid.getColumnIndex("btnShowCosting")).visible = Boolean(scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.InvoiceCostBasedOnShipmentQuantityAndCost));

	 renderTaxOptions();
	 displayTaxExemption();
	 
	 refreshUI();
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0F1DAC6E-52A4-4EDB-B8B8-DA4992BD9D93"}
 */
var btnTaxDetails_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.showSalesTaxDetails');

/**
 * @type {String}
 * @properties={typeid:35,uuid:"F97025E7-7E6B-4514-9D98-BA08FABB8CA4"}
 */
var btnShowCost_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.showInvoiceCost');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"46237DD7-68C8-4CD5-8F50-3803E4E19A3E"}
 */
var fldThreshold_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.percent_tooltip');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EF718FBC-B73B-43A0-9C35-4B96A9389E9B"}
 */
var shipd_qty_received_prod_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.qtyReceivedFromProduction');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2C57683B-166E-4FD6-91B8-337EF4A9668B"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnDelete" && col.styleClass.search(' disabled') == -1) {
		onAction_btnDeleteLine(event);
	}
	if (col.id == "btnShowCosting" && col.styleClass.search(' disabled') == -1) {
		onAction_showCostDialog(event);
	}
	if (col.id == "btnTaxDetails" && col.styleClass.search(' disabled') == -1) {
		onAction_btnTaxDetails(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"74BA4065-478F-49D5-B159-DEE860EBD976"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "invds_invoice_qty" && col.styleClass.search(' disabled') == -1) {
		onDataChange_InvoiceQty(oldValue, newValue, event);
	}
	if (col.id == "shipd_fully_invoiced" && col.styleClass.search(' disabled') == -1) {
		onDataChange_fullyShipped(oldValue, newValue, event);
	}
	if (col.id == "shipmethod_id" && col.styleClass.search(' disabled') == -1) {
		onDataChange_shipMethod(oldValue, newValue, event);
	}
	if (col.id == "invds_salestax_option" && col.styleClass.search(' disabled') == -1) {
		onDataChange_salesTaxOption(oldValue, newValue, event);
	}
	if (col.id == "taxgroup_id" && col.styleClass.search(' disabled') == -1) {
		onDataChange_taxGroup(oldValue, newValue, event);
	}
	if (col.id == "taxtype_id" && col.styleClass.search(' disabled') == -1) {
		onDataChange_TaxType(oldValue, newValue, event);
	}
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"10D7329A-5BD4-45A5-9572-C78C1D334C32"}
 */
var sInvoiceQtyLabelText = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7B75628B-E4A8-40FE-8F06-8B97BE0B0D66"}
 */
var sOriginalShipMethodType = "";

/**
 * @properties={typeid:35,uuid:"8ED14EF4-791A-4DBE-A303-4FF9BC4B657E",variableType:-4}
 */
var bCustInvoiceComplete = false;

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E60DCF95-23BC-4085-9190-30B3D9F66819"}
 */
function onDataChange_InvoiceQty(oldValue, newValue, event) {

	var bResult = true;
	var bShowWarning = false;

	if (oldValue != newValue){
		//Order line and on sa_credit_invoice_dtl
        if (globals.nav.form_view_01 == "sa_credit_invoice_dtl") {
            if (utils.hasRecords(foundset.sa_invoice_det_ship_to_sa_invoice_det) 
                    && utils.hasRecords(foundset.sa_invoice_det_ship_to_sa_ship_detail)
                    && Math.abs(newValue) > getTotalInvoiceQuantity(foundset.invds_id, foundset.sa_invoice_det_ship_to_sa_ship_detail.getRecord(1))) {
                globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), i18n.getI18NMessage('avanti.dialog.creditNotes.creditNoteQtyException'), i18n.getI18NMessage('avanti.dialog.ok'));
                bResult = false;
                newValue = validateQuantity(oldValue, sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type);
                invds_invoice_qty = newValue;
            }
            else {
                if (utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det) 
                        && utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_in_item) 
                        && !utils.hasRecords(foundset.sa_invoice_det_ship_to_sa_ship_detail)) {
                    // Do nothing, take the value as is.
                    
                }
                else{
                    newValue = validateQuantity(newValue, sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type);
                    invds_invoice_qty = newValue;
                }
            }
        }
        else {
            if (utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det) && utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_in_item) && !utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail)) {
                //do nothing, accept a positive or negative for additional charges
            }
            else {
                newValue = validateQuantity(newValue, sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type);
                invds_invoice_qty = newValue;
            }

        }

		// GD - Nov 24, 2016: SL-8274
        if (utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail) && utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail)) {
            if (sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold && invds_invoice_qty > sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.ordrevd_qty_ordered * ( 1 + sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold )) {

                invds_invoice_qty = sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.ordrevd_qty_ordered * ( 1 + sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold );

                bShowWarning = true;

            }
            else if (sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_pack_detail.packd_cancel_balance == 1 && sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold && invds_invoice_qty < sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_pack_detail.packd_qty_ordered) {

                invds_invoice_qty = sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.ordrevd_qty_ordered * ( 1 - sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold );

                bShowWarning = true;
            }
        }
		
		if (bShowWarning) {
			
			var sMsg = i18n.getI18NMessage("avanti.lbl.overUnderInvoice_msg", new Array((sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revision_detail.cust_over_under_threshold * 100).toString()));
			
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.lbl.overUnderInvoice_title"),
				sMsg,
				i18n.getI18NMessage("avanti.dialog.ok"));
		}
	}
		
    //Recalculate invoice quantity for line
    foundset.sa_invoice_det_ship_to_sa_invoice_det.invd_invoice_qty -= ( oldValue - newValue );

    var rInvoice = foundset.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.getRecord(1);

    //If the credit memo is for the entire invoice quantity then apply the deposit, otherwise no deposit adj
    if (rInvoice.inv_record_type == 'C') {
        if (utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail) && invds_invoice_qty * -1 >= sa_invoice_det_ship_to_sa_ship_detail.shipd_qty_ordered) {
            if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice)) {
                rInvoice.inv_deposits_amt = rInvoice.sa_invoice_to_sa_invoice$credit_memo_invoice.inv_deposits_amt * -1;
            }
        }
        else {
            rInvoice.inv_deposits_amt = 0;
        }
    }
    
    //Reset Job Invoice Status
    scopes.avBilling.resetJobInvoiceStatus(sa_invoice_det_ship_to_sa_invoice_det.getRecord(1));
    

    updateInvoiceFreightAmount(foundset.getSelectedRecord());

    recalculateInvoiceLine(true);

    databaseManager.recalculate(rInvoice);
    
    if (rInvoice.inv_record_type == 'C') {
        forms.sa_invoice_det_tbl.recalculateCommisionCredit();
    }
		
	return bResult;
}

/**
 * Update freight detail lines by weight invoiced.. 
 * 
 * @param {JSRecord<db:/avanti/sa_invoice_det_ship>}  rInvoiceShipDetail
 *
 * @properties={typeid:24,uuid:"0DD626C8-ADEC-4FA0-8092-6221523BCEE4"}
 */
function updateInvoiceFreightAmount(rInvoiceShipDetail) {
    if (rInvoiceShipDetail.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.sa_invoice_to_sa_invoice_freight) {
        for (var nFreightIndex = 1; nFreightIndex <= rInvoiceShipDetail.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.sa_invoice_to_sa_invoice_freight.getSize(); nFreightIndex++) {
            var rInvoiceFreight = rInvoiceShipDetail.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.sa_invoice_to_sa_invoice_freight.getRecord(nFreightIndex);
            if (rInvoiceFreight.ordrevd_id == rInvoiceShipDetail.sa_invoice_det_ship_to_sa_invoice_det.ordrevd_id && 
                utils.hasRecords(rInvoiceShipDetail.sa_invoice_det_ship_to_sa_ship_detail) && 
                rInvoiceFreight.ship_id == rInvoiceShipDetail.sa_invoice_det_ship_to_sa_ship_detail.ship_id) {
                if (!rInvoiceFreight.invf_orig_freight_amount) {
                    rInvoiceFreight.invf_orig_freight_amount = rInvoiceFreight.invf_freight_amount;
                }
                if (!rInvoiceFreight.invf_orig_freight_cost_carrier) {
                    rInvoiceFreight.invf_orig_freight_cost_carrier = rInvoiceFreight.invf_freight_cost_carrier;
                }
                var nRatio = 1;
                if (rInvoiceShipDetail.invds_invoice_qty < rInvoiceShipDetail.clc_qty_shipped) {
                    nRatio = rInvoiceShipDetail.invds_invoice_qty / rInvoiceShipDetail.clc_qty_shipped;
                }
                rInvoiceFreight.invf_freight_amount = rInvoiceFreight.invf_orig_freight_amount * nRatio;
                rInvoiceFreight.invf_freight_cost_carrier = rInvoiceFreight.invf_orig_freight_cost_carrier * nRatio;
            }
        }
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"87E2BAEB-B8FF-4705-80AF-537164F0E6AC"}
 */
function onAction_btnTaxDetails(event) {
	//Display Sales Tax Details for Shipment
	forms.sa_invoice_tax_detail_dtl.controller.loadRecords(foundset.sa_invoice_det_ship_to_sa_invoice_tax_detail)
	forms.sa_invoice_tax_detail_dtl._bPermitEditingTaxAmount = false;
	forms.sa_invoice_tax_detail_dtl._bHideTaxRate = false;

	globals.DIALOGS.showFormInModalDialog(forms.sa_invoice_tax_detail_dtl, -1, -1, -1, 575, "Sales Tax Details", true, false, "dlgSalesInvoiceTaxDetails", true);
	
	
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F8774C27-6E1F-441E-953F-EE82F065E4E1"}
 */
function onDataChange_shippingAmount(oldValue, newValue, event) {
	sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_freight_amt += (newValue - oldValue);
	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0612A861-7566-425C-BB3D-E2A5EBD05D16"}
 */
function onAction_btnDeleteLine(event) 
{
	if (!scopes.avUtils.isNavModeReadOnly())
	{
	    if (bCustInvoiceComplete
	            && !Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AllowEditOfInvoiceLinesWhenCustomerIsInvoiceComplete))) {	
	        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.lbl.invoice_complete") ,i18n.getI18NMessage("avanti.dialog.removeAllOrderLinesFromInvoice"));
	        return;  
	    }
		var rInvoiceDetail = foundset.sa_invoice_det_ship_to_sa_invoice_det.getSelectedRecord();
		var nInvoiceQty = foundset.invds_invoice_qty;
		
		var sAns = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage("svy.fr.lbl.record_delete"), i18n.getI18NMessage("svy.fr.dlg.delete"), i18n.getI18NMessage("avanti.dialog.yes"),i18n.getI18NMessage("avanti.dialog.no"));

		if (sAns == i18n.getI18NMessage("avanti.dialog.yes"))
		{
			foundset.deleteRecord(foundset.getSelectedRecord());
			
			rInvoiceDetail.invd_invoice_qty -= nInvoiceQty;
			globals.avInvoice_recalculateInvoiceLine(rInvoiceDetail, true);
			
			/*if (globals.nav.mode != "add")
			{
				dc_save(event,'svy_nav_fr_buttonbar_browser');
				dc_edit(event,'svy_nav_fr_buttonbar_browser');
			}*/
		}
	}
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2AB54CC8-AEA4-40E3-BF82-1EF6011B41C4"}
 */
function onDataChange_salesTaxOption (oldValue, newValue, event) {
	foundset.setSalesTaxGroup();
	refreshUI();
	recalculateInvoiceLine(true);
	renderTaxOptions();
	displayTaxExemption();
	return true;
}

/**
 * Refresh UI
 *
 * @properties={typeid:24,uuid:"DED1FBCD-FAAD-4827-AD46-37BA5757CF03"}
 */
function refreshUI () {
	
	elements.grid.setReadOnly(true, ["taxgroup_id"]);
	
    if (invds_salestax_option != scopes.avAccounting.TaxCode.NotTaxable) {
		elements.grid.setReadOnly(false, ["taxgroup_id"]);
    }

	elements.grid.setReadOnly(false, ["invds_invoice_qty"]);
	elements.grid.setReadOnly(false, ["shipmethod_id"]);
	elements.grid.setReadOnly(false, ["invds_salestax_option"]);
	elements.grid.setReadOnly(false, ["tax_exemption_id"]);
	elements.grid.setReadOnly(false, ["taxgroup_id"]);
	elements.grid.getColumn(elements.grid.getColumnIndex("btnDelete")).styleClass = "material-symbols-outlined delete";
	
    var rCust;
    if (utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det)
            && utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice)
            && utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.sa_invoice_to_sa_customer)) {
            rCust = sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.sa_invoice_to_sa_customer.getRecord(1);
    } 
    
    // this cust cant do partial invoices, so can not modify quanities or remove an item
    if ((!rCust || rCust.cust_invoice_complete)
            && !Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AllowEditOfInvoiceLinesWhenCustomerIsInvoiceComplete))) {
        bCustInvoiceComplete = true;
        elements.grid.setReadOnly(true, ["shipd_fully_invoiced"]);
        elements.grid.setReadOnly(true, ["invds_invoice_qty"]);
    }
    else {
        bCustInvoiceComplete = false;
        elements.grid.setReadOnly(false, ["shipd_fully_invoiced"]);
        elements.grid.setReadOnly(false, ["invds_invoice_qty"]);
    }	
    
    if (foundset.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote && 
        foundset.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.TaxOnly) {
        elements.grid.setReadOnly(true, ["invds_invoice_qty"]); 
        elements.grid.setReadOnly(true, ["shipmethod_id"]);
        elements.grid.setReadOnly(true, ["invds_salestax_option"]);
        elements.grid.setReadOnly(true, ["tax_exemption_id"]);
        elements.grid.setReadOnly(true, ["taxgroup_id"]);
        elements.grid.getColumn(elements.grid.getColumnIndex("btnDelete")).styleClass = "material-symbols-outlined delete disabled";
    }
    
    if (sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote) {
        elements.grid.setReadOnly((sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_credit_type == scopes.avUtils.CREDIT_NOTE_TYPE.AdvanceBilling ? false: true), ["shipd_fully_invoiced"]);
    }
    
    // The fully invoiced flag is not applicable to advance billing invoices so hide it.
    if (sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.inv_type == scopes.avUtils.INVOICE_TYPE.AdvanceBilling) {
        elements.grid.getColumn(elements.grid.getColumnIndex("shipd_fully_invoiced")).visible= false;
    }
    else {
        elements.grid.getColumn(elements.grid.getColumnIndex("shipd_fully_invoiced")).visible= true;
    }
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"975939FF-8541-4B07-BBB6-9CB76D93D2D1"}
 */
function onDataChange_taxGroup (oldValue, newValue, event) {
	recalculateInvoiceLine(true);
	return true
}

/**
 * recalculate the invoice line
 * 
 * @param {Boolean} [bRecalculateTaxOnly] - Recalculate tax only skipping recreating Distributions and Cost Centers. SL-14576: Recreating Distributions and Cost Centers taking too long when many invoice lines
 *
 * @properties={typeid:24,uuid:"9573FCCA-F9CA-4ABA-A3E5-3F4827B21CDB"}
 */
function recalculateInvoiceLine (bRecalculateTaxOnly) {
    
	globals.avBase_bInvoiceUpdateRunning = true;
	
	var rInvoiceDetail = foundset.sa_invoice_det_ship_to_sa_invoice_det.getSelectedRecord(), 
	    rInvDetShip = null,
	    i = 0;
	
    globals.avInvoice_recalculateInvoiceLine(rInvoiceDetail, bRecalculateTaxOnly);
	
	if (scopes.avAccounting.bGpSendInvoices) {
	    
	    var rInvoice = rInvoiceDetail.sa_invoice_det_to_sa_invoice.getRecord(1);
	    
	    for (i = 1; i <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); i++) {
	        
	        rInvDetShip = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(i);
	        rInvDetShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.deleteAllRecords();
            application.output("rInvDetShip.invds_id: " + rInvDetShip.invds_id);
	    }
	    rInvoice.sa_invoice_to_sa_invoice_tax_detail.deleteAllRecords();
	    
        scopes.avBilling.updateInvoiceShipLocationTask(rInvoice);    
        application.output("rInvoice.inv_id: " + rInvoice.inv_id);
       
        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {

            var rInvoiceDet = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);
            
            if (rInvoiceDet.invd_is_chgord == 1) {
                continue;
            }

            globals.avCalcs_salesTax_setInvoiceDetail(rInvoiceDet);
        }
        globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true, null, true);
    }

	globals.avBase_bInvoiceUpdateRunning = false;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8E5CB936-C357-48AF-8C5A-CB1FF4A8C8C2"}
 */
function onDataChange_shipMethod (oldValue, newValue, event) {
	
	if (utils.hasRecords(sa_invoice_det_ship_to_sys_shipping_method)){
		if (sOriginalShipMethodType != sa_invoice_det_ship_to_sys_shipping_method.shipmethod_type){
			taxgroup_id = globals["avCalcs_salesTax_getCustomerSalesTaxGroup"](foundset.sa_invoice_det_ship_to_sa_customer_address.getRecord(1));
			
			if (utils.hasRecords(sa_invoice_det_ship_to_sys_shipping_method)){
				if (sa_invoice_det_ship_to_sys_shipping_method.shipmethod_type == 'P'){
					
					//If pickup, then use plant / org
					if (utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship.sa_ship_to_sa_order.sa_order_to_sys_plant) && 
							utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship.sa_ship_to_sa_order.sa_order_to_sys_plant.sys_plant_to_sys_sales_tax_group$salestax)){
						taxgroup_id = sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship.sa_ship_to_sa_order.sa_order_to_sys_plant.plant_salestaxgroup_id; // use the plant sales tax group
					}
					else{
						taxgroup_id = _to_sys_organization.org_salestaxgroup_id; // use the org sales tax if a pick up
					}
				}
			}
			recalculateInvoiceLine(true);
		}
	}
	
	
	return true
}

/**
 * Handle focus gained event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BD27ADFF-8345-473C-8D5A-35339D7627AE"}
 */
function onFocusGained_shipMethod (event) {
	sOriginalShipMethodType = "";
	if (utils.hasRecords(sa_invoice_det_ship_to_sys_shipping_method)){
		sOriginalShipMethodType = sa_invoice_det_ship_to_sys_shipping_method.shipmethod_type
	}
}

/**
 * @properties={typeid:24,uuid:"87E77B38-A8B2-4632-AC99-57204BDE1176"}
 */
function displayTaxExemption() {
	var bShowTaxExemption = false;
	
	for (var nFSIndex = 1; nFSIndex <= foundset.getSize(); nFSIndex++) {
		var rTaxDetail = foundset.getRecord(nFSIndex);
		if(rTaxDetail.invds_salestax_option == 'N'
		    || (rTaxDetail.sa_invoice_det_ship_to_sa_customer_address.sa_customer_address_to_sa_customer.cust_salestax_option == scopes.avTax.SALES_TAX_OPTIONS.BasedOnCustomerShipTo
		            && rTaxDetail.sa_invoice_det_ship_to_sa_customer_address.sa_customer_address_to_sa_customer.tax_exemption_id)) {
			bShowTaxExemption = true;
		}
	}
	
	if(bShowTaxExemption) {
		elements.grid.getColumn(elements.grid.getColumnIndex("tax_exemption_id")).visible= true;
	} else {
		elements.grid.getColumn(elements.grid.getColumnIndex("tax_exemption_id")).visible= false;
	}
}

/**
 * Get total quantity invoiced
 *
 *@param {UUID} uINVDS_ID
 * @param {JSRecord<db:/avanti/sa_ship_detail>} rShipDetail - The ship detail record
 *
 * @returns {Number} Quantity Invoiced To Date
 *
 * @properties={typeid:24,uuid:"DC9452C5-56B3-4D2D-836B-702C40884B21"}
 */
function getTotalInvoiceQuantity (uINVDS_ID,rShipDetail) {
	
	var nTotal = 0;
	
	for ( var i = 1; i <= rShipDetail.sa_ship_detail_to_sa_invoice_det_ship.getSize(); i++ ) {
		var rInvoiceDetailShip = rShipDetail.sa_ship_detail_to_sa_invoice_det_ship.getRecord(i);
		if (utils.hasRecords(rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_det) && 
			utils.hasRecords(rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice)){
				if (rInvoiceDetailShip.invds_id != uINVDS_ID){
					nTotal += rInvoiceDetailShip.invds_invoice_qty;
				}
			}
	}
	
	return nTotal;
}

/**
 * @properties={typeid:24,uuid:"ABF387EE-36C5-426E-9D71-057EB6E05C39"}
 */
function renderTaxOptions() {
	if(invds_salestax_option == 'T') {
		elements.grid.getColumn(elements.grid.getColumnIndex("taxgroup_id")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("taxtype_id")).visible= false;
	} else if(invds_salestax_option == 'Y') {
		elements.grid.getColumn(elements.grid.getColumnIndex("taxgroup_id")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("taxtype_id")).visible= true;
	} else if(invds_salestax_option == 'N') {
		elements.grid.getColumn(elements.grid.getColumnIndex("taxgroup_id")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("taxtype_id")).visible= false;
	} else {
		elements.grid.getColumn(elements.grid.getColumnIndex("taxgroup_id")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("taxtype_id")).visible= false;
	}
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"037C5241-2C4F-45D3-94FD-5030A8638A3C"}
 */
function onDataChange_TaxType(oldValue, newValue, event) {
	recalculateInvoiceLine(true);
	return true
}

/**
 * clear table properties
 *
 * <AUTHOR> Dol
 * @since Feb 12, 2018
 * @private
 *
 * @properties={typeid:24,uuid:"CC4FDC24-CA79-4CB1-B88F-87FA18133D5E"}
 */
function clearTableProperties() {
    var sql = "DELETE FROM sec_user_table_properties WHERE form_name = ? AND user_id = ?";
    globals.RunSQL(sql, null, [controller.getName(), globals.user_id], globals.avBase_dbase_framework);
    forms[controller.getName()].controller.recreateUI();
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"FB55165D-6477-41C1-ADEB-8DB2F36A8635"}
 */
function onDataChange_fullyShipped(oldValue, newValue, event) {

    var bValidate = true;

    if (utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail) 
        && utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship)) {
        if (clc_qty_shipped > 0 && invds_invoice_qty >= clc_qty_shipped) {
            sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced = 1;
        }
        else if (invds_invoice_qty >= clc_qty_ordered) {
            sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced = 1;
        }

        if (sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced == 0 
            && globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.DefaultQuantityForInvoice) == scopes.avUtils.DEFAULT_INVOICE_QTY.ShippedQty) {
            //Check if the sum invoice qty > order qty
            if (utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det) 
                && utils.hasRecords(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty) 
                && sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_invoice_det_ship.total_ship_invoice_qty >= sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_order_revd_multi_ship_qty.ordrevdms_qty) {
                sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced = 1;
                bValidate = false;
            }
        }

        if (bValidate && sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced == 1) {
            if ( (clc_qty_shipped > 0 && invds_invoice_qty < clc_qty_shipped) || (clc_qty_shipped == 0 && invds_invoice_qty < clc_qty_ordered)) {
                var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
                    i18n.getI18NMessage("avanti.dialog.invoiceFullyInvoicedWithBalance_msg"), i18n.getI18NMessage('avanti.dialog.yes'),
                    i18n.getI18NMessage('avanti.dialog.no'));

                if (answer == i18n.getI18NMessage('avanti.dialog.no')) {
                    sa_invoice_det_ship_to_sa_ship_detail.shipd_fully_invoiced = 0;
                }
            }
        }

        globals.updateInvoiceStatusByShipment(sa_invoice_det_ship_to_sa_ship_detail.sa_ship_detail_to_sa_ship.getRecord(1));
        
        databaseManager.saveData(sa_invoice_det_ship_to_sa_ship_detail);
        databaseManager.refreshRecordFromDatabase(sa_invoice_det_ship_to_sa_ship_detail, 1);
    }
    
    recalculateInvoiceLine(true);
    
    return true;
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DCEEA436-A257-47EC-A92D-FB6E709CF372"}
 */
function onRender_invoiceQty(event) {
    /** @type {JSRecord<db:/avanti/sa_invoice_det_ship>}*/
    var rRec = event.getRecord();

    if (globals.nav.mode != scopes.avUtils.ENUM_NAV_MODE.Browse) {
        if (rRec && utils.hasRecords(rRec.sa_invoice_det_ship_to_sa_pack_detail) 
                 && (rRec.sa_invoice_det_ship_to_sa_pack_detail.packd_shipment_type == scopes.avUtils.ENUM_SHIPMENT_TYPE.Proofs 
                    || rRec.sa_invoice_det_ship_to_sa_pack_detail.packd_shipment_type == scopes.avUtils.ENUM_SHIPMENT_TYPE.OutsideService 
                    || rRec.sa_invoice_det_ship_to_sa_pack_detail.packd_shipment_type == scopes.avUtils.ENUM_SHIPMENT_TYPE.Other)) {
            event.getRenderable().enabled = false;
            
            var sShipType = "";
            switch (rRec.sa_invoice_det_ship_to_sa_pack_detail.packd_shipment_type) {
                case scopes.avUtils.ENUM_SHIPMENT_TYPE.Proofs:
                    sShipType = i18n.getI18NMessage("avanti.lbl.proofs");
                    break;
                    
                case scopes.avUtils.ENUM_SHIPMENT_TYPE.OutsideService:
                    sShipType = i18n.getI18NMessage("avanti.lbl.outsideServices");
                    break;
                
                case scopes.avUtils.ENUM_SHIPMENT_TYPE.Other:
                    sShipType = i18n.getI18NMessage("avanti.lbl.other");
                    break;
            
                default:
                    break;
            }
            
            event.getRenderable().toolTipText = i18n.getI18NMessage("avanti.lbl.shipmentType:") + sShipType;
        }
    }
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6AFE57F3-1346-420C-8F7F-E358579E87DF"}
 */
function onAction_showCostDialog(event) {
    if (utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det) 
            && utils.hasRecords(sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_order_revision_detail)) {
        forms.sa_invoice_det_ship_cost_dlg.showDialog(invds_cost_quantity, invds_unit_cost, invds_extended_cost, sa_invoice_det_ship_to_sa_invoice_det.sa_invoice_det_to_sa_order_revision_detail.item_id);
    }
}
